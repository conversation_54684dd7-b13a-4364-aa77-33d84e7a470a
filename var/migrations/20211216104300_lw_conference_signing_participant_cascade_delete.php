<?php


use Phinx\Migration\AbstractMigration;

class LwConferenceSigningParticipantCascadeDelete extends AbstractMigration
{

    public function up()
    {
        $this->execute('
            ALTER TABLE lw_conference_signing_participant
            DROP FOREIGN KEY FK_CONFERENCE_SIGNING_P
        ');
        $this->execute('
            ALTER TABLE lw_conference_signing_participant
            ADD CONSTRAINT FK_CONFERENCE_SIGNING_P
              FOREIGN KEY (id_conference)
              REFERENCES lw_conferences(id_conference)
              ON DELETE CASCADE
        ');
    }

    public function down()
    {
        $this->execute('
            ALTER TABLE lw_conference_signing_participant
            DROP FOREIGN KEY FK_CONFERENCE
        ');
        $this->execute('
            ALTER TABLE lw_conference_signing_participant
            ADD CONSTRAINT FK_CONFERENCE_SIGNING_P
              FOREIGN KEY(id_conference)
              REFERENCES lw_conferences(id_conference)
              ON DELETE RESTRICT
        ');


    }

}
