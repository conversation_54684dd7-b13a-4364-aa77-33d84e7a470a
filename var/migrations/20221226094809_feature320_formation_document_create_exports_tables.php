<?php


use Phinx\Migration\AbstractMigration;

class Feature320FormationDocumentCreateExportsTables extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and <PERSON><PERSON> will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $this->execute('CREATE TABLE lb_exports (id_export INT AUTO_INCREMENT NOT NULL, id_client INT NOT NULL, user_id INT, type VARCHAR(50) NOT NULL, status VARCHAR(50) NOT NULL, date_creation DATETIME NOT NULL, date_process DATETIME DEFAULT NULL, INDEX IDX_A7800FFEE173B1B8 (id_client), INDEX IDX_A7800FFEA76ED395 (user_id), PRIMARY KEY(id_export)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE lb_exports ADD CONSTRAINT FK_A7800FFEE173B1B8 FOREIGN KEY (id_client) REFERENCES lb_clients (id_client) ON DELETE CASCADE');
        $this->execute('ALTER TABLE lb_exports ADD CONSTRAINT FK_A7800FFEA76ED395 FOREIGN KEY (user_id) REFERENCES lb_users (user_id) ON DELETE SET NULL');

        $this->execute('CREATE TABLE lb_exports_download (id INT AUTO_INCREMENT NOT NULL, id_export INT NOT NULL, url VARCHAR(255) NOT NULL, file_name VARCHAR(255) NOT NULL, extension VARCHAR(10) NOT NULL, token VARCHAR(50) NOT NULL, is_expirable TINYINT(1) DEFAULT 1 NOT NULL, token_expire_date DATETIME DEFAULT NULL, INDEX IDX_7651C76F64BBA379 (id_export), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE lb_exports_download ADD CONSTRAINT FK_7651C76F64BBA379 FOREIGN KEY (id_export) REFERENCES lb_exports (id_export) ON DELETE CASCADE');

        $this->execute('CREATE TABLE lb_exports_formations_documents (id_export INT NOT NULL, id_document INT NOT NULL, idformation INT NOT NULL, recipients MEDIUMTEXT DEFAULT NULL, data VARCHAR(255) DEFAULT NULL, send_mail TINYINT(1) DEFAULT NULL, export TINYINT(1) DEFAULT NULL, INDEX IDX_6921BC6F88B266E3 (id_document), PRIMARY KEY(id_export)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE lb_exports_formations_documents ADD CONSTRAINT FK_6921BC6F88B266E3 FOREIGN KEY (id_document) REFERENCES ptf_documents (id_document) ON DELETE CASCADE');
        $this->execute('ALTER TABLE lb_exports_formations_documents ADD CONSTRAINT FK_6921BC6F3E5B884A FOREIGN KEY (idformation) REFERENCES ptf_formations (idformation) ON DELETE CASCADE');
        $this->execute('ALTER TABLE lb_exports_formations_documents ADD CONSTRAINT FK_6921BC6FBF396750 FOREIGN KEY (id_export) REFERENCES lb_exports (id_export) ON DELETE CASCADE');
    }

    public function down()
    {
        $this->execute('ALTER TABLE lb_exports_formations_documents DROP FOREIGN KEY FK_6921BC6FBF396750');
        $this->execute('DROP TABLE lb_exports_formations_documents');

        $this->execute('ALTER TABLE lb_exports_download DROP FOREIGN KEY FK_7651C76F64BBA379');
        $this->execute('DROP TABLE lb_exports_download');

        $this->execute('DROP TABLE lb_exports');
    }
}
