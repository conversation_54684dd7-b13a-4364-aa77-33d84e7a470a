<?php


use Phinx\Migration\AbstractMigration;

class Mail<PERSON><PERSON><PERSON><PERSON><PERSON> extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and <PERSON><PERSON> will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $clients = $this->fetchAll("SELECT * FROM " . DB_PREFIX . "config WHERE name='mailchimp-token'");
        foreach ($clients as $client) {
            $idClient = $client['id_client'];
            $mailchimpToken = $client['value'];

            $getMailchimpDc = $this->fetchAll("SELECT * FROM " . DB_PREFIX . "config WHERE name='mailchimp-dc' AND id_client='$idClient'");
            if (!$getMailchimpDc) {
                continue;
            }

            $mailchimpDc = $getMailchimpDc[0]['value'];

            $mailchimpApiKey = $mailchimpToken . '-' . $mailchimpDc;

            $this->insert(DB_PREFIX . 'config', [
                'id_client' => $idClient,
                'name' => 'mailchimp_api_key',
                'value' => $mailchimpApiKey,
                'date_add' => date('Y-m-d H:i:s'),
                'date_upd' => date('Y-m-d H:i:s'),
            ]);
        }
    }

    /**
     * Down Method
     */
    public function down()
    {
        $this->execute("DELETE FROM " . DB_PREFIX . "config WHERE name='mailchimp-api'");
    }
}
