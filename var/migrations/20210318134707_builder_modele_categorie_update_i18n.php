<?php


use Phinx\Migration\AbstractMigration;

class BuilderModeleCategorieUpdateI18n extends AbstractMigration
{
    public function change()
    {
        $excepts = [13, 15, 19, 23, 25, 27, 29];
        for ($i=1; $i<31; $i++) {
            if (in_array($i, $excepts)) {
                continue;
            }
            $sql = 'UPDATE builder_modeles_categories SET nom = "builder_modeles_categories.nom.' . $i . '" WHERE id_categorie = ' . $i;
            $this->execute($sql);
        }

        $this->execute('UPDATE builder_modeles_categories SET nom = "builder_modeles_categories.nom.12" WHERE id_categorie = 15');
        $this->execute('UPDATE builder_modeles_categories SET nom = "builder_modeles_categories.nom.14" WHERE id_categorie = 17');
        $this->execute('UPDATE builder_modeles_categories SET nom = "builder_modeles_categories.nom.18" WHERE id_categorie = 19');
        $this->execute('UPDATE builder_modeles_categories SET nom = "builder_modeles_categories.nom.20" WHERE id_categorie = 21');
        $this->execute('UPDATE builder_modeles_categories SET nom = "builder_modeles_categories.nom.22" WHERE id_categorie = 23');
        $this->execute('UPDATE builder_modeles_categories SET nom = "builder_modeles_categories.nom.24" WHERE id_categorie = 25');
        $this->execute('UPDATE builder_modeles_categories SET nom = "builder_modeles_categories.nom.26" WHERE id_categorie = 27');
        $this->execute('UPDATE builder_modeles_categories SET nom = "builder_modeles_categories.nom.28" WHERE id_categorie = 29');
        $this->execute('UPDATE builder_modeles_categories SET nom = "builder_modeles_categories.nom.30" WHERE id_categorie = 31');
        $this->execute('UPDATE builder_modeles_categories SET nom = "builder_modeles_categories.nom.40" WHERE id_categorie = 40');
        $this->execute('UPDATE builder_modeles_categories SET nom = "builder_modeles_categories.nom.40" WHERE id_categorie = 41');
    }
}
