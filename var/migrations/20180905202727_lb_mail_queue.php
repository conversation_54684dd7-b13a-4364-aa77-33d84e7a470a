<?php


use Phinx\Migration\AbstractMigration;

class LbMailQueue extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and <PERSON><PERSON> will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        @ini_set('memory_limit', '1024M');

        //remove id_client
        $clients = $this->fetchAll('SELECT id_client FROM ' . DB_PREFIX . 'clients');
        $clients = $this->array_change_key($clients, 'id_client');

        $mail_queues = $this->fetchAll('SELECT DISTINCT(id_client) FROM lb_mail_queue');
        $mail_queues = $this->array_change_key($mail_queues, 'id_client');

        if ($mail_queues) {
            foreach ($mail_queues as $id_client => $mail_queue) {
                if (!isset($clients[$id_client])) {
                    $this->execute('DELETE FROM lb_mail_queue WHERE id_client = ' . $id_client);
                }
            }
        }


        //remove id_client from lbar_mail_queue_users
        $mail_queue_users = $this->fetchAll('SELECT DISTINCT(id_client) FROM lb_mail_queue_users');
        $mail_queue_users = $this->array_change_key($mail_queue_users, 'id_client');

        if ($mail_queue_users) {
            foreach ($mail_queue_users as $id_client => $mail_queue_user) {
                if (!isset($clients[$id_client])) {
                    $this->execute('DELETE FROM lb_mail_queue_users WHERE id_client = ' . $id_client);
                }
            }
        }

        //remove id_client from lbar_mail_queue_users
        $mail_queue_mails = $this->fetchAll('SELECT id_mail FROM lb_mail_queue');
        $mail_queue_mails = $this->array_change_key($mail_queue_mails, 'id_mail');

        $mail_queue_users_mails = $this->fetchAll('SELECT DISTINCT(id_mail) FROM lb_mail_queue_users');
        $mail_queue_users_mails = $this->array_change_key($mail_queue_users_mails, 'id_mail');

        if ($mail_queue_users_mails) {
            foreach ($mail_queue_users_mails as $id_mail => $mail_queue_users_mail) {
                if (!isset($mail_queue_mails[$id_mail])) {
                    $this->execute('DELETE FROM lb_mail_queue_users WHERE id_mail = ' . $id_mail);
                }
            }
        }


        //add indexes
        $table = $this->table('lb_mail_queue');
        $table->addIndex('id_client')
            ->addForeignKey('id_client', 'lb_clients', 'id_client', ['delete'=> 'CASCADE', 'update'=> 'NO_ACTION'])
            ->save();

        $table = $this->table('lb_mail_queue_users');
        $table->addIndex('id_mail')
            ->addIndex('id_client')
            ->addForeignKey('id_client', 'lb_clients', 'id_client', ['delete'=> 'CASCADE', 'update'=> 'NO_ACTION'])
            ->addForeignKey('id_mail', 'lb_mail_queue', 'id_mail', ['delete'=> 'CASCADE', 'update'=> 'NO_ACTION'])
            ->save();
    }

    public function down()
    {
        $table = $this->table('lb_mail_queue');
        $table->dropForeignKey('id_client')
            ->save();

        $table = $this->table('lb_mail_queue_users');
        $table->removeIndex('id_mail')
            ->dropForeignKey('id_client')
            ->dropForeignKey('id_mail')
            ->save();
    }

    public function array_change_key($array, $key1, $key2 = '')
    {
        $array_output = array();
        if (!is_array($array)) {
            return;
        }
        if(empty($array)) {
            return $array_output;
        }

        foreach ($array as $datas) {
            if (isset($datas[$key1])) {
                if ($key2 and isset($datas[$key2])) {
                    $array_output[$datas[$key1]] = $datas[$key2];
                } else {
                    $array_output[$datas[$key1]] = $datas;
                }
            }
        }

        return $array_output;
    }
}
