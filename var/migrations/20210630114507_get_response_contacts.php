<?php


use Phinx\Migration\AbstractMigration;

class GetResponseContacts extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and <PERSON>nx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table(DB_PREFIX . 'getresponse_contacts')
            ->addColumn('email', 'string', ['limit' => 255])
            ->addColumn('contact_id', 'string', ['null' => true, 'limit' => 255])
            ->addColumn('date', 'datetime')
            ->addIndex('email', ['unique' => true])
            ->addIndex('contact_id')
            ->create();
    }
}
