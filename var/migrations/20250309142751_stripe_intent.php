<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class StripeIntent extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $table = $this->table(DB_PREFIX . 'stripe_intents');
        $table->addColumn('id_client', 'integer')
            ->addColumn('intent_id', 'string', ['limit' => 255])
            ->addColumn('intent_type', 'string', ['limit' => 50])
            ->addColumn('status', 'string', ['limit' => 50])
            ->addColumn('id_trans', 'string', ['limit' => 50])
            ->addColumn('id_formulaire', 'integer', ['null' => true])
            ->addColumn('id_customer', 'integer', ['null' => true])
            ->addColumn('id_cart', 'integer', ['null' => true])
            ->addColumn('first_name', 'string', ['null' => true])
            ->addColumn('last_name', 'string', ['null' => true])
            ->addColumn('email', 'string', ['null' => true])
            ->addColumn('custom', 'text', ['null' => true])
            ->addColumn('amount', 'integer', ['null' => true])
            ->addColumn('currency', 'string', ['limit' => 3, 'default' => 'eur'])
            ->addColumn('post', 'json')
            ->addColumn('result', 'text')
            ->addColumn('created_at', 'datetime')
            ->addForeignKey('id_client', DB_PREFIX . 'clients', 'id_client', ['delete' => 'CASCADE'])
            ->addIndex('intent_id')
            ->create();
    }
}
