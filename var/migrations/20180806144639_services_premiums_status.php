<?php


use Phinx\Migration\AbstractMigration;

class ServicesPremiumsStatus extends AbstractMigration
{
    public function up()
    {
        $this->table(DB_PREFIX . 'premiums_services_status', ['id' => 'id'])
           ->addColumn('libelle', 'string')
            ->save();

        $rows = [
            [
                'id'    => 1,
                'libelle'  => 'Enregistré'
            ],
            [
                'id'    => 2,
                'libelle'  => 'En cours d\'exécution'
            ],
            [
                'id'    => 3,
                'libelle'  => 'Terminé'
            ],
            [
                'id'    => 4,
                'libelle'  => 'Annulé'
            ]
        ];

        $this->table(DB_PREFIX . 'premiums_services_status')->insert($rows)->save();
    }

    public function down()
    {
        $this->table(DB_PREFIX . 'premiums_services_status')->drop()->save();
    }
}
