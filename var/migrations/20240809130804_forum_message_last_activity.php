<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class ForumMessageLastActivity extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        $table = $this->table('ptf_forums_messages');
        $table->addColumn('date_last_activity', 'datetime', ['null' => true, 'after' => 'date_modification'])
            ->update();

        $this->execute("UPDATE ptf_forums_messages SET date_last_activity = date");
    }

    public function down(): void
    {
        $table = $this->table('ptf_forums_messages');
        $table->removeColumn('date_last_activity')
            ->update();
    }
}
