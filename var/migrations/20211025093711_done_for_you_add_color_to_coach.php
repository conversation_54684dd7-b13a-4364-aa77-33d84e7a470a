<?php

use Phinx\Migration\AbstractMigration;

class DoneForYouAddColorToCoach extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and <PERSON>nx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $colors = [
            '\'#46B2CC\'',
            '\'#D96F32\'',
            '\'#359e18\'',
            '\'#9119a8\''
        ];

        $this->execute('ALTER TABLE lb_coachs ADD color VARCHAR(20) DEFAULT \'#AAAAAA\'');

        $activeCoachs = $this->fetchAll('SELECT id_coach FROM lb_coachs WHERE active = 1');

        for ($i = 0; $i <= count($activeCoachs) - 1; $i++) {
            if (isset($colors[$i])) {
                $this->execute('UPDATE lb_coachs SET color = ' . $colors[$i] . ' WHERE id_coach = ' . $activeCoachs[$i]['id_coach']);
            }
        }
    }
}
