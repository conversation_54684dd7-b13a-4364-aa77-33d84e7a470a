<?php


use Phinx\Migration\AbstractMigration;

class HrAppCollaboratorVacationCreditRecoveryCollaboratorVacationMigration extends AbstractMigration
{
    public function up() : void
    {
        $this->execute('ALTER TABLE hr_collaborator_vacation_credit_recovery ADD collaborator_vacation_id INT DEFAULT NULL');
        $this->execute('ALTER TABLE hr_collaborator_vacation_credit_recovery ADD CONSTRAINT FK_DCFDE7F83FD10537 FOREIGN KEY (collaborator_vacation_id) REFERENCES hr_collaborator_vacation (id)');
        $this->execute('CREATE UNIQUE INDEX UNIQ_DCFDE7F83FD10537 ON hr_collaborator_vacation_credit_recovery (collaborator_vacation_id)');
    }

    public function down() : void
    {
        $this->execute('ALTER TABLE hr_collaborator_vacation_credit_recovery DROP FOREIGN KEY FK_DCFDE7F83FD10537');
        $this->execute('DROP INDEX UNIQ_DCFDE7F83FD10537 ON hr_collaborator_vacation_credit_recovery');
        $this->execute('ALTER TABLE hr_collaborator_vacation_credit_recovery DROP collaborator_vacation_id');
    }
}
