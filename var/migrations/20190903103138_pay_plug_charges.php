<?php


use Phinx\Migration\AbstractMigration;

class PayPlugCharges extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and <PERSON>nx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $table = $this->table(DB_PREFIX . 'payplug_charges');
        $table->addColumn('secret_key', 'string', ['after' => 'id_cart', 'limit' => 100])
            ->addColumn('card', 'string', ['after' => 'secret_key', 'limit' => 100])
            ->addColumn('last4', 'string', ['after' => 'result', 'limit' => 20])
            ->changeColumn('amount', 'float')
            ->addColumn('adresse', 'string', ['after' => 'email', 'limit' => 100])
            ->addColumn('code_postal', 'string', ['after' => 'adresse', 'limit' => 100])
            ->addColumn('ville', 'string', ['after' => 'code_postal', 'limit' => 100])
            ->addColumn('pays', 'string', ['after' => 'ville', 'limit' => 100])
            ->addColumn('telephone', 'string', ['after' => 'pays', 'limit' => 100])
            ->update();

        $table = $this->table(DB_PREFIX . 'payplug_abonnements');
        $table->addColumn('checkout', 'text', ['after' => 'custom'])
            ->addColumn('token', 'string', ['after' => 'card', 'limit' => 100])
            ->addColumn('adresse', 'string', ['after' => 'email', 'limit' => 100])
            ->addColumn('code_postal', 'string', ['after' => 'adresse', 'limit' => 100])
            ->addColumn('ville', 'string', ['after' => 'code_postal', 'limit' => 100])
            ->addColumn('pays', 'string', ['after' => 'ville', 'limit' => 100])
            ->addColumn('telephone', 'string', ['after' => 'pays', 'limit' => 100])
            ->update();
    }
}
