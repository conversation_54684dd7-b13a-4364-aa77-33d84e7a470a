<?php


use Phinx\Migration\AbstractMigration;

class Feature6OnTheBicheCreateMastertheme7 extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and <PERSON>nx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $design = json_encode([
            'bgcolor1' => '',
            'bgcolor2' => '',
            'gradient_angle' => 0,
            'colors' => [
                0 => '#E6AD89',
                1 => '#354463',
                2 => '#FEE8D6'
            ],
            'color_gray' => '#a5a5a5',
            'fonts' => [
                0 => 'Playfair Display',
                1 => 'Poppins'
            ],
            'font_size' => [
                'h1' => [
                    'font_size' => '52',
                    'font_color' => '#354463',
                    'font_family' => 'Playfair Display'
                ],
                'h2' => [
                    'font_size' => '36',
                    'font_color' => '#354463',
                    'font_family' => 'Playfair Display'
                ],
                'h3' => [
                    'font_size' => '28',
                    'font_color' => '#354463',
                    'font_family' => 'Playfair Display'
                ],
                'h4' => [
                    'font_size' => '24',
                    'font_color' => '#354463',
                    'font_family' => 'Poppins'
                ],
                'h5' => [
                    'font_size' => '20',
                    'font_color' => '#354463',
                    'font_family' => 'Poppins'
                ],
                'h6' => [
                    'font_size' => '16',
                    'font_color' => '#354463',
                    'font_family' => 'Poppins'
                ],
                'p' => [
                    'font_size' => '16',
                    'font_color' => '#354463',
                    'font_family' => 'Poppins'
                ],
                'small' => [
                    'font_size' => '14',
                    'font_color' => '#354463',
                    'font_family' => 'Poppins'
                ]
            ],
            'line_themes' => [
                1 => [
                    'bgcolor1' => '#FFFFFF',
                    'bgcolor2' => '',
                    'angle' => '0',
                    'opacity' => '',
                    'opacity2' => ''
                ],
                2 => [
                    'bgcolor1' => '#FFF8F3',
                    'bgcolor2' => '',
                    'angle' => '0',
                    'opacity' => '',
                    'opacity2' => ''
                ],
                3 => [
                    'bgcolor1' => '#E6AD89',
                    'bgcolor2' => '',
                    'angle' => '',
                    'opacity' => '',
                    'opacity2' => ''
                ],
                4 => [
                    'bgcolor1' => '#E6AD89BF',
                    'bgcolor2' => '',
                    'angle' => '',
                    'opacity' => '0.75',
                    'opacity2' => '0.75'
                ]
            ]
        ], JSON_FORCE_OBJECT);

        $this->execute("INSERT INTO builder_master_themes VALUES (10, 'MasterTheme7', 'mastertheme7', '', '', NOW())");
        $this->execute("INSERT INTO builder_themes VALUES (103, 10, 1, 0, 0, '$design', '')");
    }

    public function down()
    {
        $this->execute('DELETE FROM builder_themes WHERE id_master_theme = 10');
        $this->execute('DELETE FROM builder_master_themes WHERE id_master_theme = 10');
    }
}
