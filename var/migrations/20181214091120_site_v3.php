<?php


use Phinx\Migration\AbstractMigration;

class SiteV3 extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and <PERSON><PERSON> will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $this->execute("
          INSERT INTO `lb_themes` (`name`, `displayName`, `version`, `author`, `author_email`, `author_website`, `preview`, `preview_large`, `active`, `date`) VALUES
          ('focusweb', 'FocusWeb', '1.0', '<PERSON>hieu <PERSON>', '<EMAIL>', 'https://learnybox.com', 'preview.png', 'preview-large.png', 1, '2015-01-25 13:23:51');
        ");

        $this->execute("
          INSERT INTO `builder_themes_pages` (`name`, `type`, `version`, `nb_utilisations`, `date`) VALUES
          ('site-index', 'site', '', 0, '2018-12-14 09:41:31'),
          ('site-articles', 'site', '', 0, '2018-12-14 10:29:23'),
          ('site-article', 'site', '', 0, '2018-12-14 10:41:05'),
          ('site-contact', 'site', '', 0, '2018-12-14 10:41:05'),
          ('site-connexion', 'site', '', 0, '2018-12-14 10:41:05');
        ");

        $this->execute('UPDATE builder_modeles_cols SET design = \'{"bgcolor1":"#ffffff","bgcolor2":"","gradient_angle":"0","bgimage":"","bgimage_position":"","bgimage_size":"auto","bg_color_overlay1":"","bg_color_overlay2":"","bg_color_overlay_opacity":"0","bg_overlay_gradient_angle":"0","padding_top":"24","padding_bottom":"24","padding_left":"24","padding_right":"24","margin_top":"0","margin_bottom":"0","margin_left":"0","margin_right":"0","border_width":"0","border_color":"","border_color_opacity":"1","border_style":"none","border_radius_top_left":"8","border_radius_top_right":"8","border_radius_bottom_left":"8","border_radius_bottom_right":"8","box_shadow_x":"0","box_shadow_y":"0","box_shadow_blur":"0","box_shadow_color":"","box_shadow_opacity":"1","delay":"0","delay_disappear":"0","full_width":"0","box_shadow":"1","bg_side":"0","bg_side_position":"","bg_side_width":"100","bg_side_height":"100"}\' WHERE id_modele = 53;');

        $table = $this->table('lb_pages');
        $table->addColumn('id_master_theme', 'integer', ['after' => 'type'])
            ->addColumn('id_theme', 'integer', ['after' => 'id_master_theme'])
            ->update();

        $table = $this->table('lb_pages_lines');
        $table->addColumn('type', 'string', ['limit' => 10, 'after' => 'id_client'])
            ->update();

        $table = $this->table('lb_pages_lines_backups');
        $table->addColumn('type', 'string', ['limit' => 10, 'after' => 'id_page'])
            ->update();
    }

    public function down()
    {
        $this->execute("DELETE FROM lb_themes WHERE name='focusweb';");

        $this->execute("DELETE FROM builder_themes_pages WHERE name='site-index';");
        $this->execute("DELETE FROM builder_themes_pages WHERE name='site-articles';");
        $this->execute("DELETE FROM builder_themes_pages WHERE name='site-article';");
        $this->execute("DELETE FROM builder_themes_pages WHERE name='site-contact';");
        $this->execute("DELETE FROM builder_themes_pages WHERE name='site-connexion';");

        $table = $this->table('lb_pages');
        $table->removeColumn('id_master_theme')
            ->removeColumn('id_theme')
            ->update();

        $table = $this->table('lb_pages_lines');
        $table->removeColumn('type')
            ->update();

        $table = $this->table('lb_pages_lines_backups');
        $table->removeColumn('type')
            ->update();
    }
}
