<?php


use Phinx\Migration\AbstractMigration;

class AddIndexes2 extends AbstractMigration
{
    public function up()
    {
        $this->execute('ALTER TABLE lb_tunnels_comments ADD INDEX parent(parent)');
        $this->execute('ALTER TABLE lb_clients_abonnements ADD INDEX id_client(id_client)');
        $this->execute('ALTER TABLE lb_clients_abonnements ADD INDEX etat(etat)');
        $this->execute('ALTER TABLE lb_clients_abonnements ADD INDEX id_produit(id_produit)');
        $this->execute('ALTER TABLE lb_clients_abonnements ADD INDEX id_customer(id_customer)');
        $this->execute('ALTER TABLE ptf_modules ADD INDEX parent(parent)');
        $this->execute('ALTER TABLE oauth_access_token ADD INDEX token(token)');

    }

    public function down()
    {
        $this->execute('ALTER TABLE lb_tunnels_comments DROP INDEX parent');
        $this->execute('ALTER TABLE lb_clients_abonnements DROP INDEX id_client');
        $this->execute('ALTER TABLE lb_clients_abonnements DROP INDEX etat');
        $this->execute('ALTER TABLE lb_clients_abonnements DROP INDEX id_produit');
        $this->execute('ALTER TABLE lb_clients_abonnements DROP INDEX id_customer');
        $this->execute('ALTER TABLE ptf_modules DROP INDEX parent');
        $this->execute('ALTER TABLE oauth_access_token DROP INDEX token');
    }
}
