<?php


use Phinx\Migration\AbstractMigration;

class UpdateConference extends AbstractMigration
{
    public function up()
    {
        $this->execute('ALTER TABLE lw_conferences ADD rgpd_checked_default TINYINT(1) NOT NULL, ADD rgpd_under_submit_button TINYINT(1) NOT NULL, ADD rgpd_aff_checked_default TINYINT(1) NOT NULL, ADD rgpd_aff_under_submit_button TINYINT(1) NOT NULL');
    }

    public function down()
    {
        $this->execute('ALTER TABLE lw_conferences DROP rgpd_checked_default, DROP rgpd_under_submit_button, DROP rgpd_aff_checked_default, DROP rgpd_aff_under_submit_button');
    }
}
