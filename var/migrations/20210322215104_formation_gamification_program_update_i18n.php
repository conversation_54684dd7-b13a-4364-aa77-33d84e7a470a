<?php


use Phinx\Migration\AbstractMigration;

class FormationGamificationProgramUpdateI18n extends AbstractMigration
{
    public function change()
    {
        for ($i=1; $i<5; $i++) {
            $sql = 'UPDATE ptf_gamification_programs SET nom = "ptf_gamification_programs.nom.' . $i . '" WHERE id = ' . $i;
            $this->execute($sql);
            $sql = 'UPDATE ptf_gamification_programs SET description = "ptf_gamification_programs.description.' . $i . '" WHERE id = ' . $i;
            $this->execute($sql);
        }
    }
}
