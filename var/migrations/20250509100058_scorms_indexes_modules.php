<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class ScormsIndexesModules extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        @ini_set('memory_limit', '4096M');

        $table = $this->table('ptf_scorms_datas');
        $table->changeColumn('idmodule', 'integer', ['null' => true])
            ->save();
        $this->execute("UPDATE ptf_scorms_datas SET idmodule = NULL WHERE idmodule = 0;");

        $modules = $this->fetchAll('SELECT idmodule FROM ptf_modules');
        $modules = $this->array_change_key($modules, 'idmodule');

        $scorms = $this->fetchAll('SELECT DISTINCT(idmodule) FROM ptf_scorms_datas WHERE idmodule IS NOT NULL');
        $scorms = $this->array_change_key($scorms, 'idmodule');

        foreach ($scorms as $idmodule => $scorm) {
            if (!isset($modules[$idmodule])) {
                $this->execute('DELETE FROM ptf_scorms_datas WHERE idmodule = ' . $idmodule);
            }
        }

        $table = $this->table('ptf_scorms_datas');
        $table->addForeignKey('idmodule', 'ptf_modules', 'idmodule', ['delete'=> 'CASCADE', 'update'=> 'NO_ACTION'])
            ->save();
    }

    public function down()
    {

    }

    public function array_change_key($array, $key1, $key2 = ''): array
    {
        $array_output = array();
        if (!is_array($array)) {
            return [];
        }
        if (empty($array)) {
            return $array_output;
        }

        foreach ($array as $datas) {
            if (isset($datas[$key1])) {
                if ($key2 and isset($datas[$key2])) {
                    $array_output[$datas[$key1]] = $datas[$key2];
                } else {
                    $array_output[$datas[$key1]] = $datas;
                }
            }
        }

        return $array_output;
    }
}
