<?php


use Phinx\Migration\AbstractMigration;

class TransactionsErrorsStatus extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and <PERSON>nx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $table = $this->table(DB_PREFIX . 'transactions_errors_status');
        $table->changeColumn('icon', 'string', ['limit' => 30])
            ->update();

        $status = [
            [
                'name'  => 'payment_in_progress',
                'color' => '#21a3ff',
                'icon' => 'nc-icon nc-icon-payment',
                'libelle' => 'En cours de paiement',
            ],
            [
                'name'  => 'needs_delay',
                'color' => '#21a3ff',
                'icon' => 'fa fa-clock-o',
                'libelle' => 'Demande de délai de paiement',
            ]
        ];

        $table->insert($status)->save();
    }
}
