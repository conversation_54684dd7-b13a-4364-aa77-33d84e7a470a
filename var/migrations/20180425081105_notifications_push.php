<?php


use Phinx\Migration\AbstractMigration;

class NotificationsPush extends AbstractMigration
{
    public function up()
    {
        $this->query(
            'DROP TABLE IF EXISTS ' . DB_PREFIX . 'notifications_integrations
            ,' . DB_PREFIX . 'notifications_settings
            ,' . DB_PREFIX . 'notifications_welcome
            ,' . DB_PREFIX . 'notifications
            ,' . DB_PREFIX . 'notifications_campaigns
            ,' . DB_PREFIX . 'notifications_applications
            ,' . DB_PREFIX . 'notifications_links
            ,' . DB_PREFIX . 'notifications_queue
            ,' . DB_PREFIX . 'notifications_subscriptions'
        );

        /**
         * Table notifications_campaigns
         */
        $this->table(DB_PREFIX . 'notifications_campaigns', ['id' => 'id_campaign'])
            ->addColumn('id_client', 'integer', ['limit' => 11])
            ->addColumn('date', 'datetime')
            ->addColumn('title', 'string', ['limit' => 100])
            ->addColumn('subtitle', 'string', ['limit' => 100])
            ->addColumn('popup_title', 'string', ['limit' => 30])
            ->addColumn('popup_message', 'string', ['limit' => 90])
            ->addColumn('popup_image', 'text')
            ->addColumn('welcome_title', 'string', ['limit' => 250])
            ->addColumn('welcome_message', 'text')
            ->addColumn('welcome_url', 'string', ['limit' => 250, 'null' => true])
            ->addColumn('welcome_image', 'string', ['limit' => 250, 'null' => true])
            ->addColumn('welcome_ttl', 'integer', ['default' => 25600])
            ->addColumn('welcome_interaction', 'integer', ['default' => 0])
            ->addColumn('pages', 'text')
            ->addColumn('devices', 'integer', ['default' => 0])
            ->addColumn('status', 'boolean', ['default' => 1])
            ->addForeignKey('id_client', DB_PREFIX . 'clients', 'id_client', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();

        /**
         * Table notifications
         */
        $this->table(DB_PREFIX . 'notifications', ['id' => 'id'])
            ->addColumn('id_client', 'integer', ['limit' => 11])
            ->addColumn('id_campaign', 'integer')
            ->addColumn('onesignal_id', 'string', ['limit' => 50])
            ->addColumn('date', 'datetime')
            ->addColumn('date_envoi', 'datetime')
            ->addColumn('title', 'string', ['limit' => 100])
            ->addColumn('message', 'string', ['limit' => 200])
            ->addColumn('random_id', 'string', ['limit' => 100, 'null' => true])
            ->addColumn('image', 'string', ['limit' => 100])
            ->addColumn('ttl', 'integer')
            ->addColumn('sent', 'integer')
            ->addColumn('clics', 'integer')
            ->addColumn('status', 'string', ['limit' => 10])
            ->addColumn('response', 'text')
            ->addForeignKey('id_client', DB_PREFIX . 'clients', 'id_client', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('id_campaign', DB_PREFIX . 'notifications_campaigns', 'id_campaign', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();

        /**
         * Table notifications_applications
         */
        $this->table(DB_PREFIX . 'notifications_applications', ['id' => 'id_application'])
            ->addColumn('id_client', 'integer', ['limit' => 11])
            ->addColumn('onesignal_app_id', 'string', ['limit' => 250, 'null' => true])
            ->addColumn('onesignal_api_key', 'string', ['limit' => 250])
            ->addColumn('date', 'datetime')
            ->addForeignKey('id_client', DB_PREFIX . 'clients', 'id_client', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();

        /**
         * Table notifications_links
         */
        $this->table(DB_PREFIX . 'notifications_links', ['id' => 'id_link'])
            ->addColumn('id_client', 'integer', ['limit' => 11])
            ->addColumn('id_notification', 'integer')
            ->addColumn('url', 'text')
            ->addColumn('nb_clics', 'integer')
            ->addColumn('random_id', 'string', ['limit' => 20])
            ->addColumn('date', 'datetime')
            ->addForeignKey('id_client', DB_PREFIX . 'clients', 'id_client', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();

        /**
         * Table notifications_queue
         */
        $this->table(DB_PREFIX . 'notifications_queue', ['id' => 'id'])
            ->addColumn('id_client', 'integer', ['limit' => 11])
            ->addColumn('id_notification', 'integer')
            ->addColumn('id_campaign', 'integer')
            ->addColumn('title', 'string', ['limit' => 100])
            ->addColumn('sent', 'boolean', ['default' => 0])
            ->addColumn('error', 'boolean', ['default' => 0])
            ->addColumn('error_message', 'string', ['limit' => 250, 'null' => true])
            ->addColumn('in_process', 'boolean', ['default' => 0])
            ->addColumn('date_sent', 'datetime')
            ->addForeignKey('id_client', DB_PREFIX . 'clients', 'id_client', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('id_notification', DB_PREFIX . 'notifications', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('id_campaign', DB_PREFIX . 'notifications_campaigns', 'id_campaign', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();

        /**
         * Table notifications_subscriptions
         */
        $this->table(DB_PREFIX . 'notifications_subscriptions', ['id' => 'id'])
            ->addColumn('id_client', 'integer', ['limit' => 11])
            ->addColumn('id_campaign', 'integer')
            ->addColumn('id_device', 'string', ['limit' => 1000])
            ->addColumn('ip', 'string', ['limit' => 100])
            ->addColumn('browser', 'string', ['limit' => 50])
            ->addColumn('date', 'datetime')
            ->addForeignKey('id_client', DB_PREFIX . 'clients', 'id_client', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('id_campaign', DB_PREFIX . 'notifications_campaigns', 'id_campaign', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();
    }

    public function down()
    {
        $this->dropTable(DB_PREFIX . 'notifications_subscriptions');
        $this->dropTable(DB_PREFIX . 'notifications_queue');
        $this->dropTable(DB_PREFIX . 'notifications_links');
        $this->dropTable(DB_PREFIX . 'notifications_applications');
        $this->dropTable(DB_PREFIX . 'notifications');
        $this->dropTable(DB_PREFIX . 'notifications_campaigns');
    }
}
