<?php


use Phinx\Migration\AbstractMigration;

class DoneF<PERSON>YouMigration extends AbstractMigration
{
    public function up()
    {
        $this->execute('CREATE TABLE lb_doneforyou_attached_file (id INT AUTO_INCREMENT NOT NULL, request_id INT DEFAULT NULL, claim_id INT DEFAULT NULL, file_path VARCHAR(255) NOT NULL, INDEX IDX_599C6038427EB8A5 (request_id), INDEX IDX_599C60387096A49F (claim_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('CREATE TABLE lb_doneforyou_claim (id INT AUTO_INCREMENT NOT NULL, service_id INT NOT NULL, description LONGTEXT NOT NULL, refusal_reason LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, UNIQUE INDEX UNIQ_116C8E9FED5CA9E6 (service_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('CREATE TABLE lb_doneforyou_finish (id INT AUTO_INCREMENT NOT NULL, service_id INT NOT NULL, coach_report LONGTEXT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, UNIQUE INDEX UNIQ_E5C5D5F6ED5CA9E6 (service_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('CREATE TABLE lb_doneforyou_offer (id INT AUTO_INCREMENT NOT NULL, service_id INT NOT NULL, description LONGTEXT NOT NULL, credit_cost INT NOT NULL, work_start_at DATE NOT NULL, work_end_at DATE NOT NULL, refusal_reason VARCHAR(255) DEFAULT NULL, refusal_comment LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, UNIQUE INDEX UNIQ_9FD3D786ED5CA9E6 (service_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('CREATE TABLE lb_doneforyou_request (id INT AUTO_INCREMENT NOT NULL, service_id INT NOT NULL, type VARCHAR(255) NOT NULL, universe VARCHAR(255) DEFAULT NULL, hoster VARCHAR(255) DEFAULT NULL, hoster_username VARCHAR(255) DEFAULT NULL, hoster_password VARCHAR(255) DEFAULT NULL, cloudflare_username VARCHAR(255) DEFAULT NULL, cloudflare_password VARCHAR(255) DEFAULT NULL, domain_name VARCHAR(255) DEFAULT NULL, description LONGTEXT NOT NULL, goal LONGTEXT NOT NULL, practical_case LONGTEXT DEFAULT NULL, refusal_reason LONGTEXT DEFAULT NULL, UNIQUE INDEX UNIQ_7CE04CDEED5CA9E6 (service_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('CREATE TABLE lb_doneforyou_review (id INT AUTO_INCREMENT NOT NULL, service_id INT NOT NULL, rating DOUBLE PRECISION NOT NULL, comment LONGTEXT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, UNIQUE INDEX UNIQ_BC4FBF28ED5CA9E6 (service_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('CREATE TABLE lb_doneforyou_service (id INT AUTO_INCREMENT NOT NULL, request_id INT DEFAULT NULL, offer_id INT DEFAULT NULL, finish_id INT DEFAULT NULL, review_id INT DEFAULT NULL, claim_id INT DEFAULT NULL, id_client INT NOT NULL, id_coach INT DEFAULT NULL, state VARCHAR(255) NOT NULL, delivery_date DATE DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, UNIQUE INDEX UNIQ_A6EA5993427EB8A5 (request_id), UNIQUE INDEX UNIQ_A6EA599353C674EE (offer_id), UNIQUE INDEX UNIQ_A6EA59932B4667EB (finish_id), UNIQUE INDEX UNIQ_A6EA59933E2E969B (review_id), UNIQUE INDEX UNIQ_A6EA59937096A49F (claim_id), INDEX IDX_A6EA5993E173B1B8 (id_client), INDEX IDX_A6EA5993D1DC2CFC (id_coach), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('CREATE TABLE lb_doneforyou_transition_log (id INT AUTO_INCREMENT NOT NULL, id_client INT DEFAULT NULL, service_id INT NOT NULL, id_coach INT DEFAULT NULL, admin_id INT DEFAULT NULL, transition VARCHAR(255) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, INDEX IDX_968E1A3BE173B1B8 (id_client), INDEX IDX_968E1A3BED5CA9E6 (service_id), INDEX IDX_968E1A3BD1DC2CFC (id_coach), INDEX IDX_968E1A3B642B8210 (admin_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->execute('ALTER TABLE lb_doneforyou_attached_file ADD CONSTRAINT FK_599C6038427EB8A5 FOREIGN KEY (request_id) REFERENCES lb_doneforyou_request (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE lb_doneforyou_attached_file ADD CONSTRAINT FK_599C60387096A49F FOREIGN KEY (claim_id) REFERENCES lb_doneforyou_claim (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE lb_doneforyou_claim ADD CONSTRAINT FK_116C8E9FED5CA9E6 FOREIGN KEY (service_id) REFERENCES lb_doneforyou_service (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE lb_doneforyou_finish ADD CONSTRAINT FK_E5C5D5F6ED5CA9E6 FOREIGN KEY (service_id) REFERENCES lb_doneforyou_service (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE lb_doneforyou_offer ADD CONSTRAINT FK_9FD3D786ED5CA9E6 FOREIGN KEY (service_id) REFERENCES lb_doneforyou_service (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE lb_doneforyou_request ADD CONSTRAINT FK_7CE04CDEED5CA9E6 FOREIGN KEY (service_id) REFERENCES lb_doneforyou_service (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE lb_doneforyou_review ADD CONSTRAINT FK_BC4FBF28ED5CA9E6 FOREIGN KEY (service_id) REFERENCES lb_doneforyou_service (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE lb_doneforyou_service ADD CONSTRAINT FK_A6EA5993427EB8A5 FOREIGN KEY (request_id) REFERENCES lb_doneforyou_request (id) ON DELETE SET NULL');
        $this->execute('ALTER TABLE lb_doneforyou_service ADD CONSTRAINT FK_A6EA599353C674EE FOREIGN KEY (offer_id) REFERENCES lb_doneforyou_offer (id) ON DELETE SET NULL');
        $this->execute('ALTER TABLE lb_doneforyou_service ADD CONSTRAINT FK_A6EA59932B4667EB FOREIGN KEY (finish_id) REFERENCES lb_doneforyou_finish (id) ON DELETE SET NULL');
        $this->execute('ALTER TABLE lb_doneforyou_service ADD CONSTRAINT FK_A6EA59933E2E969B FOREIGN KEY (review_id) REFERENCES lb_doneforyou_review (id) ON DELETE SET NULL');
        $this->execute('ALTER TABLE lb_doneforyou_service ADD CONSTRAINT FK_A6EA59937096A49F FOREIGN KEY (claim_id) REFERENCES lb_doneforyou_claim (id) ON DELETE SET NULL');
        $this->execute('ALTER TABLE lb_doneforyou_service ADD CONSTRAINT FK_A6EA5993E173B1B8 FOREIGN KEY (id_client) REFERENCES lb_clients (id_client)');
        $this->execute('ALTER TABLE lb_doneforyou_service ADD CONSTRAINT FK_A6EA5993D1DC2CFC FOREIGN KEY (id_coach) REFERENCES lb_coachs (id_coach)');
        $this->execute('ALTER TABLE lb_doneforyou_transition_log ADD CONSTRAINT FK_968E1A3BE173B1B8 FOREIGN KEY (id_client) REFERENCES lb_clients (id_client)');
        $this->execute('ALTER TABLE lb_doneforyou_transition_log ADD CONSTRAINT FK_968E1A3BED5CA9E6 FOREIGN KEY (service_id) REFERENCES lb_doneforyou_service (id) ON DELETE CASCADE');
        $this->execute('ALTER TABLE lb_doneforyou_transition_log ADD CONSTRAINT FK_968E1A3BD1DC2CFC FOREIGN KEY (id_coach) REFERENCES lb_coachs (id_coach)');
        $this->execute('ALTER TABLE lb_doneforyou_transition_log ADD CONSTRAINT FK_968E1A3B642B8210 FOREIGN KEY (admin_id) REFERENCES lb_users (user_id)');
    }

    public function down()
    {
        $this->execute('ALTER TABLE lb_doneforyou_attached_file DROP FOREIGN KEY FK_599C60387096A49F');
        $this->execute('ALTER TABLE lb_doneforyou_service DROP FOREIGN KEY FK_A6EA59937096A49F');
        $this->execute('ALTER TABLE lb_doneforyou_service DROP FOREIGN KEY FK_A6EA59932B4667EB');
        $this->execute('ALTER TABLE lb_doneforyou_service DROP FOREIGN KEY FK_A6EA599353C674EE');
        $this->execute('ALTER TABLE lb_doneforyou_attached_file DROP FOREIGN KEY FK_599C6038427EB8A5');
        $this->execute('ALTER TABLE lb_doneforyou_service DROP FOREIGN KEY FK_A6EA5993427EB8A5');
        $this->execute('ALTER TABLE lb_doneforyou_service DROP FOREIGN KEY FK_A6EA59933E2E969B');
        $this->execute('ALTER TABLE lb_doneforyou_claim DROP FOREIGN KEY FK_116C8E9FED5CA9E6');
        $this->execute('ALTER TABLE lb_doneforyou_finish DROP FOREIGN KEY FK_E5C5D5F6ED5CA9E6');
        $this->execute('ALTER TABLE lb_doneforyou_offer DROP FOREIGN KEY FK_9FD3D786ED5CA9E6');
        $this->execute('ALTER TABLE lb_doneforyou_request DROP FOREIGN KEY FK_7CE04CDEED5CA9E6');
        $this->execute('ALTER TABLE lb_doneforyou_review DROP FOREIGN KEY FK_BC4FBF28ED5CA9E6');
        $this->execute('ALTER TABLE lb_doneforyou_transition_log DROP FOREIGN KEY FK_968E1A3BED5CA9E6');
        $this->execute('DROP TABLE lb_doneforyou_attached_file');
        $this->execute('DROP TABLE lb_doneforyou_claim');
        $this->execute('DROP TABLE lb_doneforyou_finish');
        $this->execute('DROP TABLE lb_doneforyou_offer');
        $this->execute('DROP TABLE lb_doneforyou_request');
        $this->execute('DROP TABLE lb_doneforyou_review');
        $this->execute('DROP TABLE lb_doneforyou_service');
        $this->execute('DROP TABLE lb_doneforyou_transition_log');
    }

}
