<?php


use Phinx\Migration\AbstractMigration;

class ConferenceRenseignementsPosition extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and <PERSON>nx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $table = $this->table('lw_conference_renseignements');
        $table->addColumn('position', 'integer', ['default' => 0, 'after' => 'obligatoire'])
            ->update();

        $this->execute("UPDATE lw_renseignements SET type='input_text' WHERE nom='adresse';");

        $renseignements = $this->query("SELECT DISTINCT(id_conference) FROM lw_conference_renseignements;")->fetchAll();
        foreach ($renseignements as $renseignement) {
            $id_conference = $renseignement['id_conference'];
            $confRenseignements = $this->query("SELECT * FROM lw_conference_renseignements WHERE id_conference = $id_conference;")->fetchAll();

            $position = 0;
            foreach ($confRenseignements as $confRenseignement) {
                $position++;
                $this->execute("UPDATE lw_conference_renseignements SET position='$position' WHERE ID='" . $confRenseignement['ID'] . "';");
            }
        }
    }
}
