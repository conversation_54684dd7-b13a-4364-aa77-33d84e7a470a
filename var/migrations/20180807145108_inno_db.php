<?php


use Phinx\Migration\AbstractMigration;

class InnoDb extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and <PERSON><PERSON> will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->execute('
            ALTER TABLE lb_meteo ENGINE=InnoDB;
            ALTER TABLE lb_pays ENGINE=InnoDB;
            ALTER TABLE lb_pays2 ENGINE=InnoDB;
            ALTER TABLE lb_photo_gallery ENGINE=InnoDB;
            ALTER TABLE lb_piwik ENGINE=InnoDB;
            ALTER TABLE lb_server_status ENGINE=InnoDB;
        ');
    }
}
