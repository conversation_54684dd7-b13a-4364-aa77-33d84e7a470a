<?php
declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class ScormsIndexesPages extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up(): void
    {
        @ini_set('memory_limit', '4096M');

        $pages = $this->fetchAll('SELECT idpage FROM ptf_pages');
        $pages = $this->array_change_key($pages, 'idpage');

        $scorms = $this->fetchAll('SELECT DISTINCT(idpage) FROM ptf_scorms_datas');
        $scorms = $this->array_change_key($scorms, 'idpage');

        foreach ($scorms as $idpage => $scorm) {
            if (!isset($pages[$idpage])) {
                $this->execute('DELETE FROM ptf_scorms_datas WHERE idpage = ' . $idpage);
            }
        }

        $table = $this->table('ptf_scorms_datas');
        $table->addForeignKey('idpage', 'ptf_pages', 'idpage', ['delete'=> 'CASCADE', 'update'=> 'NO_ACTION'])
            ->save();
    }

    public function down()
    {

    }

    public function array_change_key($array, $key1, $key2 = ''): array
    {
        $array_output = array();
        if (!is_array($array)) {
            return [];
        }
        if (empty($array)) {
            return $array_output;
        }

        foreach ($array as $datas) {
            if (isset($datas[$key1])) {
                if ($key2 and isset($datas[$key2])) {
                    $array_output[$datas[$key1]] = $datas[$key2];
                } else {
                    $array_output[$datas[$key1]] = $datas;
                }
            }
        }

        return $array_output;
    }
}
