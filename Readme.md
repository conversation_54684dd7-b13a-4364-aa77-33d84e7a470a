# LB Installation

## Pré-requis
- Si vous souhaitez utiliser `Docker` (ce qui est recommandé), il faut au préalable l'installer sur votre machine (https://docs.docker.com/install/).
- Si vous ne souhaitez pas utiliser `Docker`:
  - modules Apache :
    * headers
    * rewrite
  - extensions PHP :
    * pdo
    * pdo_mysql
    * gettext
    * mbstring
    * gd
    * imap
  -  WkHtmlToPdf (_Using WkHtmlToPdf Without X Server :_
     https://github.com/JazzCore/python-pdfkit/wiki/Using-wkhtmltopdf-without-X-server) + WkHtmlToImage
  - Postfix + Spamassassin

## Mise en route du projet

### Domaines locaux

Ajouter dans `/etc/hosts` vos domaines locaux :
```
127.0.0.1   learnybox.local
127.0.0.1   master.learnybox.local
127.0.0.1   comptedetest1.learnybox.local
```

Ces domaines vous permettront par la suite d'accéder à l'application en local.

### Dossiers / Fichiers
* Créer le dossier `var/sessions` accessible en écriture
* Lancer `bin/generate-env.sh dev`
* Editer `config/global.php` et définir :
    * les identifiants de connexion à la BDD (définis dans `docker/.env`)
    * les différentes clé, var, paths, etc ...

Pour info, voici la liste des dossiers devant être accessibles en écriture :
* `var/*`
* `web/.well-known/pki-validation/`
* `web/medias/`
* `web/medias/factures/`
* `web/medias/imports/`
* `web/medias/upload/`
* `web/assets/images/`
* `web/assets/countdown/`
* `web/assets/images/conferences/`
* `web/assets/images/formations/`
* `web/assets/images/lbar_themesformulaires/`
* `web/assets/images/lbar_themesmails/`
* `web/assets/images/themespages/`
* `web/assets/images/tunnels_pages_thumbnails/`
* `web/assets/images/tunnels_thumbnails/`
* `web/assets/images/webinaires/`

### Construction via Docker
> Une fois votre projet cloné, vos fichers de config crées et paramétrés, vos hosts en local configurés, vous pouvez lancer le projet via Docker.
* Installer, si cela n'est pas encore fait, Docker sur la machine (https://docs.docker.com/install/)
* Si vous n'avez pas de fichier `/docker/.env`, copier le fichier `/docker/.env.dist` en `/docker/.env`. Vous pouvez le modifier si besoin, notamment pour :
    * l'environnement souhaité (dev, test, ou prod)
    * la configuration mysql (dbname, username, password, ...)
    * les ports locaux utilisés
    * les paths des dossiers locaux présents sur le NFS en prod (logs, medias et images)
    * la configuration Xdebug (décommenter la partie OS X, ou Windows / Linux en fonction de l'OS de l'hôte)
* Lancer `make build` pour construire les containers
* Lancer `make start` pour démarrer les containers
* Lancer `make bash-php` pour accéder au container PHP et lancer la commande suivante: `composer install` pour installer les dépendances

* Une fois terminé, le projet est accessible :
  * **Web** : https://comptedetest1.learnybox.local/app/index/ (si vous utilisez un port autre que le port par défaut 443 pour le https dans `HTTP_PORT`, pensez à le renseigner)
  * **PhpMyAdmin** : http://localhost:8181 (en fonction du port défini dans `PHPMYADMIN_PORT`)

_⚠ Si vous n'utilisez pas docker, pensez à copier (ou créer des liens symboliques) les dossiers `var/logs`, `web/medias` et `web/assets/images`_


### BDD
Une fois votre environnement prêt, vous pouvez importer les dumps SQL des bases de données nécessaires au projet dans `phpmyadmin` (ou une autre application Web de gestion pour les systèmes de gestion de base de données MySQL).

3 bases de données sont nécessaires pour le projet (learnybox, learnybox_prestashop, learnybox_backups) .

Le wiki ci-dessous contient leur nom et un dump SQL de ces 3 bases ainsi que les informations de connexion pour la principale :

https://gitlab.com/learnybox/learnybox/-/wikis/la-tech/specificites-techniques-de-notre-framework/base-de-donnees/dumps-sql

## Utilisations courantes

### Composer

Installer les dépendances du projet :
```bash
$ composer install

ou via docker :
$ make bash-php
$ composer install
```

Lorsque vous ajoutez une nouvelle classe, ou alors que vous modifiez le path d'une existante, il faut penser à regénérer le fichier d'autoload via :

```bash
$ composer dump-autoload

ou via docker :
$ make bash-php
$ composer dump-autoload
```
(fait automatiquement à la fin d'un install ou update)

### Commandes

Vous pouvez utiliser toutes les commandes console via :

```bash
$ bin/console -a action_name [-f filename]

ou via docker :
$ make bash-php
$ bin/console -a action_name [-f filename]
```

### Migrations

Le projet utilise la lib [Phinx](http://docs.phinx.org/en/latest/) pour la gestion des migrations.

#### Créer une nouvelle migration :

```bash
$ vendor/bin/phinx create MyNewMigrationName

ou via docker :
$ make bash-php
$ vendor/bin/phinx create MyNewMigrationName
```

Le fichier de migration sera créé dans le dossier `var/migrations`

#### Appliquer les migrations non appliquées sur sa base :

```bash
$ vendor/bin/phinx migrate

ou via docker :
$ make bash-php
$ vendor/bin/phinx migrate
```

#### Effectuer un rollback de la précédente migration :

```bash
$ vendor/bin/phinx rollback

ou via docker :
$ make bash-php
$ vendor/bin/phinx rollback
```

#### Effectuer un rollback à partir d'une précédente migration :

```bash
$ vendor/bin/phinx rollback

ou via docker :
$ make bash-php
$ vendor/bin/phinx rollback -t 20231211103645
```

## Xdebug

Pour débugger l'appli avec Xdebug :
* Configurer le port défini dans `docker/.env`
* Utiliser une extension de navigateur pour XDebug (par ex Xdebug helper sous Chrome)
* Configurer la clé à la clé définie dans `docker/.env`
* Activer le debug sur la page en cours, et sur l'IDE

## Aller plus loin avec Docker

La configuration définie ici créé 5 containers :
* **db** : contient un mysql 8.0 (container officiel de mysql, non modifié)
* **php** : contient un php 8.2 avec Apache 2.4 (container officiel PHP/Apache, modifié pour y ajouter toutes les extensions et modules nécessaires au projet)
* **phpmyadmin** : contient la dernière version de PhpMyAdmin (container officiel, non modifié)
* **spamassassin**: contient la dernière version de spamassassin (container dinkel/spamassassin, non modifié)
* **node**: contient la dernière version de node (container officiel, non modifié)

Vous pouvez consulter le ficher `Makefile` à la racine du projet pour voir les commandes docker disponibles.

## Submodules (pas nécessaire)
Le projet contient une application `prestashop` en sous module Git.

Pour la récupérer, il faut ajouter l'option`--recurse-submodules` lors du `git clone`, ou alors lancer `git submodule init`  puis `git submodule update`
