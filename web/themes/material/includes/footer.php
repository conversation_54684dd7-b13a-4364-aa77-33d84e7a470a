    </div>
    
	<div id="footer">
        <div class="row copyright">
            <?php echo $this->data['footer']; ?>
        </div>
    </div>
    <?php \Learnybox\Helpers\Assets::addJs('themes/material/js/material.min.js', null) ?>
    <?php \Learnybox\Helpers\Assets::addJs('themes/material/js/ripples.min.js', null) ?>
    <?php \Learnybox\Helpers\Assets::addJs('themes/material/js/theme.js', null) ?>

    <?php \Learnybox\Helpers\Assets::renderJs(); ?>
    <?php \Learnybox\Helpers\Assets::renderInlineJs(); ?>
	
	<?php
    if (isset($this->data['action']) and 'accueil' == $this->data['action']) {
        echo '<script>FullTriggerGritter(\'Bienvenue sur la plateforme de formation ' . SITE_NAME . '\', \'Pour démarrer tout de suite votre formation, cliquez sur \"Commencer ma formation\"\')</script>';
    }

    if (isset($this->gritters) and $this->gritters) {
        foreach ($this->gritters as $_gritter) {
            echo '<script>FullTriggerGritter(\'' . $_gritter['title'] . '\', \'' . $_gritter['content'] . '\', \'' . $_gritter['icon'] . '\')</script>';
        }
    }

    //client analytics
    if (defined('CLIENT_ANALYTICS') and '' != trim(CLIENT_ANALYTICS)) {
        echo stripslashes(CLIENT_ANALYTICS);
    }
    ?>
    
</body>
</html>