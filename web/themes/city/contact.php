<!-- contact -->
<div id="contact" class="container contact_page" style="background: white;">

    <?php if (isset($success) and '' != trim($success)) {
    ?>
		<div class="row">
			<div class="col-md-6 col-md-offset-3">
                <div class="alert alert-success">
            		<?php echo $success; ?>
            	</div>
            </div>
        </div>
	<?php
} else {
        ?>

    <div class="row form">
        <div class="col-md-5 col-md-offset-1">

            <form class="form" method="post" action="">
            	<?php
                if (isset($error) and '' != trim($error)) {
                    echo '<div class="alert alert-danger">
                			<ul>' . $error . '</ul>
                		  </div>';
                } ?>
                <div class="form-group">
                    <label class="control-label" for="nom"><?php echo __('Nom'); ?> *</label>
                    <div class="controls">
                        <div class="input-group">
							<span class="input-group-addon"><i class="fa fa-user"></i></span>
							<input class="form-control input-xlarge" id="nom" name="nom" type="text" placeholder="<?php echo __('Nom'); ?>" value="<?php if (isset($nom)) {
                    echo $nom;
                } ?>" required />
						</div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="prenom"><?php echo __('Prénom'); ?> *</label>
                    <div class="controls">
                        <div class="input-group">
							<span class="input-group-addon"><i class="fa fa-user"></i></span>
							<input class="form-control input-xlarge" id="prenom" name="prenom" type="text" placeholder="<?php echo __('Prénom'); ?>" value="<?php if (isset($prenom)) {
                    echo $prenom;
                } ?>" required />
						</div>
					</div>
                </div>

                <div class="form-group">
                    <label class="control-label" for="email"><?php echo __('Email'); ?> *</label>
                    <div class="controls">
                        <div class="input-group">
							<span class="input-group-addon"><i class="fa fa-at"></i></span>
							<input class="form-control input-xlarge" id="email" name="email" type="email" placeholder="<?php echo __('Adresse email'); ?>" value="<?php if (isset($email)) {
                    echo $email;
                } ?>" required />
						</div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label" for="subject"><?php echo __('Sujet'); ?> *</label>
                    <div class="controls">
                        <select name="subject" id="subject" data-rel="select2">
			              <option value="question" <?php if (isset($subject) and 'question' == $subject) {
                    echo 'selected';
                } ?>>Question</option>
			              <option value="rappelez-moi" <?php if (isset($subject) and 'rappelez-moi' == $subject) {
                    echo 'selected';
                } ?>>Rappelez-moi</option>
			              <option value="technique" <?php if (isset($subject) and 'technique' == $subject) {
                    echo 'selected';
                } ?>>Support technique</option>
			              <option value="pedagogique" <?php if (isset($subject) and 'pedagogique' == $subject) {
                    echo 'selected';
                } ?>>Support pédagogique</option>
			              <option value="bug" <?php if (isset($subject) and 'bug' == $subject) {
                    echo 'selected';
                } ?>>Rapporter un bug</option>
			              <option value="autre" <?php if (isset($subject) and 'autre' == $subject) {
                    echo 'selected';
                } ?>>Autre</option>
			            </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label" for="message"><?php echo __('Message'); ?> *</label>
                    <div class="controls">
                        <textarea class="form-control input-xlarge" id="message" rows="6" name="message" required><?php if (isset($message)) {
                    echo $message;
                } ?></textarea>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label" for="verify"><?php echo __('Code de sécurité'); ?> *</label>
                    <div class="controls">
                        <div class="input-group" style="float:left; margin-right:10px">
							<span class="input-group-addon"><i class="fa fa-lock"></i></span>
							<input class="form-control input-small" id="verify" name="verify" type="text" value="" required />
						</div>
						<div style="float:left">
                    		<img src="<?php echo $captchaUrl; ?>" alt="<?php echo __('Image de vérification'); ?>" border="0" width="100" />
                    	</div>
                    	<div style="clear: both"></div>
                    </div>
                </div>

                <div style="display: none">
                    <input class="form-control input" id="xyz" name="xyz" value="" tabindex="-1">
                </div>

                <div class="form-actions">
                    [[CSRF]]
	            	<input type="hidden" name="form_action" value="contact" />
	            	<button type="submit" id="submit" class="btn btn-primary"><i class="fa fa-envelope"></i>&nbsp;<?php echo __('Sujet'); ?> <?php echo __('Valider'); ?></button>
				</div>
			</form>
        </div>

        <div class="col-md-5 col-md-offset-1 sidebar">
            <div class="address">

            	<?php
                if ($site) {
                    echo '<h2>' . $site . '</h2>';
                }
        if ($adresse) {
            echo '<p><i class="fa fa-building-o"></i> <strong>' . __('Adresse') . ' : </strong><br>' . $adresse . '</p>';
        }
        if ($tel) {
            echo '<p><i class="fa fa-phone"></i> <strong>' . __('Tel') . ' :</strong> ' . $tel . '</p>';
        }

        //social
        echo eden()->Social()->displaySocialSidebar(); ?>

            </div>
        </div>
    </div>
    <?php
    } ?>
</div>
