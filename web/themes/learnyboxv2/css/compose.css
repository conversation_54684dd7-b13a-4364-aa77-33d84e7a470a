.line {
    padding: 0;
}   
.container .col-md-1, .container .col-md-10, .container .col-md-11, .container .col-md-12, .container .col-md-2, .container .col-md-3, .container .col-md-4, .container .col-md-5, .container .col-md-6, .container .col-md-7, .container .col-md-8, .container .col-md-9 {
    padding: 0;
}
.box-masonry {
    margin-bottom: 0;
}
.txt {
	margin-bottom: 0;
}
input[type="text"],
input[type="email"],
input[type="password"],
textarea {
    display: inline-block;
    border: 1px solid #bbb;
    background: white;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

#articles_template2 .col-md-4 {
	padding: 0 10px;
}
#articles_template2 .media>.pull-left {
	padding-right: 0;
}



.temoignage {
    position: relative;
    display: block;
    width: 100%;
    box-sizing: border-box;   
}
.temoignage hr {
    border-color: transparent;
}

.temoignage .temoignage-content {
    padding: 10px;
    position: relative;
    box-sizing: border-box;
}

.temoignage .temoignage-user .picture {
    float: left;
    max-width: 70px;
    margin-right: 10px;
}
.temoignage .picture img {
    max-width: 100%;
	border-radius: 50%;
}
.temoignage .temoignage-user {
    box-sizing: border-box;
}
.temoignage .temoignage-user .name {
    font-size: 16px;
    float: left;
}
.temoignage .temoignage-user .name h4 {
    margin-bottom: 0;
}
.temoignage .temoignage-user .description {
    font-size: 12px;
}



.temoignage.theme1 .temoignage-icone {
	margin-bottom: 10px;
}
.temoignage.theme1 .temoignage-user {
    padding: 5px;
}


.temoignage.theme2 .temoignage-content {
    padding: 0px;
}
.temoignage.theme2 .temoignage-user {
    padding: 0px;
}
.temoignage.theme2 .temoignage-user .picture {
	display: none !important;
}
.temoignage.theme2 .temoignage-user .name {
    font-size: 16px;
    color: white;
}
.temoignage.theme2 .temoignage-icone {
	display: none !important;
}
.temoignage.theme2 .picture img {
	max-width: 150px !important;
}


.temoignage.theme3 > .picture {
	text-align: center;
}
.temoignage.theme3 > .picture img {
	margin: 0 auto;
	max-width: 250px !important;
}
.temoignage.theme3 .temoignage-hr hr {
	margin: 20px auto;
}
.temoignage.theme3 .temoignage-user .picture {
	display: none !important;
}
.temoignage.theme3 .temoignage-user .name {
	text-align: center;
	float: none;
}
.temoignage.theme3 .temoignage-icone {
	display: none !important;
}


.temoignage.theme4 .temoignage-user .picture {
	max-width: 50px;
}
.temoignage.theme4 .temoignage-user .picture img {
	max-width: 100% !important;
}
.temoignage.theme4 .temoignage-user h4 {
	margin-top: 5px;
}


.temoignage.theme5 .temoignage-icone {
	text-align: center;
}
.temoignage.theme5 .temoignage-hr hr {
	margin: 20px auto;
	border-top-width: 5px;
}



.temoignage.theme6 .media-left {
    text-align: center;
    min-width: 250px;
}
.temoignage.theme6 .media-left .picture img {
	margin: 0 auto;
}
.temoignage.theme6 .temoignage-content {
    padding: 0px;
}
.temoignage.theme6 .temoignage-user {
    padding: 0px;
}
.temoignage.theme6 .temoignage-user .picture {
	display: none !important;
}
.temoignage.theme6 .temoignage-user .name {
    font-size: 16px;
    float: none;
    text-align: center;
}
.temoignage.theme6 .temoignage-icone {
	margin-bottom: 10px;
}
.temoignage.theme6 .picture img {
	max-width: 150px !important;
}




/*===================================================================================*/
/*	PRICING TABLES
/*===================================================================================*/
#pricing-tables {
	background: white;
}
.pricing .col-md-3 {
	width: 220px;
}

ul.features li {
	text-align: center;
	background: white;
}
#details {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    margin-top: 206px;
}
#details ul.features li {
	text-align: left;
	white-space: nowrap;
}
.pricing .plan {
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
	overflow: hidden;
	position: relative;
}
.pricing .plan header {
	background: #2F4052;
	text-align: center;
	padding: 50px;
}
.pricing .plan.bestvalue {
	margin-top: -20px;
}
.pricing .plan.bestvalue header {
    background: #F27A24 !important;
    color: white;
    padding: 40px 20px 20px 20px !important;
}
.pricing .plan.bestvalue .plan-star {
    position: absolute;
    right: 5px;
    top: 5px;
}
.pricing .plan h2 {
	font-size: 20px;
	font-weight: 400;
	letter-spacing: .15em;
	text-transform: uppercase;
	color: #FFF;
}
.pricing .plan .aulieude {
	font-weight: 300;
	color: #EEE
}
.pricing .plan .btn {
	margin-bottom: 0;
}
.pricing .plan .price {
	margin-bottom: 0px;
}
.pricing .plan .header-price-small {
	margin-bottom: 5px;
    font-size: 13px;
    text-align: center;
}
.pricing .plan .price-small {
	margin-bottom: 5px;
    font-size: 14px;
    border-top: none !important;
    padding: 0 !important;
    text-align: center;
    line-height: 18px;
    margin-top: 5px;
}
.pricing .plan .no-engadgement {
	font-size: 16px;
	margin-bottom: 10px
}
.pricing .plan .for-who {
	font-size: 16px;
	color: white;
}
.pricing .plan.bestvalue .no-engadgement {
    color: #CCC;
}
.pricing .plan .price * {
	font-family: 'Lato', sans-serif;
	line-height: 100%;
}
.pricing .plan .price .amount {
	font-size: 120px;
	font-weight: 900;
	color: #FFF;
}
.pricing .plan .price .currency {
	position: relative;
	top: 6px;
	font-size: 16px;
	vertical-align: top;
	margin-right: 5px;
	color: #FFF;
}
.pricing .plan .price .period {
	font-size: 16px;
	text-transform: uppercase;
	margin-left: -20px;
	color: #FFF;
}
.pricing .plan .price .reglement {
    font-size: 14px;
}
.pricing .plan .price .reduction {
    color: #EEE;
}
.pricing .plan .price .reduction .pourcentage {
    background: #F27A24;
    padding: 2px;
    color: white;
}
.pricing .plan.bestvalue .price .reduction .pourcentage {
    background: #2F4052;
}
.pricing .plan .price .reduction .strikethrough {
  position: relative;
}
.pricing .plan .price .reduction .strikethrough:before {
  position: absolute;
  content: "";
  left: 0;
  top: 50%;
  right: 0;
  border-top: 1px solid yellow;

  -webkit-transform:rotate(-5deg);
  -moz-transform:rotate(-5deg);
  -ms-transform:rotate(-5deg);
  -o-transform:rotate(-5deg);
  transform:rotate(-5deg);
}

.pricing .plan .features {
    background: #F5F7FA;
    padding: 0px;
    /*border: 1px solid #E6E9ED;*/
    border: none;
	margin-top: 0 !important;
}
.pricing .plan .features li {
    padding: 6px 12px;
	border-top: 1px solid #CED7E0;
	font-size:14px;
	background: white;
    color: #2F404F;
}
.pricing .plan .features.white li {
    background: white;
	border-top: 1px solid #CED7E0;
    color: #73879C;
}
.pricing .plan .features li:first-child {
	border: none;
}
.pricing .plan .features li i {
	font-size: 18px;
	color: #58a549;
	font-size: 18px;
}
.pricing .plan .features li i.fa-remove {
	color: #e05b49;
}
.pricing.col-4 .plan header {
	padding: 20px;
	height: auto;
}
.pricing.col-4 .plan h2 {
	font-size: 18px;
}
.pricing.col-4 .plan .price .amount {
	font-size: 44px;
}
.pricing.col-4 .plan .features {
    padding: 0px;
    margin-left: 0;
    margin-bottom: 0;
}

.pricing .features.footer {
	height: 120px;
	border-top: 1px solid #CED7E0;
	border-bottom: 1px solid #CED7E0;
}
.pricing .plan .features.footer li {
    background: #F5F7FA;
    color: #73879C;
    padding-top: 10px;
}
.pricing .features.footer a.btn{
	margin-top: 0px;
}

.btns-tarifs {
    padding-top: 0px;
    text-align: center;
    width: 150px;
    position: absolute;
    top: 55px;
    right: -20px;
}
.btns-tarifs a {
    width: 100%;
    margin:10px 0 0 0;
}



.accompagnement .header {
    background: #2F4052;
    color: white;
    padding: 20px 10px;
    text-align: center;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}
.accompagnement .header h3 {
    color: white;
    margin-bottom: 0px;
}
.accompagnement .features {
    width: 70%;
    margin-left: 0px;
    padding-left: 0px;
    float: left;
}
.accompagnement .features li {
    background: white;
    color: #73879C;
    padding: 6px 12px;
    text-align: left;
    font-size: 14px;
}



.accompagnement .features-details {
    width: 30%;
    margin-left: 0px;
    padding-left: 0px;
    float: left;
}
.accompagnement .features-details li {
    background: white;
    color: #73879C;
    padding: 6px 12px;
    text-align: center;
    font-size: 14px;
}
.accompagnement .features-details li:nth-child(even) {
    background: white;
}



@media (max-width: 1199px) and (min-width: 980px) {
	.pricing .plan .price .currency { font-size: 12px; }
	.pricing .plan .price .period { font-size: 12px; }
	.pricing .plan .no-engadgement { font-size: 14px; }
	.pricing .col-md-3 { width: 170px; }
	.pricing .plan .for-who { font-size: 12px; }
	.btns-tarifs { right: -25px; }
	.pricing.col-4 .plan h2 { font-size: 17px; }
	#details { margin-top: 196px; }
}

@media (max-width: 979px) and (min-width: 768px) {
	.pricing .plan .price .currency { font-size: 12px; }
	.pricing .plan .price .period { font-size: 12px; }
	.pricing .plan .no-engadgement { font-size: 13px; }
	.pricing .col-md-3 { width: 155px; }
	.pricing .plan .for-who { font-size: 11px; }
	.btns-tarifs { left: 55px; top: 25px; }
	.pricing.col-4 .plan h2 { font-size: 15px; }
	.pricing.col-4 .plan .price .amount { font-size: 34px; }
	#details ul.features li { white-space: nowrap; font-size: 12px; }
	#details { margin-top: 178px; }
}
@media (max-width: 767px) {
	.pricing .col-md-3 { width: auto; }
	.pricing .features.footer a.btn { width: 100%; }
	.btns-tarifs { position: relative; top: inherit; }
	#details { margin-top: 0px; }
}

.header-price-small {
	display: none !important;
}


.btn.btn-orange {
    padding: 11px 20px 13px;
	margin: 15px 0;
	font-family: 'Source Sans Pro', sans-serif;
    font-weight: 700;
    font-size: 14px;
    background: #F27A24;
    color: white;
}
.btn.btn-secondary {
    padding: 11px 20px 13px;
	margin: 15px 0;
	font-family: 'Source Sans Pro', sans-serif;
    font-weight: 700;
    font-size: 14px;
    background: #95A5A6;
    color: white;
}
.btn-orange:hover,
.btn-orange:focus,
.btn-orange:active,
.btn-orange.active {
    background: #D96716 !important;
}
.btn-navy:hover,
.btn-navy:focus,
.btn-navy:active,
.btn-navy.active {
    background: #2C3E50 !important;
}
.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary:active,
.btn-secondary.active {
    background: #859394 !important;
}

.row.btn-pricing {
	margin: 20px 0;
}
.pricing .plan .no-engadgement {
	font-size: 16px;
	margin-bottom: 10px;
    color: white;
}
.pricing .plan.bestvalue .no-engadgement {
    color: #CCC;
}