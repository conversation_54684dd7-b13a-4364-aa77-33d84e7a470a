$(document).ready(function() {
    var source = get_url_params('source');
    if (source) {
        $('#source').val(source);
    }
    addModalTarifs();

    TarifsMois();

    $('#btnTarifsMois a.btn').click(function(e) {
	    e.preventDefault();
	    TarifsMois();
	});
	$('#btnTarifs1an a.btn').click(function(e) {
	    e.preventDefault();
	    Tarifs1an();
	});
});

function TarifsMois() {
    $('#btnTarifsMois a.btn').removeClass('btn-secondary').addClass('btn-active');
    $('#btnTarifs1an a.btn').removeClass('btn-active').addClass('btn-secondary');

    $('#start-price img').attr('src', '/images/nouveau-site/tarif/prix1.png').css('width', '150px');
    $('#btn-start .btn').attr('onclick', "setForm('LearnyStart', '1month');");

    $('#pro-price img').attr('src', '/images/nouveau-site/tarif/prix2.png').css('width', '150px');
    $('#btn-pro .btn').attr('onclick', "setForm('LearnyPro', '1month');");

    $('#vip-price img').attr('src', '/images/nouveau-site/tarif/prix3.png').css('width', '150px');
    $('#btn-vip .btn').attr('onclick', "setForm('LearnyVIP', '1month');");
}

function Tarifs1an() {
    $('#btnTarifsMois a.btn').removeClass('btn-active').addClass('btn-secondary');
    $('#btnTarifs1an a.btn').removeClass('btn-secondary').addClass('btn-active');

    $('#start-price img').attr('src', '/images/nouveau-site/tarif/prixannuel11.png').css('width', '250px');
    $('#btn-start .btn').attr('onclick', "setForm('LearnyStart', '1year');");

    $('#pro-price img').attr('src', '/images/nouveau-site/tarif/prixannuel21.png').css('width', '250px');
    $('#btn-pro .btn').attr('onclick', "setForm('LearnyPro', '1year');");

    $('#vip-price img').attr('src', '/images/nouveau-site/tarif/prixannuel31.png').css('width', '250px');
    $('#btn-vip .btn').attr('onclick', "setForm('LearnyVIP', '1year');");
}


function setForm(formule, duree) {
    if (formule == 'LearnyStart') {
        if (duree == '1month') {
            $('#redirect').val("https://learnybox.com/learnystart-mensuel-90/");
        } else if (duree == '1year') {
            $('#redirect').val("https://learnybox.com/learnystart-annuel-90/");
        }
    } else if (formule == 'LearnyPro') {
        if (duree == '1month') {
            $('#redirect').val("https://learnybox.com/learnypro-mensuel-90/");
        } else if (duree == '1year') {
            $('#redirect').val("https://learnybox.com/learnypro-annuel-90/");
        }
    } else if (formule == 'LearnyVIP') {
        if (duree == '1month') {
            $('#redirect').val("https://learnybox.com/learnyvip-mensuel-90/");
        } else if (duree == '1year') {
            $('#redirect').val("https://learnybox.com/learnyvip-annuel-90/");
        }
    }
    $('#modalForm').modal('show');
    return false;
}

function get_url_params(param) {
	var vars = {};
	window.location.href.replace( location.hash, '' ).replace( 
		/[?&]+([^=&]+)=?([^&]*)?/gi, // regexp
		function( m, key, value ) { // callback
			vars[key] = value !== undefined ? value : '';
		}
	);

	if (param) {
		return vars[param] ? vars[param] : null;	
	}
	return vars;
}


function addModalTarifs() {	
	var modal = '' +
	'<div class="modal fade" id="modalForm" style="top:30% !important">' +
		'<div class="modal-dialog">' +
		    '<div class="modal-content">' +
			    '<button type="button" class="close" data-dismiss="modal" aria-hidden="true" style="margin-right: 5px;">×</button>' +
				'<div class="modal-body">' +
					'<h3 style="text-align:center; margin-bottom:20px">' + __('Entrez votre prénom et adresse email %s pour commencer l\'expérience LearnyBox %s pendant 90 jours gratuitement', '<br>', '<br>') + '</h3>' +
					'<form class="form" action="https://learnybox.com/form/amcok82stt6pmh4mo8azy/" method="post" id="_form_14" style="text-align:center; margin-bottom: 0px;" accept-charset="utf-8" target="_self"> ' +
				        '<div class="control-group" style="margin-bottom:20px">' +
				            '<div class="controls">' +
				                '<input type="text" name="fname" class="input-xlarge form-control" id="name" placeholder="' + __('Votre prénom') + '" style="height:50px; width: 350px !important;">' +
				            '</div>' +
				        '</div>' +
				        '<div class="control-group" style="margin-bottom:20px">' +
				            '<div class="controls">' +
				                '<input type="email" name="email" class="input-xlarge form-control" id="email" placeholder="' + __('Votre adresse email') + '" style="height:50px; width: 350px !important;">' +
				            '</div>' +
				        '</div>' +
				        '<input type="hidden" id="redirect" name="redirect" value="">' +
				        '<input type="hidden" id="source" name="custom[source]" value="site">' +
				        '<button type="submit" class="btn btn-success btn-xlarge" style="margin-top:0px; margin-bottom: 0px;">' + __('Valider') + '</button>' +
					'</form>' +
				'</div>' +
			'</div>' +
		'</div>' +
	'</div>';
	$('body').append(modal);
}