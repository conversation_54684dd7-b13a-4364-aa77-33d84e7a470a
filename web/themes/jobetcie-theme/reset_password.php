<div id="box_login" class="container">

    <div class="col-md-12">
        <div class="box_wrapper">
            <div class="box">
                <div class="head">
                    <h4><?php echo $title; ?></h4>
                </div>

                <div class="form">
                	<?php
                    if (isset($error) and '' != trim($error)) {
                        echo '<div class="alert alert-danger" style="text-align:left">
								<button type="button" class="close" data-dismiss="alert">×</button>
								' . $error . '
							  </div>';
                    }
                    ?>

                    <form action="" method="post">

                        <div class="input-group" title="Password">
							<span class="input-group-addon"><i class="fa fa-lock"></i></span>
							<input class="form-control input-large" name="password1" id="password" type="password" placeholder="<?php echo __('Mot de passe'); ?>" value="<?php if (isset($password1)) {
                        echo $password1;
                    } ?>" required />
						</div>

						<div class="input-group" title="Password">
							<span class="input-group-addon"><i class="fa fa-lock"></i></span>
							<input class="form-control input-large" name="password2" id="password" type="password" placeholder="<?php echo __('Retapez votre mot de passe'); ?>" value="<?php if (isset($password2)) {
                        echo $password2;
                    } ?>" required />
						</div>

		            	[[CSRF]]
		            	<input type="hidden" name="form_action" value="modif_password" />
		            	<input type="hidden" name="redirect_password" value="<?php echo $redirect_password; ?>" />
		            	<input type="hidden" name="rid" value="<?php echo $random_id; ?>" />
                        <input type="hidden" name="user_id" value="<?php echo $user_id; ?>" />
						<button type="submit" class="btn btn-danger"><i class="fa fa-lock"></i> <?php echo __('Modifier mon mot de passe'); ?></button>
                    </form>
                </div>
            </div>
        </div>
    </div>

</div>
