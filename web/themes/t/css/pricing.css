/*===================================================================================*/
/*	PRICING TABLES
/*===================================================================================*/
#pricing-tables {
	background: white;
}
.pricing .col-md-3 {
	width: 220px;
}

ul.features li {
	text-align: center;
	background: white;
}
#details {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    margin-top: 206px;
}
#details ul.features li {
	text-align: left;
	white-space: nowrap;
}
.pricing .plan {
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
	overflow: hidden;
	position: relative;
}
.pricing .plan header {
	background: #2F4052;
	text-align: center;
	padding: 50px;
}
.pricing .plan.bestvalue {
	margin-top: -20px;
}
.pricing .plan.bestvalue header {
    background: #F27A24 !important;
    color: white;
    padding: 40px 20px 20px 20px !important;
}
.pricing .plan.bestvalue .plan-star {
    position: absolute;
    right: 5px;
    top: 5px;
}
.pricing .plan h2 {
	font-size: 20px;
	font-weight: 400;
	letter-spacing: .15em;
	text-transform: uppercase;
	color: #FFF;
}
.pricing .plan .aulieude {
	font-weight: 300;
	color: #EEE
}
.pricing .plan .btn {
	margin-bottom: 0;
}
.pricing .plan .price {
	margin-bottom: 0px;
}
.pricing .plan .header-price-small {
	margin-bottom: 5px;
    font-size: 13px;
    text-align: center;
}
.pricing .plan .price-small {
	margin-bottom: 5px;
    font-size: 14px;
    border-top: none !important;
    padding: 0 !important;
    text-align: center;
    line-height: 18px;
    margin-top: 5px;
}
.pricing .plan .no-engadgement {
	font-size: 16px;
	margin-bottom: 10px
}
.pricing .plan .for-who {
	font-size: 16px;
	color: white;
}
.pricing .plan.bestvalue .no-engadgement {
    color: #CCC;
}
.pricing .plan .price * {
	font-family: 'Lato', sans-serif;
	line-height: 100%;
}
.pricing .plan .price .amount {
	font-size: 120px;
	font-weight: 900;
	color: #FFF;
}
.pricing .plan .price .currency {
	position: relative;
	top: 6px;
	font-size: 16px;
	vertical-align: top;
	margin-right: 5px;
}
.pricing .plan .price .period {
	font-size: 16px;
	text-transform: uppercase;
	margin-left: -20px;
}
.pricing .plan .price .reglement {
    font-size: 14px;
}
.pricing .plan .price .reduction {
    color: #EEE;
}
.pricing .plan .price .reduction .pourcentage {
    background: #F27A24;
    padding: 2px;
    color: white;
}
.pricing .plan.bestvalue .price .reduction .pourcentage {
    background: #2F4052;
}
.pricing .plan .price .reduction .strikethrough {
  position: relative;
}
.pricing .plan .price .reduction .strikethrough:before {
  position: absolute;
  content: "";
  left: 0;
  top: 50%;
  right: 0;
  border-top: 1px solid yellow;

  -webkit-transform:rotate(-5deg);
  -moz-transform:rotate(-5deg);
  -ms-transform:rotate(-5deg);
  -o-transform:rotate(-5deg);
  transform:rotate(-5deg);
}

.pricing .plan .features {
    background: #F5F7FA;
    padding: 0px;
    /*border: 1px solid #E6E9ED;*/
    border: none;
	margin-top: 0 !important;
}
.pricing .plan .features li {
    padding: 6px 12px;
	border-top: 1px solid #CED7E0;
	font-size:14px;
	background: white;
    color: #2F404F;
}
.pricing .plan .features.white li {
    background: white;
	border-top: 1px solid #CED7E0;
    color: #73879C;
}
.pricing .plan .features li:first-child {
	border: none;
}
.pricing .plan .features li i {
	font-size: 18px;
	color: #58a549;
	font-size: 18px;
}
.pricing .plan .features li i.fa-remove {
	color: #e05b49;
}
.pricing.col-4 .plan header {
	padding: 20px;
}
.pricing.col-4 .plan h2 {
	font-size: 18px;
}
.pricing.col-4 .plan .price .amount {
	font-size: 44px;
}
.pricing.col-4 .plan .features {
    padding: 0px;
    margin-left: 0;
    margin-bottom: 0;
}

.pricing .features.footer {
	height: 120px;
	border-top: 1px solid #CED7E0;
	border-bottom: 1px solid #CED7E0;
}
.pricing .plan .features.footer li {
    background: #F5F7FA;
    color: #73879C;
    padding-top: 10px;
}
.pricing .features.footer a.btn{
	margin-top: 0px;
}

.btns-tarifs {
    padding-top: 0px;
    text-align: center;
    width: 150px;
    position: absolute;
    top: 55px;
    right: -20px;
}
.btns-tarifs a {
    width: 100%;
    margin:10px 0 0 0;
}


.btn.btn-orange {
    background: #F27A24 !important;
    color: #FFFFFF !important;
    padding: 11px 20px 13px;
    margin: 15px 0;
    margin-bottom: 0;
    font-family: 'Source Sans Pro', sans-serif;
    font-weight: 700;
    font-size: 14px;
    letter-spacing: 0.02em;
    text-shadow: none;
    -webkit-font-smoothing: antialiased;
    border: none;
    text-transform: uppercase;
    -webkit-transition: all 200ms ease-out;
    -o-transition: all 200ms ease-out;
    -moz-transition: all 200ms ease-out;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -webkit-box-shadow: inset 0 -2px 0 rgba(0,0,0,0.15);
    -moz-box-shadow: inset 0 -2px 0 rgba(0,0,0,0.15);
    box-shadow: inset 0 -2px 0 rgba(0,0,0,0.15);
}
.btn-orange:hover,
.btn-orange:focus,
.btn-orange:active,
.btn-orange.active {
    background: #D96716 !important;
}