.multilevelmenu-collapse {
    margin-bottom: 20px;
    position: relative;
}

#multilevelmenu-overlay {
    display: block;
    z-index: 10;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    background: #F6F6F6;
    left: 0;
}

#multilevelmenu,
#menu {
    position: relative;
    display: block;
    border: 1px solid #CCC;
    border-radius: 4px 4px 4px 4px;
}

.mainmenu {
    position: relative;
    overflow-x: hidden;
    margin-top: 7px;
}


.multilevelpushmenu_wrapper {
	position: absolute;
	overflow: hidden;
	min-width: 100%;
	min-height: 100%;
	margin: 0;
	padding: 0;
}

.multilevelpushmenu_wrapper .levelHolderClass {
	position: absolute;
	overflow: hidden;
	top: 0;
	width: auto;
	min-height: 100%;
	font-family: 'Open Sans Condensed', sans-serif;
	font-size: 1em;
	zoom: 1;
	background: white;    
}
.multilevelpushmenu_wrapper .levelHolderClass:first-child {
    border-radius: 4px 4px 4px 4px;
}

.multilevelpushmenu_wrapper .ltr {
	margin-left: -100%;
	left: 0;
}

.multilevelpushmenu_wrapper .rtl {
	margin-right: -100%;
    right: 0;
}

.multilevelpushmenu_wrapper .multilevelpushmenu_inactive {
	background: #2e6196;
}

.multilevelpushmenu_wrapper h2 {
	font-size: 1.5em !important;
	line-height: 1em;
	font-weight: bold;
	padding: 10px 0px 8px 10px;
	margin-top: 0px;
	background-color: #F4F4F4;
    border-bottom: 1px solid #D6D6D6;
    border-radius: 4px 4px 1px 0;
    color: #373D44;
}
.multilevelpushmenu_wrapper h2 i {
    margin-top: 0px !important;
    padding-right: 5px;
}
.multilevelpushmenu_wrapper ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.multilevelpushmenu_wrapper li {
	cursor: pointer;
	border-top: 1px solid #242B35;
	padding: .4em .4em .4em .4em;
	margin-bottom: 0px;
}
.multilevelpushmenu_wrapper li.nonvu {
    font-style: italic;
    opacity: .6;
}

.multilevelpushmenu_wrapper li.header {
    height:50px;
}
.multilevelpushmenu_wrapper li.header:hover {
    background: transparent;
}

.multilevelpushmenu_wrapper li:last-child {
	border-bottom: 1px solid #295685;
}

.multilevelpushmenu_wrapper li:hover {
	background-color: #242B35;
}
.multilevelpushmenu_wrapper li:hover > a,
.multilevelpushmenu_wrapper li:hover > label {
	color: white;
}

.multilevelpushmenu_wrapper a {
	display: block;
	outline: none;
	overflow: hidden;
	font-size: 14px;
	line-height: 20px;
	padding: 0px;
	text-decoration: none;
	color: #373D44;
}

.multilevelpushmenu_wrapper a:hover {
	color: white;
}

.multilevelpushmenu_wrapper li.active {
    background-color: #242B35;
}
.multilevelpushmenu_wrapper li.active a {
    color: white;
}

.multilevelpushmenu_wrapper li.action {
    border: none;
}
.multilevelpushmenu_wrapper li.action a {
	padding: 0px;
	font-size: 14px;
}

.multilevelpushmenu_wrapper li.action i.floatLeft {
    padding-top: 3px;
    padding-right: 0px;
    width: 15px;
}


.multilevelpushmenu_wrapper h2 > .main-edition {
    float:right;
    position: absolute;
    right: 10px;
    top:10px;
}
.multilevelpushmenu_wrapper h2 > .main-edition > li {
    border: none;
    padding: 0;
    opacity: .8;
    list-style: none;
}
.multilevelpushmenu_wrapper h2 > .main-edition > li:hover {
    background: none;
    opacity: 1;
}


.multilevelpushmenu_wrapper .edition {
    margin: 0;
    padding: 0;
    height: 10px;
    margin-top: 10px;
}
.multilevelpushmenu_wrapper .edition > a {
    display: block;
    outline: none;
    overflow: hidden;
    float: left;
    margin-right: 10px;
    font-size: 12px;
    line-height: 0px;
    padding: 0px;
    text-decoration: none;
    color: #fff;
    text-shadow: none;
    font-weight: 300;
    opacity: .8;
}
.multilevelpushmenu_wrapper .edition > a:hover {
    opacity: 1;
}


.multilevelpushmenu_wrapper .backItemClass {
	display: block;
	padding: .4em .4em .4em .4em;
}
.multilevelpushmenu_wrapper .backItemClass:hover {
	background-color: #242B35;
}
.multilevelpushmenu_wrapper .backItemClass:hover a {
	color: white;
}

.multilevelpushmenu_wrapper .floatRight {
	float: right;
	margin-top: 3px;
}

.multilevelpushmenu_wrapper .floatLeft {
	float: left;
	width: 22px;
	margin-top: 2px;
}
.levelHolderClass > h2 > i {
    margin-top: -2px !important;
}

#menu > .multilevelpushmenu_wrapper > .levelHolderClass > h2 > .floatLeft {
    margin-top: -1px !important;
}

.multilevelpushmenu_wrapper .cursorPointer {
	cursor: pointer;
}

.multilevelpushmenu_wrapper .iconSpacing_ltr {
	padding: 0;
	padding-left: 10px;
}

.multilevelpushmenu_wrapper .iconSpacing_rtl {
	padding: 0 0 0 .4em;
}



#multilevelmenu2 {
    position: relative;
    min-width: 100%;
    min-height: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background: white; 
}
#multilevelmenu2 .levelHolderClass {
    display: none;
    position: relative;
    padding-left: 20px;
}
#multilevelmenu2 .levelHolderClass > h2 {
    display: none;
}
#multilevelmenu2 li > label {
    margin-bottom: 0px;
    width: 100%;
}
#multilevelmenu2 input[type=checkbox] {
	position: absolute;
	opacity: 0;
}
#multilevelmenu2 li > ul {
	display: none;
}
#multilevelmenu2 li > h2 {
    display: none;
}
#multilevelmenu2 input[type=checkbox]:checked + li + div.levelHolderClass,
#multilevelmenu2 input[type=checkbox]:checked + li:nth-of-type(n) + div.levelHolderClass {
	display: block;
}



.multilevelpushmenu_wrapper .edition > a {
    color: #1e3948 !important;
}
.multilevelpushmenu_wrapper .edition > a:hover {
    color: #333 !important;
}