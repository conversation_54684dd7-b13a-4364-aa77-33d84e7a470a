h2.title {
    text-align: center;
    font-size: 28px;
    margin-bottom: 20px;
}


/* Style du sommaire */
#sommaire {
	margin:0;
	padding:0px;
	background: white;
	min-height: 400px;
	border: 1px solid #CCC;
border-radius: 4px 4px 4px 4px;
}

@media screen and (max-width: 768px) {
    #sommaire{
        width: 100%;
        float: none;
        margin-bottom: 0;
        padding-bottom: 0;
        min-height: 0;
    }
}

#sommaire .alert-notification {
    padding: 0 10px;
}
#sommaire .alert-notification .alert {
    margin-bottom: 10px;
}

#sommaire a{
	text-decoration : none;
}

#sommaire a:hover{
	text-decoration : underline;
}

#sommaire ul {
	font-size : 13px;
	padding : 0px;
	margin : 0px;
	width:100%;
}

#sommaire ul li {
	list-style-position:outside;
	margin:0;
	padding-bottom:5px;
	padding-top:5px;
	-webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    line-height: 16px;
    height: auto;
}
#sommaire form ul li {
	border-top:none;
	border-bottom:1px solid #AAA;
}
#sommaire ul li:last-child{
	border-bottom: none;
}

#sommaire ul li.active{
    background-color: #242B35;
}
#sommaire ul li.active img.puce-violet {
    margin-top: 5px;
    margin-left: 10px;
}
#sommaire ul li.nonvu:hover {
    cursor: not-allowed
}

.sequenceinfo {
    margin:0px;
    padding:0px;
    text-align: left;
    margin-top:1px;
    padding-left: 30px;
}
.sequenceinfo p {
	margin:0px;
	padding:0px;
	vertical-align:middle;
	font-size: 10px;
	color:#999;
	text-shadow: none
}
#sommaire ul li.active .sequenceinfo p {
	color:white;
}

.sequenceinfo img {
	margin:0px;
	padding:0px;
	vertical-align:middle
}

/* SPAN : donc pas de lien */
#sommaire ul li span {
	color : #828C8C;
	text-shadow: black 0px 1px 0px;
}

/* A : donc il y a un lien */
#sommaire ul a{
	color : #373D44;
}
#sommaire ul li.active a {
	color : white;
}

#sommaire h2{
	color: #000000;
	text-align: center;
}


#sommaire .state {

}
#sommaire .state:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0;
}

#sommaire .module_header {
   	background-color: #F4F4F4;
    border-bottom: 1px solid #D6D6D6;
    border-radius: 4px 4px 1px 0;
    display: inline-block;
    padding: 15px 0 15px;
    width: 100%;
}
#sommaire .module_header .pastille {
    width: 10%;
    text-align: center;
}
#sommaire .module_header .pastille > i {
    padding-top: 7px;
    font-size: 12px;
    text-shadow: black 0px 1px 0px;
}
#sommaire .module_header .title {
    width: 90%;
}
#sommaire .module_header h3 {
	color: #373D44;
    font-size: 18px;
	margin:0;
	cursor: pointer;
	line-height: 20px;
	font-weight: bold;
	padding-top: 0px;
	text-decoration: none;
}
#sommaire .module_header h3.close {
    float: none;
}
#sommaire .module_header h3:hover{
    text-decoration: underline;
}
#sommaire .module_header.nonvu > .pastille > i {
    color:#b60202;
}
#sommaire .module_header.vu > .pastille > i {
    color:#4dc23a;
}
#sommaire .module_header.encours > .pastille > i {
    color:#E657E4;
}



#sommaire ul.sequences {
	list-style: none;
	margin: 0px;
	padding: 0px;
}
#sommaire ul.sequences > li {
    padding: 5px 0px;
	margin: 0;
	font-size: 13px;
	color: #373D44;
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
}

#sommaire ul.sequences > li h4 {
	display: block;
	color:white;
	text-shadow: black 0px 1px 0px;
	text-decoration: none;
	position: relative;
	margin: 0;
    font-size: 12px;
    line-height: 16px;
    cursor: pointer;
    padding-right: 10px;
}
#sommaire ul.sequences > li h4:hover {
    text-decoration: underline;
}
#sommaire ul.sequences > li h4.close {
    float:none;
    opacity: 1;
}
#sommaire ul.sequences > li.nonvu {
    color: #828C8C;
}
#sommaire ul.sequences > li:hover {

}
#sommaire ul.sequences > li > .pastille {
    width: 10%;
    text-align: center;
}
#sommaire ul.sequences > li > .pastille > i {
    font-size: 12px;
}
#sommaire ul.sequences > li > .title {
    width: 90%;
}

#sommaire ul.sequences > li.nonvu > .pastille > i {
    color:#b60202;
}
#sommaire ul.sequences > li.vu > .pastille > i {
    color:#4dc23a;
}
#sommaire ul.sequences > li.active > .pastille > i {
    color:#E657E4;
}
#sommaire ul.sequences > li.encours > .pastille > i {
    color:yellow;
}


#sommaire ul.pages {
    margin-top: 5px;
}
#sommaire ul.pages li {
	border-top:none;
	border-bottom:none;
    height: auto;
    list-style: disc;
    padding: 3px 10px 3px 50px;
    color:white;
    font-size: 12px;
    font-weight: normal;
}
#sommaire ul.pages li.nonvu {
    color: #CCC;
}
#sommaire ul.pages li.nonvu:hover {
    cursor: not-allowed;
}

#sommaire ul.pages li:hover {
    background: #6b9dc8;
}
#sommaire ul.pages li a {
	display: block;
	text-decoration: none;
	position: relative;
}
#sommaire ul.pages li a.page-active {
    text-decoration: underline;
}
#sommaire ul.pages li a.page-active i {
    margin-left: -15px;
    margin-right: 5px;
    text-shadow: black 0px 1px 0px;
}


/* Fin du style du sommaire */