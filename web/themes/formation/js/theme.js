$(document).ready(function () {
    template_functions();
    widthFunctions();
});


/* ---------- Template Functions ---------- */
function template_functions() {

    /* ---------- Header Menu ---------- */
    $("#header a").click(function () {
        if ($(this).attr("href") != '' && $(this).attr("href") != '#') {
            window.location.href = $(this).attr("href");
        }
        return true;
    });

    $("#header > ul > li").click(function () {
        var menu = $(this).children("ul");
        $("#header").removeClass("inactive");

        if (menu.length > 0) {
            $("#header > ul > li").removeClass("active");
            $(this).addClass("active");
            $("#header > ul > li > ul").not(menu).hide();
            $("#header").addClass("inactive");
            if (menu.is(":hidden")) {
                menu.show();
            } else {
                menu.hide();
                $(this).removeClass("active");
                $("#header").removeClass("inactive");
            }

        } else {
            $("#header > ul > li > ul").hide();
            $("#header").removeClass("inactive");
            $("#header > ul > li").removeClass("active");
        }
        return false;
    });

    $("body").click(function () {
        $("#header > ul > li > ul").hide();
        $("#header").removeClass("inactive");
        $("#header > ul > li").removeClass("active");
    });

    $("#amore").click(function () {
        if ($("#more").is(":hidden")) {
            $("#more").fadeIn(200);
        } else {
            $("#more").fadeOut(200);
        }
    });
    $("#more a").click(function () {
        $("#more").hide();
    });


    // FAQs
    $("#faq .faq").click(function () {
        $(this).find(".reponse").slideToggle('fast');
        setTimeout(function() { Masonry(); }, 500);
    });

    // FAQs
    var $faqs = $("#faq .faq");
    $faqs.click(function () {
        var $answer = $(this).find(".answer");
        $answer.slideToggle('fast');
    });

    /* Menu de la formation */
    if (window.matchMedia('(min-width: 768px)').matches) {
        $(".dropdown-menu > li > a.trigger").on("click", function (e) {
            e.stopPropagation();
        });
        $(".navbar-nav > li.dropdown > a").on("click", function (e) {
            e.stopPropagation();
        });
    }

}


/* ---------- Page width functions ---------- */
$(window).bind("resize", widthFunctions);

function widthFunctions(e) {
    var winHeight = $(window).height();
    var winWidth = $(window).width();

    if (winHeight) {
        $("#content").css("min-height", winHeight);
    }


    /*if (winWidth <= 767) {
        var menuHeight = $('.levelHolderClass ul').height();

        $('#sommaire').height(menuHeight);
        $('#multilevelmenu').height(menuHeight);
        $('#multilevelmenu_multilevelpushmenu').height(menuHeight);

        $('#sommaire').css('min-height', menuHeight);
        $('#multilevelmenu').css('min-height', menuHeight);
        $('#multilevelmenu_multilevelpushmenu').css('min-height', menuHeight);
    }*/


    if (winWidth < 980 && winWidth > 767) {

        if ($(".main-menu-span").hasClass("col-md-2")) {
            $(".main-menu-span").removeClass("col-md-2");
            $(".main-menu-span").addClass("col-md-1");
        }

        if ($("#content").hasClass("col-md-10")) {
            $("#content").removeClass("col-md-10");
            $("#content").addClass("col-md-11");
        }

        $("a").each(function () {
            if ($(this).hasClass("quick-button-small col-md-1")) {
                $(this).removeClass("quick-button-small col-md-1");
                $(this).addClass("quick-button col-md-2 changed");
            }
        });

        $(".circleStatsItem").each(function () {
            var getOnTablet = $(this).parent().attr('onTablet');
            var getOnDesktop = $(this).parent().attr('onDesktop');
            if (getOnTablet) {
                $(this).parent().removeClass(getOnDesktop);
                $(this).parent().addClass(getOnTablet);
            }
        });

        $(".box").each(function () {
            var getOnTablet = $(this).attr('onTablet');
            var getOnDesktop = $(this).attr('onDesktop');
            if (getOnTablet) {
                $(this).removeClass(getOnDesktop);
                $(this).addClass(getOnTablet);
            }
        });

    } else {

        if ($(".main-menu-span").hasClass("col-md-1")) {
            $(".main-menu-span").removeClass("col-md-1");
            $(".main-menu-span").addClass("col-md-2");
        }

        if ($("#content").hasClass("col-md-11")) {
            $("#content").removeClass("col-md-11");
            $("#content").addClass("col-md-10");
        }

        $("a").each(function () {
            if ($(this).hasClass("quick-button col-md-2 changed")) {
                $(this).removeClass("quick-button col-md-2 changed");
                $(this).addClass("quick-button-small col-md-1");
            }
        });

        $(".circleStatsItem").each(function () {
            var getOnTablet = $(this).parent().attr('onTablet');
            var getOnDesktop = $(this).parent().attr('onDesktop');
            if (getOnTablet) {
                $(this).parent().removeClass(getOnTablet);
                $(this).parent().addClass(getOnDesktop);
            }
        });

        $(".box").each(function () {
            var getOnTablet = $(this).attr('onTablet');
            var getOnDesktop = $(this).attr('onDesktop');
            if (getOnTablet) {
                $(this).removeClass(getOnTablet);
                $(this).addClass(getOnDesktop);
            }
        });

    }

}


/* ---------- Additional functions for data table ---------- */
$.fn.dataTableExt.oApi.fnPagingInfo = function (oSettings) {
    return {
        "iStart": oSettings._iDisplayStart,
        "iEnd": oSettings.fnDisplayEnd(),
        "iLength": oSettings._iDisplayLength,
        "iTotal": oSettings.fnRecordsTotal(),
        "iFilteredTotal": oSettings.fnRecordsDisplay(),
        "iPage": Math.ceil(oSettings._iDisplayStart / oSettings._iDisplayLength),
        "iTotalPages": Math.ceil(oSettings.fnRecordsDisplay() / oSettings._iDisplayLength)
    };
}
$.extend($.fn.dataTableExt.oPagination, {
    "bootstrap": {
        "fnInit": function (oSettings, nPaging, fnDraw) {
            var oLang = oSettings.oLanguage.oPaginate;
            var fnClickHandler = function (e) {
                e.preventDefault();
                if (oSettings.oApi._fnPageChange(oSettings, e.data.action)) {
                    fnDraw(oSettings);
                }
            };

            $(nPaging).append(
                '<ul class="pagination">' +
                '<li class="prev disabled"><a href="#">&larr; ' + oLang.sPrevious + '</a></li>' +
                '<li class="next disabled"><a href="#">' + oLang.sNext + ' &rarr; </a></li>' +
                '</ul>'
            );
            var els = $('a', nPaging);
            $(els[0]).bind('click.DT', {action: "previous"}, fnClickHandler);
            $(els[1]).bind('click.DT', {action: "next"}, fnClickHandler);
        },

        "fnUpdate": function (oSettings, fnDraw) {
            var iListLength = 5;
            var oPaging = oSettings.oInstance.fnPagingInfo();
            var an = oSettings.aanFeatures.p;
            var i, j, sClass, iStart, iEnd, iHalf = Math.floor(iListLength / 2);

            if (oPaging.iTotalPages < iListLength) {
                iStart = 1;
                iEnd = oPaging.iTotalPages;
            }
            else if (oPaging.iPage <= iHalf) {
                iStart = 1;
                iEnd = iListLength;
            } else if (oPaging.iPage >= (oPaging.iTotalPages - iHalf)) {
                iStart = oPaging.iTotalPages - iListLength + 1;
                iEnd = oPaging.iTotalPages;
            } else {
                iStart = oPaging.iPage - iHalf + 1;
                iEnd = iStart + iListLength - 1;
            }

            for (i = 0, iLen = an.length; i < iLen; i++) {
                // remove the middle elements
                $('li:gt(0)', an[i]).filter(':not(:last)').remove();

                // add the new list items and their event handlers
                for (j = iStart; j <= iEnd; j++) {
                    sClass = (j == oPaging.iPage + 1) ? 'class="active"' : '';
                    $('<li ' + sClass + '><a href="#">' + j + '</a></li>')
                        .insertBefore($('li:last', an[i])[0])
                        .bind('click', function (e) {
                            e.preventDefault();
                            oSettings._iDisplayStart = (parseInt($('a', this).text(), 10) - 1) * oPaging.iLength;
                            fnDraw(oSettings);
                        });
                }

                // add / remove disabled classes from the static elements
                if (oPaging.iPage === 0) {
                    $('li:first', an[i]).addClass('disabled');
                } else {
                    $('li:first', an[i]).removeClass('disabled');
                }

                if (oPaging.iPage === oPaging.iTotalPages - 1 || oPaging.iTotalPages === 0) {
                    $('li:last', an[i]).addClass('disabled');
                } else {
                    $('li:last', an[i]).removeClass('disabled');
                }
            }
        }
    }
});


// @PLUGIN Url hash change
(function ($) {
    $.change = function (page, title, first) {
        if (!$(page).length > 0) {
            TriggerGritter('error', __("Le lien %s n'est pas valide", "<strong>" + page + "</strong>"));
        } else {
            window.location.hash = page;
            if (!$.browser.msie && first == true) {
                history.replaceState('', title, page);
            }

            $("ul.tabs > li").each(function () {
                $(this).removeClass('current');
            });
            $("div.tabs > div").each(function () {
                $(this).removeClass('current');
            });

            $(page).addClass('current');
            var find_a = $("ul.tabs").find("a[data-href='" + page + "']");
            if (find_a != 'undefined') {
                $(find_a).parent().addClass('current');
            }
        }
    };
})(jQuery);


// @PLUGIN Hashchange - http://benalman.com/projects/jquery-hashchange-plugin/ | Copyright (c) 2010 "Cowboy" Ben Alman | MIT & GPL license
(function ($, e, b) {
    var c = "hashchange",
        h = document,
        f, g = $.event.special,
        i = h.documentMode,
        d = "on" + c in e && (i === b || i > 7);

    function a(j) {
        j = j || location.href;
        return "#" + j.replace(/^[^#]*#?(.*)$/, "$1")
    }

    $.fn[c] = function (j) {
        return j ? this.bind(c, j) : this.trigger(c)
    };
    $.fn[c].delay = 50;
    g[c] = $.extend(g[c], {
        setup: function () {
            if (d) {
                return false
            }
            $(f.start)
        },
        teardown: function () {
            if (d) {
                return false
            }
            $(f.stop)
        }
    });
    f = (function () {
        var j = {},
            p, m = a(),
            k = function (q) {
                return q
            },
            l = k,
            o = k;
        j.start = function () {
            p || n()
        };
        j.stop = function () {
            p && clearTimeout(p);
            p = b
        };

        function n() {
            var r = a(),
                q = o(m);
            if (r !== m) {
                l(m = r, q);
                $(e).trigger(c)
            } else {
                if (q !== m) {
                    location.href = location.href.replace(/#.*/, "") + q
                }
            }
            p = setTimeout(n, $.fn[c].delay)
        }

        $.browser.msie && !d && (function () {
            var q, r;
            j.start = function () {
                if (!q) {
                    r = $.fn[c].src;
                    r = r && r + a();
                    q = $('<iframe tabindex="-1" title="empty"/>').hide().one("load", function () {
                        r || l(a());
                        n()
                    }).attr("src", r || "javascript:0").insertAfter("body")[0].contentWindow;
                    h.onpropertychange = function () {
                        try {
                            if (event.propertyName === "title") {
                                q.document.title = h.title
                            }
                        } catch (s) {
                        }
                    }
                }
            };
            j.stop = k;
            o = function () {
                return a(q.location.href)
            };
            l = function (v, s) {
                var u = q.document,
                    t = $.fn[c].domain;
                if (v !== s) {
                    u.title = h.title;
                    u.open();
                    t && u.write('<script>document.domain="' + t + '"<\/script>');
                    u.close();
                    q.location.hash = v
                }
            }
        })();
        return j
    })()
})(jQuery, this);


// @PLUGIN Overlay plugin by me
(function ($) {
    $.fn.overlay = function (heading, url, animation) {
        if (url != undefined) {
            $.ajax({
                url: url,
                success: function (html) {
                    makeOverlay(heading, html, animation);
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    $.notification(
                        {
                            title: errorThrown,
                            content: "Couldn't access the provided URL",
                            error: true
                        }
                    );
                    return false;
                }
            });
        } else if (this.length > 0) {
            makeOverlay(heading, this.html(), animation);
        } else {
            $.notification(
                {
                    title: 'You have to provide something to the function',
                    content: 'You need to provide an url like this: <br>$.fn.overlay("Heading","http://example.com") <br><br><strong>or</strong><br><br>Pass an object to the function like this: <br>$("#object").overlay("Heading")',
                    error: true
                }
            );
            return false;
        }
        var overlay;
        $(document).keyup(function (e) {
            if (e.which == 27) {
                overlay.remove();
                $(document).unbind("keyup");
            }
        });

        function makeOverlay(heading, html, animation) {
            overlay = $('<div class="overlay"> /')
            var wrapper = $('<div class="wrapper" />');

            if (animation != undefined) {
                overlay.addClass("animated " + animation);
            } else {
                overlay.addClass("animated fadeInDown");
            }

            if (heading != undefined) {
                wrapper.append('<h2>' + heading + '</h2>');
            }
            wrapper.append(html);
            wrapper.appendTo(overlay);
            $('#overlays').append(overlay);
        }
    };
})(jQuery);
