.multilevelmenu-collapse {
    margin-bottom: 20px;
    position: relative;
}

#multilevelmenu-overlay {
    display: block;
    z-index: 10;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    background: #dee9e9;;
    left: 0;
}

#sommaire{
	margin:0;
	padding:0px;
	width: 25%;
	float:left;
	/*min-height: 685px;*/
	margin-bottom: -99999px;
    padding-bottom: 99999px;
}

#sommaire.menu-right {
    -webkit-box-shadow: inset rgba(0,0,0,0.1) 8px 0px 10px 0px;
       -moz-box-shadow: inset rgba(0,0,0,0.1) 8px 0px 10px 0px;
            box-shadow: inset rgba(0,0,0,0.1) 8px 0px 10px 0px;
}

#sommaire.menu-right .multilevelpushmenu_wrapper .ltr {
    -webkit-box-shadow: none;
       -moz-box-shadow: none;
            box-shadow: none;
}

div.tabs.full {
    width: 100%
}

@media screen and (max-width: 768px) {
    #sommaire{
        width: 100%;
        float: none;
        margin-bottom: 0;
        padding-bottom: 0;
        min-height: 0;
        height: auto !important;
    }
}


#multilevelmenu {
    position: relative;
    display: block;
    border: none;
}

.mainmenu {
    position: relative;
    overflow-x: hidden;
    margin-top: 7px;
}


.multilevelpushmenu_wrapper {
	position: absolute;
	overflow: hidden;
	min-width: 100%;
	min-height: 100%;
	margin: 0;
	padding: 0;
}

.multilevelpushmenu_wrapper .levelHolderClass {
	position: absolute;
	overflow: hidden;
	top: 0;
	width: auto;
	min-height: 100%;
	font-family: 'Open Sans Condensed', sans-serif;
	font-size: 1em;
	zoom: 1;
}

.multilevelpushmenu_wrapper .ltr {
	margin-left: -100%;
	left: 0;
}

.multilevelpushmenu_wrapper .rtl {
	margin-right: -100%;
	right: 0;
}

.multilevelpushmenu_wrapper h2 {
	font-size: 18px !important;
	line-height: 22px;
    font-weight: bold;
	color: #4c4c4c;
	padding: 10px;
	margin-top: 0px;
	/*min-height: 25px;*/
    background: #dee9e9;
    background-image: -webkit-linear-gradient(#dee3e3 0%, #b7c5c5 100%);
}

.multilevelpushmenu_wrapper ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.multilevelpushmenu_wrapper li {
	cursor: pointer;
    padding: .4em .4em .4em .4em;
    margin-bottom: 0px;
    border-bottom: 1px solid #a2afaf;
    font-size: 13px;
    color: #828c8c;
    background: #DEE9E9;
    text-shadow: white 0px 1px 0px;
    -webkit-box-shadow: white 0px 1px 0px;
    -moz-box-shadow: white 0px 1px 0px;
    box-shadow: white 0px 1px 0px;
    
    padding: 10px;
    font-weight: bold;
}
.multilevelpushmenu_wrapper li.action {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
	background: transparent;
}
.multilevelpushmenu_wrapper li.nonvu {
    font-style: italic;
    opacity: .6;
}

.multilevelpushmenu_wrapper li.header {
    height:50px;
}
.multilevelpushmenu_wrapper li.header:hover {
    background: transparent;
}

.multilevelpushmenu_wrapper li:hover {
	background: #D4DEDE;
}

.multilevelpushmenu_wrapper a {
	display: block;
    outline: none;
    overflow: hidden;
    font-size: 14px;
    line-height: 20px;
    padding: 0px;
    text-decoration: none;
    color: #828c8c;
}

.multilevelpushmenu_wrapper a:hover,
.multilevelpushmenu_wrapper li.active a {
	color: #4c4c4c;
}

.multilevelpushmenu_wrapper li.active {
    background-image: -webkit-linear-gradient(#dee3e3 0%, #b7c5c5 100%);
    background-image: -moz-linear-gradient(#dee3e3 0%, #b7c5c5 100%);
    -moz-box-shadow: rgba(255,255,255,0.7) 0px 1px 0px inset;
    -webkit-box-shadow: rgba(255,255,255,0.7) 0px 1px 0px inset;
    box-shadow: rgba(255,255,255,0.7) 0px 1px 0px inset;
    color: #6a6f76;
    text-shadow: rgba(255,255,255,0.7) 0px 1px 0px;
    cursor: default;
}

.multilevelpushmenu_wrapper li.action {
    border: none;
}
.multilevelpushmenu_wrapper li.action a {
	padding: 0px;
	font-size: 14px;
}

.multilevelpushmenu_wrapper li.action i.floatLeft {
    padding-top: 3px;
    padding-right: 0px;
    width: 15px;
}


.multilevelpushmenu_wrapper h2 > .main-edition {
    float:right;
    position: absolute;
    right: 10px;
    top:10px;
}
.multilevelpushmenu_wrapper h2 > .main-edition > li {
    border: none;
    padding: 0;
    opacity: .8;
    list-style: none;
}
.multilevelpushmenu_wrapper h2 > .main-edition > li:hover {
    background: none;
    opacity: 1;
}


.multilevelpushmenu_wrapper .edition {
    margin: 0;
    padding: 0;
    height: 10px;
    margin-top: 10px;
}
.multilevelpushmenu_wrapper .edition > a {
    display: block;
    outline: none;
    overflow: hidden;
    float: left;
    margin-right: 10px;
    font-size: 12px;
    line-height: 0px;
    padding: 0px;
    text-decoration: none;
    color: #fff;
    text-shadow: none;
    font-weight: 300;
    opacity: .8;
}
.multilevelpushmenu_wrapper .edition > a:hover {
    opacity: 1;
}


.multilevelpushmenu_wrapper .backItemClass {
	display: block;
	background: #DEE9E9;
	padding: .4em .4em .4em .4em;
	border-bottom: 1px solid #a2afaf;
    -webkit-box-shadow: white 0px 1px 0px;
       -moz-box-shadow: white 0px 1px 0px;
            box-shadow: white 0px 1px 0px;
}
.multilevelpushmenu_wrapper .backItemClass a {
    font-size: 13px;
}
.multilevelpushmenu_wrapper .backItemClass .floatLeft {
	width: 10px;
	margin-top: 3px;
}


.multilevelpushmenu_wrapper .floatRight {
	float: right;
	margin-top: 3px;
}

.multilevelpushmenu_wrapper .floatLeft {
	float: left;
	width: 22px;
	margin-top: 2px;
}

.multilevelpushmenu_wrapper .cursorPointer {
	cursor: pointer;
}

.multilevelpushmenu_wrapper .iconSpacing_ltr {
	padding: 0;
	padding-left: 10px;
}

.multilevelpushmenu_wrapper .iconSpacing_rtl {
	padding: 0 0 0 .4em;
}



#multilevelmenu2 {
    position: relative;
    min-width: 100%;
    min-height: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}
#multilevelmenu2 .levelHolderClass {
    display: none;
    position: relative;
    padding-left: 20px;
}
#multilevelmenu2 .levelHolderClass > h2 {
    display: none;
}
#multilevelmenu2 li > label {
    margin-bottom: 0px;
    width: 100%;
}
#multilevelmenu2 input[type=checkbox] {
	position: absolute;
	opacity: 0;
}
#multilevelmenu2 li > ul {
	display: none;
}
#multilevelmenu2 li > h2 {
    display: none;
}
#multilevelmenu2 input[type=checkbox]:checked + li + div.levelHolderClass,
#multilevelmenu2 input[type=checkbox]:checked + li:nth-of-type(n) + div.levelHolderClass {
	display: block;
}