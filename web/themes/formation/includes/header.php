<!DOCTYPE html>
<html lang="<?php echo isset($language) ? $language : 'fr'; ?>" xmlns:fb="http://ogp.me/ns/fb#">
<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">

	<?php
echo '<title>' . $this->data['title'] . '</title>' . "\n\t";
echo '<meta name="description" content="' . $this->data['seo_description'] . '">' . "\n\t";
echo '<meta name="keywords" content="' . $this->data['seo_tags'] . '">' . "\n";
    ?>

    <meta name="robots" content="noodp" />
    <meta name="Identifier-URL" content="<?php echo SITE_URL; ?>">
    <link rel="alternate" type="application/rss+xml" href="<?php echo Tools::getLink($_SESSION['actual_domaine_id'], 'site', 'feed'); ?>" title="<?php echo SITE_NAME; ?>" />

    <!-- Main CSS -->
    <?php \Learnybox\Helpers\Assets::renderCss(); ?>

    <!-- Fav and touch icons -->
    <?php if (isset($this->data['favicon']) and $this->data['favicon']) {
        ?>
    <link rel="icon" href="<?php echo $this->data['favicon']; ?>">
    <?php
    } else {
        ?>
    <link rel="apple-touch-icon" sizes="144x144" href="<?php echo \Learnybox\Helpers\Assets::getImageUrl('apple-touch-icon-144x144.png', 'assets/') ?>">
    <link rel="apple-touch-icon" sizes="114x114" href="<?php echo \Learnybox\Helpers\Assets::getImageUrl('apple-touch-icon-114x114.png', 'assets/') ?>">
    <link rel="apple-touch-icon" sizes="72x72" href="<?php echo \Learnybox\Helpers\Assets::getImageUrl('apple-touch-icon-72x72.png', 'assets/') ?>">
    <link rel="apple-touch-icon" href="<?php echo \Learnybox\Helpers\Assets::getImageUrl('apple-touch-icon.png', 'assets/') ?>">
    <link rel="shortcut icon" href="<?php echo SITE_URL; ?>favicon.ico">
    <?php
    } ?>

    <?php \Learnybox\Helpers\Assets::renderJs(true); ?>
    <?php \Learnybox\Helpers\Assets::renderInlineJs(true); ?>

</head>

<body>

    <?php if ($this->data['adminbar']) {
            echo $this->data['adminbar'];
        } ?>
    <?php if ($this->data['banniere']) {
            echo $this->data['banniere'];
        } ?>

    <div id="header" class="navbar-inner">
		<ul class="con">
			<li class="dashboard">
			    <?php echo $this->data['logo']; ?>
			</li>
            <button type="button" class="navbar-toggle collapsed btn btn-navbar" data-toggle="collapse" data-target=".navbar-collapse" aria-expanded="false" aria-controls="navbar">
                <span class="fa fa-bars"></span> <?php echo __('Menu'); ?>
            </button>
		    <?php echo $this->data['postHeader']; ?>
        </ul>
	</div>

    <?php if ($this->data['menu']) {
            ?>
    <div class="navbar navbar-mainmenu navbar-menu" id="stream">
        <div class="con">
            <div class="container">
                <nav class="navbar navbar-menu">
                    <div class="container-fluid">
                        <div class="collapse navbar-collapse">
                            <ul class="nav navbar-nav">
                                <?php echo $this->data['menu']; ?>
                            </ul>
                        </div>
                    </div>
                </nav>
            </div>
        </div>
    </div>
    <?php
        } ?>

    <div id="main-content">
