<?php

echo '
	<div class="post">
        <div class="row">';

if ('' != trim($article['image'])) {
    echo '
      		<div class="col-md-4">
                <a href="' . $article['link'] . '">
                    <img class="main_pic" src="' . $article['image'] . '" style="width:100%" />
                </a>
            </div>
            <div class="col-md-8 info">';
} else {
    echo '<div class="col-md-12 info-large">';
}

echo '
    <h3>
        <a href="' . $article['link'] . '">' . $article['titre'] . '</a>
    </h3>
    <p>' . stripslashes($article['description_short']) . '</p>
    <div class="post_info">
    	<div class="infos">';

if (isset($article['type'])) {
    echo $article['type'] . '<br>';
}

if (isset($article['author']) and $article['author']) {
    echo '<span class="post_info_author">';
    if (isset($article['gplus']) and $article['gplus']) {
        echo '<i class="fa fa-google-plus-square"></i> <a rel="author" href="' . $article['gplus'] . '" target="_blank">' . $article['author'] . '</a><br>';
    }
    echo '<i class="fa fa-user"></i> ' . $article['author'] . '<br></span>';
}

if ($article['article_date']) {
    echo '<i class="fa fa-clock-o"></i> ' . $article['article_date'] . '<br>';
}

if ($article['categorie']) {
    echo '<i class="fa fa-folder-open"></i> ' . $article['categorie'] . '<br>';
}

if ($article['comments']) {
    echo '<i class="fa fa-comments-o"></i> ';
    echo 0 == $article['nb_commentaires'] ? 'Aucun commentaire' : $article['nb_commentaires'] . ' commentaire' . ($article['nb_commentaires'] > 1 ? 's' : '');
}

echo '
    			</div>
            </div>
        </div>
    </div>
    <a href="' . $article['link'] . '" class="btn">' . __('Lire la suite') . '</a>
</div>';
