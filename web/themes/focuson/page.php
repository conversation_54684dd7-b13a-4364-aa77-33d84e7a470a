<div class="container">
    <?php if (isset($additionnal_css) and $additionnal_css) {
    ?>
    <style type="text/css">
        <?php echo $additionnal_css; ?>
    </style>
    <?php
} ?>
    
    <div class="row user-interface">
        <?php
        $content = stripslashes($content);

        if (isset($large) and $large) {
            echo '<div class="col-md-12">' . $content . '</div>';
        } else {
            if (isset($menu) and $menu) {
                echo '
                	<div class="col-md-3">
                	    ' . $menu . '
                	</div>
                    <div class="col-md-9">
                        ' . $content . '
                    </div>';
            } else {
                echo '<div class="col-md-8 col-md-offset-2">' . $content . '</div>';
            }
        }
        ?>
    </div>
</div>