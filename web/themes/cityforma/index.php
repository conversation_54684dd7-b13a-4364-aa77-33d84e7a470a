
    <div class="container">
        <div class="row" style="margin-top:0px">
            <div class="col-md-8">
                <?php
                if ($formation) {
                    echo '
                        <h2 style="text-align:center; padding-top: 50px;">' . __('Bienvenue sur la formation') . '<br>"' . $formation['nomformation'] . '"</h2>
                        <div class="media" style="margin-top:15px">
                            <div class="media-body">
								<h4 style="text-align:center">' . __('Cliquez sur le bouton "Démarrer ma formation"<br>pour commencer l\'entraînement !') . '</h4>
								<p style="text-align:center;"><em>' . __('Ou choisissez une formation ci-dessous.') . '</em></p>
							</div>
                        </div>';
                } else {
                    if (defined('SITE_LOGO')) {
                        $absolute_url_logo = WEB_PATH . '/' . str_replace(SITE_URL, '', SITE_LOGO);
                        if (file_exists($absolute_url_logo)) {
                            echo '<center><img src="' . SITE_LOGO . '" alt="Logo" style="max-height:120px;" /></center>';
                        }
                    }
                }
                ?>
            </div>

            <div class="col-md-4" style="margin-top:50px">
                <div class="box">
    				<div class="box-content">
    					<?php echo eden()->Formation_Methode()->IndexProgressionProgram($idformation); ?>
    				</div>
                </div>
			</div>
        </div>
    </div>

    <div class="container">
        <div class="row" id="modules" style="margin-top:0px">
            <div class="box">
                <div class="head">
	    	      <h4><i class="fa fa-book"></i> <?php echo __('Sommaire de la formation'); ?></h4>
                </div>
    	    	<div class="box-content">
    				<?php echo eden()->Formation_Formation()->DisplayModulesAccueil(); ?>
    	    	</div>
            </div>
		</div>
	</div>

    <div class="container">
        <div class="row" id="formations" style="margin-top:0px">
	    	<div class="box">
                <div class="head">
                    <h4><i class="fa fa-book"></i> <?php echo __('Mes formations'); ?></h4>
                </div>
	    	    <div class="box-content">
				    <?php echo eden()->Formation_Formation()->DisplayFormationsByUser(true); ?>
                </div>
	    	</div>
    	</div>
    </div>
