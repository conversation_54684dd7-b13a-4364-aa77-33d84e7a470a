/* Basic
=================================================================== */
.dropdown-menu li > a {
    padding: 3px 10px;
    white-space: normal;
    min-width: 200px;
}
.dropdown-menu .sub-menu {
    left: 100%;
    position: absolute;
    top: 0;
    display:none;
    margin-top: -5px;
    margin-left: 0px;
    -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
       -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}
.dropdown-menu .sub-menu > li {
    margin-bottom: 0px;
}
.right-caret {
    float:right;
}
.right-caret:after {
    content:"";
    border-bottom: 5px solid transparent;
    border-top: 5px solid transparent;
    display: inline-block;
    height: 0;
    width: 0;
    border-left: 5px solid black;
}

.dropdown-menu .arrow {
  position: absolute;
  display: none;
  width: 0;
  height: 0;
  border-width: 8px;
  border-color: transparent;
  border-style: solid;
  top: 15px;
  left: -11px;
  margin-top: -8px;
  border-right-color: #999;
  border-right-color: rgba(0, 0, 0, 0.25);
  border-left-width: 0;
}

.dropdown-menu .arrow:after {
    position: absolute;
    display: none;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 10px;
    content: "";
    bottom: -10px;
    left: 1px;
    border-right-color: #ffffff;
    border-left-width: 0;
}



html {
    margin: 0;
}

body {
    position: relative;
    -webkit-font-smoothing: antialiased;
    font-size: 14px;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    color: #333;
}

.user-interface,
.user-interface p,
.dropdown-menu-user p,
.message p {
    color: #333 !important;
    font-size: 14px !important;
}

#overlay {
    background: white url('../images/ajax-loader.gif') no-repeat 50% 50%;
    position: fixed;
    z-index: 99999;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
}


.input-append .add-on, .input-prepend .add-on {
    padding: 4px 5px;
}

body > .container {
    margin-top: 5px;
}
.container > .row {
    margin-top: 15px;
}
ul.tabs {
    list-style: none;
    padding: 0;
    margin: 0;
}
ul.tabs li {
    cursor: pointer;
    padding: .4em .4em .4em .4em;
    margin-bottom: 0px;
}
ul.tabs li:hover {
    background-color: #EEE;
}
ul.tabs li a {
    display: block;
    outline: none;
    overflow: hidden;
    font-size: 16px;
    line-height: 20px;
    padding: 0px;
    text-decoration: none;
    color: black;
}
ul.tabs li a:hover {
    color: #333;
}


.container.notifications {
	z-index: 2;
	overflow: auto;
	width: 1170px;
}
.container.notifications .alert {
	margin: 10px;
	margin-top: 20px;
}


.banniere {
    width: 1170px;
    position: relative;
    margin: 0px auto;
}
.banniere img {
    margin: 0 auto;
    display: block;
    max-width: 100%;
}
@media screen and (max-width: 1170px) {
    .banniere {
        width: 100%;
        border: none;
    }
    .banniere img {
        width: 100%;
        border: none;
    }
}




ul.postheader {
    position: absolute;
    right: 0px;
    top: 15px;
    margin: 0px;
    padding: 0px;
}

ul.postheader > li.dropdown {
	float: left;
	list-style: none;
	font-size:14px;
	color: #666;
	cursor: pointer;
	position: relative;
	margin: 0px;
	padding-right: 30px;
	-webkit-transform: translate3d(0,0,0);
	-webkit-transition: opacity .4s ease-in-out;
	-moz-transition: opacity .4s ease-in-out;
	z-index: 1000;
}

ul.postheader > li > i {
	font-size: 16px;
	margin-top: 24px;
	float: left;
	padding-right: 5px;
}
ul.postheader > li > a {
	text-decoration: none;
	color: #666;
}
ul.postheader > li > a.dropdown-toggle {
	display: block;
	padding-bottom: 10px;
}
ul.postheader > li:hover > a,
ul.postheader > li:hover > span {
	color: black;
}


ul.postheader li > ul > li > a.change-formation.active {
	background-color: #475B77 !important;
    text-decoration: none;
    color: #ffffff;
}


ul.postheader li.avatar {
	padding: 0px;
	height: 50px;
	width: 50px;
	margin-top: -15px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	background-size: 100%;
	z-index: 1000;
}
ul.postheader li.avatar img {
	width: 50px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
}


ul.postheader li.count {
    margin-right: 20px;
}
ul.postheader li.count > a:after,
ul.postheader li.count > span:after {
	content: attr(data-count);
	position: absolute;
	height: 20px;
	width: 20px;
	background: #CCC;
	line-height: 20px;
	text-align: center;
	color: black;
	top: 50%;
	margin-top: -15px;
	right: 5px;
	-moz-border-radius: 40px;
	-webkit-border-radius: 40px;
	border-radius: 40px;
	font-size: 12px;
}
ul.postheader li.count:hover > a:after,
ul.postheader li.count:hover > span:after {
	background: black;
	color: white;
	font-weight: bold;
}


ul.postheader li.dropdown > .dropdown-menu {
    left: auto;
    right: 15%;
    min-width: 300px;
    margin-top: 0px;
}
ul.postheader li.count > .dropdown-menu {
    margin-top: 0px;
}
ul.postheader li.dropdown > .dropdown-menu:after {
    position: absolute;
    top: -6px;
    left: auto;
    right: 13px;
    display: inline-block;
    border-right: 6px solid transparent;
    border-bottom: 6px solid black;
    border-left: 6px solid transparent;
    content: '';
}
ul.postheader ul.dropdown-menu .avatar {
    padding: 1px;
    box-shadow: none;
    max-width: 50px;
    float: left;
    margin-right: 10px;
}
ul.postheader .dropdown-menu > li > a {
    padding: 3px 10px;
}
ul.postheader .dropdown-menu h4 {
    margin: 0;
    padding: 0;
}

ul.postheader .dropdown-menu p {
    margin-bottom: 0px;
}



.scrolltop {
	background-color: #F1F1F1;
	border: 1px solid darkGray;
	border-radius: 100px 100px 100px 100px;
	-webkit-border-radius: 100px;
	-moz-border-radius: 100px;
	display: none;
	height: 36px;
	opacity: 0.6;
	filter: alpha(opacity=60);
	position: fixed;
	right: 2%;
	/*text-indent: -9999px;*/
	top: 50%;
	width: 36px;
	z-index: 33;
}
.scrolltop i {
	position: relative;
    top: 6px;
    left: 8px;
    font-size: 22px;
    color: #AAA
}
.scrolltop i:hover {
    color: #666;
    text-decoration: none
}

.scrolltop span {
	position: relative;
	top: 7px;
	left: 4px;
}


/* Old versions of IE patches */
.old-ie #hero .carousel-inner .row {
	padding-left: 55px;
}
.old-ie #box_login .box .social a.face_login span.text {
    font-size: 16px;
    margin: 8px 0 0 10px;
}
.old-ie .nav-collapse.collapse {
	height: auto !important;
	overflow: visible !important;
}

.wrapper-btn {
    text-align: center;
    margin: 20px 0;
}


a.link {

}
a.link:hover {
    text-decoration: none;
}

/* Bootstrap overrides */

textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus,
.uneditable-input:focus {
  border-color: #81A74C;
  outline: 0;
  outline: thin dotted \9;
  /* IE6-9 */
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px #92C65F;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px #92C65F;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px #92C65F;
}


/* Navbar
-------------------------------------------------- */
.navbar-fixed-top {
    margin-bottom: 0;
}
.navbar-fixed-top.scroll {
    opacity: 0.96;
}


.navbar {
    margin-bottom: 0px;
    border: none;
    border-radius: 0px;
}
.nav>li>a:focus, .nav>li>a:hover {
    background: transparent;
}

@media (min-width:768px) {
    .navbar-collapse {
        padding-left: 0px;
        padding-right: 0px;
    }
    .navbar-menu .container-fluid {
        padding-left: 0px;
        padding-right: 0px;
    }
}
@media (max-width:768px) {
    .navbar-nav>li>.dropdown-menu {
        border-radius: 0px;
        width: 100%;
        position: static;
    }
}

.navbar-toggle {
    z-index: 10;
    background-color: #fff;
    border-color: #ccc;
}


.navbar-inner {
	border-bottom: 0 none;
    filter: none;
    padding: 7px 0 8px 0;
    border-radius: 0px;
    border: none;
    background: white;
}
.navbar-inner .container {
    position: relative;
}

@media (max-width: 979px) {
    .nav-collapse, .nav-collapse.collapse {
        height: 0;
        overflow: hidden;
    }
    .nav-collapse.collapse.in {
        height: auto !important;
    }
    .nav-collapse .open > .dropdown-menu {
        position: relative;
    }
}


.navbar-menu {
	border-bottom: 0 none;
	-webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
	-moz-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    filter: none;
    padding: 7px 0 8px 0;
    border-radius: 0px;
    border: none;

	background: #242424;
	background: -moz-linear-gradient(top, #242424 0%, #242424 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#242424), color-stop(100%,#242424));
	background: -webkit-linear-gradient(top, #242424 0%,#242424 100%);
	background: -o-linear-gradient(top, #242424 0%,#242424 100%);
	background: -ms-linear-gradient(top, #242424 0%,#242424 100%);
	background: linear-gradient(top, #242424 0%,#242424 100%);
	filter:progid:DXImageTransform.Microsoft.gradient( startColorstr='#242424', endColorstr='#242424',GradientType=0 );
}
.navbar-menu .container {
    position: relative;
}



.navbar .name {
    display: block;
}
.navbar .name img {
	max-height: 75px;
    max-width: 100%;
}
.navbar .brand h1 {
    font-size: 28px;
	text-shadow: none;
	color: white;
	line-height: 20px
}
.navbar .nav.pull-right {
	top: 50px;
	/*margin-right: 40px;*/
}
.navbar .nav > li > a {
	color: #F8F8F8;
	font-size: 15px;
	font-weight: bold;
    text-shadow: none;
	-webkit-transition: all .2s linear;
	-moz-transition: all .2s linear;
	-ms-transition: all .2s linear;
	-o-transition: all .2s linear;
	transition: all .2s linear;
}
.navbar .nav > li > a:hover {
	color: #ccc;
}
.navbar .nav > li.active > a,
.navbar .nav > li.active:hover > a,
.navbar .nav > .active > a,
.navbar .nav > .active > a:hover,
.navbar .nav > .active > a:focus {
    padding-bottom: 4px;
	-webkit-box-shadow: none;
	box-shadow: none;
	-moz-box-shadow: none;
	background: none;
	color: #fff;
}


.navbar .nav li.dropdown.open > .dropdown-toggle,
.navbar .nav li.dropdown.active > .dropdown-toggle,
.navbar .nav li.dropdown.open.active > .dropdown-toggle,
.navbar .nav li.dropdown.open .dropdown-menu li a,
.navbar .nav li.dropdown.active .dropdown-menu li a,
.navbar .nav li.dropdown.open.active .dropdown-menu li a {
  background: none;
  color: #fff;
}
.dropdown-menu li > a:hover,
.dropdown-menu li > a:focus,
.dropdown-submenu:hover > a {
	background-color: #475B77 !important;
	text-decoration: none;
	color: #ffffff;
	background-image: -moz-linear-gradient(top, #475B77, #475B77);
	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#475B77), to(#475B77));
	background-image: -webkit-linear-gradient(top, #475B77, #475B77);
	background-image: -o-linear-gradient(top, #475B77, #475B77);
	background-image: linear-gradient(to bottom, #475B77, #475B77);
	background-repeat: repeat-x;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#475B77', endColorstr='#475B77', GradientType=0);
}
.dropdown-menu li > a:hover h4,
.dropdown-menu li > a:hover p {
    color: white !important;
}

.navbar .btn-navbar .fa fa-bars {
  display: block;
  width: 18px;
  height: 2px;
  background-color: #5FAF08;
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  border-radius: 1px;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

.navbar .btn,
.navbar .btn-group {
    margin-top: 60px;
}

.navbar .nav-actions {
    position: absolute;
    top: 5px;
    right: 0px;
}
.navbar .btn-header {
	background: #414A57;
	background: -moz-linear-gradient(top, #414A57 0%, #292f38 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#414A57), color-stop(100%,#292f38));
	background: -webkit-linear-gradient(top, #414A57 0%,#292f38 100%);
	background: -o-linear-gradient(top, #414A57 0%,#292f38 100%);
	background: -ms-linear-gradient(top, #414A57 0%,#292f38 100%);
	background: linear-gradient(to bottom, #414A57 0%,#292f38 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#414A57', endColorstr='#292f38',GradientType=0 );
	border: 1px solid #13161b;
    border-radius: 6px;
    display: inline-block;
    position: relative;
    top: 5px;
    margin-left: 5px;
    margin-right: 5px;
	color: #F8F8F8;
	font-size: 15px;
	font-weight: bold;
    text-shadow: none;
	-webkit-transition: all .2s linear;
	-moz-transition: all .2s linear;
	-ms-transition: all .2s linear;
	-o-transition: all .2s linear;
	transition: all .2s linear;
	padding: 10px 15px 10px;
}
.navbar .btn-header:hover {
	background: #545F6D;
	background: -moz-linear-gradient(top, #545F6D 0%, #303946 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#545F6D), color-stop(100%,#303946));
	background: -webkit-linear-gradient(top, #545F6D 0%,#303946 100%);
	background: -o-linear-gradient(top, #545F6D 0%,#303946 100%);
	background: -ms-linear-gradient(top, #545F6D 0%,#303946 100%);
	background: linear-gradient(to bottom, #545F6D 0%,#303946 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#545F6D', endColorstr='#303946',GradientType=0 );
	color: #ccc;
	text-decoration: none;
}


@media (max-width: 979px) {
	.nav-collapse .nav > li > a:hover,
	.nav-collapse .dropdown-menu a:hover {
    	background: none !important;
	}
	.nav-collapse .nav > li > a, .nav-collapse .dropdown-menu a {
		color: #ccc;
	}

    .navbar .nav>li>.dropdown-menu .sub-menu {
        width: 100%;
        position: relative;
        top: 0px;
        left: 0px;
        border: none;
        box-shadow: none;
        background: none;
    }
    .navbar .nav>li>.dropdown-menu .sub-menu li a {
        padding: 9px 30px;
    }
}


/* Index
-------------------------------------------------- */

/* carousel section */
#hero {
	padding: 10px 0;
	background: #ffffff; /* Old browsers */
	background: -moz-linear-gradient(top,  #ffffff 1%, #f9f9f9 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(1%,#ffffff), color-stop(100%,#f9f9f9)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top,  #ffffff 1%,#f9f9f9 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top,  #ffffff 1%,#f9f9f9 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top,  #ffffff 1%,#f9f9f9 100%); /* IE10+ */
	background: linear-gradient(to bottom,  #ffffff 1%,#f9f9f9 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#f9f9f9',GradientType=0 ); /* IE6-9 */
	height: auto;
	border-bottom: 1px solid #d1d2d4;
	box-shadow: 0px 3px 8px 0px #e7e7e7;
}
#hero .carousel-control.left {
	left: 0px;
	padding-right: 1px;
}
#hero .carousel-control.right {
	right: 0px;
}
#hero .carousel-inner .item img {
	max-height: 349px;
}
#hero .carousel-inner .item.slide3 img {
	display: block;
	margin: 0 auto;
}
#hero .item h1 {
	font-size: 24px;
	font-weight: bolder;
	margin-top: 10px;
}
#hero .item p {
	font-size: 13px;
	color: #4E4E4E;
	line-height: 20px;
}
#hero .item .btn {
	margin-top: 10px;
	font-weight: bolder;
}
/* introduction heading section*/
#intro {
	text-align: center;
	margin-top: 50px;
}
#intro h1 {
	width: 55%;
	font-size: 28px;
	margin: 0 auto;
	color: #252B34;
	font-weight: normal;
	text-shadow: white 2px 2px 1px;
}
/* features section */
#produits { }
#produits .container { }
#produits .produit img.thumb,
#produits .produit .thumb {
	border: 3px solid #fff;
	border-radius: 5px;
	box-shadow: 0px 0px 0px 1px #bcbcbc;
	display: block;
	margin: 0 auto;
	-webkit-transition: all .2s linear;
	-moz-transition: all .2s linear;
	-ms-transition: all .2s linear;
	-o-transition: all .2s linear;
	transition: all .2s linear;
}
#produits .produit img.thumb {
    height: 288px;
}
#produits .produit img.thumb:hover {
	box-shadow: 0px 0px 7px 1px #8cbe5b;
}
#produits .produit h3 {
	font-size: 22px;
	font-weight: bolder;
	color: #252b34;
	margin-top: 20px;
	/*text-align: center;*/
	padding-left: 35px;
}
#produits .produit h3 a {
	color:#252b34
}
#produits .produit h3 i {
	margin-right: 5px;
	position: relative;
	top: 2px;
	opacity: .8
}
#produits .produit p.description {
	color: #44474D;
	width: 85%;
	padding-left: 35px;
}
#produits .produit .btn {
	margin-top: 15px;
	margin-left: 35px;
}

#produits .box_info{
	width: 331px;
    background-color: white;
	border: 1px solid #CCC;
	border-radius: 6px;
	padding-bottom: 0px;
	margin: 0 auto;
	margin-top: 20px;
	box-shadow: 0px 0px 10px 0px #DDD;
}
#produits .box_info .head{
	background-color: #F4F4F4;
    border-bottom: 1px solid #D6D6D6;
    border-radius: 4px 4px 1px 0;
    display: inline-block;
    margin-bottom: 20px;
    padding: 25px 0 25px;
    text-align: left;
    width: 100%;

}
#produits .box_info .head h4{
	font-weight: normal;
	color: #373D44;
	font-size: 21px;
	margin: 0px;
	padding-left: 35px;
}
#produits .box_info p.description {
	color: #44474D;
	width: 85%;
	padding-left: 35px;
}

#produits .box_info .footer{
	background-color: #F4F4F4;
    border-top: 1px solid #D6D6D6;
    display: inline-block;
    margin-top: 20px;
    padding: 20px 0 20px;
    text-align: center;
    width: 100%;
}

/* pricing charts section */
#pricing { margin-top:0px }
#pricing .section_header {
	margin-bottom: 70px;
}
#pricing .section_header hr {
	width: 35%;
}
#pricing .price_wrapper {
	background-color: #fafafa;
	border: 1px solid #d9d9d9;
	box-shadow: 0px 0px 5px 2px #E8E8E8;
	border-radius: 6px;
	width: 321px;
	min-height: 375px;
	height: auto;
	position: relative;
}
#pricing .price_wrapper.regular .header {
	background: url('../images/chart1.png') 0 0 no-repeat;
}
#pricing .price_wrapper.pro {
	width: 331px;
	margin: 0 auto;
}
#pricing .price_wrapper.pro .header {
	background: url('../images/chart2.png') 0 0 no-repeat;
}
#pricing .price_wrapper section .btn {
	left: 35%;
}
#pricing .price_wrapper .header {
	height: 98px;
	color: #fff;
	padding: 25px 0px 0px 40px;
}
#pricing .price_wrapper .header > span {
	font-size: 24px;
	font-style: italic;
}
#pricing .price_wrapper .header .price {
	font-size: 20px;
	top: 25px;
	position: relative;
	font-weight: bold;
}
#pricing .price_wrapper .header .price span {
	font-size: 48px;
	margin-right: 10px;
}
#pricing .price_wrapper .section {
	padding: 15px 30px;
	background: white;
}
#pricing .price_wrapper .section h3 {
	color: #535861;
	font-size: 17px;
	line-height: 22px;
}
#pricing .price_wrapper .section ul.perks {
	list-style-type: none;
	margin-left: 0px;
	margin-top: 18px;
}
#pricing .price_wrapper .section ul.perks li {
	padding: 0px;
	line-height: 22px;
}
#pricing .price_wrapper .section ul.perks i {
	margin-right: 10px;
	width:20px;
}
#pricing .price_wrapper .section ul.perks li span {
	margin-right: 2px;
}
#pricing .price_wrapper .section .btn {
	position: absolute;
	bottom: 15px;
	left: 32%;
	font-weight: bold;
}
#pricing .price_wrapper .footer {
	background-color: #F4F4F4;
    border-top: 1px solid #D6D6D6;
    display: inline-block;
    margin-top: 0px;
    padding: 20px 0 20px;
    text-align: center;
    width: 100%;
}

/* testimonials section */
#testimonials {
	margin-top: 50px;
}
#testimonials .controls hr {
	border-top: 1px solid #BEC0C3;
	border-bottom: 2px solid white;
}
#testimonials .phrases_wrapper {
	margin-bottom: 50px;
	padding: 20px 10px 0px 10px;
}
#testimonials .phrases_wrapper .phrase { }
#testimonials .phrases_wrapper .phrase p.testimony {
	color:#3a3f46;
	font-style: italic;
	font-size: 21px;
	/*width: 60%;*/
	line-height: 28px;
	position: relative;
	float: left;
}
#testimonials .phrases_wrapper .phrase p.testimony span {
	font-family: "Georgia", Arial;
	font-size: 50px;
	color: #c9cbce;
	position: absolute;
	margin-left: 10px;
	margin-top: 15px;
}
#testimonials .phrases_wrapper .phrase .pic {
	float: right;
}
#testimonials .phrases_wrapper .phrase .pic img {
	max-width: 55px;
	float: left;
	margin-right: 20px;
	position: relative;
	top: -6px;
}
#testimonials .phrases_wrapper .phrase .pic .name {
	font-weight: bold;
	color: #2C3644;
}
#testimonials .phrases_wrapper .phrase .pic .position { }

/* footer section */
#footer {
	min-height: 50px;
	margin-top: 30px;
	color: #666;
	padding-top: 35px;
	border-top: 1px solid #CCC;
}
#footer h3 {
	margin-bottom: 25px;
}
#footer a.affiliation {
    color: #666;
}
#footer .col-md-5 {
    width: 100% !important;
    text-align: center;
}
#footer hr {
	border-top: 1px solid black;
	border-bottom: 1px solid #363B43;
	margin: 8px 0px 0px 0px;
}
#footer .copyright h3 {
	float: left;
}
#footer .copyright .social {
    display: inline-block;
    margin-top: 8px;
	margin-left: 10px;
	border-radius: 22px;
	padding: 10px;
	text-align: center;
	color:#6e6e6e;
	opacity: .7;
	text-decoration: none
}
#footer .copyright .social:hover {
	background-color: #1A1E25;
	opacity:1;
}
#footer .copyright .social.facebook:hover { color:#3b5998 }
#footer .copyright .social.twitter:hover { color:#5dd7fc }
#footer .copyright .social.googleplus:hover { color:#d94a39 }
#footer .copyright .social.youtube:hover { color:#D51727 }
#footer .copyright .social.linkedin:hover { color: #007AB5; }
#footer .copyright .social.skype:hover { color: #00AFF0; }
#footer .copyright .social.instagram:hover { color: #E7DDD3; }
#footer .copyright .social.tumblr:hover { color: #35465D; }
#footer .copyright .social.pinterest:hover { color: #D21F24; }
#footer .copyright .social.rss:hover { color:#fb9b27 }
#footer .copyright .social.email:hover { color:white }

#footer .copyright .copy p {
	margin-top: 20px;
}

/* section header */
.section_header {
	text-align: center;
	font-weight: bolder;
	color: #252b34;
	margin-top: 80px;
	position: relative;
	margin-bottom: 40px;
}
.section_header.left span {
	margin-left: 0px;
	text-align: left;
}
.section_header hr {
	border-top: 1px solid #BEC0C3;
	border-bottom: 2px solid white;
	width: 36%;
	position: absolute;
}
.section_header hr.left { }
.section_header hr.right {
	right: 0px;
	top: 0px;
}
.section_header span {
	width: 27%;
	display: block;
	margin: 0 auto;
}
.section_header small {
	margin-left: 20px;
	font-size: 16px;
	color: #7f8387;
	display: none;
}


.box {
    background-color: white;
	float: none;
	margin: 0 auto;
	width: 100%;
	margin-bottom: 25px;
	margin-top: 30px;
}
.box.maformation {
	border: 1px solid #CCC;
}
.box:first-child {
	margin-top: 0px;
}

.box .head{
	background-color: #F4F4F4;
    border-bottom: 1px solid #D6D6D6;
    display: inline-block;
    padding: 15px 0 15px;
    width: 100%;
}
.box .head h4 {
	font-weight: normal;
	color: #373D44;
	font-size: 18px;
	margin: 0px;
	text-align: left;
	padding-left: 15px
}
.box .head h4 i {
	padding-right:10px;
}
.box .box-content {
	padding:10px;
}



/* Responsive
-------------------------------------------------- */

/* Large desktop */
@media (min-width: 1200px) {
	.section_header small {
		display: inline-block;
	}
	#pricing .price_wrapper.lite {
		float: right;
		right: -30px;
	}
	#pricing .price_wrapper.standard {
		left: -30px;
	}
	#hero .carousel-inner .item.slide1 .col-md-6 img {
		margin-left: 15px;
	}
	#hero .carousel-inner .item.slide1 .col-md-4 {
		padding-left: 30px;
	}
	#hero .carousel-inner .item.slide2 .col-md-6 img {
		margin-left: 30px;
	}
	#hero .item h1 {
		font-size: 30px;
		margin-top: 40px;
		margin-left: 20px;
	}
	#hero .item p {
		font-size: 16px;
		line-height: 24px;
		margin-left: 20px;
	}
	#hero .item .btn {
		margin-top: 25px;
		margin-left: 20px;
		font-size: 18px;
	}
	footer .contact_us input[type="text"],
	footer .contact_us textarea{
		width: 270px;
	}
	footer .contact_us button[type="submit"] {
		margin-left: 300px;
	}
}
@media (min-width: 980px) {
	#pricing .price_wrapper.pro {
		position: relative;
		top: -20px;
		height: auto;
		z-index: 9;
		box-shadow: 0px 0px 5px 4px #E8E8E8;
	}
	#produits .box_info {
		position: relative;
		top: -20px;
		z-index: 9;
		box-shadow: 0px 0px 5px 4px #E8E8E8;
	}
	#hero .carousel-inner .item {
		padding: 0px 70px;
	}

}
@media (max-width: 979px) {
	body {
		padding-top: 0px;
	}
	.section_header {
		margin-top: 60px;
	}
	.section_header small {
		display: none;
	}
	#pricing .price_wrapper {
		margin: 0 auto;
		margin-bottom: 20px !important;
	}
	#produits .box_info {
		margin: 0 auto;
		margin-bottom: 20px !important;
	}
	#hero .item h1 {
		font-size: 18px;
		line-height: 18px;
	}
	#hero .carousel-inner .item.slide2 img {
		margin-top: 20px;
	}
	#testimonials .phrases_wrapper .phrase .pic .position {
		display: block;
	}
	#footer {
		margin-top: 20px;
	}
}
/* Portrait tablet to landscape and desktop */
@media (min-width: 768px) and (max-width: 979px) {
	#intro h1 {
		width: 80%;
	}
	#features .feature h3 {
		padding-left: 0px;
	}
	#features .feature p.description {
		padding-left: 0px;
	}
	#features .feature .btn {
		margin-left: 0px;
	}
	#pricing .price_wrapper {
		width: 240px !important;
	}
	#pricing .price_wrapper .header .price span {
		font-size: 25px;
	}
	#pricing .price_wrapper.pro {
		min-height: 400px;
		position: relative;
		top: -10px;
	}
	#pricing .price_wrapper.regular .header {
		background: url('../images/chart1_small.png') 0 0 no-repeat;
	}
	#pricing .price_wrapper.pro .header {
		background: url('../images/chart2_small.png') 0 0 no-repeat;
	}
	#produits .box_info {
		width: 240px !important;
		position: relative;
	}
	#testimonials .phrases_wrapper {
		padding-left: 0px;
		padding-right: 0px;
	}
	#testimonials .phrases_wrapper .phrase .col-md-3 {
		margin-left: 30px;
		width: 228px;
	}
	#footer .contact_us button[type="submit"] {
		margin-left: 0px;
	}
	#hero { }
	#hero .carousel-control.left {
		left: -55px;
	}
	#hero .carousel-control.right {
		right: -15px;
	}
	#hero .item .col-md-4 {
		margin-left: 50px;
	}
}
/* Landscape phone to portrait tablet */
@media (max-width: 767px) {
	#intro h1 {
		width: 90%;
	}
	#hero {
		padding: 50px 20px 20px 20px;
		margin-right: -20px;
		margin-left: -20px;
		height: auto;
	}
	#hero .carousel-inner .item {
		padding: 0px 70px;
	}
	#hero .item img {
		/*display: none;*/
		width: 240px;
		display: block;
		margin: 0 auto;
	}
	#hero .carousel-inner .item .btn {
		font-size: 13px;
		padding: 7px 10px;
	}
	#footer {
		margin-right: 0px;
		margin-left: 0px;
	}
	#features .feature .btn {
		margin-top: 8px;
	}
	#features .feature {
		margin-bottom: 30px;
	}
	#testimonials .phrases_wrapper .phrase > p {
		width: 100%;
		font-size: 18px;
	}
	#testimonials .phrases_wrapper .phrase .pic {
		float: left;
		margin-top: 8px;
		font-size: 13px;
	}
	#footer .container {
		padding: 0px 20px;
	}
	#footer .blog_post .img-circle {
		float: left;
		margin-right: 20px;
	}
	#footer .copyright .copy {
		float: left;
	}
	#footer .copyright .copy p {
		margin-top: 0px;
	}
}
/* Landscape phones and down */
@media (max-width: 480px) {
	.section_header {
		margin-top: 30px;
	}
	.section_header span {
		width: auto;
	}
	#intro h1 {
		font-size: 22px;
		line-height: 30px;
	}
	#pricing .price_wrapper {
		width: 321px !important;
	}
	#pricing .price_wrapper .header .price span {
		font-size: 38px;
	}
	#produits .box_info {
		width: 321px !important;
	}
	#footer .contact_us button[type="submit"] {
		margin-left: 0px;
	}
}

/* Icons
-------------------------------------------------- */
.i_bars,
.i_bookmark,
.i_chart,
.i_cloud,
.i_facebook,
.i_loading,
.i_movil,
.i_shuffle,
.i_twitter,
.i_youtube{
	background: url('../images/icons.png') no-repeat;
	display: inline-block;
}
.i_bars{
	background-position: 0 -303px ;
	width: 18px;
	height: 16px;
}
.i_bookmark{
	background-position: 0 -253px ;
	width: 18px;
	height: 13px;
}
.i_chart{
	background-position: 0 -49px ;
	width: 22px;
	height: 24px;
}
.i_cloud{
	background-position: 0 0;
	width: 32px;
	height: 21px;
}
.i_facebook{
	background-position: 0 -401px ;
	width: 12px;
	height: 24px;
}
.i_loading{
	background-position: 0 -202px ;
	width: 21px;
	height: 14px;
}
.i_movil{
	background-position: 0 -99px ;
	width: 17px;
	height: 25px;
}
.i_shuffle{
	background-position: 0 -151px ;
	width: 18px;
	height: 12px;
}
.i_twitter{
	background-position: 0 -351px ;
	width: 24px;
	height: 18px;
}
.i_youtube{
	background-position: 0 -452px ;
	width: 24px;
	height: 23px;
}

/* Social icons
-------------------------------------------------- */
i.social {
	background: url('../images/social_icons.png') no-repeat 0 0;
	width: 33px;
	height: 33px;
	display: inline-block;
}
i.tw { background-position: 0px 0px; }
i.fb { background-position: -39px 0; }
i.flickr { background-position: -78px 0; }
i.in { background-position: -117px 0; }
i.gp { background-position: -156px 0; }
i.pin { background-position: -195px 0; }
i.tumblr { background-position: -233px 0; }
i.wp { background-position: 0 -51px; }
i.yt { background-position: -39px -51px; }
i.vim { background-position: -78px -51px; }
i.picasa { background-position: -116px -51px; }
i.forrst { background-position: -156px -51px; }
i.rss { background-position: -194px -51px; }
i.myspace { background-position: -233px -51px; }




/* Sidebar socials
-------------------------------------------------- */

.sidebar {

}

.sidebar a.social {
	border-radius: 30px;
	width: 33px;
	height: 33px;
	display: inline-block;
	margin-right: 10px
}

.sidebar a.social.twitter { background: #1898c9; }
.sidebar a.social.facebook { background: #436dac; }
.sidebar a.social.googleplus { background: #d64532; }
.sidebar a.social.youtube { background: #D51727; }
.sidebar a.social.linkedin { background: #007AB5; }
.sidebar a.social.skype { background: #00AFF0; }
.sidebar a.social.instagram { background: #E7DDD3; }
.sidebar a.social.tumblr { background: #35465D; }
.sidebar a.social.pinterest { background: #D21F24; }
.sidebar a.social.rss { background: #ec7422; }
.sidebar a.social.mail { background: #1898c9; }

.sidebar a.social i {
	font-size: 22px;
	padding-top: 7px;
	padding-left: 6px;
	color: white;
}
.sidebar a.social.twitter i { padding-left: 7px }
.sidebar a.social.facebook i { padding-left: 9px }
.sidebar a.social.googleplus i { padding-left: 7px }
.sidebar a.social.youtube i { padding-top: 5px; padding-left: 7px }
.sidebar a.social.linkedin i { padding-left: 7px }
.sidebar a.social.skype i { padding-left: 7px }
.sidebar a.social.instagram i { padding-left: 8px }
.sidebar a.social.pinterest i { padding-left: 7px }
.sidebar a.social.tumblr i { padding-left: 9px }
.sidebar a.social.rss i { padding-left: 8px; }
.sidebar a.social.mail i { font-size: 20px; padding-left: 6px; }

.sidebar hr:first-child {
	display: none;
}

.sidebar .recent_posts {
	list-style-type: none;
	margin-top: 10px;
	margin-left: 0px;
}
.sidebar .recent_posts li {
	margin-bottom: 20px;
	font-size: 15px;
}
.sidebar .recent_posts li:last-child {
	margin-bottom: 0px;
}
.sidebar .recent_posts li:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.sidebar .recent_posts li .thumb {
	border: 3px solid #fff;
	box-shadow: 0px 0px 1px 1px #bdbdbd;
	float:left;
	max-width: 75px;
	margin-right: 10px;
}
.sidebar .recent_posts li a.link {
	float:left;
	color: #333;
	display: block;
	font-weight: bold
}
.sidebar .recent_posts a:hover {
	color: #87a46e;
	text-decoration: underline;
}
.sidebar .recent_posts li .text {
	float:left;
}




.avatar {
	padding:10px;
	border-radius:3px;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
}
#useravatar .control-label {
	width:auto;
}
#useravatar .avatar {
	width:100px;
	height: 100px;
	border:1px solid #666;
	text-align: center
}
#useravatar .controls {
	margin-left:0px
}
#useravatar .fileinput-button {
	width: 96px;
	margin-top: 10px;
}
#useravatar .progress {
	width:120px;
	display:none;
}



.comments {
    background-color: white;
	border: 1px solid #CCC;
	float: none;
	margin: 0 auto;
	width: 100%;
	margin-bottom: 25px;
	margin-top: 30px;
}
.comments #addCommentForm {
    padding: 10px;
}
.comments .head{
	background-color: #242424 !important;
    border-bottom: 1px solid #D6D6D6;
    border-radius: 0;
    display: inline-block;
    padding: 15px 0 15px;
    width: 100%;
}
.comments .head h4 {
	font-weight: normal;
	color: white !important;
	font-size: 18px;
	margin: 0px;
	text-align: left;
	padding-left: 15px
}
.comments .head h4 i {
	padding-right:10px;
}
.comments .comments-content {
	padding:10px;
}
.comments .comments-content .comment .name {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px
}
.comments .comments-content .comment .name a {
    font-weight: normal;
}
.comments .comments-content .comment.reply_to {
	margin-top: 10px;
	margin-left: 60px
}
.comments .reply {
    font-size: 12px;
    color: #666;
    margin-left: 10px;
}
.comments .reply:hover {
    color: black;
}



/*Chart tooltip*/
.chart-tooltip {
	background: black;
	background: rgba(0,0,0,0.8);
	background: -webkit-linear-gradient(rgba(0,0,0,0.6), black);
	background: -moz-linear-gradient(rgba(0,0,0,0.6), black);
	border-radius: 4px;
	-webkit-box-shadow: rgba(0,0,0,0.3) 0px 2px 10px;
	-moz-box-shadow: rgba(0,0,0,0.3) 0px 2px 10px;
	box-shadow: rgba(0,0,0,0.3) 0px 2px 10px;
	z-index: 20;
	color: white;
	padding: 12px;
	padding-left: 16px;
	padding-right: 16px;
	text-align: center;
	font: normal 12px HelveticaNeue, Helvetica, Arial;
	display: inline-block;
	position: absolute;
	text-shadow: black 0px 1px 1px;
	border: 1px solid black;
	max-width: 240px;
	visibility: hidden;
}
.chart-tooltip .arrow {
	position: absolute;
	left: 30px;
	bottom: -16px;
	border-color: transparent;
	border-top-color: #000;
	border-style:solid;
	border-width:8px;
	width:0px;
	height:0px;
}
.chart-tooltip h4 {
	margin: 0px;
	font-size: 18px;
	color: white !important;
}




.modules {
    margin-bottom: 20px;
    margin-top: 20px;
}

.modules .element {
    padding: 20px;
    border: 1px solid #ececec;
    background: white;
    position: relative;
    display: block;
}
.modules .element:hover {
    background: #f4f4f4;
}
.modules .element img {
    max-width: 130px;
    max-height: 80px;
}
.modules .element.error img {
    opacity: .7;
}

.element.main-module {
    border: none;
    padding: 0px;
}
.element.main-module img {
    max-width: 200px;
    max-height: 125px;
}
.element.main-module .description {
    margin-top: 10px;
}
.element.main-module .infos {
    padding: 10px;
}
.element.box .head {
    padding-bottom: 0px;
}
.element.box .head i {
    padding-right: 0px;
}
.element.main-module .btn-return {
    margin: 20px 0 10px;
}


.modules .element i.module-icone {
    font-size: 40px;
}
.modules .element i.page-icone {
    font-size: 40px;
}
.modules .element h4 {
    margin: 0px;
}
.modules .element .infos::after {
    clear: both;
    display: table;
    line-height: 0;
    content: "";
}
.modules .element .raty {
    float: left;
    margin-right: 0px;
}
.modules .element .duree {
    float: left;
    margin-right: 0px;
}
.modules .element span.label {
    float: left;
    margin-right: 10px;
    margin-top: 1px;
}
.modules .element .btn-access {
    position: absolute;
    top: 10px;
    right: 10px;
}
.modules .element.page .infos {
    margin-top: 5px;
}



@media(min-width:768px) {
    .dropdown:hover > .dropdown-menu {
        display: block;
    }
}




.message-row {
    box-sizing: border-box;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-flex-direction: row;
    box-sizing: border-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 20px;
}
.message-row.user {
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
    -ms-flex-align: end;
    align-items: flex-end;
}
.message-row .avatar {
    float:left;
    width: 60px;
    min-width: 60px;
    height: 60px;
    border-radius: 50%;
    text-align: center;
    color: #fff;
    box-shadow: none;
    padding: 0px;
    border: none;
    margin: 0 16px 0 0;
}
.message-row.user .avatar {
    -webkit-box-ordinal-group: 3;
    -webkit-order: 2;
    -ms-flex-order: 2;
    order: 2;
    margin: 0 0 0 16px;
}
.message-row .bubble {
    position: relative;
    padding: 8px;
    background-color: #E8F5E9;
    border: 1px solid #DFEBE0;
    box-sizing: border-box;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
}
.message-row.user .bubble {
    background-color: #eceff1;
    border: 1px solid #dcdfe1;
    -webkit-box-ordinal-group: 2;
    -webkit-order: 1;
    -ms-flex-order: 1;
    order: 1;
}
.message-row .bubble .infos {
    font-size: 11px;
    margin-bottom: 8px;
}
.message-row .bubble .attachments {
    margin-top: 10px;
    font-size: 12px;
}


.message-row .bubble:before {
    left: -9px;
    right: auto;
    top: -1px;
    bottom: auto;
    border: 8px solid;
    border-color: #DFEBE0 transparent transparent transparent;
}
.message-row .bubble:after {
    left: -7px;
    right: auto;
    top: 0;
    bottom: auto;
    border: 11px solid;
    border-color: #E8F5E9 transparent transparent transparent;
}
.message-row .bubble:after,
.message-row .bubble:before {
    content: ' ';
    position: absolute;
    width: 0;
    height: 0;
}

.message-row.user .bubble:before {
    right: -9px;
    left: auto;
    top: auto;
    bottom: -1px;
    border-color: transparent transparent #dcdfe1 transparent;
}
.message-row.user .bubble:after {
    right: -7px;
    left: auto;
    top: auto;
    bottom: 0;
    border-color: transparent transparent #eceff1 transparent;
}




#faq h3 {
    margin-bottom: 20px;
}
#faq .section_header {
	margin-top: 55px;
}
#faq .faq {
	padding-left: 20px;
	position: relative;
	margin-bottom: 15px;
}
#faq .faq:hover .number {
	background-color: #7fa662;
	color: #fff;
}
#faq .faq:hover .question {
	color: #7fa662;
}
#faq .faq .number {
	background-color: white;
	border-radius: 50%;
	color: #394350;
	position: absolute;
	left: 0px;
	padding: 10px;
	font-weight: bold;
	font-size: 17px;
	top: -5px;
	transition: background-color 0.3s linear;
	-moz-transition: background-color 0.3s linear;
	-webkit-transition: background-color 0.3s linear;
	-o-transition: background-color 0.3s linear;
}
#faq .faq .question {
	color: #2c3339;
	font-weight: bold;
	font-size: 17px;
	cursor: pointer;
	transition: color 0.2s linear;
	-moz-transition: color 0.2s linear;
	-webkit-transition: color 0.2s linear;
	-o-transition: color 0.2s linear;
}
#faq .faq .answer {
	margin-top: 20px;
	margin-bottom: 30px;
	font-size: 15px;
	border-bottom: 1px solid #BEC0C3;
	padding-bottom: 15px;
	box-shadow: 0px 1px 1px 0px white;
	display: none;
}



.btn-secondary {
    color: #fff;
    background-color: #337ab7;
    border-color: #2e6da4;
}
.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary:active,
.btn-secondary:active:hover,
.btn-secondary:active:focus {
    color: #fff;
    background-color: #286090;
    border-color: #204d74;
}



.left-avatar {
    border: 1px solid #CCC;
    margin-top: 0px;
}
.left-menu {
    border-radius: 0px;
}
