/* Basic sample */
.flipbook-viewport{
	overflow:hidden;
	width:100%;
	height:100%;
}

.flipbook-viewport .flipbook{

}

.flipbook-viewport .page{
	background-color:transparent;
	background-repeat:no-repeat;
	background-size:100% 100%;
}

.flipbook .page{
	-webkit-box-shadow:0 0 20px rgba(0,0,0,0.2);
	-moz-box-shadow:0 0 20px rgba(0,0,0,0.2);
	-ms-box-shadow:0 0 20px rgba(0,0,0,0.2);
	-o-box-shadow:0 0 20px rgba(0,0,0,0.2);
	box-shadow:0 0 20px rgba(0,0,0,0.2);
}

.flipbook-viewport .page img{
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	margin:0;
}

.flipbook-viewport .shadow{
	-webkit-transition: -webkit-box-shadow 0.5s;
	-moz-transition: -moz-box-shadow 0.5s;
	-o-transition: -webkit-box-shadow 0.5s;
	-ms-transition: -ms-box-shadow 0.5s;

	-webkit-box-shadow:0 0 20px #ccc;
	-moz-box-shadow:0 0 20px #ccc;
	-o-box-shadow:0 0 20px #ccc;
	-ms-box-shadow:0 0 20px #ccc;
	box-shadow:0 0 20px #ccc;
}



.homebar {
    width: 100%;
	padding: 4px;
	height: 35px !important;
	box-sizing: border-box;
	margin: 0 auto !important;
	margin-bottom: 10px !important;
	display: block;
	background-color: #2a2f36;
}
.homebar .left,
.homebar .right {
    width: 40%;
    float:left;
}
.homebar .infos {
    width: 20%;
    float: left;
    color: white;
    text-align: center;
    padding-top: 3px;
}
.homebar a.home-button {
    float:left;
    margin-right: 5px;
}
.homebar a.prev-button {
    float:left;
}
.homebar a.next-button {
    float: right;
    margin-right: 5px;
}
.homebar a.last-button {
    float:right;
}