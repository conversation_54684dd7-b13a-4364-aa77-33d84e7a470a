<?php

echo '
<div class="post">
    <h3>
        <a href="' . $article['link'] . '">' . $article['titre'] . '</a>
    </h3>
    <div class="post_info">
    	<ul class="infos">';

if (isset($article['type'])) {
    echo '<li>' . $article['type'] . '</li>';
}

if (isset($article['gplus']) and $article['gplus'] and isset($article['author']) and $article['author']) {
    echo '<li><i class="fa fa-google-plus-square"></i> <a rel="author" href="' . $article['gplus'] . '" target="_blank">' . $article['author'] . '</a></li>';
} elseif (isset($article['author']) and $article['author']) {
    echo '<li><i class="fa fa-user"></i> ' . $article['author'] . '</li>';
}

if ($article['article_date']) {
    echo '<li><i class="fa fa-clock-o"></i> ' . $article['article_date'] . '</li>';
}

if ($article['categorie']) {
    echo '<li><i class="fa fa-folder-open"></i> ' . $article['categorie'] . '</li>';
}

if ($article['comments']) {
    echo '<li><i class="fa fa-comments-o"></i> ' . (0 == $article['nb_commentaires'] ? 'Aucun commentaire' : $article['nb_commentaires'] . ' commentaire' . ($article['nb_commentaires'] > 1 ? 's' : '')) . '</li>';
}

echo '
        </ul>
    </div>
    
    <div class="row">';

if ('' != trim($article['image'])) {
    echo '
		<div class="col-md-4">
            <a href="' . $article['link'] . '">
                <img class="main_pic" src="' . $article['image'] . '" style="width:100%" />
            </a>
        </div>
        <div class="col-md-8 info">';
} else {
    echo '<div class="col-md-12 info-large">';
}

echo '
            <p>' . stripslashes($article['description_short']) . '</p>
            <a href="' . $article['link'] . '" class="btn">' . __('Lire la suite') . '</a>
        </div>
    </div>
</div>';
