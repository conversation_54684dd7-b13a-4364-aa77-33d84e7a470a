.countdown-timer {
	margin: 0 auto
}

.countdown-timer-style-1 {
	min-width: 103px;
	max-width: 622px;
	text-align: center;
	white-space: nowrap
}

.countdown-timer-style-1 .hasCountdown {
	display: inline-block
}

.countdown-timer-style-1 .countdown_rtl {
	direction: rtl
}

.countdown-timer-style-1 .countdown_row {
	clear: both
}

.countdown-timer-style-1 .countdown-amount {
	position: relative;
	display: inline-block;
	padding: .2em 0;
	min-width: 2em;
	line-height: 1em;
	font-family: Impact;
	font-weight: 700;
	font-size: 48px;
	color: #fff;
	text-shadow: -1px -1px 0 #000,1px -1px 0 #000,-1px 1px 0 #000,1px 1px 0 #000;
	background: #191919;
	background: -moz-linear-gradient(top,rgba(25,25,25,1) 0,rgba(31,31,31,1) 50%,rgba(38,38,38,1) 50%,rgba(51,51,51,1) 100%);
	background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,rgba(25,25,25,1)),color-stop(50%,rgba(31,31,31,1)),color-stop(50%,rgba(38,38,38,1)),color-stop(100%,rgba(51,51,51,1)));
	background: -webkit-linear-gradient(top,rgba(25,25,25,1) 0,rgba(31,31,31,1) 50%,rgba(38,38,38,1) 50%,rgba(51,51,51,1) 100%);
	background: -o-linear-gradient(top,rgba(25,25,25,1) 0,rgba(31,31,31,1) 50%,rgba(38,38,38,1) 50%,rgba(51,51,51,1) 100%);
	background: -ms-linear-gradient(top,rgba(25,25,25,1) 0,rgba(31,31,31,1) 50%,rgba(38,38,38,1) 50%,rgba(51,51,51,1) 100%);
	background: linear-gradient(to bottom,rgba(25,25,25,1) 0,rgba(31,31,31,1) 50%,rgba(38,38,38,1) 50%,rgba(51,51,51,1) 100%);
	filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#191919', endColorstr='#333333', GradientType=0);
	-webkit-box-shadow: inset 0 0 0 1px rgba(64,64,64,1),inset 0 -1px 0 0 rgba(255,255,255,.3),inset 0 -2px 0 0 rgba(0,0,0,1),inset 0 -3px 0 0 rgba(255,255,255,.3),inset 0 -4px 0 0 rgba(0,0,0,1),inset 0 -5px 0 0 rgba(255,255,255,.1);
	-moz-box-shadow: inset 0 0 0 1px rgba(64,64,64,1),inset 0 -1px 0 0 rgba(255,255,255,.3),inset 0 -2px 0 0 rgba(0,0,0,1),inset 0 -3px 0 0 rgba(255,255,255,.3),inset 0 -4px 0 0 rgba(0,0,0,1),inset 0 -5px 0 0 rgba(255,255,255,.1);
	box-shadow: inset 0 0 0 1px rgba(64,64,64,1),inset 0 -1px 0 0 rgba(255,255,255,.3),inset 0 -2px 0 0 rgba(0,0,0,1),inset 0 -3px 0 0 rgba(255,255,255,.3),inset 0 -4px 0 0 rgba(0,0,0,1),inset 0 -5px 0 0 rgba(255,255,255,.1);
	border: 3px solid #000;
	-webkit-border-radius: 8px;
	-moz-border-radius: 8px;
	border-radius: 8px
}
.countdown-timer-style-1 .countdown-amount: after {
	content: "";
	height: 1px;
	width: 100%;
	background: #000;
	position: absolute;
	top: 50%;
	left: 0;
	border-top: 1px solid rgba(255,255,255,.175);
	border-bottom: 1px solid rgba(255,255,255,.175)
}
.countdown-timer-style-1 .countdown-section {
	display: inline-block;
	margin: 0 1px;
	font-size: 11px;
	font-weight: 400;
	text-align: center;
	text-transform: uppercase;
	color: inherit
}
.countdown-timer-style-1 .countdown-section: first-child {
	margin-left: 0
}
.countdown-timer-style-1 .countdown-section: last-child {
	margin-right: 0
}
.floating-featured-panel-fluid .countdown-timer-style-1 .countdown-amount {
	font-size: 30px
}
@media only screen and (max-width: 479px) {
	.countdown-timer-style-1 .countdown-amount {
    	font-size: 32px
    }
}


.countdown-timer-style-2 {
	min-width: 103px;
	max-width: 622px;
	text-align: center;
	white-space: nowrap
}

.countdown-timer-style-2 .hasCountdown {
	display: inline-block
}

.countdown-timer-style-2 .countdown_rtl {
	direction: rtl
}

.countdown-timer-style-2 .countdown_row {
	clear: both
}

.countdown-timer-style-2 .countdown-amount {
    display: block;
    padding: 10px 0px;
    font-weight: normal;
    text-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
}
.countdown-timer-style-2 .countdown-amount: after {
	content: "";
	height: 1px;
	width: 100%;
	background: #000;
	position: absolute;
	top: 50%;
	left: 0;
	border-top: 1px solid rgba(255,255,255,.175);
	border-bottom: 1px solid rgba(255,255,255,.175)
}
.countdown-timer-style-2 .countdown-section {
    display: inline-block;
    text-align: center;
    text-transform: uppercase;
    color: inherit;
    position: relative;
    display: inline-block;
    padding: 1px;
    min-width: 65px;
    min-height: 60px;
    font-weight: 200;
    font-size: 32px;
    color: black;
    background: white;

    -webkit-border-radius: 8px;
       -moz-border-radius: 8px;
            border-radius: 3px;

    box-shadow: 0px 0px 5px #333;
    -webkit-box-shadow: 0px 0px 5px #333;
    -moz-box-shadow: 0px 0px 5px #333;
    margin-right: 10px;
}
.countdown-timer-style-2 .countdown-section: first-child {
	margin-left: 0
}
.countdown-timer-style-2 .countdown-section: last-child {
	margin-right: 0
}
.floating-featured-panel-fluid .countdown-timer-style-2 .countdown-amount {
	font-size: 30px
}

.countdown-timer-style-2 .countdown-period {
    font-size: 12px;
    display: block;
    padding: 0;
    margin: 0;
    letter-spacing: 0;
    background: black;
    color: white;
    border-radius: 3px;
    text-transform: none;
}

@media only screen and (max-width: 479px) {
	.countdown-timer-style-2 .countdown-amount {
    	font-size: 32px
    }
}


.countdown-timer-style-3 {
	text-align: center
}
.countdown-timer-style-3 .hasCountdown {
	display: inline-block
}
.countdown-timer-style-3 .countdown_rtl {
	direction: rtl
}
.countdown-timer-style-3 .countdown_row {
	clear: both
}
.countdown-timer-style-3 .countdown-amount {
	position: relative;
	display: inline-block;
	padding: .25em;
	margin: 0 10px;
	min-width: 1.25em;
	line-height: 1em;
	font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
	font-weight: 700;
	font-size: 48px;
	color: #333;
	text-shadow: -1px -1px 0 #fff,1px -1px 0 #fff,-1px 1px 0 #fff,1px 1px 0 #fff;
	background: #f2f2f2;
	background: -moz-linear-gradient(top,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,rgba(255,255,255,1)),color-stop(50%,rgba(234,234,234,1)),color-stop(51%,rgba(242,242,242,1)),color-stop(100%,rgba(250,250,250,1)));
	background: -webkit-linear-gradient(top,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	background: -o-linear-gradient(top,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	background: -ms-linear-gradient(top,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	background: linear-gradient(to bottom,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e5e5e5', GradientType=0);
	-webkit-box-shadow: 0 0 0 1px rgba(255,255,255,.5),inset 0 0 0 1px rgba(0,0,0,.25),inset 0 -3px 0 0 rgba(204,204,204,1),inset 0 -4px 0 0 rgba(179,179,179,1),inset 0 -5px 0 0 rgba(255,255,255,1),inset 0 2px 0 0 rgba(234,234,234,1),inset 0 3px 0 0 rgba(179,179,179,1),inset 0 4px 0 0 rgba(255,255,255,1);
	-moz-box-shadow: 0 0 0 1px rgba(255,255,255,.5),inset 0 0 0 1px rgba(0,0,0,.25),inset 0 -3px 0 0 rgba(204,204,204,1),inset 0 -4px 0 0 rgba(179,179,179,1),inset 0 -5px 0 0 rgba(255,255,255,1),inset 0 2px 0 0 rgba(234,234,234,1),inset 0 3px 0 0 rgba(179,179,179,1),inset 0 4px 0 0 rgba(255,255,255,1);
	box-shadow: 0 0 0 1px rgba(255,255,255,.5),inset 0 0 0 1px rgba(0,0,0,.25),inset 0 -3px 0 0 rgba(204,204,204,1),inset 0 -4px 0 0 rgba(179,179,179,1),inset 0 -5px 0 0 rgba(255,255,255,1),inset 0 2px 0 0 rgba(234,234,234,1),inset 0 3px 0 0 rgba(179,179,179,1),inset 0 4px 0 0 rgba(255,255,255,1);
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	text-transform: uppercase;
}
.countdown-timer-style-3 .countdown-amount: after {
	content: "";
	height: 1px;
	width: 100%;
	background: rgba(255,255,255,.75);
	position: absolute;
	top: 50%;
	left: 0
}
.countdown-timer-style-3 .countdown-section br {
	display: none
}
.countdown-timer-style-3 .countdown-section {
	display: inline-block;
	font-size: 28px;
	font-weight: 700;
	text-align: left;
	text-transform: uppercase;
	vertical-align: center;
	color: inherit;
	text-align: center
}
.countdown-timer-style-3 .countdown-section: first-child {
	margin-left: 0
}
.countdown-timer-style-3 .countdown-section: last-child {
	margin-right: 0
}
.floating-featured-panel-fluid .countdown-timer-style-3 .countdown-amount {
	font-size: 30px
}
.floating-featured-panel-fluid .countdown-timer-style-3 .countdown-section {
	font-size: 20px
}
@media only screen and (min-width: 480px) and (max-width: 767px) {
	.countdown-timer-style-3 .countdown-amount {
    	font-size: 32px
    }
    .countdown-timer-style-3 .countdown-section {
    	font-size: 16px
    }
}

@media only screen and (max-width: 479px) {
	.countdown-timer-style-3 .countdown-amount {
    	display: block;
    	font-size: 32px;
    	margin: 0
    }
    .countdown-timer-style-3 .countdown-section br {
    	display: block
    }
    .countdown-timer-style-3 .countdown-section {
    	margin: 0 5px;
    	font-size: 11px;
    	font-weight: 400;
    	text-transform: uppercase
    }
    .countdown-timer-style-3 .countdown-section: first-child {
    	margin-left: 0
    }
    .countdown-timer-style-3 .countdown-section: last-child {
    	margin-right: 0
    }
}

/*****************************/

.countdown-timer-style-4 .countdown-row {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	gap: 6px;
}

.countdown-timer-style-4 .countdown-row .countdown-section {
	display: flex;
	flex-direction: column;
	gap: 5px;
	align-items: center;
	justify-content: center;
	font-weight: 700;
	color: inherit;
	border-radius: 100%;
	border: 2px solid #59ab4a;
	width: 110px;
	height: 110px;
	text-transform: uppercase;
}

.countdown-timer-style-4 .countdown-row .countdown-amount {
	min-width: 1.25em;
	font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
	font-weight: 700;
	color: #59ab4a;
	border-bottom: 1px solid #59ab4a;
}

/** FOR MOBILE BUILDER DISPLAY */
.mobile .countdown-timer-style-4 .countdown-row .countdown-section {
	width: 70px;
	height: 70px;
}

.mobile .countdown-timer-style-4 .countdown-row .countdown-amount {
	font-weight: normal;
}

.mobile .countdown-timer-style-4 .countdown-row .countdown-period {
	font-size: 9px !important;
	font-weight: normal;
}
/**/

@media (max-width: 767px) {
	.countdown-timer-style-4 .countdown-row .countdown-section {
		width: 100px;
		height: 100px;
	}
}

@media (max-width: 480px) {
	.countdown-timer-style-4 .countdown-row .countdown-section {
		width: 90px;
		height: 90px;
	}
}

@media (max-width: 360px) {
	.countdown-timer-style-4 .countdown-row .countdown-section {
		width: 70px;
		height: 70px;
	}

	.countdown-timer-style-4 .countdown-row .countdown-amount {
		font-weight: normal;
	}

	.countdown-timer-style-4 .countdown-row .countdown-period {
		font-size: 9px !important;
		font-weight: normal;
	}
}

/*****************************/

.countdown-timer-style-5 {
	text-align: center
}
.countdown-timer-style-5 .hasCountdown {
	display: inline-block
}
.countdown-timer-style-5 .countdown_rtl {
	direction: rtl
}
.countdown-timer-style-5 .countdown_row {
	clear: both
}
.countdown-timer-style-5 .countdown-amount {
	position: relative;
	display: inline-block;
    padding: 10px;
    padding-bottom: 5px;
    margin: 0 5px;
    margin-bottom: 5px;
	min-width: 1.25em;
	line-height: 1em;
	font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
	font-weight: 700;
	font-size: 32px;
	color: #444444;
	border-bottom: 1px solid #dedede;
}
.countdown-timer-style-5 .countdown-amount: after {
	content: "";
	height: 1px;
	width: 100%;
	background: rgba(255,255,255,.75);
	position: absolute;
	top: 50%;
	left: 0;
}
.countdown-timer-style-5 .countdown-section br {
	display: none
}
.countdown-timer-style-5 .countdown-section {
	display: inline-block;
	font-size: 28px;
	font-weight: 700;
	text-align: left;
	vertical-align: center;
	color: inherit;
	text-align: center;
    margin: 0 4px;
	text-transform: uppercase;
}
.countdown-timer-style-5 .countdown-section: first-child {
	margin-left: 0
}
.countdown-timer-style-5 .countdown-section: last-child {
	margin-right: 0
}
.floating-featured-panel-fluid .countdown-timer-style-5 .countdown-amount {
	font-size: 30px
}
.floating-featured-panel-fluid .countdown-timer-style-5 .countdown-section {
	font-size: 20px
}
.countdown-timer-style-5 .countdown-period {
    letter-spacing: 0px;
    color: #59ab4a;
    font-weight: normal;
    font-size: 12px;
	color: #59ab4a;
}

@media only screen and (min-width: 480px) and (max-width: 767px) {
	.countdown-timer-style-5 .countdown-amount {
    	font-size: 30px;
    	padding: 15px 15px 5px 15px;
    }
    .countdown-timer-style-5 .countdown-section {
    	font-size: 16px;
    	width: 85px;
    	height: 85px;
    }
    .countdown-timer-style-5 .countdown-period {
        font-size: 12px;
        font-weight: normal;
    }
}

@media only screen and (max-width: 479px) {
	.countdown-timer-style-5 .countdown-amount {
    	display: block;
    	font-size: 24px;
    }
    .countdown-timer-style-5 .countdown-section br {
    	display: block
    }
    .countdown-timer-style-5 .countdown-section {
    	margin: 0 3px;
    	font-size: 11px;
    	font-weight: 400;
    }
    .countdown-timer-style-5 .countdown-section: first-child {
    	margin-left: 0
    }
    .countdown-timer-style-5 .countdown-section: last-child {
    	margin-right: 0
    }
    .countdown-timer-style-5 .countdown-period {
        font-size: 10px;
        font-weight: normal;
    }
}




















.countdown-cookie-timer {
	margin: 0 auto
}
.countdown-cookie-timer-style-1 {
	min-width: 103px;
	max-width: 622px;
	text-align: center;
	white-space: nowrap
}
.countdown-cookie-timer-style-1 .hasCountdown {
	display: inline-block
}
.countdown-cookie-timer-style-1 .countdown_rtl {
	direction: rtl
}
.countdown-cookie-timer-style-1 .countdown_row {
	clear: both
}
.countdown-cookie-timer-style-1 .countdown-amount {
	position: relative;
	display: block;
	padding: .2em 0;
	min-width: 2em;
	line-height: 1em;
	font-family: Impact;
	font-weight: 700;
	font-size: 48px;
	color: #fff;
	text-shadow: -1px -1px 0 #000,1px -1px 0 #000,-1px 1px 0 #000,1px 1px 0 #000;
	background: #191919;
	background: -moz-linear-gradient(top,rgba(25,25,25,1) 0,rgba(31,31,31,1) 50%,rgba(38,38,38,1) 50%,rgba(51,51,51,1) 100%);
	background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,rgba(25,25,25,1)),color-stop(50%,rgba(31,31,31,1)),color-stop(50%,rgba(38,38,38,1)),color-stop(100%,rgba(51,51,51,1)));
	background: -webkit-linear-gradient(top,rgba(25,25,25,1) 0,rgba(31,31,31,1) 50%,rgba(38,38,38,1) 50%,rgba(51,51,51,1) 100%);
	background: -o-linear-gradient(top,rgba(25,25,25,1) 0,rgba(31,31,31,1) 50%,rgba(38,38,38,1) 50%,rgba(51,51,51,1) 100%);
	background: -ms-linear-gradient(top,rgba(25,25,25,1) 0,rgba(31,31,31,1) 50%,rgba(38,38,38,1) 50%,rgba(51,51,51,1) 100%);
	background: linear-gradient(to bottom,rgba(25,25,25,1) 0,rgba(31,31,31,1) 50%,rgba(38,38,38,1) 50%,rgba(51,51,51,1) 100%);
	filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#191919', endColorstr='#333333', GradientType=0);
	-webkit-box-shadow: inset 0 0 0 1px rgba(64,64,64,1),inset 0 -1px 0 0 rgba(255,255,255,.3),inset 0 -2px 0 0 rgba(0,0,0,1),inset 0 -3px 0 0 rgba(255,255,255,.3),inset 0 -4px 0 0 rgba(0,0,0,1),inset 0 -5px 0 0 rgba(255,255,255,.1);
	-moz-box-shadow: inset 0 0 0 1px rgba(64,64,64,1),inset 0 -1px 0 0 rgba(255,255,255,.3),inset 0 -2px 0 0 rgba(0,0,0,1),inset 0 -3px 0 0 rgba(255,255,255,.3),inset 0 -4px 0 0 rgba(0,0,0,1),inset 0 -5px 0 0 rgba(255,255,255,.1);
	box-shadow: inset 0 0 0 1px rgba(64,64,64,1),inset 0 -1px 0 0 rgba(255,255,255,.3),inset 0 -2px 0 0 rgba(0,0,0,1),inset 0 -3px 0 0 rgba(255,255,255,.3),inset 0 -4px 0 0 rgba(0,0,0,1),inset 0 -5px 0 0 rgba(255,255,255,.1);
	border: 3px solid #000;
	-webkit-border-radius: 8px;
	-moz-border-radius: 8px;
	border-radius: 8px
}
.countdown-cookie-timer-style-1 .countdown-amount: after {
	content: "";
	height: 1px;
	width: 100%;
	background: #000;
	position: absolute;
	top: 50%;
	left: 0;
	border-top: 1px solid rgba(255,255,255,.175);
	border-bottom: 1px solid rgba(255,255,255,.175)
}
.countdown-cookie-timer-style-1 .countdown-section br {
	content: "";
	display: block;
	margin: 0;
	margin-top: 8px
}
.countdown-cookie-timer-style-1 .countdown-section {
	float: left;
	display: block;
	margin: 0 1px;
	font-size: 11px;
	font-weight: 400;
	text-align: center;
	text-transform: uppercase;
	color: inherit
}
.countdown-cookie-timer-style-1 .countdown-section: first-child {
	margin-left: 0
}
.countdown-cookie-timer-style-1 .countdown-section: last-child {
	margin-right: 0
}
@media only screen and (max-width: 479px) {
	.countdown-cookie-timer-style-1 .countdown-amount {
    	font-size: 32px
    }
}

.countdown-cookie-timer-style-2 {
	min-width: 85px;
	max-width: 587px;
	text-align: center;
	white-space: nowrap
}
.countdown-cookie-timer-style-2 .hasCountdown {
	display: inline-block
}
.countdown-cookie-timer-style-2 .countdown_rtl {
	direction: rtl
}
.countdown-cookie-timer-style-2 .countdown_row {
	clear: both
}
.countdown-cookie-timer-style-2 .countdown-amount {
	position: relative;
	display: block;
	padding: .25em;
	min-width: 1.25em;
	line-height: 1em;
	font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
	font-weight: 700;
	font-size: 48px;
	color: #333;
	text-shadow: -1px -1px 0 #fff,1px -1px 0 #fff,-1px 1px 0 #fff,1px 1px 0 #fff;
	background: #f2f2f2;
	background: -moz-linear-gradient(top,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,rgba(255,255,255,1)),color-stop(50%,rgba(234,234,234,1)),color-stop(51%,rgba(242,242,242,1)),color-stop(100%,rgba(250,250,250,1)));
	background: -webkit-linear-gradient(top,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	background: -o-linear-gradient(top,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	background: -ms-linear-gradient(top,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	background: linear-gradient(to bottom,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e5e5e5', GradientType=0);
	-webkit-box-shadow: 0 0 0 1px rgba(255,255,255,.5),inset 0 0 0 1px rgba(0,0,0,.25),inset 0 -3px 0 0 rgba(204,204,204,1),inset 0 -4px 0 0 rgba(179,179,179,1),inset 0 -5px 0 0 rgba(255,255,255,1),inset 0 2px 0 0 rgba(234,234,234,1),inset 0 3px 0 0 rgba(179,179,179,1),inset 0 4px 0 0 rgba(255,255,255,1);
	-moz-box-shadow: 0 0 0 1px rgba(255,255,255,.5),inset 0 0 0 1px rgba(0,0,0,.25),inset 0 -3px 0 0 rgba(204,204,204,1),inset 0 -4px 0 0 rgba(179,179,179,1),inset 0 -5px 0 0 rgba(255,255,255,1),inset 0 2px 0 0 rgba(234,234,234,1),inset 0 3px 0 0 rgba(179,179,179,1),inset 0 4px 0 0 rgba(255,255,255,1);
	box-shadow: 0 0 0 1px rgba(255,255,255,.5),inset 0 0 0 1px rgba(0,0,0,.25),inset 0 -3px 0 0 rgba(204,204,204,1),inset 0 -4px 0 0 rgba(179,179,179,1),inset 0 -5px 0 0 rgba(255,255,255,1),inset 0 2px 0 0 rgba(234,234,234,1),inset 0 3px 0 0 rgba(179,179,179,1),inset 0 4px 0 0 rgba(255,255,255,1);
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px
}
.countdown-cookie-timer-style-2 .countdown-amount: after {
	content: "";
	height: 1px;
	width: 100%;
	background: rgba(255,255,255,.75);
	position: absolute;
	top: 50%;
	left: 0
}
.countdown-cookie-timer-style-2 .countdown-section br {
	content: "";
	display: block;
	margin: 0;
	margin-top: 8px
}
.countdown-cookie-timer-style-2 .countdown-section {
	float: left;
	display: block;
	margin: 0 5px;
	font-size: 11px;
	font-weight: 400;
	text-align: center;
	text-transform: uppercase;
	color: inherit
}
.countdown-cookie-timer-style-2 .countdown-section: first-child {
	margin-left: 0
}
.countdown-cookie-timer-style-2 .countdown-section: last-child {
	margin-right: 0
}
@media only screen and (max-width: 479px) {
	.countdown-cookie-timer-style-2 .countdown-amount {
    	font-size: 32px
    }
}

.countdown-cookie-timer-style-3 {
	text-align: center;
	white-space: nowrap
}
.countdown-cookie-timer-style-3 .hasCountdown {
	display: inline-block
}
.countdown-cookie-timer-style-3 .countdown_rtl {
	direction: rtl
}
.countdown-cookie-timer-style-3 .countdown_row {
	clear: both
}
.countdown-cookie-timer-style-3 .countdown-amount {
	position: relative;
	display: inline-block;
	padding: .25em;
	margin: 0 10px;
	min-width: 1.25em;
	line-height: 1em;
	font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
	font-weight: 700;
	font-size: 48px;
	color: #333;
	text-shadow: -1px -1px 0 #fff,1px -1px 0 #fff,-1px 1px 0 #fff,1px 1px 0 #fff;
	background: #f2f2f2;
	background: -moz-linear-gradient(top,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,rgba(255,255,255,1)),color-stop(50%,rgba(234,234,234,1)),color-stop(51%,rgba(242,242,242,1)),color-stop(100%,rgba(250,250,250,1)));
	background: -webkit-linear-gradient(top,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	background: -o-linear-gradient(top,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	background: -ms-linear-gradient(top,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	background: linear-gradient(to bottom,rgba(255,255,255,1) 0,rgba(234,234,234,1) 50%,rgba(242,242,242,1) 51%,rgba(250,250,250,1) 100%);
	filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e5e5e5', GradientType=0);
	-webkit-box-shadow: 0 0 0 1px rgba(255,255,255,.5),inset 0 0 0 1px rgba(0,0,0,.25),inset 0 -3px 0 0 rgba(204,204,204,1),inset 0 -4px 0 0 rgba(179,179,179,1),inset 0 -5px 0 0 rgba(255,255,255,1),inset 0 2px 0 0 rgba(234,234,234,1),inset 0 3px 0 0 rgba(179,179,179,1),inset 0 4px 0 0 rgba(255,255,255,1);
	-moz-box-shadow: 0 0 0 1px rgba(255,255,255,.5),inset 0 0 0 1px rgba(0,0,0,.25),inset 0 -3px 0 0 rgba(204,204,204,1),inset 0 -4px 0 0 rgba(179,179,179,1),inset 0 -5px 0 0 rgba(255,255,255,1),inset 0 2px 0 0 rgba(234,234,234,1),inset 0 3px 0 0 rgba(179,179,179,1),inset 0 4px 0 0 rgba(255,255,255,1);
	box-shadow: 0 0 0 1px rgba(255,255,255,.5),inset 0 0 0 1px rgba(0,0,0,.25),inset 0 -3px 0 0 rgba(204,204,204,1),inset 0 -4px 0 0 rgba(179,179,179,1),inset 0 -5px 0 0 rgba(255,255,255,1),inset 0 2px 0 0 rgba(234,234,234,1),inset 0 3px 0 0 rgba(179,179,179,1),inset 0 4px 0 0 rgba(255,255,255,1);
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px
}
.countdown-cookie-timer-style-3 .countdown-amount: after {
	content: "";
	height: 1px;
	width: 100%;
	background: rgba(255,255,255,.75);
	position: absolute;
	top: 50%;
	left: 0
}
.countdown-cookie-timer-style-3 .countdown-section br {
	display: none
}
.countdown-cookie-timer-style-3 .countdown-section {
	float: left;
	display: inline-block;
	font-size: 28px;
	font-weight: 700;
	text-align: center;
	text-transform: lowercase;
	vertical-align: center;
	color: inherit
}
.countdown-cookie-timer-style-3 .countdown-section: first-child {
	margin-left: 0
}
.countdown-cookie-timer-style-3 .countdown-section: last-child {
	margin-right: 0
}
@media only screen and (min-width: 480px) and (max-width: 767px) {
	.countdown-cookie-timer-style-3 .countdown-amount {
    	font-size: 32px
    }
    .countdown-cookie-timer-style-3 .countdown-section {
    	font-size: 16px
    }
}

@media only screen and (max-width: 479px) {
	.countdown-cookie-timer-style-3 .countdown-amount {
    	display: block;
    	font-size: 32px;
    	margin: 0
    }
    .countdown-cookie-timer-style-3 .countdown-section br {
    	display: block
    }
    .countdown-cookie-timer-style-3 .countdown-section {
    	display: block;
    	margin: 0 5px;
    	font-size: 11px;
    	font-weight: 400;
    	text-transform: uppercase
    }
    .countdown-cookie-timer-style-3 .countdown-section: first-child {
    	margin-left: 0
    }
    .countdown-cookie-timer-style-3 .countdown-section: last-child {
    	margin-right: 0
    }
}

.countdown-period {
    font-size: 14px;
    display: block;
    letter-spacing: 1px;
}



@media (max-width:767px) {
    .countdown-period {
        font-size: 11px !important;
    }
    .countdown-timer-style-1 .countdown-amount {
        min-width: 1.8em !important;
    }

    .countdown-timer-style-2 .countdown-section {
        min-width: 55px !important;
    }
    .countdown-timer-style-2 .countdown-period {
        font-size: 11px !important;
    }
}
