# <?php echo __('Introduction'); ?>


<?php echo __('L\'API v2.0 de LearnyBox vous permet de récupérer des informations provenant de votre compte (formations, membres, transactions, etc.) mais aussi d\'inscrire des membres dans vos formations, de créer des transactions, etc.'); ?>


<?php echo __('La colonne de droite vous affiche un exemple de retour de la requête à l\'API de LearnyBox.'); ?>


<div class="alert alert-danger">
    <?php echo __('L\'API de LearnyBox est limitée à 20000 appels par 24h par compte, vos appels seront bloqués au delà.'); ?>
</div>


<div class="alert alert-info">
    <?php echo __('L\'ancienne API v1 est toujours disponible mais ne sera plus maintenue.'); ?>
    <a href="/app/api/"><?php echo __('Voir la documentation de l\'API v1'); ?></a>
</div>


# <?php echo __('Client API PHP'); ?>

<?php echo __('Le client API LearnyBox pour PHP fournit un accès pratique à l\'API LearnyBox à partir d\'applications écrites en langage PHP.'); ?>


<?php echo __('Il inclut un ensemble prédéfini de classes pour les ressources d\'API qui s\'initialisent de manière dynamique à partir des réponses d\'API, ce qui le rend compatible avec un large éventail de versions de l\'API LearnyBox.'); ?>


[<?php echo __('Client PHP disponible sur Packagist'); ?>](https://packagist.org/packages/learnybox/learnybox-client-php)


# <?php echo __('Authentification'); ?>


<?php echo __('Pour vous connecter à l\'API de LearnyBox, vous avez besoin de 2 éléments'); ?> :


- **<?php echo __('Votre adresse LearnyBox'); ?>** : https://{votre-sous-domaine}.learnybox.com/
- **<?php echo __('Clé API'); ?>** : <?php echo __('en tant qu\'administrateur de votre compte LearnyBox, vous pouvez accéder à votre clé API principale (générée automatiquement), regénérer votre clé et créer des clés API restreintes'); ?> [<?php echo __('ici'); ?>](/app/api/keys/).


## <?php echo __('Générer un token'); ?>


<?php echo __('Après avoir récupéré votre clé, vous pouvez exécuter une requête pour obtenir un **bearer** token en utilisant l\'opération'); ?>


[<?php echo __('Grant access token'); ?>](#operation/api_v2_oauth_token).


<?php echo __('Cette opération requiert les paramètres suivants'); ?> :
- [<?php echo __('ajouter le header'); ?> custom **X-API-Key**](#section/Authentification/apiKeyAuth) : `X-API-Key: {votre_api_key}`
- [<?php echo __('ajouter le paramètre'); ?> POST **grant_type**](#operation/api_v2_oauth_token) : <?php echo __('ce paramètre permet de choisir quel est le type de token demandé. La valeur du paramètre sera %s', '"**access_token**"'); ?>.

<?php echo __('Le token est valable pendant 24 heures après sa génération.'); ?>


## <?php echo __('Utiliser un token'); ?>


<?php echo __('Pour authentifier vos requêtes API, vous devez ajouter à vos requêtes un bearer token valide dans le header HTTP.'); ?>


<?php echo __('Toutes les requêtes effectuées doivent contenir un token valide.'); ?>


`Authorization: Bearer {bearer_token}`


## <?php echo __('Expiration ou invalidité du token'); ?>


<?php echo __('Toute requête exécutée avec un token invalide ou expiré renvoie le code status HTTP suivant'); ?> : **HTTP/1.0 498 Token expired/invalid**


<?php echo __('Le résultat est le suivant'); ?> :


```
{
    "status": false,
    "message": "Invalid or expired token"
}
```

## <?php echo __('Regénérer un access token à partir du refresh token'); ?>


<?php echo __('Vous pouvez regénérer un access token quand celui-ci est expiré avec le refresh token en utilisant l\'opération'); ?> [Grant access token](#operation/api_v2_oauth_token).


<?php echo __('Cette opération requiert les paramètres suivants'); ?> :
- [<?php echo __('ajouter le header'); ?> custom **X-API-Key**](#section/Authentification/apiKeyAuth) : `X-API-Key: {votre_api_key}`
- [<?php echo __('ajouter le paramètre'); ?> POST **grant_type**](#operation/api_v2_oauth_token) : <?php echo __('ce paramètre permet de choisir quel est le type de token demandé.'); ?> <?php echo __('La valeur du paramètre sera %s', '"**refresh_token**"'); ?>.

<?php echo __('Le refresh token est valable pendant 1 mois après sa génération.'); ?>


# <?php echo __('Utiliser la pagination'); ?>


<?php echo __('LearnyBox API fournit des paramètres de pagination pour les listes de données.'); ?>


<?php echo __('Vous pouvez utiliser les paramètres'); ?> :


<?php echo __('`?limit` pour définir le nombre maximum de résultats de la réponse (par défaut 100 résultats maximum).'); ?>


<?php echo __('`?offset` pour spécifier le numéro de classement du premier élément de la page (par défaut 0).'); ?>


<?php echo __('Voici un exemple de retour d\'une requête pour une liste de données avec les informations de pagination'); ?> :
```
{
    "status": true,
    "data": [
        {
          Course data
        }
    ],
    "message": "",
    "offset": 4,
    "limit": 1,
    "total": 9,
    "_links": {
        "first": {
            "rel": "first",
            "href": "https://api.learnybox.com/api/v2/formations/?limit=1&offset=0"
        },
        "previous": {
            "rel": "previous",
            "href": "https://api.learnybox.com/api/v2/formations/?limit=1&offset=3"
        },
        "self": {
            "rel": "self",
            "href": "https://api.learnybox.com/api/v2/formations/?limit=1&offset=4"
        },
        "next": {
            "rel": "next",
            "href": "https://api.learnybox.com/api/v2/formations/?limit=1&offset=5"
        },
        "last": {
            "rel": "last",
            "href": "https://api.learnybox.com/api/v2/formations/?limit=1&offset=8"
        }
    }
}
```
