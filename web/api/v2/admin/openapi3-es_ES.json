{"openapi": "3.0.0", "info": {"title": "Learnybox API ADMIN", "version": "2.0", "description": "# Introducción\n\nLa API LearnyBox v2.0 te permite recuperar información de tu cuenta (cursos, miembros, transacciones, etc.) pero también registrar miembros en tus cursos, crear transacciones, etc.\n\nLa columna de la derecha le muestra un ejemplo de cómo se devuelve la solicitud a la API LearnyBox.\n\n<div class=\"alert alert-danger\">\n    La API LearnyBox está limitada a 20.000 llamadas por 24 horas por cuenta, sus llamadas serán bloqueadas más allá de eso.</div>\n\n\n<div class=\"alert alert-info\">\n    La antigua API v1 sigue disponible, pero ya no se mantendrá.    <a href=\"/app/api/\">Consulte la documentación de la API v1</a>\n</div>\n\n\n# Cliente API PHP\nEl Cliente de la API LearnyBox para PHP brinda un cómodo acceso a la API LearnyBox desde aplicaciones escritas en PHP.\n\nIncluye un conjunto predefinido de clases para los recursos de la API que se inicializan dinámicamente a partir de las respuestas de la API, por lo que es compatible con una amplia gama de versiones de la API LearnyBox.\n\n[Cliente PHP disponible en Packagist](https://packagist.org/packages/learnybox/learnybox-client-php)\n\n\n# Autenticación\n\nPara conectarte a la API LearnyBox, necesitas 2 cosas :\n\n\n- **Su dirección LearnyBox** : https://{votre-sous-domaine}.learnybox.com/\n- **Clave API** : como administrador de tu cuenta LearnyBox, puedes acceder a tu clave API primaria (generada automáticamente), regenerar tu clave y crear claves API restringidas [aquí](/app/api/keys/).\n\n\n## Generar un token\n\nDespués de recuperar su clave, puede ejecutar una consulta para obtener un token **bearer** utilizando la función\n\n[Conceder token de acceso](#operation/api_v2_oauth_token).\n\n\nEsta operación requiere los siguientes parámetros :\n- [añadir cabecera custom **X-API-Key**](#section/Authentification/apiKeyAuth) : `X-API-Key: {votre_api_key}`\n- [añadir el parámetro POST **grant_type**](#operation/api_v2_oauth_token) : este parámetro permite elegir qué tipo de token se solicita. El valor del parámetro será \"**access_token**\".\n\nLe token est valable pendant 24 heures après sa génération.\n\n## Utilizar una ficha\n\nPara autenticar sus solicitudes de API, debe añadir un token de portador válido a sus solicitudes en el encabezado HTTP.\n\nTodas las solicitudes realizadas deben contener un token válido.\n\n`Authorization: Bearer {bearer_token}`\n\n\n## Caducidad o invalidez del token\n\nCualquier solicitud ejecutada con un token no válido o caducado devuelve el siguiente código de estado HTTP : **HTTP/1.0 498 Token expired/invalid**\n\n\nEl resultado es el siguiente :\n\n\n```\n{\n    \"status\": false,\n    \"message\": \"Invalid or expired token\"\n}\n```\n\n## Regenerar un token de acceso a partir del token de actualización\n\nPuede regenerar un token de acceso cuando haya caducado con el token de actualización utilizando la función [Grant access token](#operation/api_v2_oauth_token).\n\n\nEsta operación requiere los siguientes parámetros :\n- [añadir cabecera custom **X-API-Key**](#section/Authentification/apiKeyAuth) : `X-API-Key: {votre_api_key}`\n- [añadir el parámetro POST **grant_type**](#operation/api_v2_oauth_token) : este parámetro permite elegir qué tipo de token se solicita. El valor del parámetro será \"**refresh_token**\".\n\nLe refresh token est valable pendant 1 mois après sa génération.\n\n# Uso de la paginación\n\nLa API LearnyBox proporciona parámetros de paginación para las listas de datos.\n\nPuede utilizar la función :\n\n\ntambién puede utilizar la opción `?limit` para establecer el número máximo de resultados de la respuesta (por defecto, 100 resultados como máximo).\n\n?offset` para especificar el número de offset del primer elemento de la página (por defecto es 0).\n\nA continuación se muestra un ejemplo de devolución de consulta para una lista de datos con información de paginación :\n```\n{\n    \"status\": true,\n    \"data\": [\n        {\n          Course data\n        }\n    ],\n    \"message\": \"\",\n    \"offset\": 4,\n    \"limit\": 1,\n    \"total\": 9,\n    \"_links\": {\n        \"first\": {\n            \"rel\": \"first\",\n            \"href\": \"https://api.learnybox.com/api/v2/formations/?limit=1&offset=0\"\n        },\n        \"previous\": {\n            \"rel\": \"previous\",\n            \"href\": \"https://api.learnybox.com/api/v2/formations/?limit=1&offset=3\"\n        },\n        \"self\": {\n            \"rel\": \"self\",\n            \"href\": \"https://api.learnybox.com/api/v2/formations/?limit=1&offset=4\"\n        },\n        \"next\": {\n            \"rel\": \"next\",\n            \"href\": \"https://api.learnybox.com/api/v2/formations/?limit=1&offset=5\"\n        },\n        \"last\": {\n            \"rel\": \"last\",\n            \"href\": \"https://api.learnybox.com/api/v2/formations/?limit=1&offset=8\"\n        }\n    }\n}\n```\n"}, "servers": [{"url": "/", "description": ""}], "paths": {"/api/v2/activities/": {"get": {"tags": ["Actividades"], "summary": "Lista de actividades", "operationId": "api_v2_activities_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id": 1, "viewed": false, "transaction": [], "type": "transaction"}, {"id": 2, "viewed": false, "comment": [], "type": "comment"}, {"id": 3, "viewed": false, "page_comment": [], "type": "page_comment"}, {"id": 4, "viewed": false, "formation_comment": [], "type": "formation_comment"}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/activities/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/activities/{id}/": {"get": {"tags": ["Actividades"], "summary": "Datos de la actividad", "operationId": "api_v2_activities_show", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id": 1, "viewed": false, "transaction": [], "type": "transaction"}}}}}}}}, "/api/v2/comment_activities/": {"get": {"tags": ["Actividades"], "summary": "Lista de actividades de tipo comentario", "operationId": "api_v2_activities_comments_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id": 2, "viewed": false, "comment": [], "type": "comment"}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/comment_activities/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/comment_activities/{id}/": {"get": {"tags": ["Actividades"], "summary": "Datos de una actividad de tipo comentario", "operationId": "api_v2_activities_comments_show", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id": 2, "viewed": false, "comment": [], "type": "comment"}}}}}}}}, "/api/v2/formation_comment_activities/": {"get": {"tags": ["Actividades"], "summary": "Lista de actividades de tipo formativo comentario", "operationId": "api_v2_activities_formation_comments_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id": 2, "viewed": false, "formation_comment": [], "type": "formation_comment"}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/formation_comment_activities/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/formation_comment_activities/{id}/": {"get": {"tags": ["Actividades"], "summary": "Datos de un comentario sobre una actividad de tipo formativo", "operationId": "api_v2_activities_formation_comments_show", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id": 2, "viewed": false, "formation_comment": [], "type": "formation_comment"}}}}}}}}, "/api/v2/page_comment_activities/": {"get": {"tags": ["Actividades"], "summary": "Lista de actividades de la página de comentarios", "operationId": "api_v2_activities_page_comments_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id": 2, "viewed": false, "page_comment": [], "type": "page_comment"}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/page_comment_activities/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/page_comment_activities/{id}/": {"get": {"tags": ["Actividades"], "summary": "Datos de una actividad de la página de comentarios", "operationId": "api_v2_activities_page_comments_show", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id": 2, "viewed": false, "page_comment": [], "type": "page_comment"}}}}}}}}, "/api/v2/transaction_activities/": {"get": {"tags": ["Actividades"], "summary": "Lista de actividades de tipo transacción", "operationId": "api_v2_activities_transactions_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id": 2, "viewed": false, "transaction": [], "type": "transaction"}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/transaction_activities/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/transaction_activities/{id}/": {"get": {"tags": ["Actividades"], "summary": "Datos de una actividad de tipo transacción", "operationId": "api_v2_activities_transactions_show", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id": 2, "viewed": false, "transaction": [], "type": "transaction"}}}}}}}}, "/api/v2/affilies/": {"get": {"tags": ["Afiliaciones"], "summary": "Lista de afiliados", "operationId": "api_v2_affiliations_affilies_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_affilie": 1, "nom": "Learny", "prenom": "Box1", "email": "<EMAIL>", "parent": 0, "commentaire": "", "approved": true, "date": "2019-03-18T16:49:05+0100"}, {"id_affilie": 2, "nom": "Learny", "prenom": "Box1", "email": "<EMAIL>", "parent": 0, "commentaire": "", "approved": true, "date": "2019-03-18T17:00:39+0100"}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/affilies/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/affilies/{id_affilie}/": {"get": {"tags": ["Afiliaciones"], "summary": "Datos de afiliación", "operationId": "api_v2_affiliations_affilies_show", "parameters": [{"name": "id_affilie", "in": "path", "description": "ID de afiliado", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_affilie": 1, "nom": "Learny", "prenom": "Box1", "email": "<EMAIL>", "parent": 0, "commentaire": "", "approved": true, "date": "2019-03-18T16:49:05+0100"}, "message": ""}}}}}}, "delete": {"tags": ["Afiliaciones"], "summary": "Borrar un afiliado", "operationId": "api_v2_affiliations_affilies_delete", "parameters": [{"name": "id_affilie", "in": "path", "description": "ID de afiliado", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "patch": {"tags": ["Afiliaciones"], "summary": "Activar - Desactivar un afiliado", "operationId": "api_v2_affiliations_affilies_activate", "parameters": [{"name": "id_affilie", "in": "path", "description": "ID de afiliado", "required": true, "schema": {"type": "integer"}}], "requestBody": {"description": "De<PERSON> rellenarse al menos uno de los siguientes campos *", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"activate": {"description": "Activar o desactivar un afiliado", "type": "boolean", "enum": ["true", "false"]}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/affilies/{id_affilie}/{action}/": {"get": {"tags": ["Afiliaciones"], "summary": "Datos y acciones para un afiliado", "operationId": "api_v2_affiliations_affilies_show_actions", "parameters": [{"name": "id_affilie", "in": "path", "description": "ID de afiliado", "required": true, "schema": {"type": "integer"}}, {"name": "action", "in": "path", "description": "", "required": true, "schema": {"type": "string", "enum": ["stats", "commissions", "links", "reinit_password", "clients"]}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/affiliations/": {"get": {"tags": ["Afiliaciones"], "summary": "Lista de campañas de su programa de afiliación", "operationId": "api_v2_affiliations_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_campaign": 1, "nom": "Formation LearnyBox", "image": "https://api.learnybox.com/images/ma-formation/13.jpeg", "description": "<p>Gagnez 30% de commission sur chaque vente !</p>\\r\\n", "type_commission": "pourcentage", "montant_commission": 30, "type_montant": "ht", "type_commission2": "pourcentage", "montant_commission2": 10, "type_montant2": "ht", "tva": 20, "tva2015": true, "cookie": 0, "duree_garantie": 10, "active": true, "type": "publique", "affilies": "11", "position": 12, "permalink": "5880d8", "date": "2017-03-14T14:46:34+0100"}, {"id_campaign": 2, "nom": "Formation LearnyBox 2", "image": "https://api.learnybox.com/images/ma-formation/13.jpeg", "description": "<p>Gagnez 30% de commission sur chaque vente !</p>\\r\\n", "type_commission": "pourcentage", "montant_commission": 30, "type_montant": "ht", "type_commission2": "pourcentage", "montant_commission2": 10, "type_montant2": "ht", "tva": 20, "tva2015": true, "cookie": 0, "duree_garantie": 10, "active": true, "type": "publique", "affilies": "11", "position": 12, "permalink": "5880d8", "date": "2017-03-14T14:46:34+0100"}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/affiliations/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/affiliations/{id_campaign}/": {"get": {"tags": ["Afiliaciones"], "summary": "Datos de una campaña de su programa de afiliación", "operationId": "api_v2_affiliations_show", "parameters": [{"name": "id_campaign", "in": "path", "description": "ID de campaña", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_campaign": 1, "nom": "Formation LearnyBox", "image": "https://api.learnybox.com/images/ma-formation/13.jpeg", "description": "<p>Gagnez 30% de commission sur chaque vente !</p>\\r\\n", "type_commission": "pourcentage", "montant_commission": 30, "type_montant": "ht", "type_commission2": "pourcentage", "montant_commission2": 10, "type_montant2": "ht", "tva": 20, "tva2015": true, "cookie": 0, "duree_garantie": 10, "active": true, "type": "publique", "affilies": "11", "position": 12, "permalink": "5880d8", "date": "2017-03-14T14:46:34+0100"}, "message": ""}}}}}}, "delete": {"tags": ["Afiliaciones"], "summary": "Eliminar una campaña de su programa de afiliación", "operationId": "api_v2_affiliations_delete", "parameters": [{"name": "id_campaign", "in": "path", "description": "ID de campaña", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "patch": {"tags": ["Afiliaciones"], "summary": "Activar - Desactivar una campaña en su programa de afiliación", "operationId": "api_v2_affiliations_activate", "parameters": [{"name": "id_campaign", "in": "path", "description": "ID de campaña", "required": true, "schema": {"type": "integer"}}], "requestBody": {"description": "De<PERSON> rellenarse al menos uno de los siguientes campos *", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"activate": {"description": "Activar o desactivar la campaña", "type": "boolean", "enum": ["true", "false"]}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/affiliations/{id_campaign}/stats/": {"get": {"tags": ["Afiliaciones"], "summary": "Estadísticas de afiliación de una campaña", "operationId": "api_v2_affiliations_stats", "parameters": [{"name": "id_campaign", "in": "path", "description": "ID de campaña", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/commissions/": {"get": {"tags": ["Afiliaciones"], "summary": "Lista de comisiones", "operationId": "api_v2_commissions_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_commission": 1, "type": "normal", "produit": "test produit", "montant": 89.21, "commission": 20.32, "etat": "valid", "date": "2019-02-20T16:37:40+0100"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/commissions/?limit=100&offset=0", "rel": "self"}}}}}}}}, "post": {"tags": ["Afiliaciones"], "summary": "Añadir una comisión", "operationId": "api_v2_commissions_create", "requestBody": {"description": "Los campos id_campaña, id_afiliado, importe e importe_comisión son obligatorios *", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"id_campaign": {"description": "ID de campaña", "type": "integer"}, "id_affilie": {"description": "ID de afiliado", "type": "string"}, "montant": {"description": "Importe de la venta", "type": "string"}, "montant_commission": {"description": "Importe de lacomisión", "type": "string"}, "id_transaction": {"description": "ID de transacción", "type": "string"}, "produit": {"description": "Nombre del producto", "type": "string"}, "ip": {"description": "Dirección IP del cliente", "type": "string"}, "date": {"description": "Fecha de la comisión (en formato AAAA-MM-DD HH:MM:SS)", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}, "x-code-samples": [{"lang": "Form data", "source": "id_campaign:1&id_affilie:1&montant:99.99&montant_commission:30&id_transaction:TEST0001&produit:test+produit&date:2019-02-20+16:37:40"}]}}, "/api/v2/commissions/{id_comission}/": {"get": {"tags": ["Afiliaciones"], "summary": "Datos de la Comisión", "operationId": "api_v2_commissions_show", "parameters": [{"name": "id_comission", "in": "path", "description": "Comité ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_commission": 1, "type": "normal", "produit": "test produit", "montant": 89.21, "commission": 20.32, "etat": "valid", "date": "2019-02-20T16:37:40+0100"}, "message": ""}}}}}}}, "/api/v2/oauth/token/": {"post": {"tags": ["Token de autenticación"], "summary": "Generar un token de acceso/refresco", "operationId": "api_v2_oauth_token", "requestBody": {"description": "Campo Grant_type obligatorio *. Clave Api obligatoria para un token de acceso", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"grant_type": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string", "enum": ["access_token", "refresh_token"]}, "refresh_token": {"description": "valor refresh_token en caso de regeneración de una ficha de acceso", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"access_token": "XXXXXXXXX", "refresh_token": "YYYYYYYYY", "token_type": "Bearer", "expires_in": "00"}], "message": ""}}}}}, "security": [{"apiKeyAuth": []}]}}, "/api/v2/oauth/token/revoke/": {"post": {"tags": ["Token de autenticación"], "summary": "Revocar un token de acceso (y el token de actualización asociado)", "operationId": "api_v2_oauth_token_revoke", "requestBody": {"description": "Campo access_token obligatorio *. Clave API necesaria para eliminar un token de acceso", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"access_token": {"description": "ficha de acceso que debe revocarse", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}, "security": [{"apiKeyAuth": []}]}}, "/api/v2/conferences/": {"get": {"tags": ["Seminarios web en directo"], "summary": "Liste des webinaires live", "operationId": "api_v2_conferences_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_conference": 1, "nom": "test webinaire live", "date": "2019-03-14T14:00:00+0100"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/conferences/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/conferences/{id_conference}/": {"get": {"tags": ["Seminarios web en directo"], "summary": "Données d'un webinaire live", "operationId": "api_v2_conferences_show", "parameters": [{"name": "id_conference", "in": "path", "description": "Identifiant d'un webinaire live", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_conference": 1, "nom": "test webinaire live", "type_conf": "tokbox", "accroche": "", "description": "", "nb_coaches": 0, "nb_auditeurs": 9999, "is_coache_payant": false, "coache_productid": 0, "is_auditeur_payant": false, "auditeur_productid": 0, "image_inscription": "", "afficher_participants": false, "delta_participants": 0, "type": "privee", "type_youtube": "privee", "acces_conference": "inscription", "type_inscription": "automatique", "email_inscription": false, "page_confirmation": "defaut", "url_page_confirmation": "", "theme_inscription": "1-1-1", "theme_confirmation": "1-1-19", "theme_attente": "", "theme_broadcast": "", "thumbnail": "", "email_rappel": false, "chat": "question", "youtube": "", "hangout_link": "", "replay": false, "replay_auto": false, "replay_desactivation": 0, "replay_redirection": "", "replay_inscription": false, "date": "2019-03-14T14:00:00+0100", "date_timezone": "2019-03-14T14:00:00+0100", "timezone": "Europe/Paris", "date_diffusion": "-0001-11-30T00:00:00+0009", "date_terminee": "-0001-11-30T00:00:00+0009", "etat": "enattente", "inscription_link": "https://example.com/conference/inscription/test-webi-live/", "inscription_js": "", "inscription_js_head": "", "confirmation_js": "", "confirmation_js_head": "", "broadcast_js": "", "broadcast_js_head": "", "broadcast": "", "layout": "", "rtmp": "", "rgpd": true, "rgpd_text": "En indiquant votre adresse mail, vous acceptez que nous vous invitions par mail à ce webinaire live et que nous vous adressions des offres personnalisées de formations. Vous pouvez vous désinscrire à tout moment en nous adressant un mail et à travers les liens de désinscription.", "rgpd_aff": false, "rgpd_aff_text": "En indiquant votre adresse mail, vous acceptez en échange de recevoir des offres commerciales de nos partenaires.", "client": {"nom_client": "Learny Box"}, "conference_inscrits": []}, "message": ""}}}}}}}, "/api/v2/conferences/{id_conference}/chat/": {"get": {"tags": ["Seminarios web en directo"], "summary": "Chat d'un webinaire live", "operationId": "api_v2_conferences_chat_list", "parameters": [{"name": "id_conference", "in": "path", "description": "Identifiant d'un webinaire live", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_chat": 1, "prenom": "Learny", "email": "<EMAIL>", "message": "salut ! ", "pinned": true, "date": "2019-03-14T14:15:15+0100"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/conferences/1/chat/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/conferences/{id_conference}/inscrits/": {"get": {"tags": ["Seminarios web en directo"], "summary": "Liste des inscrits à un webinaire live", "operationId": "api_v2_conferences_inscrits_list", "parameters": [{"name": "id_conference", "in": "path", "description": "Identifiant d'un webinaire live", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_inscrit": 1, "type": "participant", "enattente": false, "replay": false, "cpg": "", "aff": "", "date_inscr": "2018-01-17T16:28:07+0100", "date_replay": "-0001-11-30T00:00:00+0009", "conference_user": {"id_user": 1, "prenom": "Learny", "email": "<EMAIL>", "telephone": "", "pays": "", "disabled": false, "newsletter": true, "date_inscr": "2017-08-30T07:58:43+0200", "rgpd": false, "rgpd_aff": false, "conference_inscrits": [], "custom_fields": {"Quelle est votre profession ?": "Conférencier", "Comment avez-vous connu LearnyBox ?": "Internet"}}}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/conferences/1/inscrits/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/conferences/{id_conference}/inscrits/{id_inscrit}/": {"get": {"tags": ["Seminarios web en directo"], "summary": "Données d'un inscrit à un webinaire live", "operationId": "api_v2_conferences_inscrits_show", "parameters": [{"name": "id_conference", "in": "path", "description": "Identifiant d'un webinaire live", "required": true, "schema": {"type": "integer"}}, {"name": "id_inscrit", "in": "path", "description": "Identificador de un solicitante de registro", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_inscrit": 1, "type": "participant", "enattente": false, "replay": false, "cpg": "", "aff": "", "date_inscr": "2018-01-17T16:28:07+0100", "date_replay": "-0001-11-30T00:00:00+0009", "conference_user": {"id_user": 1, "prenom": "Learny", "email": "<EMAIL>", "telephone": "", "pays": "", "disabled": false, "newsletter": true, "date_inscr": "2017-08-30T07:58:43+0200", "rgpd": false, "rgpd_aff": false, "conference_inscrits": [], "custom_fields": {"Quelle est votre profession ?": "Conférencier", "Comment avez-vous connu LearnyBox ?": "Internet"}}}, "message": ""}}}}}}}, "/api/v2/conferences/{id_conference}/messages/": {"get": {"tags": ["Seminarios web en directo"], "summary": "Liste des messages d'un webinaire live", "operationId": "api_v2_conferences_messages_list", "parameters": [{"name": "id_conference", "in": "path", "description": "Identifiant d'un webinaire live", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_message": 1, "message": "test message", "date": "2019-04-30T16:08:08+0200"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/conferences/1/messages/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/conferences/{id_conference}/participants/": {"get": {"tags": ["Seminarios web en directo"], "summary": "Liste des participants d'un webinaire live", "operationId": "api_v2_conferences_participants_list", "parameters": [{"name": "id_conference", "in": "path", "description": "Identifiant d'un webinaire live", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_participant": 1, "pseudo": "Learny", "date": "2018-01-17T16:28:07+0100", "delta": "2018-01-17T16:42:12+0100", "conference_user": {"id_user": 1, "prenom": "Learny", "email": "<EMAIL>", "telephone": "", "pays": "", "disabled": false, "newsletter": true, "date_inscr": "2017-08-30T07:58:43+0200", "rgpd": false, "rgpd_aff": false, "conference_inscrits": []}}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/conferences/1/participants/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/conferences/{id_conference}/participants/{id_participant}/": {"get": {"tags": ["Seminarios web en directo"], "summary": "Données d'un participant a un webinaire live", "operationId": "api_v2_conferences_participants_show", "parameters": [{"name": "id_conference", "in": "path", "description": "Identifiant d'un webinaire live", "required": true, "schema": {"type": "integer"}}, {"name": "id_participant", "in": "path", "description": "ID de participante", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_participant": 1, "pseudo": "Learny", "date": "2018-01-17T16:28:07+0100", "delta": "2018-01-17T16:42:12+0100", "total_time": 845, "conference_user": {"id_user": 1, "prenom": "Learny", "email": "<EMAIL>", "telephone": "", "pays": "", "disabled": false, "newsletter": true, "date_inscr": "2017-08-30T07:58:43+0200", "rgpd": false, "rgpd_aff": false, "conference_inscrits": []}, "inscrit": {"id_inscrit": 12, "type": "participant", "enattente": false, "replay": true, "cpg": "", "aff": "", "date_inscr": "2017-08-30T07:58:43+0200", "date_replay": "2017-09-02T09:12:34+0200", "conference": {"id_conference": 1, "nom": "Conférence de découverte de LearnyBox"}, "waiting_queue": {"id": 1, "created_at": "2017-08-30T20:04:43+0200", "unlocked_at": "2017-08-30T20:06:12+0200"}}}, "message": ""}}}}}}}, "/api/v2/conferences/user/{id_user}/": {"get": {"tags": ["Seminarios web en directo"], "summary": "Liste des webinaires live d'un utilisateur", "operationId": "api_v2_conferences_user_list", "parameters": [{"name": "id_user", "in": "path", "description": "Identifiant d'un utilisateur", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_user": 1, "prenom": "Learny", "email": "<EMAIL>", "telephone": "", "pays": "", "region": "", "disabled": false, "newsletter": true, "rgpd": true, "rgpd_aff": false, "date_inscr": "2018-01-17T16:28:07+0100", "md5_email": "", "conference_inscrits": {"id_inscrit": 12, "type": "participant", "enattente": false, "replay": false, "cpg": "", "aff": "", "date_inscr": "2017-08-30T07:58:43+0200", "date_replay": "", "conference": {"id_conference": 161, "nom": "Conférence de découverte de LearnyBox"}, "waiting_queue": {"id": 1, "created_at": "2017-08-30T20:04:43+0200", "unlocked_at": "2017-08-30T20:06:12+0200"}}}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/conferences/user/1/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/evaluation/user/{user_id}/": {"get": {"tags": ["Evaluación"], "summary": "Liste des évaluations d'un utilisateur", "operationId": "api_v2_evaluation_user", "parameters": [{"name": "user_id", "in": "path", "description": "Identifiant d'un utilisateur", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_participant": 39, "etat": "termine", "ip": "127.0.0.1", "disabled": false, "nb_times": 2, "date": "2024-02-19T13:35:51+01:00", "evaluation": {"id_evaluation": 31, "nom": "Evaluation test"}}, {"id_participant": 38, "etat": "1", "ip": "127.0.0.1", "disabled": false, "nb_times": 0, "date": "2024-02-01T15:48:25+01:00", "evaluation": {"id_evaluation": 32, "nom": "Evaluation test 2"}}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"rel": "self", "href": "https://api.learnybox.com/api/v2/evaluation/user/1/?limit=100&offset=0"}}}}}}}}}, "/api/v2/evaluation/{evaluation_id}/user/{user_id}/": {"get": {"tags": ["Evaluación"], "summary": "Liste des réponses à une évaluation pour un utilisateur", "operationId": "api_v2_evaluation_id_user", "parameters": [{"name": "evaluation_id", "in": "path", "description": "Identifiant d'une évaluation", "required": true, "schema": {"type": "integer"}}, {"name": "user_id", "in": "path", "description": "Identifiant d'un utilisateur", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_participant": 39, "etat": "termine", "ip": "::1", "disabled": false, "nb_times": 2, "date": "2018-02-19T13:35:51+01:00", "evaluation": {"id_evaluation": 31, "nom": "Evaluation test"}, "reponses": [{"id_question": 1, "title": "Titre de la question", "type": "input_checkbox", "reponses": [{"title": "Titre de la réponse", "correct": false}, {"title": "Titre de la réponse 2", "correct": true}]}], "calculs": [{"id_calcul": 1, "nom": "Nom du calcul", "userPoints": 10, "maxPoints": 20, "ratio": 50}]}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"rel": "self", "href": "https://api.learnybox.com/api/v2/evaluation/31/user/1/?limit=100&offset=0"}}}}}}}}}, "/api/v2/formations/": {"get": {"tags": ["Cursos de formación"], "summary": "Lista de cursos", "operationId": "api_v2_formations_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_formation": "103", "nom": "Formation LearnyBox", "type": "freeaccess_total", "etat": "enable", "lien": "https://api.learnybox.com/formation/index/", "membres": "10", "date": "2017-01-16 16:35:22"}, {"id_formation": "101", "nom": "Ma première formation", "type": "program", "etat": "enable", "lien": "https://api.learnybox.com/formation/index/", "membres": "2", "date": "2016-11-23 14:33:17"}], "message": ""}}}}}}}, "/api/v2/formations/{id_formation}/": {"get": {"tags": ["Cursos de formación"], "summary": "Données d'une formation", "operationId": "api_v2_formations_show", "parameters": [{"name": "id_formation", "in": "path", "description": "Identificador de un curso", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_formation": 103, "nom": "Formation LearnyBox", "type": "freeaccess_total", "etat": "enable", "lien": "https://api.learnybox.com/formation/index/", "membres": 10, "date": "2017-01-16 16:35:22", "description": "", "image": "", "prix": 0, "devise": "EUR", "acces_duree": 0, "acces_unite": "jours", "modules_count": 0, "lecons_count": 0}, "message": ""}}}}}}}, "/api/v2/formations/{id_formation}/commentaires/": {"get": {"tags": ["Cursos de formación"], "summary": "Lista de comentarios sobre un curso", "operationId": "api_v2_formations_commentaires_list", "parameters": [{"name": "id_formation", "in": "path", "description": "Identificador de un curso", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"display_etat": "Approu<PERSON><PERSON>", "id_commentaire": 1, "idmodule": 1, "commentaire": "nouveau commentaire test", "etat": "approuve", "notification": true, "telephone": "0987654321", "adresse": "25 rue de Learnybox 34000 Montpellier", "site": "https://api.learnybox.com", "date": "2019-04-23T14:14:15+0200"}, {"id_commentaire": 2, "idmodule": 0, "parent": 1, "commentaire": "réponse du premier commentaire", "etat": "approuve", "notification": false, "telephone": "", "adresse": "", "site": "", "date": "2019-05-14T10:52:09+0200"}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"rel": "self", "href": "https://api.learnybox.com/api/v2/formations/1/commentaires/?limit=100&offset=0"}}}}}}}}, "post": {"tags": ["Cursos de formación"], "summary": "Añadir un nuevo comentario a un curso", "operationId": "api_v2_formations_commentaires_create", "parameters": [{"name": "id_formation", "in": "path", "description": "Identificador de un curso", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"idelement": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "idmodule": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "idpage": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "message": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "site": {"description": "URL del sitio", "type": "string"}, "adresse": {"description": "Dirección", "type": "string"}, "telephone": {"description": "Teléfono", "type": "integer"}, "orig_comment": {"description": "0 para no mostrar el comentario original / 1 para mostrar el comentario original", "type": "boolean"}, "notification": {"description": "0 para no notificar / 1 para notificar", "type": "boolean"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}, "x-code-samples": [{"lang": "Form data", "source": "idelement=1&idmodule=1&idpage=1&message=nouveau+commentaire+test&site=https://api.learnybox.com&adresse=25 rue de Learnybox 34000 Montpellier&telephone=0987654321&orig_comment=0&notification=0"}]}}, "/api/v2/formations/{id_formation}/commentaires/{id_commentaire}/": {"get": {"tags": ["Cursos de formación"], "summary": "Datos de un comentario de formación", "operationId": "api_v2_formations_commentaires_show", "parameters": [{"name": "id_formation", "in": "path", "description": "Identificador de un curso", "required": true, "schema": {"type": "integer"}}, {"name": "id_commentaire", "in": "path", "description": "Identificador de un comentario de formación", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"display_etat": "Approu<PERSON><PERSON>", "id_commentaire": 1, "idmodule": 1, "commentaire": "nouveau commentaire test", "etat": "approuve", "notification": true, "telephone": "0987654321", "adresse": "25 rue de Learnybox 34000 Montpellier", "site": "https://api.learnybox.com", "date": "2019-04-23T14:14:15+0200"}, "message": ""}}}}}}, "patch": {"tags": ["Cursos de formación"], "summary": "Actualizar un comentario de curso", "operationId": "api_v2_formations_commentaires_edit", "parameters": [{"name": "id_formation", "in": "path", "description": "Identificador de un curso", "required": true, "schema": {"type": "integer"}}, {"name": "id_commentaire", "in": "path", "description": "Identificador de comentarios", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"message": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "site": {"description": "URL del sitio", "type": "string"}, "adresse": {"description": "Dirección", "type": "string"}, "telephone": {"description": "Teléfono", "type": "integer"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/formations/commentaires/{id_commentaire}/": {"delete": {"tags": ["Cursos de formación"], "summary": "Eliminar un comentario", "operationId": "api_v2_site_formations_commentaires_delete", "parameters": [{"name": "id_commentaire", "in": "path", "description": "Identificador de comentarios", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/formations/{idformation}/commentaires/reponse/": {"post": {"tags": ["Cursos de formación"], "summary": "Añadir una respuesta a un comentario sobre un curso", "operationId": "api_v2_formations_commentaires_response_create", "parameters": [{"name": "idformation", "in": "path", "description": "Identificador de un curso", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"user_id": {"description": "ID de usuario", "type": "integer"}, "idmodule": {"description": "Identificador del módulo", "type": "integer"}, "idpage": {"description": "ID de página", "type": "integer"}, "orig_comment": {"description": "Identificador del comentario original", "type": "integer"}, "message": {"description": "Men<PERSON><PERSON>", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/formations/{id_formation}/groupes/": {"get": {"tags": ["Cursos de formación"], "summary": "Lista de grupos de un curso", "operationId": "api_v2_formations_groupes_list", "parameters": [{"name": "id_formation", "in": "path", "description": "ID de formación", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_groupe": "1", "nom": "grouptest", "modules": "[1,2]", "pages": "[1,2,3]", "date": "2019-04-23 14:24:38", "nb_membres": "10"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/formations/1/groupes/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/formations/{id_formation}/membres/": {"get": {"tags": ["Cursos de formación"], "summary": "Lista de todos los miembros de un grupo", "operationId": "api_v2_formations_membres_list", "parameters": [{"name": "id_formation", "in": "path", "description": "ID de formación", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"user_id": 1, "idgroupe": "0", "active": true, "datecreation": "2019-04-23T14:05:42+0200", "datemodification": "2019-04-23T14:05:42+0200", "dateinscription": "2019-04-23T14:05:42+0200", "user": {"user_id": 1, "validated": 1, "contact_technique": 0, "fname": "Learny 1", "lname": "Box", "email": "<EMAIL>", "newsletter": true, "rgpd": false, "rgpd_aff": false, "user_configs": []}}, {"user_id": 2, "idgroupe": "0", "active": true, "datecreation": "2019-04-23T14:05:42+0200", "datemodification": "2019-04-23T14:05:42+0200", "dateinscription": "2019-04-23T14:05:42+0200", "user": {"user_id": 2, "validated": 1, "contact_technique": 0, "fname": "Learny 2", "lname": "Box", "email": "<EMAIL>", "newsletter": false, "rgpd": false, "rgpd_aff": false, "user_configs": []}}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/formations/1/membres/?limit=100&offset=0", "rel": "self"}}}}}}}}, "post": {"tags": ["Cursos de formación"], "summary": "Añadir un miembro a un curso", "operationId": "api_v2_formations_membres_create", "parameters": [{"name": "id_formation", "in": "path", "description": "ID de formación", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"prenom": {"description": "Nombre del participante", "type": "string"}, "nom": {"description": "Apellido del participante", "type": "string"}, "email": {"description": "Dirección de correo electrónico del participante", "type": "string"}, "groupes": {"description": "ID de grupo separados por comas", "type": "string"}, "adresse": {"description": "Dirección del miembro", "type": "string"}, "ville": {"description": "Ciudad del miembro", "type": "string"}, "pays": {"description": "País del diputado", "type": "string"}, "code_postal": {"description": "Código postal del afiliado", "type": "string"}, "tel": {"description": "Número de teléfono del afiliado", "type": "string"}, "rgpd": {"description": "0 para no cualificado RGPD / 1 para cualificado RGPD", "type": "boolean"}, "rgpd_date": {"description": "Fecha en formato AAAA-MM-DD de la cualificación RGPD del miembro (obligatorio si rgpd = 1)", "type": "string"}, "rgpd_notice": {"description": "Texto de la autorización", "type": "string"}, "rgpd_aff": {"description": "0 para socios RGPD no cualificados / 1 para socios RGPD cualificados", "type": "boolean"}, "rgpd_aff_date": {"description": "Fecha en formato AAAA-MM-DD de la cualificación de socio del RGPD (obligatorio si rgpd_aff = 1)", "type": "string"}, "rgpd_aff_notice": {"description": "Texto de la autorización", "type": "string"}, "no_mail": {"description": "1 pour ne pas envoyer l'email de bienvenue", "type": "boolean"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"data": {"status": true, "message": "Le membre a été enregistré avec succès.", "id_membre": 1234}}}}}}}}, "/api/v2/formations/{id_formation}/membres/progression/": {"get": {"tags": ["Cursos de formación"], "summary": "Recuperar el progreso de los miembros de un grupo", "operationId": "api_v2_formations_membres_progression", "parameters": [{"name": "id_formation", "in": "path", "description": "ID de formación", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"nom": "my formation", "link": "https://my-formation.learnybox.com", "etat": "enable", "type": "program", "theme": "focuson", "datecreation": "2019-04-23 14:05:42", "membres": [{"id_membre": "1", "prenom": "Learny", "nom": "Box", "email": "<EMAIL>", "groupes": "1", "progression": "0%"}]}, "message": ""}}}}}}}, "/api/v2/formations/{id_formation}/membres/{id_membre}/": {"get": {"tags": ["Cursos de formación"], "summary": "Datos de un miembro de un grupo", "operationId": "api_v2_formations_membres_show", "parameters": [{"name": "id_formation", "in": "path", "description": "ID de formación", "required": true, "schema": {"type": "integer"}}, {"name": "id_membre", "in": "path", "description": "ID de miembro", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_membre": "1", "prenom": "Learny", "nom": "Box", "email": "<EMAIL>", "pseudo": "", "adresse": "", "code_postal": "", "ville": "", "pays": "FR", "tel": "", "formations": [{"id_formation": "1", "nom": "my formation", "groupes": "1", "datecreation": "2019-04-23 14:22:30", "type": "program", "acces": "active"}], "coachs": [{"user_id": 1, "prenom": "Test", "nom": "Coach", "email": "<EMAIL>"}]}, "message": ""}}}}}}, "delete": {"tags": ["Cursos de formación"], "summary": "Eliminar un miembro de un grupo", "operationId": "api_v2_formations_membres_delete", "parameters": [{"name": "id_formation", "in": "path", "description": "ID de formación", "required": true, "schema": {"type": "integer"}}, {"name": "id_membre", "in": "path", "description": "ID de miembro", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "patch": {"tags": ["Cursos de formación"], "summary": "Editar un miembro de un grupo", "operationId": "api_v2_formations_membres_edit", "parameters": [{"name": "id_formation", "in": "path", "description": "ID de formación", "required": true, "schema": {"type": "integer"}}, {"name": "id_membre", "in": "path", "description": "ID de miembro", "required": true, "schema": {"type": "integer"}}], "requestBody": {"description": "De<PERSON> rellenarse al menos uno de los siguientes campos *", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"nom": {"description": "Nuevo nombre del miembro", "type": "string"}, "prenom": {"description": "Nuevo nombre del diputado", "type": "string"}, "new_email": {"description": "Dirección de correo electrónico del nuevo miembro", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/formations/{id_formation}/membres/{id_membre}/messages/": {"get": {"tags": ["Cursos de formación"], "summary": "Mensajes de un miembro para la formación", "operationId": "api_v2_formations_membres_messages_show", "parameters": [{"name": "id_formation", "in": "path", "description": "ID de formación", "required": true, "schema": {"type": "integer"}}, {"name": "id_membre", "in": "path", "description": "ID de miembro", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_message": 1, "destinataires": "", "nom": "Learny", "prenom": "Box", "email": "<EMAIL>", "type": "test message", "priorite": "<PERSON><PERSON><PERSON>", "message": "<p>test content message&nbsp;</p>\\r\\n", "attachments": "[]", "parent": 0, "admin": true, "lu": false, "lu_admin": true, "date": "2019-03-13T15:37:32+0100"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/formations/1/membres/1/messages/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/formations/{id_formation}/membres/{id_membre}/coachings/": {"get": {"tags": ["Cursos de formación"], "summary": "Coaching de un miembro para formación", "operationId": "api_v2_formations_membres_coachings_show", "parameters": [{"name": "id_formation", "in": "path", "description": "ID de formación", "required": true, "schema": {"type": "integer"}}, {"name": "id_membre", "in": "path", "description": "ID de miembro", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"idformation": 1, "idmodule": 0, "idpage": 0, "label_expediteur": "", "sujet": "Mon document", "message": "voici mon document, merci de me dire ce que vous en pensez.", "type": "<PERSON><PERSON><PERSON>", "date": "2017-11-14T10:38:54+0100", "etat": "repondu", "date_reponse": "2017-01-17T13:56:26+0100", "reponse": "<p><PERSON><PERSON><PERSON>,</p>\\r\\n\\r\\n<p>voici ma correction.</p>\\r\\n", "lu": true}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/formations/1/membres/1/coachings/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/formations/membres/{id_membre}/connections/": {"get": {"tags": ["Cursos de formación"], "summary": "Conexiones de los miembros", "operationId": "api_v2_formations_membres_connections_show", "parameters": [{"name": "id_membre", "in": "path", "description": "ID de miembro", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"ip": "123.456.789.100", "date": "2021-07-30 16:23:55"}, {"ip": "123.456.789.100", "date": "2021-07-27 12:45:11"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/formations/membres/1/connections/", "rel": "self"}}}}}}}}}, "/api/v2/formations/membres/{id_membre}/": {"get": {"tags": ["Cursos de formación"], "summary": "Datos de los miembros", "operationId": "api_v2_formations_by_member_show", "parameters": [{"name": "id_membre", "in": "path", "description": "ID de miembro", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"1": {"formation": {"idformation": 1, "nomformation": "first formation", "uniqid": "first-formation-1", "etat": "enable", "type": "freeaccess_total", "version": 2, "duree_validite": null, "date_validite": null, "date_start": "-0001-11-30T00:00:00-0456", "day_registration": null, "sommaire_all_elements": true, "nb_learning_days": 0, "logo": "https://api.learnybox.com/assets/images/tunnels/logo-exemple.png", "banniere": "", "favicon": null, "theme": "focuson", "id_page_accueil": 0, "hide_logo": false, "thumbnail": "", "femailname": null, "datecreation": "2019-09-17T16:50:48-0400", "pages_count": 9, "modules_count": 2, "users_count": 5, "non_admin_users_count": 2}, "progression": 100, "total_time": 721, "date_inscription": "09/10/2019"}}, "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"rel": "self", "href": "https://api.learnybox.com/api/v2/formations/membres/1/?limit=100&offset=0"}}}}}}}}}, "/api/v2/formations/{id_formation}/membres/{id_membre}/progression/": {"get": {"tags": ["Cursos de formación"], "summary": "Progresión de un miembro para un curso", "operationId": "api_v2_progression_by_formation_member_show", "parameters": [{"name": "id_formation", "in": "path", "description": "Identificador de un curso", "required": true, "schema": {"type": "integer"}}, {"name": "id_membre", "in": "path", "description": "ID de miembro", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"progression": 65, "temps": "0h 12min 1s", "time": "721", "nb_modules": 5, "nb_modules_termines": 4, "nb_pages": 20, "nb_pages_terminees": 13, "details": [{"title": "Module 1", "moduleId": 1, "duree": 120, "total_time_module": 70, "pages": [{"id": 2, "title": "Introduction", "status": 1, "details": {"06 Février 2024 à 10h12": "0h 0min 14s", "06 Février 2024 à 10h56": "0h 0min 56s", "total": "0h 1min 10s"}, "details_time": {"2024-02-06 10:12:00": 14, "2024-02-06 10:56:55": 56, "total": 70}}]}]}, "message": ""}}}}}}}, "/api/v2/formations/{id_formation}/messages/": {"get": {"tags": ["Cursos de formación"], "summary": "Lista de mensajes enviados desde la plataforma de formación", "operationId": "api_v2_formations_messages_list", "parameters": [{"name": "id_formation", "in": "path", "description": "ID de formación", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_message": 1, "destinataires": "", "nom": "Learny", "prenom": "Box", "email": "<EMAIL>", "type": "test message", "priorite": "<PERSON><PERSON><PERSON>", "message": "<p>test content message&nbsp;</p>\\r\\n", "attachments": "[]", "parent": 0, "admin": true, "lu": false, "lu_admin": true, "date": "2019-03-13T15:37:32+0100"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/formations/1/messages/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/formations/{id_formation}/messages/{id_message}/": {"delete": {"tags": ["Cursos de formación"], "summary": "Borrar un mensaje publicado desde la plataforma de formación", "operationId": "api_v2_formations_messages_delete", "parameters": [{"name": "id_formation", "in": "path", "description": "ID de formación", "required": true, "schema": {"type": "integer"}}, {"name": "id_message", "in": "path", "description": "Identificador del mensaje", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/formations/{id_formation}/modules/": {"get": {"tags": ["Cursos de formación"], "summary": "Lista de módulos principales (primer nivel) de un curso", "operationId": "api_v2_formations_modules_list", "parameters": [{"name": "id_formation", "in": "path", "description": "ID de formación", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"idmodule": 1, "nommodule": "Module 1", "description": "description module 1", "vignette": "", "difficulte": 0, "duree": 0, "delay": 0, "date_start": "-0001-11-30T00:00:00+0009", "parent": 0, "position": 2, "notification": false, "etat": "publie", "datecreation": "2019-04-23T14:05:43+0200", "datemodification": "2019-04-23T12:05:43+0200"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/formations/1/messages/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/mail/blogbroadcasts/": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Lista de boletines", "operationId": "api_v2_mail_blogbroadcasts_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_blog_broadcast": 1, "nom": 1, "feed_url": "https://api.learnybox.com/feed/", "id_mail": "1", "jour_envoi": "['lundi']", "heure_envoi": "10", "timezone": "Europe/Paris", "nb_article": "1", "sequences": "[]", "exclude_sequences": "[]", "tags": "[]", "exclude_tags": "[]", "nb_envoi": "0", "nb_opens": "0", "etat": "active", "date_creation": "2019-01-01T12:00:00+0100"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/mail/blogbroadcasts/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/mail/contacts/": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Lista de contactos", "operationId": "api_v2_mail_contacts_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_contact": "1", "prenom": "Learny", "nom": "Box", "email": "<EMAIL>", "adresse": "", "code_postal": "", "ville": "", "pays": "", "tel": "", "etat": "subscribed", "date_inscription": "2019-03-15 12:01:06", "date_desinscription": ""}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/mail/contacts/?limit=100&offset=0", "rel": "self"}}}}}}}}, "post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Registrar un nuevo contacto", "operationId": "api_v2_mail_contacts_create", "requestBody": {"description": "Campos nombre, apellidos y correo electrónico obligatorios *", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"nom": {"description": "Nombre del contacto", "type": "string"}, "prenom": {"description": "Nombre de contacto", "type": "string"}, "email": {"description": "Correo electrónico de contacto", "type": "string"}, "id_sequence": {"description": "Identificador de una secuencia", "type": "string"}, "tags": {"description": "Identificadores de etiquetas separados por comas", "type": "string"}, "adresse": {"description": "Dirección de contacto", "type": "string"}, "ville": {"description": "Ciudad de contacto", "type": "string"}, "pays": {"description": "<PERSON><PERSON>", "type": "string"}, "code_postal": {"description": "Código postal del contacto", "type": "string"}, "tel": {"description": "Teléfono de contacto", "type": "string"}, "rgpd": {"description": "0 para no cualificado RGPD / 1 para cualificado RGPD", "type": "boolean"}, "rgpd_notice": {"description": "Texto de la autorización", "type": "string"}, "rgpd_aff": {"description": "0 para socios RGPD no cualificados / 1 para socios RGPD cualificados", "type": "boolean"}, "rgpd_date": {"description": "Fecha en formato AAAA-MM-DD de la cualificación RGPD del contacto o de la cualificación de socio RGPD del contacto (obligatorio si rgpd = 1 o rgpd_aff = 1)", "type": "string"}, "rgpd_aff_notice": {"description": "Texto de la autorización", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}, "x-code-samples": [{"lang": "Form data", "source": "id_campaign:1&id_affilie:1&montant:99.99&montant_commission:30&id_transaction:TEST0001&produit:test+produit&date:2019-02-20+16:37:40"}]}, "patch": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Actualizar un contacto", "operationId": "api_v2_mail_contacts_edit", "requestBody": {"description": "Fields contact_id, email(if no contact id) required *", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"id_contact": {"description": "Contacto ID", "type": "integer"}, "email": {"description": "Dirección de correo electrónico de contacto (obligatoria si no hay ID de contacto)", "type": "string"}, "nom": {"description": "Nuevo nombre de contacto", "type": "string"}, "prenom": {"description": "Nuevo nombre del contacto", "type": "string"}, "new_email": {"description": "Nueva dirección de correo electrónico de contacto", "type": "string"}, "rgpd": {"description": "0 para no cualificado RGPD / 1 para cualificado RGPD", "type": "boolean"}, "rgpd_notice": {"description": "Texto de la autorización", "type": "string"}, "rgpd_aff": {"description": "0 para socios RGPD no cualificados / 1 para socios RGPD cualificados", "type": "boolean"}, "rgpd_date": {"description": "Fecha en formato AAAA-MM-DD de la cualificación RGPD del contacto o de la cualificación de socio RGPD del contacto (obligatorio si rgpd = 1 o rgpd_aff = 1)", "type": "string"}, "rgpd_aff_notice": {"description": "Texto de la autorización", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/mail/contacts/{id_contact}/": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Datos de un contacto", "operationId": "api_v2_mail_contacts_show", "parameters": [{"name": "id_contact", "in": "path", "description": "Contacto ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_contact": "1", "prenom": "Learny", "nom": "Box", "email": "<EMAIL>", "adresse": "", "code_postal": "", "ville": "", "pays": "", "tel": "", "mobile": "", "etat": "subscribed", "rgpd": "0", "date_inscription": "2019-03-15 12:01:06", "sequences": [], "tags": [], "custom_fields": {"tel": "", "adresse": "", "ville": "", "code_postal": "", "pays": "", "tva": "", "siret": "", "paypal_account": "0", "site_web": "", "affilie_id_client": "1"}}, "message": ""}}}}}}, "delete": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Suprimir un contacto", "operationId": "api_v2_mail_contacts_delete", "parameters": [{"name": "id_contact", "in": "path", "description": "Contacto ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/mail/contacts/{id_contact}/{action}/": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Añadir una etiqueta o secuencia a un contacto", "operationId": "api_v2_mail_contacts_add_action", "parameters": [{"name": "id_contact", "in": "path", "description": "Contacto ID", "required": true, "schema": {"type": "integer"}}, {"name": "action", "in": "path", "description": "etiqueta o secuencia", "required": true, "schema": {"type": "string", "enum": ["tag", "sequence"]}}], "requestBody": {"description": "Campos de identificación obligatorios *", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"id": {"description": "Identificador de una etiqueta o secuencia", "type": "integer"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "delete": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Désinscrire d'une séquence ou retirer un tag d'un contact", "operationId": "api_v2_mail_contacts_remove_action", "parameters": [{"name": "id_contact", "in": "path", "description": "Contacto ID", "required": true, "schema": {"type": "integer"}}, {"name": "action", "in": "path", "description": "etiqueta o secuencia", "required": true, "schema": {"type": "string", "enum": ["tag", "sequence"]}}], "requestBody": {"description": "Campos de identificación obligatorios *", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"id": {"description": "Identificador de una etiqueta o secuencia", "type": "integer"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/mail/contacts/{id_contact}/mails/": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Emails d'un contact LearnyMail", "operationId": "api_v2_mail_contacts_mails", "parameters": [{"name": "id_contact", "in": "path", "description": "Contacto ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_user_mail": 1, "open": true, "date_envoi": "2019-04-23T12:22:30+0200", "date_ouverture": "2019-04-23T15:11:23+0200", "mail": {"id_mail": 12, "type": "sequence", "subtype": "", "sujet": "Sujet de l'email", "titre": "", "from_name": "LearnyBox", "from_email": "<EMAIL>", "replyto_email": ""}}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"rel": "self", "href": "https://api.learnybox.com/api/v2/mail/contacts/1/mails/?limit=100&offset=0"}}}}}}}}}, "/api/v2/mail/envois/": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Lista de envíos", "operationId": "api_v2_mail_envois_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_envoi": "1", "id_sms": "0", "sequences": "[1,2,3]", "exclude_sequences": "[]", "tags": "[]", "exclude_tags": "[]", "rgpd": "", "etat": "processed", "nb_envois": "0", "nb_opens": "0", "nb_bounces": "0", "nb_complaints": "0", "date": "2017-01-28 07:00:00", "timezone": "Europe/Paris", "date_timezone": "2017-01-28 07:00:00", "date_envoi": "2017-01-28 07:00:00", "mail": "Mail inconnu", "destinataires": "3 séquences<br>"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/mail/envois/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/mail/envois/{id_envoi}/": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Datos de un envío", "operationId": "api_v2_mail_envois_show", "parameters": [{"name": "id_envoi", "in": "path", "description": "ID de envío", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_envoi": "1", "id_sms": "0", "sequences": "{1,2,3}", "exclude_sequences": "{}", "tags": "{}", "exclude_tags": "{}", "rgpd": "", "etat": "processed", "nb_envois": "0", "nb_opens": "0", "nb_bounces": "0", "nb_complaints": "0", "date": "2017-01-28 07:00:00", "timezone": "Europe/Paris", "date_timezone": "2017-01-28 07:00:00", "date_envoi": "2017-01-28 07:00:00", "mail": "Mail inconnu", "destinataires": "3 séquences<br>"}, "message": ""}}}}}}}, "/api/v2/mail/groupes/": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Lista de grupos", "operationId": "api_v2_mail_groupes_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_groupe": 1, "nom": "abc", "description": "", "date": "2017-11-27T13:07:00+0100", "date_modification": "2017-11-28T13:07:00+0100", "client": {"nom_client": "Learny Box"}}, {"id_groupe": 2, "nom": "clic", "description": "", "date": "2017-12-08T10:53:28+0100", "date_modification": "2017-12-09T10:53:28+0100", "client": {"nom_client": "Learny Box"}}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/mail/groupes/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/mail/groupes/{id_groupe}/": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Datos del grupo", "operationId": "api_v2_mail_groupes_show", "parameters": [{"name": "id_groupe", "in": "path", "description": "ID de grupo", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_groupe": 1, "nom": "abc", "description": "", "date": "2017-11-27T13:07:00+0100", "date_modification": "2017-11-28T13:07:00+0100", "client": {"nom_client": "Learny Box"}}, "message": ""}}}}}}}, "/api/v2/mail/sequences/": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Lista de secuencias", "operationId": "api_v2_mail_sequences_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_sequence": 1, "version": 1, "nom": "Découvrez mon cadeau gratuit", "description": "", "langue": "francais", "optin": "simple", "from_name": "Learnybox", "from_email": "<EMAIL>", "reply_to": "<EMAIL>", "signature": "", "sujet_inscription": "", "mail_inscription": "", "sujet_desinscription": "", "mail_desinscription": "", "reoptin": "", "active": true, "in_process": false, "date_in_process": ""}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/mail/sequences/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/mail/sequences/{id_sequence}/": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Datos de secuencia", "operationId": "api_v2_mail_sequences_show", "parameters": [{"name": "id_sequence", "in": "path", "description": "Identificador de secuencia", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_sequence": 1, "version": 1, "nom": "Découvrez mon cadeau gratuit", "description": "", "langue": "francais", "optin": "simple", "from_name": "Learnybox", "from_email": "<EMAIL>", "reply_to": "<EMAIL>", "signature": "", "sujet_inscription": "", "mail_inscription": "", "sujet_desinscription": "", "mail_desinscription": "", "reoptin": "", "active": true, "in_process": false, "date_in_process": ""}, "message": ""}}}}}}}, "/api/v2/mail/tags/": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Lista de etiquetas", "operationId": "api_v2_mail_tags_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_tag": 1, "nom": "abc", "description": "", "date": "2017-11-27T13:07:00+0100", "date_modification": "2017-11-28T13:07:00+0100", "client": {"nom_client": "Learny Box"}}, {"id_tag": 2, "nom": "clic", "description": "", "date": "2017-12-08T10:53:28+0100", "date_modification": "2017-12-09T10:53:28+0100", "client": {"nom_client": "Learny Box"}}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/mail/tags/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/mail/tags/{id_tag}/": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Datos de una etiqueta", "operationId": "api_v2_mail_tags_show", "parameters": [{"name": "id_tag", "in": "path", "description": "Identificador de etiqueta", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_tag": 1, "nom": "abc", "description": "", "date": "2017-11-27T13:07:00+0100", "date_modification": "2017-11-28T13:07:00+0100", "client": {"nom_client": "Learny Box"}}, "message": ""}}}}}}}, "/api/v2/quiz/user/{user_id}/": {"get": {"tags": ["Cuestionario"], "summary": "Liste des quiz d'un utilisateur", "operationId": "api_v2_quiz_user", "parameters": [{"name": "user_id", "in": "path", "description": "Identifiant d'un utilisateur", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_participant": 39, "etat": "termine", "ip": "127.0.0.1", "disabled": false, "nb_times": 2, "date": "2024-02-19T13:35:51+01:00", "quiz": {"id_quiz": 31, "nom": "Quiz test"}}, {"id_participant": 38, "etat": "1", "ip": "127.0.0.1", "disabled": false, "nb_times": 0, "date": "2024-02-01T15:48:25+01:00", "quiz": {"id_quiz": 32, "nom": "Quiz test 2"}}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"rel": "self", "href": "https://api.learnybox.com/api/v2/quiz/user/1/?limit=100&offset=0"}}}}}}}}}, "/api/v2/quiz/{quiz_id}/user/{user_id}/": {"get": {"tags": ["Cuestionario"], "summary": "Liste des réponses à un quiz pour un utilisateur", "operationId": "api_v2_quiz_id_user", "parameters": [{"name": "quiz_id", "in": "path", "description": "Identifiant d'un quiz", "required": true, "schema": {"type": "integer"}}, {"name": "user_id", "in": "path", "description": "Identifiant d'un utilisateur", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_participant": 39, "etat": "termine", "ip": "::1", "disabled": false, "nb_times": 2, "date": "2018-02-19T13:35:51+01:00", "quiz": {"id_quiz": 31, "nom": "Quiz test"}, "reponses": [{"id_question": 1, "title": "Titre de la question", "type": "input_checkbox", "reponses": [{"title": "Titre de la réponse", "correct": 0}, {"title": "Titre de la réponse 2", "correct": 1}]}]}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"rel": "self", "href": "https://api.learnybox.com/api/v2/quiz/31/user/1/?limit=100&offset=0"}}}}}}}}}, "/api/v2/quota/": {"get": {"tags": ["General"], "summary": "Recuperar el número de llamadas a la API en las últimas 24 horas", "operationId": "api_v2_quota", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"daily_total": 2500, "max_total": 20000, "percentage": 12.5}], "message": ""}}}}}}}, "/api/v2/site/articles/": {"get": {"tags": ["Menú del sitio"], "summary": "Lista de artículos de su sitio", "operationId": "api_v2_site_articles_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_article": 1, "titre": "La Nouvelle version de LearnyBox va vous changer la Vie...", "permalink": "la-nouvelle-dimension-learnybox-v3", "image": "", "image_small": "", "image_afficher": true, "description_short": "LearnyBox vous invite à découvrir en avant-première la nouvelle dimension de LearnyBox V3.  Elle va vous changer la vie...", "article_html": "<p>azeazeaze</p>\\r\\n", "additionnal_css": "", "additionnal_js": "<script type='text/javascript'> </script>", "link_demo": "", "link_download": "", "tags": "", "publication": "encours", "vues": 0, "comments": true, "display_share": true, "display_author": true, "display_related": true, "seo_titre": "", "seo_description": "", "date": "2019-04-29T10:29:39+0200", "date_creation": "2017-09-26T10:07:00+0200", "date_publication": "2017-09-27T09:53:12+0200"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/site/articles/?limit=100&offset=0", "rel": "self"}}}}}}}}, "post": {"tags": ["Menú del sitio"], "summary": "Registrar un nuevo artículo", "operationId": "api_v2_site_articles_create", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"titre": {"description": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, "date_publication": {"description": "Fecha de publicación", "type": "string"}, "seo_titre": {"description": "Título SEO", "type": "string"}, "seo_description": {"description": "Descripción SEO", "type": "string"}, "article_html": {"description": "Contenido del artículo en HTML", "type": "string"}, "additionnal_js": {"description": "Contenido javascript adicional", "type": "string"}, "id_categorie": {"description": "Categoría ID", "type": "integer"}, "user_id": {"description": "ID de usuario", "type": "integer"}, "additionnal_css": {"description": "Contenido CSS adicional", "type": "string"}, "tags": {"description": "Etiquetas (separadas por una coma)", "type": "string"}, "description_short": {"description": "Descripción breve", "type": "string"}, "image": {"description": "Imagen", "type": "string"}, "image_afficher": {"description": "0 para no mostrar la imagen / 1 para mostrar la imagen", "type": "boolean"}, "comments": {"description": "0 para no permitir comentarios / 1 para permitir comentarios", "type": "boolean"}, "display_share": {"description": "0 para no mostrar acciones / 1 para mostrar acciones", "type": "boolean"}, "display_author": {"description": "0 para no mostrar el autor / 1 para mostrar el autor", "type": "boolean"}, "publication": {"description": "valores permitidos: \"pendiente\", \"pendiente\"", "type": "string"}, "permalink": {"description": "Enlace relacionado con su artículo", "type": "string"}, "redirection_page": {"description": "Por favor, seleccione una página o introduzca la dirección de la página de redirección", "type": "string"}, "redirection_url": {"description": "Redirigir URL", "type": "string"}, "id_domaine": {"description": "ID de dominio", "type": "integer"}, "link_demo": {"description": "URL de su demo", "type": "string"}, "link_download": {"description": "Descargar URL", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "patch": {"tags": ["Menú del sitio"], "summary": "Actualizar un artículo", "operationId": "api_v2_site_articles_edit", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"titre": {"description": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, "date_publication": {"description": "Fecha de publicación", "type": "string"}, "seo_titre": {"description": "Título SEO", "type": "string"}, "seo_description": {"description": "Descripción SEO", "type": "string"}, "article_html": {"description": "Contenido del artículo en HTML", "type": "string"}, "additionnal_js": {"description": "Contenido javascript adicional", "type": "string"}, "id_categorie": {"description": "Categoría ID", "type": "integer"}, "user_id": {"description": "ID de usuario", "type": "integer"}, "additionnal_css": {"description": "Contenido CSS adicional", "type": "string"}, "tags": {"description": "Etiquetas (separadas por una coma)", "type": "string"}, "description_short": {"description": "Descripción breve", "type": "string"}, "image": {"description": "Imagen", "type": "string"}, "image_afficher": {"description": "0 para no mostrar la imagen / 1 para mostrar la imagen", "type": "boolean"}, "comments": {"description": "0 para no permitir comentarios / 1 para permitir comentarios", "type": "boolean"}, "display_share": {"description": "0 para no mostrar acciones / 1 para mostrar acciones", "type": "boolean"}, "display_author": {"description": "0 para no mostrar el autor / 1 para mostrar el autor", "type": "boolean"}, "publication": {"description": "valores permitidos: \"pendiente\", \"pendiente\"", "type": "string"}, "permalink": {"description": "Enlace relacionado con su artículo", "type": "string"}, "redirection_page": {"description": "Por favor, seleccione una página o introduzca la dirección de la página de redirección", "type": "string"}, "redirection_url": {"description": "Redirigir URL", "type": "string"}, "id_domaine": {"description": "ID de dominio", "type": "integer"}, "link_demo": {"description": "URL de su demo", "type": "string"}, "link_download": {"description": "Descargar URL", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/site/articles/{id_article}/": {"get": {"tags": ["Menú del sitio"], "summary": "Datos de un artículo", "operationId": "api_v2_site_articles_show", "parameters": [{"name": "id_article", "in": "path", "description": "Identificador de un artículo", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_article": 1, "titre": "La Nouvelle version de LearnyBox va vous changer la Vie...", "permalink": "la-nouvelle-dimension-learnybox-v3", "image": "", "image_small": "", "image_afficher": true, "description_short": "LearnyBox vous invite à découvrir en avant-première la nouvelle dimension de LearnyBox V3.  Elle va vous changer la vie...", "article_html": "<p>azeazeaze</p>\\r\\n", "additionnal_css": "", "additionnal_js": "<script type='text/javascript'> </script>", "link_demo": "", "link_download": "", "tags": "", "publication": "encours", "vues": 0, "comments": true, "display_share": true, "display_author": true, "display_related": true, "seo_titre": "", "seo_description": "", "date": "2019-04-29T10:29:39+0200", "date_creation": "2017-09-26T10:07:00+0200", "date_publication": "2017-09-27T09:53:12+0200"}, "message": ""}}}}}}, "delete": {"tags": ["Menú del sitio"], "summary": "Borrar un artículo", "operationId": "api_v2_site_articles_delete", "parameters": [{"name": "id_article", "in": "path", "description": "Identificador de un artículo", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/site/articles/{id_article}/comments/": {"get": {"tags": ["Menú del sitio"], "summary": "Comentarios sobre un artículo", "operationId": "api_v2_site_articles_comments_list", "parameters": [{"name": "id_article", "in": "path", "description": "Identificador de un artículo", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/site/articles/{id_article}/comments/{id_commentaire}/": {"get": {"tags": ["Menú del sitio"], "summary": "Datos de un comentario de artículo", "operationId": "api_v2_site_articles_comments_show", "parameters": [{"name": "id_article", "in": "path", "description": "Identificador de un artículo", "required": true, "schema": {"type": "integer"}}, {"name": "id_commentaire", "in": "path", "description": "Identificador de comentarios", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_commentaire": 1, "prenom": "", "email": "<EMAIL>", "site": "", "commentaire": "<PERSON><PERSON>", "adresse": "", "telephone": "", "nom": "", "etat": "approuve", "notification": false, "pays": "FR", "date": "2017-01-19T14:18:32+0100", "article": [], "childs": [], "user": []}, "message": ""}}}}}}}, "/api/v2/site/articles/{id_article}/comments/{id_commentaire}/response/": {"post": {"tags": ["Menú del sitio"], "summary": "Registrar un nuevo comentario para un artículo", "operationId": "api_v2_site_articles_comments_response_create", "parameters": [{"name": "id_article", "in": "path", "description": "Identificador de un artículo", "required": true, "schema": {"type": "integer"}}, {"name": "id_commentaire", "in": "path", "description": "Identificador de comentarios", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"user_id": {"description": "ID de usuario", "type": "integer"}, "name": {"description": "Nombre del usuario", "type": "integer"}, "email": {"description": "Correo electrónico del usuario", "type": "integer"}, "id_article": {"description": "ID del artículo", "type": "integer"}, "message": {"description": "Men<PERSON><PERSON>", "type": "string"}, "orig_comment": {"description": "Identificador del comentario original", "type": "integer"}, "notification_email": {"description": "0 para no notificar / 1 para notificar", "type": "boolean"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/site/comments/": {"get": {"tags": ["Menú del sitio"], "summary": "Lista de comentarios en su sitio", "operationId": "api_v2_site_comments_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_commentaire": 1, "parent": 0, "prenom": "", "email": "<EMAIL>", "site": "", "commentaire": "Je voudrais savoir comment je peux cr&eacute;er un article comme celui-ci ?", "adresse": "", "telephone": "", "nom": "", "etat": "approuve", "notification": false, "pays": "FR", "date": "2017-01-19T14:18:32+0100", "article": {"id_article": 3, "titre": "Article 3", "permalink": "article3", "image": "", "image_small": "", "image_afficher": true, "description_short": "There are many variations of passages of Lorem I<PERSON>um available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don&#39;t look even slightly believable.", "article_html": "<p class='title'>There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or raazendomised words which don&#39;t look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to&nbsp;<u><strong>be sure there isn&#39;taze anything embarrassing hidden in the middle of text2</strong></u></p>\\r\\n\\r\\n<p>Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.</p>\\r\\n\\r\\n<p><strong>Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.</strong></p>\\r\\n\\r\\n<p>Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.</p>\\r\\n", "additionnal_css": "", "additionnal_js": "<script type='text/javascript'> </script>", "link_demo": "", "link_download": "", "tags": "Android,iPhone,Jquery,PHP", "publication": "encours", "vues": 52, "comments": true, "display_share": false, "display_author": true, "display_related": true, "seo_titre": "", "seo_description": "", "date": "2017-09-27T07:52:48+0200", "date_creation": "2013-02-23T19:43:47+0100", "date_publication": "2017-01-26T08:00:00+0100"}}, {"id_commentaire": 2, "parent": 0, "prenom": "Learny", "email": "<EMAIL>", "site": "https://learnybox.com", "commentaire": "Bravo pour cette pr&eacute;sentation !", "adresse": "", "telephone": "", "nom": "", "etat": "approuve", "notification": false, "pays": "FR", "date": "2017-01-19T14:17:41+0100", "article": {"id_article": 3, "titre": "Article 3", "permalink": "article3", "image": "", "image_small": "", "image_afficher": true, "description_short": "There are many variations of passages of Lorem I<PERSON>um available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don&#39;t look even slightly believable.", "article_html": "<p class='title'>There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or raazendomised words which don&#39;t look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to&nbsp;<u><strong>be sure there isn&#39;taze anything embarrassing hidden in the middle of text2</strong></u></p>\\r\\n\\r\\n<p>Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.</p>\\r\\n\\r\\n<p><strong>Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.</strong></p>\\r\\n\\r\\n<p>Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.</p>\\r\\n", "additionnal_css": "", "additionnal_js": "<script type='text/javascript'> </script>", "link_demo": "", "link_download": "", "tags": "Android,iPhone,Jquery,PHP", "publication": "encours", "vues": 52, "comments": true, "display_share": false, "display_author": true, "display_related": true, "seo_titre": "", "seo_description": "", "date": "2017-09-27T07:52:48+0200", "date_creation": "2013-02-23T19:43:47+0100", "date_publication": "2017-01-26T08:00:00+0100"}}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/site/comments/?limit=100&offset=0", "rel": "self"}}}}}}}}, "post": {"tags": ["Menú del sitio"], "summary": "Registrar un nuevo comentario", "operationId": "api_v2_site_comments_create", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"pl": {"description": "Permalink", "type": "string"}, "message": {"description": "Men<PERSON><PERSON>", "type": "string"}, "email": {"description": "Correo electrónico", "type": "string"}, "prenom": {"description": "Nombre", "type": "string"}, "nom": {"description": "Apellido", "type": "string"}, "site": {"description": "URL del sitio", "type": "string"}, "adresse": {"description": "Dirección", "type": "string"}, "telephone": {"description": "Teléfono", "type": "integer"}, "notification": {"description": "0 para no notificar / 1 para notificar", "type": "boolean"}, "comments": {"description": "0 para no permitir comentarios / 1 para permitir comentarios", "type": "boolean"}, "orig_comment": {"description": "0 para no mostrar el comentario original / 1 para mostrar el comentario original", "type": "boolean"}, "GEOIP_COUNTRY_CODE": {"description": "Código del p<PERSON>ís", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "patch": {"tags": ["Menú del sitio"], "summary": "Error al actualizar un comentario", "operationId": "api_v2_site_comments_edit", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"id_commentaire": {"description": "Comentario ID", "type": "integer"}, "message": {"description": "Men<PERSON><PERSON>", "type": "string"}, "etat": {"description": "Estado", "type": "string"}, "site": {"description": "URL del sitio", "type": "string"}, "email": {"description": "Correo electrónico", "type": "string"}, "prenom": {"description": "Nombre", "type": "string"}, "nom": {"description": "Apellido", "type": "string"}, "adresse": {"description": "Dirección", "type": "string"}, "telephone": {"description": "Teléfono", "type": "integer"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/site/comments/{id_comment}/": {"get": {"tags": ["Menú del sitio"], "summary": "Datos de un comentario", "operationId": "api_v2_site_comments_show", "parameters": [{"name": "id_comment", "in": "path", "description": "Identificador de comentarios", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_commentaire": 1, "parent": 0, "prenom": "", "email": "<EMAIL>", "site": "", "commentaire": "Je voudrais savoir comment je peux cr&eacute;er un article comme celui-ci ?", "adresse": "", "telephone": "", "nom": "", "etat": "approuve", "notification": false, "pays": "FR", "date": "2017-01-19T14:18:32+0100", "article": {"id_article": 3, "titre": "Article 3", "permalink": "article3", "image": "", "image_small": "", "image_afficher": true, "description_short": "There are many variations of passages of Lorem I<PERSON>um available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don&#39;t look even slightly believable.", "article_html": "<p class='title'>There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or raazendomised words which don&#39;t look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to&nbsp;<u><strong>be sure there isn&#39;taze anything embarrassing hidden in the middle of text2</strong></u></p>\\r\\n\\r\\n<p>Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.</p>\\r\\n\\r\\n<p><strong>Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.</strong></p>\\r\\n\\r\\n<p>Itaque earum rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut perferendis doloribus asperiores repellat.</p>\\r\\n", "additionnal_css": "", "additionnal_js": "<script type='text/javascript'> </script>", "link_demo": "", "link_download": "", "tags": "Android,iPhone,Jquery,PHP", "publication": "encours", "vues": 52, "comments": true, "display_share": false, "display_author": true, "display_related": true, "seo_titre": "", "seo_description": "", "date": "2017-09-27T07:52:48+0200", "date_creation": "2013-02-23T19:43:47+0100", "date_publication": "2017-01-26T08:00:00+0100"}}, "message": ""}}}}}}, "delete": {"tags": ["Menú del sitio"], "summary": "Eliminar un comentario", "operationId": "api_v2_site_comments_delete", "parameters": [{"name": "id_comment", "in": "path", "description": "Identificador de comentarios", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/site/pages/": {"get": {"tags": ["Menú del sitio"], "summary": "Lista de páginas de su sitio", "operationId": "api_v2_site_pages_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_page": 1, "version": 1, "nom": "Accueil", "permalink": "accueil", "type": "compose", "theme": "site-index", "publiee": true, "seo_image": "", "additionnal_css": "", "additionnal_js": "", "additionnal_js_head": "", "permission": 0, "header": true, "footer": true, "display_header": true, "display_footer": true, "display_title": false, "display_sidebar": false, "display_menu": true, "disable_indexation": false, "design": "{'bgcolor1':'','bgcolor2':'','gradient_angle':'0','colors':['#b2ce74','#7a8c9e','#526173'],'color_gray':'#a5a5a5','fonts':['Rubik'],'font_sizes':{'h1':{'font_size':'72','font_color':'#526173','font_family':'default'},'h2':{'font_size':'36','font_color':'#526173','font_family':'default'},'h3':{'font_size':'28','font_color':'#526173','font_family':'default'},'h4':{'font_size':'22','font_color':'#526173','font_family':'default'},'h5':{'font_size':'16','font_color':'#526173','font_family':'default'},'h6':{'font_size':'14','font_color':'#526173','font_family':'default'},'p':{'font_size':'14','font_color':'#526173','font_family':'default'},'small':{'font_size':'12','font_color':'#526173','font_family':'default'}},'line_themes':{'1':{'bgcolor1':'#f3f6f9','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'2':{'bgcolor1':'#e9edf1','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'3':{'bgcolor1':'#534d89','bgcolor2':'#6dbed7','angle':'315','opacity':'','opacity2':''},'4':{'bgcolor1':'#0d1d4a','bgcolor2':'#307892','angle':'135','opacity':'0.75','opacity2':'0.75'}},'btn':{'bg_color1':'#b2ce74','bg_color2':'','color_text':'#ffffff','bg_color1_white':'','color_text_white':'','border_radius':'0'},'temoignages':{'color_icone':'#b2ce74','color_hr':'#b2ce74','color_txt':'#526173','color_user':'#526173','color_user_title':'#526173'}}", "fonts": "", "config": "", "animations": "", "vues": 0, "thumbnail": "", "editor_level": "expert", "facebook_pixel_event": "", "date_creation": "2019-03-15T12:01:04+0100", "date_modification": "2019-03-15T12:01:04+0100", "client": {"nom_client": "Learny Box"}}], "message": "", "offset": 0, "limit": 1, "total": 7, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/site/pages/?limit=100&offset=0", "rel": "self"}, "next": {"href": "https://api.learnybox.com/api/v2/site/pages/?id_page=2", "rel": "next"}, "last": {"rel": "last", "href": "https://api.learnybox.com/api/v2/site/pages/?id_page=7"}}}}}}}}, "post": {"tags": ["Menú del sitio"], "summary": "Guardar una nueva página", "operationId": "api_v2_site_pages_create", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"nom": {"description": "Apellido", "type": "string"}, "seo_description": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "seo_tags": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "additionnal_css": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "additionnal_js": {"description": "Apellido", "type": "string"}, "permission": {"description": "0 / 1 ", "type": "boolean"}, "header": {"description": "0 / 1 ", "type": "boolean"}, "footer": {"description": "0 / 1 ", "type": "boolean"}, "display_title": {"description": "0 / 1 ", "type": "boolean"}, "display_sidebar": {"description": "0 / 1 ", "type": "boolean"}, "publiee": {"description": "0 / 1 ", "type": "boolean"}, "content": {"description": "Contenido del artículo", "type": "string"}, "permalink": {"description": "Permalink", "type": "string"}, "id_domaine": {"description": "ID de dominio", "type": "integer"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "patch": {"tags": ["Menú del sitio"], "summary": "Actualizar una página", "operationId": "api_v2_site_pages_edit", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"nom": {"description": "Apellido", "type": "string"}, "seo_description": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "seo_tags": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "additionnal_css": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "additionnal_js": {"description": "Apellido", "type": "string"}, "permission": {"description": "0 / 1 ", "type": "boolean"}, "header": {"description": "0 / 1 ", "type": "boolean"}, "footer": {"description": "0 / 1 ", "type": "boolean"}, "display_title": {"description": "0 / 1 ", "type": "boolean"}, "display_sidebar": {"description": "0 / 1 ", "type": "boolean"}, "publiee": {"description": "0 / 1 ", "type": "boolean"}, "content": {"description": "Contenido del artículo", "type": "string"}, "permalink": {"description": "Permalink", "type": "string"}, "id_domaine": {"description": "ID de dominio", "type": "integer"}, "file": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/site/pages/{id_page}/": {"get": {"tags": ["Menú del sitio"], "summary": "Datos de una página de su sitio", "operationId": "api_v2_site_pages_show", "parameters": [{"name": "id_page", "in": "path", "description": "Identificador de página", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_page": "1", "id_client": "1", "id_domaine": "0", "version": "1", "nom": "Accueil", "permalink": "accueil", "type": "compose", "id_master_theme": "1", "id_theme": "1", "theme": "site-index", "publiee": "1", "seo_image": "", "additionnal_css": "", "additionnal_js": "", "additionnal_js_head": "", "permission": "0", "header": "1", "footer": "1", "display_header": "1", "display_footer": "1", "display_title": "0", "display_sidebar": "0", "display_menu": "1", "disable_indexation": "0", "design": "{'bgcolor1':'','bgcolor2':'','gradient_angle':'0','colors':['#b2ce74','#7a8c9e','#526173'],'color_gray':'#a5a5a5','fonts':['Rubik'],'font_sizes':{'h1':{'font_size':'72','font_color':'#526173','font_family':'default'},'h2':{'font_size':'36','font_color':'#526173','font_family':'default'},'h3':{'font_size':'28','font_color':'#526173','font_family':'default'},'h4':{'font_size':'22','font_color':'#526173','font_family':'default'},'h5':{'font_size':'16','font_color':'#526173','font_family':'default'},'h6':{'font_size':'14','font_color':'#526173','font_family':'default'},'p':{'font_size':'14','font_color':'#526173','font_family':'default'},'small':{'font_size':'12','font_color':'#526173','font_family':'default'}},'line_themes':{'1':{'bgcolor1':'#f3f6f9','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'2':{'bgcolor1':'#e9edf1','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'3':{'bgcolor1':'#534d89','bgcolor2':'#6dbed7','angle':'315','opacity':'','opacity2':''},'4':{'bgcolor1':'#0d1d4a','bgcolor2':'#307892','angle':'135','opacity':'0.75','opacity2':'0.75'}},'btn':{'bg_color1':'#b2ce74','bg_color2':'','color_text':'#ffffff','bg_color1_white':'','color_text_white':'','border_radius':'0'},'temoignages':{'color_icone':'#b2ce74','color_hr':'#b2ce74','color_txt':'#526173','color_user':'#526173','color_user_title':'#526173'}}", "fonts": "", "config": "", "animations": "", "vues": "0", "thumbnail": "", "editor_level": "expert", "google_analytics_id": "0", "facebook_pixel_event": "", "facebook_pixel_id": "0", "date_creation": "2019-03-15 12:01:04", "date_modification": "2019-03-15 12:01:04"}, "message": ""}}}}}}, "delete": {"tags": ["Menú del sitio"], "summary": "Sup<PERSON>ir una página", "operationId": "api_v2_site_pages_delete", "parameters": [{"name": "id_page", "in": "path", "description": "Identificador de página", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/site/pages/{id_page}/comments/": {"get": {"tags": ["Menú del sitio"], "summary": "Comentarios de una página", "operationId": "api_v2_site_pages_comments_list", "parameters": [{"name": "id_page", "in": "path", "description": "Identificador de página", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/site/pages/{id_page}/comments/{id_commentaire}/": {"get": {"tags": ["Menú del sitio"], "summary": "Datos de un comentario de una página", "operationId": "api_v2_site_pages_comments_show", "parameters": [{"name": "id_page", "in": "path", "description": "Identificador de página", "required": true, "schema": {"type": "integer"}}, {"name": "id_commentaire", "in": "path", "description": "Identificador de comentarios", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_commentaire": 1, "prenom": "", "email": "<EMAIL>", "nom": "", "site": "", "commentaire": "<PERSON><PERSON>", "adresse": "", "telephone": "", "etat": "approuve", "notification": false, "pays": "FR", "date": "2017-01-19T14:18:32+0100", "page": [], "childs": [], "user": []}, "message": ""}}}}}}}, "/api/v2/site/pages/{id_page}/comments/{id_commentaire}/response/": {"post": {"tags": ["Menú del sitio"], "summary": "Registrar un nuevo comentario para una página", "operationId": "api_v2_site_pages_comments_response_create", "parameters": [{"name": "id_page", "in": "path", "description": "Identificador de página", "required": true, "schema": {"type": "integer"}}, {"name": "id_commentaire", "in": "path", "description": "Identificador de comentarios", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"user_id": {"description": "ID de usuario", "type": "integer"}, "name": {"description": "Nombre del usuario", "type": "integer"}, "email": {"description": "Correo electrónico del usuario", "type": "integer"}, "pl": {"description": "Permalink de la página", "type": "integer"}, "message": {"description": "Men<PERSON><PERSON>", "type": "string"}, "orig_comment": {"description": "Identificador del comentario original", "type": "integer"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/sondage/user/{user_id}/": {"get": {"tags": ["Encuesta"], "summary": "Liste des sondages d'un utilisateur", "operationId": "api_v2_sondage_user", "parameters": [{"name": "user_id", "in": "path", "description": "Identifiant d'un utilisateur", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_participant": 39, "etat": "termine", "ip": "127.0.0.1", "disabled": false, "nb_times": 2, "date": "2024-02-19T13:35:51+01:00", "sondage": {"id_sondage": 31, "nom": "Sondage test"}}, {"id_participant": 38, "etat": "1", "ip": "127.0.0.1", "disabled": false, "nb_times": 0, "date": "2024-02-01T15:48:25+01:00", "sondage": {"id_sondage": 32, "nom": "Sondage test 2"}}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"rel": "self", "href": "https://api.learnybox.com/api/v2/sondage/user/1/?limit=100&offset=0"}}}}}}}}}, "/api/v2/sondage/{sondage_id}/user/{user_id}/": {"get": {"tags": ["Encuesta"], "summary": "Liste des réponses à un sondage pour un utilisateur", "operationId": "api_v2_sondage_id_user", "parameters": [{"name": "sondage_id", "in": "path", "description": "Identifiant d'un sondage", "required": true, "schema": {"type": "integer"}}, {"name": "user_id", "in": "path", "description": "Identifiant d'un utilisateur", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_participant": 39, "etat": "termine", "ip": "::1", "disabled": false, "nb_times": 2, "date": "2018-02-19T13:35:51+01:00", "sondage": {"id_sondage": 31, "nom": "Sondage test"}, "reponses": [{"id_question": 1, "title": "Titre de la question", "type": "input_checkbox", "reponses": ["Titre de la réponse", "Titre de la réponse 2"]}]}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"rel": "self", "href": "https://api.learnybox.com/api/v2/sondage/31/user/1/?limit=100&offset=0"}}}}}}}}}, "/api/v2/support/": {"get": {"tags": ["Apoyo técnico"], "summary": "Lista de sus tickets de soporte en línea", "operationId": "api_v2_support_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/support/{id_ticket}/": {"delete": {"tags": ["Apoyo técnico"], "summary": "Borrar un billete", "operationId": "api_v2_support_delete", "parameters": [{"name": "id_ticket", "in": "path", "description": "Ticket ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/messages/": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Lista de mensajes publicados en su formulario de contacto", "operationId": "api_v2_messages_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"reponse": 0, "type": "question", "priorite": "<PERSON><PERSON>", "nom": "Box1", "prenom": "Learny", "email": "test1@learnybox", "message": "test message", "lu": true, "date": "2018-01-15T15:07:05+0100"}, {"reponse": 0, "type": "question", "priorite": "<PERSON><PERSON>", "nom": "Box2", "prenom": "Learny", "email": "test2@learnybox", "message": "test message", "lu": true, "date": "2018-01-15T15:07:05+0100"}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/messages/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/messages/{id_message}/": {"delete": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Eliminar un mensaje enviado desde su formulario de contacto", "operationId": "api_v2_messages_delete", "parameters": [{"name": "id_message", "in": "path", "description": "Identificador del mensaje", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/support/tickets/": {"get": {"tags": ["Apoyo técnico"], "summary": "Tickets/Lista de sus tickets de soporte en línea", "operationId": "api_v2_support_tickets_list", "responses": {"200": {"description": ""}, "default": {"description": "an \"unexpected\" error"}}}}, "/api/v2/support/tickets/{ticketid}/": {"delete": {"tags": ["Apoyo técnico"], "summary": "Billetes/Borrar un billete", "operationId": "api_v2_support_tickets_delete", "parameters": [{"name": "ticketid", "in": "path", "description": "Ticket ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}, "default": {"description": "an \"unexpected\" error"}}}}, "/api/v2/transactions/": {"get": {"tags": ["Transacciones"], "summary": "Lista de transacciones", "operationId": "api_v2_transactions_list", "parameters": [{"name": "email", "in": "path", "description": "Adresse email du membre dans LearnyBox", "required": false, "schema": {"type": "string"}}, {"name": "user_id", "in": "path", "description": "ID de miembro en LearnyBox", "required": false, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id": 1, "valid": true, "etat": "ok", "type": "cheque", "montant_ht": 10, "display_montant_ht": "€10", "tva": 20, "montant": 12, "display_montant": "€12", "devise": "EUR", "nom": "Learny", "prenom": "Box", "email": "<EMAIL>", "descriptor": "LearnyStart", "ip": "127.0.0.1", "date": "2019-03-18T16:47:26+0100", "date_valid": "2019-03-18T16:48:11+0100", "client": {"nom_client": "Learny Box1"}, "id_trans": "TEST0001", "shop_customer": {"id_customer": 1, "datas": {"nom": "Learny", "prenom": "Box", "email": "<EMAIL>", "adresse": "", "cp": "", "ville": "", "pays": "FR", "telephone": "0123456789", "societe": "LearnyBox", "tva_intracom": "", "site": "https://learnybox.com", "uniqid": "1"}}, "transaction_produits": [{"id_transaction_produit": 1, "quantite": 1, "nom": "Produit", "montant_ht": 10, "display_montant_ht": 10, "tva": 20, "montant_ttc": 12, "display_montant_ttc": 12, "devise": "EUR", "date": "2019-03-18T16:47:26+0100"}]}, {"id": 2, "valid": true, "etat": "ok", "type": "cheque", "montant_ht": 10, "tva": 20, "montant": 12, "devise": "EUR", "nom": "Learny", "prenom": "Box", "email": "<EMAIL>", "descriptor": "LearnyStart", "ip": "127.0.0.1", "date": "2019-03-18T16:47:26+0100", "date_valid": "2019-03-18T16:48:11+0100", "client": {"nom_client": "Learny Box2"}, "id_trans": "TEST0002", "shop_customer": {"id_customer": 1, "datas": {"nom": "Learny", "prenom": "Box", "email": "<EMAIL>", "adresse": "", "cp": "", "ville": "", "pays": "FR", "telephone": "0123456789", "societe": "LearnyBox", "tva_intracom": "", "site": "https://learnybox.com", "uniqid": "1"}}, "transaction_produits": [{"id_transaction_produit": 1, "quantite": 1, "nom": "Produit", "montant_ht": 10, "display_montant_ht": 10, "tva": 20, "montant_ttc": 12, "display_montant_ttc": 12, "devise": "EUR", "date": "2019-03-18T16:47:26+0100"}]}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/transactions/?limit=100&offset=0", "rel": "self"}}}}}}}}, "post": {"tags": ["Transacciones"], "summary": "Añadir una nueva transacción", "operationId": "api_v2_transactions_create", "requestBody": {"description": "Campos nombre, apellidos y correo electrónico obligatorios *", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"id_transaction": {"description": "Número de transacción", "type": "string"}, "produit": {"description": "Nombre del producto", "type": "string"}, "prenom": {"description": "Nombre del cliente", "type": "string"}, "nom": {"description": "Apellido del cliente", "type": "string"}, "email": {"description": "Dirección de correo electrónico del cliente", "type": "string"}, "montant_ht": {"description": "Importe sin IVA", "type": "string"}, "tva": {"description": "Tasa de IVA", "type": "string"}, "montant_ttc": {"description": "Importe con IVA", "type": "string"}, "devise": {"description": "EUR, CAD, USD, GPB, etc.", "type": "string"}, "mode_paiement": {"description": "Paypal, Stripe, cheque, etc.", "type": "string"}, "etat": {"description": "Valores posibles para este campo: válid<PERSON> / pendiente / entrega / entregado / cancelar / error / reembolsado", "type": "string", "enum": ["valide", "enattente", "delivering", "delivered", "cancel", "error", "refunded"]}, "date": {"description": "Fecha (formato AAAA-MM-DD HH:II:SS)", "type": "string"}, "id_formation": {"description": "ID del curso", "type": "string"}, "groupes": {"description": "ID de grupo separados por comas", "type": "string"}, "nb_credits": {"description": "Número de créditos a añadir al socio", "type": "integer"}, "adresse": {"description": "Dirección del miembro", "type": "string"}, "ville": {"description": "Ciudad del miembro", "type": "string"}, "pays": {"description": "País del diputado", "type": "string"}, "code_postal": {"description": "Código postal del afiliado", "type": "string"}, "tel": {"description": "Número de teléfono del afiliado", "type": "string"}, "id_campaign": {"description": "ID de la campaña de afiliación", "type": "integer"}, "id_affilie": {"description": "ID de afiliado", "type": "integer"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/transactions/user/{user_id}/": {"get": {"tags": ["Transacciones"], "summary": "Transactions d'un membre", "operationId": "api_v2_transactions_user_list", "parameters": [{"name": "user_id", "in": "path", "description": "ID de miembro en LearnyBox", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id": 1, "valid": true, "etat": "ok", "type": "cheque", "montant_ht": 10, "display_montant_ht": "€12", "tva": 20, "montant": 12, "devise": "EUR", "nom": "Learny", "prenom": "Box", "email": "<EMAIL>", "descriptor": "LearnyStart", "ip": "127.0.0.1", "date": "2019-03-18T16:47:26+0100", "date_valid": "2019-03-18T16:48:11+0100", "client": {"nom_client": "Learny Box1"}, "id_trans": "TEST0001", "shop_customer": {"id_customer": 1, "datas": {"nom": "Learny", "prenom": "Box", "email": "<EMAIL>", "adresse": "", "cp": "", "ville": "", "pays": "FR", "telephone": "0123456789", "societe": "LearnyBox", "tva_intracom": "", "site": "https://learnybox.com", "uniqid": "1"}}}, {"id": 2, "valid": true, "etat": "ok", "type": "cheque", "montant_ht": 10, "display_montant_ht": "€10", "tva": 20, "montant": 12, "display_montant": "€12", "devise": "EUR", "nom": "Learny", "prenom": "Box", "email": "<EMAIL>", "descriptor": "LearnyStart", "ip": "127.0.0.1", "date": "2019-03-18T16:47:26+0100", "date_valid": "2019-03-18T16:48:11+0100", "client": {"nom_client": "Learny Box1"}, "id_trans": "TEST0002", "shop_customer": {"id_customer": 1, "datas": {"nom": "Learny", "prenom": "Box", "email": "<EMAIL>", "adresse": "", "cp": "", "ville": "", "pays": "FR", "telephone": "0123456789", "societe": "LearnyBox", "tva_intracom": "", "site": "https://learnybox.com", "uniqid": "1"}}, "transaction_produits": [{"id_transaction_produit": 1, "quantite": 1, "nom": "Produit", "montant_ht": 10, "display_montant_ht": 10, "tva": 20, "montant_ttc": 12, "display_montant_ttc": 12, "devise": "EUR", "date": "2019-03-18T16:47:26+0100"}]}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/transactions/user/1/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/transactions/{id_trans}/": {"get": {"tags": ["Transacciones"], "summary": "Datos de la transacción", "operationId": "api_v2_transactions_show", "parameters": [{"name": "id_trans", "in": "path", "description": "Identificador de la transacción", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id": 1, "valid": true, "etat": "ok", "type": "cheque", "montant_ht": 10, "tva": 20, "montant": 12, "devise": "EUR", "nom": "Learny", "prenom": "Box", "email": "<EMAIL>", "descriptor": "LearnyStart", "ip": "127.0.0.1", "date": "2019-03-18T16:47:26+0100", "date_valid": "2019-03-18T16:48:11+0100", "client": {"nom_client": "Learny Box1"}, "id_trans": "TEST0001", "shop_customer": {"id_customer": 1, "datas": {"nom": "Learny", "prenom": "Box", "email": "<EMAIL>", "adresse": "", "cp": "", "ville": "", "pays": "FR", "telephone": "0123456789", "societe": "LearnyBox", "tva_intracom": "", "site": "https://learnybox.com", "uniqid": "1"}}, "transaction_produits": [{"id_transaction_produit": 1, "quantite": 1, "nom": "Produit", "montant_ht": 10, "display_montant_ht": 10, "tva": 20, "montant_ttc": 12, "display_montant_ttc": 12, "devise": "EUR", "date": "2019-03-18T16:47:26+0100"}], "abonnement": {"id_abonnement": "1000", "montant": "106.80", "nb_payments": "x", "nb_payments_left": "x", "decalage": "1 month", "next_payment": "2019-10-10", "hour": "12", "valid": "1", "error": "", "etat": "active", "nb_payment": 1}}, "message": ""}}}}}}}, "/api/v2/tunnels/": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Lista de túneles", "operationId": "api_v2_tunnels_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_tunnel": 1, "nom": "Partir de zéro", "permalink": "partir-de-zero", "version": 1, "sequence": "", "redirection": "", "publication": "encours", "thumbnail": "", "comment_users": "", "date": "2017-12-19T09:36:03+0100", "date_creation": "2017-12-19T09:36:03+0100", "date_publication": "2017-12-24T16:21:31+0100", "date_fin": "2017-12-24T16:21:28+0100", "client": {"nom_client": "Learny Box"}, "tunnel_pages": [{"id_page": 9440, "conf_type": "", "version": 2, "nom": "test capture 1", "permalink": "test-capture-1", "type": "optin", "seo_description": "", "seo_tags": "", "seo_image": "", "additionnal_css": "", "additionnal_js": "", "additionnal_js_head": "", "permission": 0, "display_header": false, "display_footer": false, "position": 1, "publication": "encours", "design": "{'bgcolor1':'','bgcolor2':'','gradient_angle':'0','colors':['#4a90e2','#6d76b5','#575e66'],'color_gray':'#a5a5a5','fonts':['Lato'],'font_sizes':{'h1':{'font_size':'72','font_color':'#575e66','font_family':'default'},'h2':{'font_size':'36','font_color':'#575e66','font_family':'default'},'h3':{'font_size':'28','font_color':'#575e66','font_family':'default'},'h4':{'font_size':'22','font_color':'#575e66','font_family':'default'},'h5':{'font_size':'16','font_color':'#575e66','font_family':'default'},'h6':{'font_size':'14','font_color':'#575e66','font_family':'default'},'p':{'font_size':'14','font_color':'#575e66','font_family':'default'},'small':{'font_size':'12','font_color':'#575e66','font_family':'default'}},'line_themes':{'1':{'bgcolor1':'#fafafa','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'2':{'bgcolor1':'#f2f2f2','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'3':{'bgcolor1':'#989eb3','bgcolor2':'#8f9fb3','angle':'180','opacity':'','opacity2':''},'4':{'bgcolor1':'#2b2d33','bgcolor2':'#292e33','angle':'180','opacity':'0.75','opacity2':'0.75'}},'btn':{'bg_color1':'#4a90e2','bg_color2':'','color_text':'#ffffff','bg_color1_white':'#ffffff','color_text_white':'#4a90e2','border_radius':'0'},'temoignages':{'color_icone':'#4a90e2','color_hr':'#4a90e2','color_txt':'#575e66','color_user':'#575e66','color_user_title':'#575e66'}}", "animations": "", "fonts": "", "config": "", "theme": "capture-grande-image", "editor_level": "expert", "disable_indexation": false, "vues": 0, "thumbnail": "", "ligne": 1, "colonne": 1, "facebook_pixel_event": "0", "date_creation": "2019-04-25T11:33:57+0200", "date_modification": "-0001-11-30T00:00:00+0009"}]}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/tunnels/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/tunnels/{id_tunnel}/": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON>tos de un túnel", "operationId": "api_v2_tunnels_show", "parameters": [{"name": "id_tunnel", "in": "path", "description": "Identificador del túnel", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_tunnel": 1, "nom": "Partir de zéro", "permalink": "partir-de-zero", "version": 1, "sequence": "", "redirection": "", "publication": "encours", "thumbnail": "", "comment_users": "", "date": "2017-12-19T09:36:03+0100", "date_creation": "2017-12-19T09:36:03+0100", "date_publication": "2017-12-24T16:21:31+0100", "date_fin": "2017-12-24T16:21:28+0100", "client": {"nom_client": "Learny Box"}, "tunnel_pages": [{"id_page": 9440, "conf_type": "", "version": 2, "nom": "test capture 1", "permalink": "test-capture-1", "type": "optin", "seo_description": "", "seo_tags": "", "seo_image": "", "additionnal_css": "", "additionnal_js": "", "additionnal_js_head": "", "permission": 0, "display_header": false, "display_footer": false, "position": 1, "publication": "encours", "design": "{'bgcolor1':'','bgcolor2':'','gradient_angle':'0','colors':['#4a90e2','#6d76b5','#575e66'],'color_gray':'#a5a5a5','fonts':['Lato'],'font_sizes':{'h1':{'font_size':'72','font_color':'#575e66','font_family':'default'},'h2':{'font_size':'36','font_color':'#575e66','font_family':'default'},'h3':{'font_size':'28','font_color':'#575e66','font_family':'default'},'h4':{'font_size':'22','font_color':'#575e66','font_family':'default'},'h5':{'font_size':'16','font_color':'#575e66','font_family':'default'},'h6':{'font_size':'14','font_color':'#575e66','font_family':'default'},'p':{'font_size':'14','font_color':'#575e66','font_family':'default'},'small':{'font_size':'12','font_color':'#575e66','font_family':'default'}},'line_themes':{'1':{'bgcolor1':'#fafafa','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'2':{'bgcolor1':'#f2f2f2','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'3':{'bgcolor1':'#989eb3','bgcolor2':'#8f9fb3','angle':'180','opacity':'','opacity2':''},'4':{'bgcolor1':'#2b2d33','bgcolor2':'#292e33','angle':'180','opacity':'0.75','opacity2':'0.75'}},'btn':{'bg_color1':'#4a90e2','bg_color2':'','color_text':'#ffffff','bg_color1_white':'#ffffff','color_text_white':'#4a90e2','border_radius':'0'},'temoignages':{'color_icone':'#4a90e2','color_hr':'#4a90e2','color_txt':'#575e66','color_user':'#575e66','color_user_title':'#575e66'}}", "animations": "", "fonts": "", "config": "", "theme": "capture-grande-image", "editor_level": "expert", "disable_indexation": false, "vues": 0, "thumbnail": "", "ligne": 1, "colonne": 1, "facebook_pixel_event": "0", "date_creation": "2019-04-25T11:33:57+0200", "date_modification": "-0001-11-30T00:00:00+0009"}]}, "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/tunnels/?limit=100&offset=0", "rel": "self"}}}}}}}}, "delete": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> un túnel", "operationId": "api_v2_tunnels_delete", "parameters": [{"name": "id_tunnel", "in": "path", "description": "Identificador del túnel", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/site/tunnels/": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Registrar un nuevo túnel", "operationId": "api_v2_tunnels_create", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"nom": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "sequence": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "publication": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "id_conference": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "id_webinaire": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "id_domaine": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "version": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "id_master_theme": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "id_theme": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "id_categorie": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "users": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "patch": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Actualizar un túnel", "operationId": "api_v2_tunnels_edit", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"id_tunnel": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "nom": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "id_domaine": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "id_categorie": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "users": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/tunnels/{id_tunnel}/pages/": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Lista de páginas de un túnel", "operationId": "api_v2_tunnels_pages_list", "parameters": [{"name": "id_tunnel", "in": "path", "description": "Identificador del túnel", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_page": 1, "conf_type": "", "version": 1, "nom": "test capture 1", "permalink": "test-capture-1", "type": "optin", "seo_description": "", "seo_tags": "", "seo_image": "", "additionnal_css": "", "additionnal_js": "", "additionnal_js_head": "", "permission": 0, "display_header": false, "display_footer": false, "position": 1, "publication": "encours", "design": "{'bgcolor1':'','bgcolor2':'','gradient_angle':'0','colors':['#4a90e2','#6d76b5','#575e66'],'color_gray':'#a5a5a5','fonts':['Lato'],'font_sizes':{'h1':{'font_size':'72','font_color':'#575e66','font_family':'default'},'h2':{'font_size':'36','font_color':'#575e66','font_family':'default'},'h3':{'font_size':'28','font_color':'#575e66','font_family':'default'},'h4':{'font_size':'22','font_color':'#575e66','font_family':'default'},'h5':{'font_size':'16','font_color':'#575e66','font_family':'default'},'h6':{'font_size':'14','font_color':'#575e66','font_family':'default'},'p':{'font_size':'14','font_color':'#575e66','font_family':'default'},'small':{'font_size':'12','font_color':'#575e66','font_family':'default'}},'line_themes':{'1':{'bgcolor1':'#fafafa','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'2':{'bgcolor1':'#f2f2f2','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'3':{'bgcolor1':'#989eb3','bgcolor2':'#8f9fb3','angle':'180','opacity':'','opacity2':''},'4':{'bgcolor1':'#2b2d33','bgcolor2':'#292e33','angle':'180','opacity':'0.75','opacity2':'0.75'}},'btn':{'bg_color1':'#4a90e2','bg_color2':'','color_text':'#ffffff','bg_color1_white':'#ffffff','color_text_white':'#4a90e2','border_radius':'0'},'temoignages':{'color_icone':'#4a90e2','color_hr':'#4a90e2','color_txt':'#575e66','color_user':'#575e66','color_user_title':'#575e66'}}", "animations": "", "fonts": "", "config": "", "theme": "capture-grande-image", "editor_level": "expert", "vues": 0, "thumbnail": "", "facebook_pixel_event": "0", "date_creation": "2019-04-25T11:33:57+0200"}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/tunnels/2100/pages/?limit=100&offset=0", "rel": "self"}}}}}}}}, "post": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Añadir una página a un túnel", "operationId": "api_v2_tunnels_pages_create", "parameters": [{"name": "id_tunnel", "in": "path", "description": "Identificador del túnel", "required": true, "schema": {"type": "integer"}}], "requestBody": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"id_tunnel": {"description": "Identificador del túnel", "type": "integer"}, "nom": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "type": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "position": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "permalink": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "theme": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "editor_level": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "id_conference": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "conf_type": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "id_webinaire": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "id_master_theme": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "id_theme": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "google_analytics": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/tunnels/{id_tunnel}/pages/{id_page}/": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Datos de una página de túnel", "operationId": "api_v2_tunnels_pages_show", "parameters": [{"name": "id_tunnel", "in": "path", "description": "Identificador del túnel", "required": true, "schema": {"type": "integer"}}, {"name": "id_page", "in": "path", "description": "Identificador de página", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_page": 1, "conf_type": "", "version": 1, "nom": "test capture 1", "permalink": "test-capture-1", "type": "optin", "seo_description": "", "seo_tags": "", "seo_image": "", "additionnal_css": "", "additionnal_js": "", "additionnal_js_head": "", "permission": 0, "display_header": false, "display_footer": false, "position": 1, "publication": "encours", "design": "{'bgcolor1':'','bgcolor2':'','gradient_angle':'0','colors':['#4a90e2','#6d76b5','#575e66'],'color_gray':'#a5a5a5','fonts':['Lato'],'font_sizes':{'h1':{'font_size':'72','font_color':'#575e66','font_family':'default'},'h2':{'font_size':'36','font_color':'#575e66','font_family':'default'},'h3':{'font_size':'28','font_color':'#575e66','font_family':'default'},'h4':{'font_size':'22','font_color':'#575e66','font_family':'default'},'h5':{'font_size':'16','font_color':'#575e66','font_family':'default'},'h6':{'font_size':'14','font_color':'#575e66','font_family':'default'},'p':{'font_size':'14','font_color':'#575e66','font_family':'default'},'small':{'font_size':'12','font_color':'#575e66','font_family':'default'}},'line_themes':{'1':{'bgcolor1':'#fafafa','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'2':{'bgcolor1':'#f2f2f2','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'3':{'bgcolor1':'#989eb3','bgcolor2':'#8f9fb3','angle':'180','opacity':'','opacity2':''},'4':{'bgcolor1':'#2b2d33','bgcolor2':'#292e33','angle':'180','opacity':'0.75','opacity2':'0.75'}},'btn':{'bg_color1':'#4a90e2','bg_color2':'','color_text':'#ffffff','bg_color1_white':'#ffffff','color_text_white':'#4a90e2','border_radius':'0'},'temoignages':{'color_icone':'#4a90e2','color_hr':'#4a90e2','color_txt':'#575e66','color_user':'#575e66','color_user_title':'#575e66'}}", "animations": "", "fonts": "", "config": "", "theme": "capture-grande-image", "editor_level": "expert", "vues": 0, "thumbnail": "", "facebook_pixel_event": "0", "date_creation": "2019-04-25T11:33:57+0200"}, "message": ""}}}}}}}, "/api/v2/tunnels/pages/{id_page}/": {"delete": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Sup<PERSON>ir una página", "operationId": "api_v2_tunnels_pages_delete", "parameters": [{"name": "id_page", "in": "path", "description": "Identificador de página", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "patch": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Actualizar la página de un túnel", "operationId": "api_v2_tunnels_pages_edit", "parameters": [{"name": "id_page", "in": "path", "description": "Identificador de página", "required": true, "schema": {"type": "integer"}}], "requestBody": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "required": true, "content": {"application/x-www-form-urlencoded;charset=UTF-8": {"schema": {"properties": {"id_page": {"description": "Identificador del túnel", "type": "integer"}, "nom": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "type": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "publication": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "permalink": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "permission": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "boolean"}, "disable_indexation": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "boolean"}, "seo_description": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "seo_tags": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "seo_image": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "editor_level": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "facebook_pixel": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "boolean"}, "facebook_pixel_id": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "facebook_pixel_event": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "google_analytics": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "boolean"}, "google_analytics_id": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "google_optimize_id": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "string"}, "ligne": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}, "colonne": {"description": "Project-Id-Version: \nReport-Msgid-Bugs-To: \nLast-Translator: \nLanguage-Team: \nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\nPOT-Creation-Date: 2016-07-12 16:37+0200\nPO-Revision-Date: 2023-06-28 00:59+0200\nLanguage: es\nPlural-Forms: nplurals=2; plural=(n != 1);\nX-Generator: Poedit 3.3.1\n", "type": "integer"}}, "type": "object"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v2/users/{id_user}/": {"get": {"tags": ["Usuarios"], "summary": "Datos del usuario", "operationId": "api_v2_users_show", "parameters": [{"name": "id_user", "in": "path", "description": "ID de usuario", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"user_id": 1, "validated": 1, "contact_technique": 0, "fname": "Learny", "lname": "Box2", "email": "<EMAIL>", "newsletter": true, "rgpd": false, "rgpd_aff": false, "user_configs": {"pseudo": {"id": 17053, "name": "pseudo", "value": "", "date": "2019-04-23T12:22:30+0200"}, "adresse": {"id": 17054, "name": "adresse", "value": "", "date": "2019-04-23T12:22:30+0200"}}, "_string": "Learny Box2"}, "message": ""}}}}}}}, "/api/v2/users/email/{email}/": {"get": {"tags": ["Usuarios"], "summary": "Rechercher un utilisateur par email", "operationId": "api_v2_users_show_by_email", "parameters": [{"name": "email", "in": "path", "description": "Dirección de correo electrónico del usuario", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "Utilisateur non trouvé", "content": {"application/json": {"schema": {"properties": {"status": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "User not found."}}, "type": "object"}}}}}}}, "/api/v2/users/": {"post": {"tags": ["Usuarios"], "summary": "Créer un nouvel utilisateur pour un client", "operationId": "api_v2_users_create", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "fname", "lname"], "properties": {"email": {"type": "string", "format": "email"}, "fname": {"type": "string"}, "lname": {"type": "string"}, "client_id": {"type": "integer"}, "password": {"type": "string"}, "newsletter": {"type": "boolean"}, "rgpd": {"type": "boolean"}, "rgpd_aff": {"type": "boolean"}}, "type": "object"}}}}, "responses": {"201": {"description": "Utilisateur c<PERSON>é avec succès", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "400": {"description": "Données invalides"}}}}, "/api/v2/users/{id_user}/mails/": {"get": {"tags": ["Usuarios"], "summary": "Emails d'un utilisateur", "operationId": "api_v2_users_mails", "parameters": [{"name": "id_user", "in": "path", "description": "ID de usuario", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id": 1, "fname": "Learny", "email": "<EMAIL>", "sent": true, "error": false, "error_message": "", "open": true, "date": "2019-04-23T12:22:30+0200", "date_open": "2019-04-23T15:11:23+0200", "mail": {"id": 12, "subject": "Sujet de l'email", "from_nom": "LearnyBox", "from_email": "<EMAIL>", "sent": true, "error": false, "error_message": "", "date": "2019-04-23T12:22:30+0200"}}, "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"rel": "self", "href": "https://api.learnybox.com/api/v2/users/1/mails/?limit=100&offset=0"}}}}}}}}}, "/api/v2/webinaires/": {"get": {"tags": ["Seminarios en línea"], "summary": "Liste des auto-webinaires", "operationId": "api_v2_webinaires_list", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_webinaire": 146, "nom": "client 1", "jours": "['lundi','mardi','mercredi','jeudi','vendredi','samedi']", "nb_session": 5, "nb_date": 1, "horaires": "['10:00','12:00','14:00','18:00','20:00']", "feries": "['2017-02-22','2017-02-23','2017-02-24','2017-02-25','2017-02-26','2017-02-27','2017-02-28','2017-03-01','2017-03-02','2017-03-03','2017-03-04','2017-03-05']", "nb_jour_next": 0, "date_today": false, "date_today_from": "09:00", "date_today_to": "20:00", "video": "", "duree_video": "02:10:30", "disable_pause": false, "accroche": "", "description": "<p style='box-sizing: border-box; margin: 0px 0px 10px; font-size: 14px; font-family: Lato, sans-serif;'>Dans ce webinaire, vous allez apprendre :</p>\\r\\n\\r\\n<ul style='box-sizing: border-box; margin-top: 0px; margin-bottom: 10px; font-family: Lato, sans-serif; font-size: 14px;'>\\r\\n\\t<li style='box-sizing: border-box;'>promesse 1</li>\\r\\n\\t<li style='box-sizing: border-box;'>promesse 2</li>\\r\\n\\t<li style='box-sizing: border-box;'>promesse 3</li>\\r\\n</ul>\\r\\n\\r\\n<p style='box-sizing: border-box; margin: 0px 0px 10px; font-size: 14px; font-family: Lato, sans-serif;'>Inscrivez-vous en remplissant le formulaire ci-contre.</p>\\r\\n", "nb_auditeurs": 9999, "image_inscription": "", "afficher_participants": true, "delta_participants": 200, "simuler_participants": false, "simuler_participants_start": 0, "simuler_participants_min": 0, "simuler_participants_max": 0, "type": "publique", "type_inscription": "automatique", "email_inscription": false, "participation_unique": false, "page_confirmation": "defaut", "url_page_confirmation": "", "theme_inscription": "0", "theme_confirmation": "0", "theme_attente": "everwebinaire_attente", "theme_broadcast": "", "thumbnail": "", "autorepondeur": "", "email_rappel": false, "chat": "chat", "replay": true, "replay_auto": false, "replay_desactivation": 0, "replay_redirection": "", "redirection_url": "", "users": "", "timezone": "Europe/Paris", "etat": "enattente", "inscription_js": "", "inscription_js_head": "", "confirmation_js": "", "confirmation_js_head": "", "broadcast_js": "", "broadcast_js_head": "", "date": "2017-12-21T11:05:09+0100", "rgpd": false, "rgpd_text": "", "rgpd_aff": false, "rgpd_aff_text": "", "client": {"nom_client": "Learny Box"}, "webinaire_chats": []}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/webinaires/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/webinaires/{id_webinaire}/": {"get": {"tags": ["Seminarios en línea"], "summary": "Données d'un auto-webinaire", "operationId": "api_v2_webinaires_show", "parameters": [{"name": "id_webinaire", "in": "path", "description": "Identifiant d'un auto-webinaire", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_webinaire": 146, "nom": "client 1", "jours": "['lundi','mardi','mercredi','jeudi','vendredi','samedi']", "nb_session": 5, "nb_date": 1, "horaires": "['10:00','12:00','14:00','18:00','20:00']", "feries": "['2017-02-22','2017-02-23','2017-02-24','2017-02-25','2017-02-26','2017-02-27','2017-02-28','2017-03-01','2017-03-02','2017-03-03','2017-03-04','2017-03-05']", "nb_jour_next": 0, "date_today": false, "date_today_from": "09:00", "date_today_to": "20:00", "video": "", "duree_video": "02:10:30", "disable_pause": false, "accroche": "", "description": "<p style='box-sizing: border-box; margin: 0px 0px 10px; font-size: 14px; font-family: Lato, sans-serif;'>Dans ce webinaire, vous allez apprendre :</p>\\r\\n\\r\\n<ul style='box-sizing: border-box; margin-top: 0px; margin-bottom: 10px; font-family: Lato, sans-serif; font-size: 14px;'>\\r\\n\\t<li style='box-sizing: border-box;'>promesse 1</li>\\r\\n\\t<li style='box-sizing: border-box;'>promesse 2</li>\\r\\n\\t<li style='box-sizing: border-box;'>promesse 3</li>\\r\\n</ul>\\r\\n\\r\\n<p style='box-sizing: border-box; margin: 0px 0px 10px; font-size: 14px; font-family: Lato, sans-serif;'>Inscrivez-vous en remplissant le formulaire ci-contre.</p>\\r\\n", "nb_auditeurs": 9999, "image_inscription": "", "afficher_participants": true, "delta_participants": 200, "simuler_participants": false, "simuler_participants_start": 0, "simuler_participants_min": 0, "simuler_participants_max": 0, "type": "publique", "type_inscription": "automatique", "email_inscription": false, "participation_unique": false, "page_confirmation": "defaut", "url_page_confirmation": "", "theme_inscription": "0", "theme_confirmation": "0", "theme_attente": "everwebinaire_attente", "theme_broadcast": "", "thumbnail": "", "autorepondeur": "", "email_rappel": false, "chat": "chat", "replay": true, "replay_auto": false, "replay_desactivation": 0, "replay_redirection": "", "redirection_url": "", "users": "", "timezone": "Europe/Paris", "etat": "enattente", "inscription_js": "", "inscription_js_head": "", "confirmation_js": "", "confirmation_js_head": "", "broadcast_js": "", "broadcast_js_head": "", "date": "2017-12-21T11:05:09+0100", "rgpd": false, "rgpd_text": "", "rgpd_aff": false, "rgpd_aff_text": "", "client": {"nom_client": "Learny Box"}, "webinaire_chats": []}, "message": ""}}}}}}}, "/api/v2/webinaires/{id_webinaire}/chat/": {"get": {"tags": ["Seminarios en línea"], "summary": "Chat d'un auto-webinaire", "operationId": "api_v2_webinaires_chat_list", "parameters": [{"name": "id_webinaire", "in": "path", "description": "Identifiant d'un auto-webinaire", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_chat": 1, "prenom": "Learny", "email": "<EMAIL>", "message": "test message 1", "date": "2017-12-27T08:05:17+0100"}, {"id_chat": 2, "prenom": "Learny", "email": "<EMAIL>", "message": "test message 2", "date": "2017-12-27T08:06:11+0100"}], "message": "", "offset": 0, "limit": 100, "total": 2, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/webinaires/1/chat/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/webinaires/{id_webinaire}/inscrits/": {"get": {"tags": ["Seminarios en línea"], "summary": "Liste des inscrits d'un auto-webinaire", "operationId": "api_v2_webinaires_inscrits_list", "parameters": [{"name": "id_webinaire", "in": "path", "description": "Identifiant d'un auto-webinaire", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_inscrit": 1, "type": "participant", "enattente": false, "replay": false, "cpg": "", "aff": "", "date_inscr": "2018-01-05T15:06:02+0100", "date_replay": "-0001-11-30T00:00:00+0009", "webinaire_user": {"id_user": 1, "prenom": "Test", "email": "<EMAIL>", "telephone": "", "pays": "FR", "disabled": false, "newsletter": true, "date_webinaire": "2018-01-10T10:00:00+0100", "custom_fields": {"Quelle est votre profession ?": "Conférencier", "Comment avez-vous connu LearnyBox ?": "Internet"}}}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/webinaires/1/inscrits/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/webinaires/{id_webinaire}/inscrits/{id_inscrit}/": {"get": {"tags": ["Seminarios en línea"], "summary": "Données d'un inscrit à un auto-webinaire", "operationId": "api_v2_webinaires_inscrits_show", "parameters": [{"name": "id_webinaire", "in": "path", "description": "Identifiant d'un auto-webinaire", "required": true, "schema": {"type": "integer"}}, {"name": "id_inscrit", "in": "path", "description": "Identificador de un solicitante de registro", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_inscrit": 1, "type": "participant", "enattente": false, "replay": false, "cpg": "", "aff": "", "date_inscr": "2018-01-05T15:06:02+0100", "date_replay": "-0001-11-30T00:00:00+0009", "webinaire_user": {"id_user": 1, "prenom": "Test", "email": "<EMAIL>", "telephone": "", "pays": "FR", "disabled": false, "newsletter": true, "date_webinaire": "2018-01-10T10:00:00+0100", "custom_fields": {"Quelle est votre profession ?": "Conférencier", "Comment avez-vous connu LearnyBox ?": "Internet"}}}, "message": ""}}}}}}}, "/api/v2/webinaires/{id_webinaire}/messages/": {"get": {"tags": ["Seminarios en línea"], "summary": "Liste des messages d'un auto-webinaire", "operationId": "api_v2_webinaires_messages_list", "parameters": [{"name": "id_webinaire", "in": "path", "description": "Identifiant d'un auto-webinaire", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_message": 1, "prenom": "Test", "message": "test message", "date": "2017-02-02T21:31:12+0100", "webinaire_user": {"id_user": 1, "prenom": "Test", "email": "<EMAIL>", "telephone": "", "pays": "FR", "disabled": false, "newsletter": true, "date_webinaire": "2017-12-21T08:00:00+0100"}}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/webinaires/1/messages/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/webinaires/{id_webinaire}/participants/": {"get": {"tags": ["Seminarios en línea"], "summary": "Liste des participants d'un auto-webinaire", "operationId": "api_v2_webinaires_participants_list", "parameters": [{"name": "id_webinaire", "in": "path", "description": "Identifiant d'un auto-webinaire", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": [{"id_participant": 1, "type": "participant", "enattente": false, "replay": false, "cpg": "", "aff": "", "date_inscr": "2018-01-05T15:06:02+0100", "date_replay": "-0001-11-30T00:00:00+0009", "webinaire_user": {"id_user": 1, "prenom": "Test", "email": "<EMAIL>", "telephone": "", "pays": "FR", "disabled": false, "newsletter": true, "date_webinaire": "2018-01-10T10:00:00+0100"}}], "message": "", "offset": 0, "limit": 100, "total": 1, "_links": {"self": {"href": "https://api.learnybox.com/api/v2/webinaires/1/participants/?limit=100&offset=0", "rel": "self"}}}}}}}}}, "/api/v2/webinaires/{id_webinaire}/participants/{id_participant}/": {"get": {"tags": ["Seminarios en línea"], "summary": "Données d'un participant à un auto-webinaire", "operationId": "api_v2_webinaires_participants_show", "parameters": [{"name": "id_webinaire", "in": "path", "description": "Identifiant d'un auto-webinaire", "required": true, "schema": {"type": "integer"}}, {"name": "id_participant", "in": "path", "description": "ID de participante", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"status": true, "data": {"id_participant": 1, "type": "participant", "enattente": false, "replay": false, "cpg": "", "aff": "", "date_inscr": "2018-01-05T15:06:02+0100", "date_replay": "-0001-11-30T00:00:00+0009", "webinaire_user": {"id_user": 1, "prenom": "Test", "email": "<EMAIL>", "telephone": "", "pays": "FR", "disabled": false, "newsletter": true, "date_webinaire": "2018-01-10T10:00:00+0100"}}, "message": ""}}}}}}}}, "components": {"schemas": {"ApiResponse": {"type": "array", "items": {"properties": {"status": {"description": "Statut de la réponse (true ou false)", "type": "boolean"}, "data": {"description": "Données de la réponse", "type": "array", "items": []}, "message": {"description": "Message de la réponse", "type": "string"}}, "type": "object"}, "example": {"status": true, "data": [], "message": ""}}}}, "security": [{"httpBearer": []}], "tags": [{"name": "Token de autenticación", "description": ""}, {"name": "General", "description": ""}, {"name": "Cursos de formación", "description": ""}, {"name": "Afiliaciones", "description": ""}, {"name": "<PERSON><PERSON><PERSON>", "description": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, {"name": "Apoyo técnico", "description": ""}, {"name": "Transacciones", "description": ""}, {"name": "Usuarios", "description": ""}]}