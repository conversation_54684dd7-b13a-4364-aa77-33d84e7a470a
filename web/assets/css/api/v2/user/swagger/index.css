html {
    box-sizing: border-box;
    overflow: -moz-scrollbars-vertical;
    overflow-y: scroll;
}

*,
*:before,
*:after {
    box-sizing: inherit;
}

body {
    margin: 0;
    background: #fafafa;
}

.swagger-ui .wrapper {
    padding: unset;
}

.swagger-ui .block {
    padding: 16px;
}

.swagger-ui pre {
    background-color: unset;
    border: unset;
}

.swagger-ui .topbar-wrapper {
    padding: 18px;
}

.swagger-ui .scheme-container {
    padding: 0;
}

.swagger-ui .servers-title {
    display: inline-block;
    margin-bottom: 5px;
}