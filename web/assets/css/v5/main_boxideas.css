#Ideas{
	margin-top: 0px;
}
#idea{
	margin-top: 50px;
}
#idea .section_header {
	margin: 0px 50px 0px 0px;
	float: left;
}
#idea hr.right {
	border-bottom: 2px solid #ffffff;
}

#idea .box{
    background-color: white;
	border: 1px solid #CCC;
	border-radius: 16px;
	float: none;
	margin: 0 auto;
	width: 100%;
	margin-bottom: 25px;
}
#idea .box .head{
	background-color: #F4F4F4;
    border-bottom: 1px solid #D6D6D6;
    border-radius: 4px 4px 1px 0;
    display: inline-block;
    margin-bottom: 37px;
    padding: 25px 0 25px;
    width: 100%;
}
#idea .box.nav {
	border-radius: 16px;
	border: 1px solid #CCC;
	margin-top: 0px;
}
#idea .box.nav .head {
	background-color: #FFFFFF;
	background-repeat: repeat-x;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#545c68cc', endColorstr='#313740b3', GradientType=0);
	margin-bottom: 0px;
}
#idea .box.nav .navigation {
	padding: 24px;
	margin-top: -5px;
}
#idea .box.nav .navigation ul {
	list-style: none;
	margin: 0;
	padding: 0;
	z-index: 1;
	display: block;
}

#idea .box.nav .navigation li a {
	padding: 2px 0;
	display: block;
    color: #333333;
}

#idea .box.nav .navigation li a:hover {
	text-decoration: none;
	background: white;
	color:black
}

#idea .box.nav .navigation li.active > a,
#idea .box.nav .navigation li.active > a:hover {
	background: white;
	color:black
}

#idea .box .division{
	display: inline-block;
    margin: 17px 0 23px;
    position: relative;
    text-align: center;
    width: 100%;
}
#idea .box .division hr{
	border-color: #E2E2E4;
    border-width: 1px;
    margin: 0;
    position: absolute;
    width: 40%;
}
#idea .box .division hr.left{
	top: 13px;
}
#idea .box .division hr.right{
	bottom: 6px;
    right: 0;
}
#idea .box .division span{
	color: #666666;
	font-size: 18px;
}

#idea form{
	margin: 0px;
}

#idea #addCommentForm .form-actions {
	background: unset;
}
#idea #Comments {
	min-height: 150px;
}
#idea .comment {
	padding-top: 10px;
	border-top: 1px solid #dee2e7;
	border-top: 1px solid rgba(0,0,0,0.1);
	margin-bottom: 20px;
}
#idea .comment-reponse {
	padding-top: 10px;
	border-top: 1px solid #dee2e7;
	border-top: 1px solid rgba(0,0,0,0.1);
	margin-bottom: 20px;
	margin-left: 70px;
}

#idea .comment .linkreponse {
	font-size: 12px;
	color: #999;
}

/* Affichage d'un ticket */
#idea .box .head {
	background-color: #F4F4F4;
    border-bottom: 1px solid #D6D6D6;
    border-radius: 4px 4px 1px 0;
    display: inline-block;
    padding: 24px;
    width: 100%;
    margin-bottom: 0;
}
#idea .box .head h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
}
#idea .box .head h4 {
    display: flex;
    align-items: center;
	justify-content: space-between;
    gap: 8px;
	font-weight: normal;
	color: #646464;
	font-size: 21px;
	margin: 0px;
	text-align: left;
}
#idea .box .head h4 .btn {
    white-space: nowrap;
}
#idea .box .btn:has(i) {
	display: inline-flex;
	align-items: center;
	gap: 6px;
}
#idea .box .box-content {
	padding: 24px;
	border: none;
}

#idea .box .box-content .idea-description {
	line-height: 1.1 !important;
}
#idea .box .box-content .idea-description h1 {
	line-height: 1.1 !important;
	font-size: 36px !important;
}
#idea .box .box-content .idea-description h2 {
	line-height: 1.1 !important;
	font-size: 30px !important;
}
#idea .box .box-content .idea-description h3 {
	line-height: 1.1 !important;
	font-size: 24px !important;
}
#idea .box .box-content .idea-description h4 {
	line-height: 1.1 !important;
	font-size: 18px !important;
}

/* Formulaire d'ouverture d'un ticket */
#idea .box .form{
	margin: 0 auto;
	width: 83%;
}
#idea .box .form .input-prepend,
.input-prepend {
	background: #fff;
	text-align: center;
	padding: 0px;
	margin-bottom: 0px;
	margin-left: 20px
}
#idea .box .form .add-on {
	background: #fff;
	padding: 5px;
}
#idea .box .form .add-on i{
	opacity: .5;
}
#idea .box .form label{
	width:160px;
}

#idea .box .form input[type="text"],
#idea .box .form input[type="email"],
#idea .box .form input[type="password"] {
	border-color: #DBDBDB #EAEAEA #EAEAEA #DBDBDB;
    border-left: 1px solid #DBDBDB;
    border-style: solid;
    border-width: 1px;
    font-size: 14px;
    padding:5px;
    padding-left: 10px
}
#idea .box .form button[type="submit"]{
    margin-top: 0px;
}


#idea .box .form-accueil {
	text-align: center;
}
#idea .box .form-accueil .input-prepend {
	width:100%;
	height: 30px;
	margin-left: 0px;
	margin-bottom: 10px;
	padding: 10px 0px;
}
#idea .box .form-accueil .add-on {
	background: #fff;
	padding: 10px;
}

.idea-content {
	display: block;
	padding: 10px;
	margin: 0px;
	margin-bottom: 20px;
	font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
	font-size: 13px;
	line-height: 16px;
	background-color: #f5f5f5;
	border: 1px solid #ccc;
	border: 1px solid rgba(0, 0, 0, 0.15);
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	color: #333333;
}

.idea-content.alert {
	background-color: #fffee1 !important;
	color: #daac50 !important;
	border: 1px solid #f5c056 !important;
}



/* Votes */
#idea .box .vote {
	width:150px;
	height: 150px;
	text-align: center;
	padding: 0;
	position: relative;
	padding-bottom: 10px;
	margin-right: 10px;
	display: flex;
	flex-direction: column;
}
.vote .btn {
	padding: 16px !important;
	width: 48px;
	height: 48px;
	border:0;
}

.vote button.btn-info {
	width:100px;
	font-size: 14px;
	line-height: 16px;
	cursor: not-allowed
}
.vote button.btn-inverse {
	width:100px;
	font-size: 14px;
	line-height: 16px;
	cursor: not-allowed
}
.vote .hasVoted,
.vote .cannotVote {
	display: flex;
	justify-content: center;
	align-items: center;
	line-height: var(--app-font-size);
	padding: var(--app-btn-padding);
	border-radius: var(--app-btn-radius);
	height: var(--app-btn-height);
}
.vote .hasVoted {
	border: 1px solid var(--app-color-success);
	color: var(--app-color-success);
}
.vote .cannotVote {
	border: 1px solid var(--app-color-danger);
	color: var(--app-color-danger);
}

/* Portrait tablet to landscape and desktop */
@media (min-width: 768px) and (max-width: 979px) {
	#idea .box .division{
	}
	#idea .box .division hr{
	}
	#idea .box .division hr.left{
	}
	#idea .box .division hr.right{
	}
}
@media (max-width: 979px) {

}

@media (min-width: 980px) {
}

@media (min-width: 480px) and (max-width: 768px) {
	#idea .box .form input[type="text"],
	#idea .box .form input[type="email"],
	#idea .box .form input[type="password"] {
		border-color: #DBDBDB #EAEAEA #EAEAEA #DBDBDB;
	    border-left: 1px solid #DBDBDB;
	    border-style: solid;
	    border-width: 1px;
	    font-size: 16px;
	    height: 42px;
	    padding:5px;
	    width: 82%;
	    padding-left: 10px
	}
}

@media (max-width: 480px) {
	#idea .box .form input[type="text"],
	#idea .box .form input[type="email"],
	#idea .box .form input[type="password"] {
		border-color: #DBDBDB #EAEAEA #EAEAEA #DBDBDB;
	    border-left: 1px solid #DBDBDB;
	    border-style: solid;
	    border-width: 1px;
	    font-size: 16px;
	    height: 42px;
	    padding:5px;
	    width: 82%;
	    padding-left: 10px
	}

}
/* Large desktop */
@media (min-width: 1200px) {
}


#CreateIdeaForm #categorie {
    min-width: 300px;
}
