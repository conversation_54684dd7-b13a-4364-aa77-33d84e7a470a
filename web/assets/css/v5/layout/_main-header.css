#main-header {
    position: fixed;
    width: 100%;
    display: flex;
    align-items: center;
    height: 64px;
    border-bottom: var(--app-layout-border);
    background: #FFFFFF;
    z-index: 1000;
}
#main-header.main-header-admin {
    width: unset;
}

#main-header #main-logo {
    display: flex;
    align-items: center;
    position: relative;
    width: 248px;
    height: 100%;
    border-right: var(--app-layout-border);
}

#main-header #main-logo a {
    display: flex;
    justify-content: center;
}

#main-header #main-logo img {
    width: 50%;
}

#main-header #main-navbar {
    flex: 5;
    height: 100%;
    min-height: 64px;
    margin: 0;
    border: 0;
    border-radius: 0;
    display: flex;
    gap: 25px;
}

#main-header #main-search {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100%;
    padding-right: 0;
    flex: 1;
}

#main-header #main-notif {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100%;
    list-style: none;
    padding: 0;
}

#main-header #main-notif i {
    color: var(--app-color-grey-medium);
    font-size: 14px;
}

#main-header #main-responsive-menu-button {
    display: flex;
    align-items: center;
    justify-content: center;
}

.navbar-toggle {
    margin: 0 0 0 12px;
}

@media (max-width: 1200px) {
    #main-header #main-logo:not(.not-hide) {
        display: none;
    }
}

@media (max-width: 768px) {
    #main-header #main-navbar {
        gap: 12px;
    }
}

@media (min-width: 1200px) {
    .navbar-toggle {
        display: none;
    }
}
