#main-composition {
    font-family: var(--font-family);
}
#main-composition .main_color0 {
    color: var(--primary-color);
}
#main-composition .main_color1 {
    color: var(--secondary-color);
}
#main-composition .main_color2 {
    color: var(--tertiary-color);
}
#main-composition .maincontainer .btn {
    background: var(--primary-color);
    border-radius: var(--button-border-radius);
    border: none;
}
#main-composition .h1,
#main-composition .h2,
#main-composition .h3,
#main-composition .h4,
#main-composition .h5,
#main-composition .h6,
#main-composition h1,
#main-composition h2,
#main-composition h3:not(.menu-sequence-page-title),
#main-composition h4,
#main-composition h5,
#main-composition h6 {
    font-family: var(--font-family);
}
#main-composition h1 {
    font-size: var(--h1-font-size);
    color: var(--h1-font-color);
    line-height: var(--h1-line-height);
    text-transform: var(--h1-text-transform);
    font-weight: var(--h1-font-weight);
}
#main-composition h2 {
    font-size: var(--h2-font-size);
    color: var(--h2-font-color);
    letter-spacing: 0;
    line-height: var(--h2-line-height);
    text-transform: var(--h2-text-transform);
    font-weight: var(--h2-font-weight);
}
#main-composition h3:not(.menu-sequence-page-title) {
    font-size: var(--h3-font-size);
}

.line_theme1 {
    background: var(--line-theme1);
}
.line_theme2 {
    background: var(--line-theme2);
}
.line_theme3 {
    background: var(--line-theme3);
}
.line_theme4 .line_overlay {
    background: var(--line-theme4);
}

.countdown-timer-style-6 .countdown-amount {
    color: var(--primary-color);
}

.temoignage,
.temoignage p,
.temoignage h4 {
    color: var(--tertiary-color);
}
.temoignage h4 {
    font-weight: bold;
}

/*
 * Formations
 */
div.index {
    background: var(--background-image);
}
.index-container {
    background-image: var(--line-theme4);
}

.navbar.navbar-top {
    background: transparent;
}
.navbar .nav > li > a.active:after,
.navbar .nav > li > a:hover:after {
    color: var(--primary-color);
}
.navbar-inner, #footer {
    background: var(--navbar-inner-background);
}

.multilevelpushmenu_wrapper h2,
.multilevelpushmenu_wrapper h2.home {
    background: var(--primary-color);
}
.multilevelpushmenu_wrapper li.active {
    background-color: var(--primary-color);
}
.multilevelpushmenu_wrapper li:hover {
    background-color: var(--primary-color);
}
.multilevelpushmenu_wrapper li.page > a:before {
    border-color: var(--primary-color);
}
.multilevelpushmenu_wrapper li.page > a i {
    color: var(--primary-color);
}
.multilevelpushmenu_wrapper .backItemClass {
    background: var(--primary-color);
}
.multilevelpushmenu_wrapper > .levelHolderClass h2.home:before {
    border-color: var(--primary-color-border);
}
.multilevelpushmenu_wrapper .backItemClass a:before {
    border-color: var(--primary-color-border);
}
#multilevelmenu2 li a i {
    color: var(--primary-color);
}
#multilevelmenu2 li a:before,
#multilevelmenu2 li a.menu-page:before {
    border-color: var(--primary-color);
}

.header-progression .wrapper-btn .btn {
    background: var(--primary-color);
}
.header-progression .formation-progress i,
.header-progression .formation-time i {
    color: var(--primary-color);
}

.progress .progress-bar.progress-bar-success {
    background: var(--primary-color);
}

.modules .element .btn-access {
    background: var(--primary-color);
}
#formations table tr td .btn {
    background: var(--primary-color);
    box-shadow: none;
    border-radius: var(--button-border-radius);
}

.menu-sequence .menu-prev-link a,
.menu-sequence .menu-next-link a {
    border-color: var(--primary-color);
    color: var(--primary-color);
}
.menu-sequence .module .progress .progress-bar.progress-bar-success {
    background: var(--primary-color);
}

/*
 * Site
 */
.sidebar hr {
    border-bottom: 2px solid #797979;
}
.sidebar-search input {
    background: white;
    border-radius: 0;
    border: 2px solid #d2d2d2;
    border-right: none;
    box-shadow: none;
}
#main-composition .sidebar-search .btn {
    background: white;
    color: #1f2020;
    border: 2px solid #d2d2d2;
    border-left: none;
}
.sidebar .recent_posts li a {
    font-size: 18px;
}

/*** ARTICLES ***/
.blog-post .post_content .social {
    border: none;
}
.comments #addCommentForm .talkbubble {
    border-radius: 0px;
}
.comments #addCommentForm .talkbubble:before {
    display: none;
}
.comments .img-circle {
    border-radius: 0;
}
.comments .btn-show-answers {
    background: transparent !important;
    color: #1f2020;
    border: 1px solid #d2d2d2 !important;
    margin-left: 70px;
}
#shownextelements a {
    padding: 15px;
    border-radius: 0;
    font-size: 18px;
}
.comments .form-actions .btn {
    padding: 15px 45px;
    border-radius: 0;
    font-size: 18px;
}
