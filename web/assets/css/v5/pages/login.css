body:before {
    content: "";
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    background-color: var(--app-color-default);
}

.container #content {
    padding:0;
}

.navbar {
    background: white;
    border-radius: 0;
    height: 66px;
    position: fixed;
    width: 100%;
    z-index: 100;
}
.navbar > .row.mainmenu {
    margin-left: 0;
    margin-right: 0;
    padding: 20px 9px 20px 9px;
}
.navbar > .row.mainmenu > .right-actions {
    display: none;
}
#password {
    display: none;
}

.card.card-login {
    position: absolute;
    width: 440px;
    height: auto;
    top: calc(75px + 10%);
    left: 50%;
    margin-left: -220px;
    padding: 40px 40px 40px 40px;
    color: #34364B;
}
.card.card-login:hover {
    cursor: default;
}
.card.card-login .card-login-header {
    margin-bottom: 34px;
}
.card.card-login .card-login-header img.card-login-header-logo {
    height: auto;
    width: auto;
    max-height: 28px;
    max-width: 100%;
}
.card.card-login .card-login-header .card-login-header-title {
    font-weight: 400;
    font-size: 24px;
    padding-top: 26px;
    margin-bottom: 5px;
    line-height: 24px;
}
.card.card-login .card-login-header .card-login-header-title:first-child {
    padding-top: 4px;
    margin-bottom: 42px;
}
.card.card-login .card-login-header .card-login-header-subtitle {
    font-size: 16px;
    margin-bottom: 5px;
}
.card.card-login button[type="submit"] {
    margin-top: 16px;
    font-weight: 400;
    outline: none;
}
.card.card-login .card-login-footer a:not(.btn) {
    color: var(--app-color-grey-medium);
    text-decoration: none;
    cursor: pointer;
    border-bottom: 1px solid var(--app-color-grey-medium);
}
.card.card-login .card-login-footer p {
    margin-top: 15px;
    margin-bottom: 0;
}
.card.card-login .card-login-footer #reset_logs_connexion {
    font-size: 12px;
    line-height: 12px;
}
.card.card-login.card-reset-password {
    width: 440px;
    margin-left: -230px;
    padding: 32px 40px 32px 40px;
}

.card.card-login > .current-user {
    border-radius: 4px;
    color: var(--app-color-dark);
    cursor: pointer;
    display: block;
    overflow: hidden;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.card.card-login > .current-user > .current-user-change-account {
    color: var(--app-color-grey-medium);
    margin-bottom: 18px;
}
.card.card-login > .current-user > .current-user-change-account i {
    margin-right: 1px;
    font-size: 12px;
}
.card.card-login > .current-user > .current-user-content {
    display: flex;
    margin-bottom: 2px;
}
.card.card-login > .current-user > .current-user-content > .current-user-avatar > .img {
    width: 40px;
    height: 40px;
    background-position: center;
    background-size: cover;
}
.card.card-login > .current-user > .current-user-content > .current-user-infos {
    width: calc(100% - 40px);
    padding-top: 0;
    padding-left: 8px;
}
.card.card-login > .current-user > .current-user-content > .current-user-infos > .current-user-email {
    font-size: 12px;
    padding-top: 2px;
}

.card.card-login > .current-user > .current-user-content > .current-user-infos > .current-user-name {
    font-size: 14px;
}

.card.card-login > #password > form > .input.password {
    margin-top: 12px;
}

.card.card-login > #password > form > .input.password > div {
    position: relative;
}

.card.card-login input[name="password"] {
    padding-right: 40px;
}

.card.card-login input[name="password"] + i {
    font-size: 14px;
    color: var(--app-input-placeholder-color);
    cursor: pointer;
    position: absolute;
    bottom: 17px;
    right: 16px;
}

.card.card-login > #users > ul.users-list {
    height: auto;
    overflow-y: auto;
    list-style: none;
    padding: 0;
    margin-bottom: 2px;
    margin-left: -8px;
    margin-right: -8px;
}
.card.card-login > #users > ul.users-list > li {
    display: flex;
    width: 100%;
    padding: 8px;
    margin-top: 8px;
    border-radius: 8px;
}
.card.card-login > #users > ul.users-list > li:first-child {
    margin-top: 3px!important;
}
.card.card-login > #users > ul.users-list > li div {
    width: 100%;
}
.card.card-login > #users > ul.users-list > li > .avatar {
    width: 40px;
}
.card.card-login > #users > ul.users-list > li > .avatar .avatar-img,
.card.card-login > #users > ul.users-list > li > .avatar img {
    width: 40px;
    height: 40px;
    border-radius: 100%;
    color: #4b5175;
    text-align: center;
    line-height: 48px;
    background-color: #F5F5F6;
    background-position: center;
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: center;
}
.card.card-login > #users > ul.users-list > li > .avatar .avatar-img > i {
    font-size: 16px;
    color: var(--app-color-grey-medium);
    margin-left: 2px;
}
.card.card-login > #users > ul.users-list > li > .info {
    width: calc(100% - 40px);
    padding-top: 0;
    padding-left: 8px;
}
.card.card-login > #users > ul.users-list > li:hover {
    cursor: pointer;
    background-color: #FBFBFB;
}
.card.card-login > #users > ul.users-list > li > .info {
    font-weight: 400;
}
.card.card-login > #users > ul.users-list > li > .info .info-name {
    font-size: 14px;
}
.card.card-login > #users > ul.users-list > li > .info .info-email {
    font-size: 12px;
    padding-top: 2px;
}
.card.card-login > #users > ul.users-list > li.other > .info {
    display: flex;
    align-items: center;
}

input.mfa {
    font-size: 24px;
    height: auto;
    padding: 16px;
    text-align: center;
}
