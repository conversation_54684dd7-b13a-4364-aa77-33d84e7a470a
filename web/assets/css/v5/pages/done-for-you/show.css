.service-container .form {
    margin: 1em 0;
    border: none;
    background-color: #F9FAFB;
}

.service-container .form.bg-white {
     background-color: #ffffff;
 }

.service-container .section-title {
    margin: 2em 0 1.5em 0;
}

/***************************/
/******** TIMELINE *********/
/***************************/

#dfy-timeline {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    padding: 0;
}

#dfy-timeline .panel-body {
    padding: 15px 25px 15px 20px;
}

.timeline-dfy {
    list-style-type: none;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 0;
    margin: 0;
}

.timeline-dfy li {
    flex: 1;
    max-width: 200px;
    position: relative;
}

.timeline-dfy li.first-step {
    flex: 0 0 auto;
    width: 50px;
}

li .timeline-title {
    color: #cfcfcf;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

/* Premier titre (<PERSON>mande) */
li.first-step .timeline-title {
    position: absolute;
    left: -24px;
    top: -25px;
    text-align: left;
    align-items: flex-start;
}

/* Titres du milieu (Offre et Réalisation) */
li:not(.first-step):not(:last-child) .timeline-title {
    position: absolute;
    left: 80%;
    transform: translateX(-50%);
    top: -25px;
    width: 100%;
}

/* Dernier titre (Fin) */
li:last-child .timeline-title {
    position: absolute;
    right: 0;
    top: -25px;
    text-align: right;
    align-items: flex-end;
}

.status {
    display: flex;
    justify-content: center;
    position: relative;
}

.status:before {
    content: '';
    width: 15px;
    height: 15px;
    background-color: #F0F1F5;
    border-radius: 25px;
    position: absolute;
    top: -6px;
    left: 96%;
    z-index: 1;
}

/* Point de la première étape */
.first-step .status:before {
    left: 0;
}

/* Points des étapes du milieu */
li:not(.first-step):not(:last-child) .status:before {
    left: 80%;
    transform: translateX(-50%);
}

/* Point de la dernière étape */
li:last-child .status:before {
    left: auto;
    right: 0;
}

.li .status .progress {
    width: 100%;
    height: 4px;
    margin: 0;
    background: #F0F1F5;
}

.li.complete .timeline-title {
    font-weight: 400;
    color: #34364B;
}

.li.complete .timeline-title.canceled {
    color: #A6AAAF;
    font-weight: 400;
}

.li.complete .status .progress .progress-bar {
    width: 100%;
    background-color: var(--app-color-primary);
}

.li.complete .status:before {
    background-color: var(--app-color-primary);
}

.li.canceled .status:before,
.li.canceled .status .progress .progress-bar {
    background-color: #A6AAAF;
}

@media (max-width: 768px) {
    #dfy-timeline {
        max-width: 100%;
    }
    
    .timeline-dfy {
        padding: 0 15px;
    }
    
    .timeline-dfy li {
        max-width: 150px;
    }
    
    li .timeline-title {
        font-size: 12px;
    }
}

/***************************/
/********** ALERT **********/
/***************************/

.panel-body .alert {
    width: 65%;
    margin: 0 0 20px 0;
}

/***************************/
/********* SUMMARY *********/
/***************************/

.panel-body .description {
    color: #90959A;
}

.panel-body .summary-panel {
    width: 100%;
}

.panel-body .summary-panel span:not(.help-block) {
    color: #34364B;
}

.panel-body label {
    color: #34364B;
}

/***************************/
/********** TABLE **********/
/***************************/

.panel-body table {
    margin-top: 2em;
    margin-bottom: 0;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
}

.panel-body table tbody tr th {
    padding: 15px 24px;
}

/***************************/
/********** FORM ***********/
/***************************/

.panel-body .panel-form-container,
.panel-sub-container {
    background-color: #F9FAFB;
    padding: 24px 24px;
    margin-top: 25px;
    margin-bottom: 7px;
    border: 1px solid #ddd
}

.panel-body .panel-form-container .form-title {
    margin-top: 0;
    margin-bottom: 20px;
    font-weight: normal;
}

.panel-body .panel-form-container textarea {
    width: 50%;
}

input.datepicker {
    width: 60%;
}

/***************************/
/********* BUTTONS *********/
/***************************/

.panel-actions-container a,
.panel-actions-container button {
    margin-top: 25px;
}

/***************************/
/********** MODAL **********/
/***************************/

.modal .modal-dialog .modal-content {
    text-align: center;
    width: 65%;
}

.modal-form-container .modal-confirm {
    text-align: center;
}

.modal-form-container .modal-header {
    font-size: 18px;
    padding: 24px 48px;
}

.modal-form-container .modal-body {
    font-size: 16px;
}

.modal-form-container .modal-body .group-button {
    padding-top: 12px;
    text-align: center;
}

/***************************/
/********* RATING **********/
/***************************/

#stars {
    width: 45%;
    cursor: pointer;
}

.rating-stars ul {
    list-style-type:none;
    padding:0;
}
.rating-stars ul > li.star,
.rating-stars ul > li.star-rated {
    display:inline-block;
}

.rating-stars ul > li.star > i.fa {
    font-size:1.5em;
    color: lightgrey;
}

.rating-stars ul > li.star.hover > i.fa,
.rating-stars ul > li.star.selected > i.fa,
.rating-stars ul > li.star-rated.selected > i.fa{
    color:#F2BE46;
}
