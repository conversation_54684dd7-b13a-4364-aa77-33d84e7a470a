#content {
    /*background: rgb(71, 80, 92);*/
}
.top-page-title h3, .top-page-title h4 {
    color: #FFFFFF;
}

#sequence_steps_content {
    margin: 40px 0 0 0;
    position: relative;
}

#sequence_steps_navigation {
    display: flex;
    flex-wrap: wrap;
    height: 70px;
    margin: 0 0 43px 0;
    overflow-x: auto;
    overflow-y: hidden;
    padding-left: 3px;
    white-space: nowrap;
}

#sequence_steps_navigation_btns_container {
    font-size: 0px; /* For display inline-block margin fix */
    left: 0;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0 0 8px 0px;
    width: 100%;
}

.sequence-step-navigation-title {
    color: #90959A;
    font-size: 10px;
    font-weight: 400;
    letter-spacing: 0.6px;
    line-height: 20px;
    margin-left: 0;
    padding-left: 3px;
    text-transform: uppercase;
}

.sequence-step-navigation-title .tooltip-help-video {
    width: 50px;
    height: 48px;
    line-height: 48px;
    font-size: 15px;
    display: inline-block;
    margin-left: 10px;
}


.sequence-step-navigation-btn-wrapper {
    display: inline-block;
    font-size: 13px;
    height: 48px;
    margin-right: 4px;
    vertical-align: top;
    white-space: normal;
    width: 64px;
}

.sequence-step-navigation-btn {
    background-color: #FFFFFF;
    border-radius: 4px;
    box-shadow: 0 1px 4px 0 rgba(20, 27, 58, 0.14);
    color: #A0A5AB;
    display: inline-block;
    height: 48px;
    padding: 15px 0px;
    text-align: center;
    text-decoration: none;
    width: 64px;
}

.sequence-step-navigation-btn:focus,
.sequence-step-navigation-btn:hover {
    background-color: #FFFFFF;
    box-shadow: 0 2px 6px 0 rgba(20,27,58,0.2);
    color: #34364B;
    text-decoration: none;
}

.sequence-step-navigation-btn:not(.active).disabled {
    background-color: #EFEFF1;
    box-shadow: none;
    pointer-events: auto;
    cursor: pointer;
}

.sequence-step-navigation-btn:not(.active).disabled .sequence-step-navigation-btn-position,
.sequence-step-navigation-btn:not(.active).disabled i {
    color: #34364B;
    opacity: 0.3;
}

.sequence-step-navigation-btn.moved .sequence-step-navigation-btn-position,
.sequence-step-navigation-btn:not(.active).disabled.moved .sequence-step-navigation-btn-position {
    color: #8289FB;
    opacity: 1;
}

.sequence-step-navigation-btn i {
    margin-left: 10px;
    vertical-align: middle;
}

.sequence-step-navigation-btn.active {
    background-color: var(--app-color-primary);
    color: #FFFFFF;
}

.sequence-step-navigation-btn-placeholder {
    background-color: rgba(130,137,251,0.1);
    border: 1px solid #8289FB;
    border-radius: 4px;
    box-sizing: border-box;
    display: inline-block;
    height: 48px;
    width: 64px;
}

#sequence_steps_cards {
    display: flex;
    flex-wrap: wrap;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0 0 40px 0;
    white-space: nowrap;
}
#sequence_steps_cards_container {
    left: 0;
    overflow-x: scroll;
    overflow-y: hidden;
    position: absolute;
    width: 100%;

}
.step-card-wrapper {
    display: inline-block;
    margin-right: 20px;
    vertical-align: top;
    white-space: normal;
    width: 360px;
}

.step-card-wrapper:last-child {
    margin-right: 360px;
}

.step-card-placeholder {
    background-color: rgba(130,137,251,0.1);
    border: 1px solid #8289FB;
    border-radius: 4px;
    box-sizing: border-box;
    display: inline-block;
    height: 845px;
    margin-right: 20px;
    width: 363px;
}

.step-card {
    background-color: #FFFFFF;
    border-radius: 4px;
    box-shadow: 0 1px 4px 0 rgba(20,27,58,0.14);
    min-height: 850px;
}
.step-card.step-card-end {
    min-height: auto;
    height: 126px;
}

.step-card.hovered {
    box-shadow: 0 2px 6px 0 rgba(20,27,58,0.2);
}

.step-card-header {
    cursor: grab;
    padding: 12px;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.step-card.active .step-card-header {
    border-left: 4px solid var(--app-color-primary);
    border-top-left-radius: 4px;
}

.step-card-header-delay {
    background-color: #212B36;
    border-radius: 4px;
    color: #8F959B;
    font-size: 13px;
    height: 48px;
    padding: 15px 0;
    text-align: center;
    width: 64px;
}

.step-card-header-delay.disabled {
    background-color: #EFEFF1;
}

.step-card-header-delay i {
    color: #FFFFFF;
    margin-left: 10px;
    vertical-align: middle;
}

.step-card-header-delay.disabled .step-card-position,
.step-card-header-delay.disabled i {
    color: #34364B;
    opacity: 0.3;
}

.step-card-header-title {
    color: #34364B;
    font-size: 13px;
    font-weight: 500;
    line-height: 24px;
}

.step-card-header-title a,
.step-card-header-title a:hover,
.step-card-header-title a:focus {
    color: #34364B;
}

.step-card-header-subject,
.step-card-header-subject a,
.step-card-header-subject a:hover {
    color: #90959A;
    font-size: 11px;
    height: 24px;
    line-height: 16px;
    overflow: hidden;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.step-card-header .btn {
    padding: 0;
}

.step-card .reorder {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    border-radius: 4px;
}

.subject-mail-editable {
    font-size: 16px;
    font-weight: bold;
    margin-bottom : 10px;
}

.subject-mail-editable input,
.subject-mail-editable input:focus {
    border: none;
    border-radius: 0px;
    box-shadow: none;
    font-size: 11px;
    font-weight: 500;
    line-height: 18px;
    padding: 11px 17px;
}

.step-card-header-stats {
    background-color: #F9FAFB;
    border-top: 1px solid #EFEFF1;
    border-bottom: 1px solid #EFEFF1;
    box-sizing: border-box;
    cursor: grab;
    padding: 7px 17px;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.step-card-header-stats-value {
    color: #34364B;
    font-weight: 500;
}

.step-card-content {
    padding: 0 0 70px 0;
}

.step-card-content ul.nav-tabs li {
    background-color: #F9FAFB;
    border-bottom: 1px solid #EFEFF1;
    box-shadow: -1px 0px 0px 0px #EFEFF1;
    box-sizing: border-box;
}

.step-card-content ul.nav-tabs li.active {
    box-sizing: border-box;
    /*border-top-width: 0;
    border-right-width: 0;
    border-bottom-width: 0;
    border-right: 1px solid #EFEFF1;*/
    border: none;
    border-bottom: 4px solid var(--app-color-primary);
    background-color: #FFFFFF;
}

.step-card-content ul.nav-tabs li a,
.step-card-content ul.nav-tabs li a:hover,
.step-card-content ul.nav-tabs li a:focus {
    border: none !important;
    border-radius: 0;
    color: #34364B;
    cursor: pointer;
    font-size: 13px;
    line-height: 24px;
    text-align: center;
    padding: 7px 15px;
}

.step-card-content ul.nav-tabs li:not(.active) a {
    opacity: 0.5;
}

.step-card-content ul.nav-tabs li:not(.active) a:hover,
.step-card-content ul.nav-tabs li:not(.active) a:focus {
    background-color: #F9FAFB;
}

.step-card-content .subjectmailp,
.step-card-content .subject-mail-editable input,
.step-card-content .subject-mail-editable input:focus {
    border-bottom: 1px solid #EFEFF1;
    color: #34364B;
    font-size: 11px;
    font-weight: 500;
    line-height: 18px;
    padding: 11px 17px;
}

.step-card-content .step-card-content-subject-condition {
    border-bottom: 1px solid #EFEFF1;
    color: #34364B;
    font-size: 13px;
    font-weight: 500;
    line-height: 18px;
    padding: 11px 17px;
}

.step-card-content-element {
    color: #2E3845;
    font-size: 12px;
    margin: 16px;
}

.step-card-content-element img {
    height: auto;
}

/* Overriding edited html elements to force them to stay in the sequence display container */
.step-card-content-element * {
    max-width: 100% !important;
}
.step-card-content-element > .step-card-content-element-action-values > div.label {
    display: block!important;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-bottom: 5px;
}

.step-card-content-element-sms {
    position: relative;
    background-color: #4A90E2;
    border-radius: 8px;
    color: #FFFFFF;
    padding: 16px;
}

.step-card-content-element-sms::after {
    border: solid transparent;
    border-bottom-color: #4A90E2;
    border-left-color: #4A90E2;
    border-width: 5px;
    bottom: 3px;
    content: " ";
    height: 0;
    left: 99%;
    margin-left: -1px;
    pointer-events: none;
    position: absolute;
    width: 0;
}

.step-card-content-element-sms-form .label-count {
    color: #90959A;
    float: right;
    font-size: 11px;
    letter-spacing: 0;
    line-height: 16px;
    margin-top: 4px;
    text-align: right;
}

.step-card-content-element-action {
    border-bottom: 1px solid #DDDDE1;
    padding-bottom: 16px;
}

.step-card-content-element-action i {
    font-size: 14px;
    line-height: 24px;
    margin-left: 10px;
    margin-right: 10px;
}

.step-card-content-element-action p {
    font-size: 13px;
    font-weight: normal;
    line-height: 24px;
    margin: 0 0 2px 0;
}

.step-card-content-element-action-values {
    margin-left: 32px;
}

.step-card-content-element-action .badge,
.step-card-content-element-condition .badge {
    border-radius: 2px;
    color: #34364B;
    font-size: 11px;
    line-height: 16px;
    margin-right: 4px;
}

.step-card-content-element-condition {
    border-bottom: 1px solid #DDDDE1;
    padding-bottom: 20px;
    position: relative;
}

.step-card-content-element-condition p {
    color: #707281;
    font-size: 10px;
    letter-spacing: 0.6px;
    line-height: 20px;
    margin: 0;
    text-transform: uppercase;
}

.step-card-content-element-condition .label {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    height: auto;
    margin-bottom: 4px;
    white-space: nowrap;
}

.step-card-content-element-condition-badge {
    background-color: #FFFFFF;
    border: 1px solid #DDDDE1;
    border-radius: 2px;
    color: #34364B;
    font-size: 11px;
    letter-spacing: 0;
    padding: 2px 5px;
    position: absolute;
    text-align: center;
    top: -27px;
}

.step-card-content-element-condition-badge.first {
    background-color: #424B54;
    border: none;
    color: #FFFFFF;
}

.status-footer-tunnels {
    display: inline-block;
    margin-left: 15px;
}

.step-card-status .status-action {
    height: 18px;
    font-size: 11px;
    text-align: center;
}

.step-card-status .status-action i {
    font-size: 20px;
    height: 50px;
    line-height: 50px;
    margin-right: 10px;
    text-align: center;
    vertical-align: middle;
}

.step-card-status .status-action a {
    text-decoration: none;
}

.step-card-status .status-action:hover {
    border: none;
    background-color: transparent;
}

.step-card-status .status-action .popover {
    top: 40px !important;
}


@media (max-width: 576px) {
    #sequence_steps_navigation_btns_container {
        max-width: 430px;
    }
}
