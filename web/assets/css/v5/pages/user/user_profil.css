.header-container hr.divider {
    height: auto;
    margin: 0;
    border-left: 1px solid var(--app-color-grey-light);
}

.header-container .header-body {
    gap: 16px;
    align-items: flex-start;
}

.header-container .header-body-avatar img,
.header-container .header-body-avatar .image-avatar {
    width: 88px;
    height: 88px;
    background-position: center;
    background-size: cover;
    border-radius: 100%;
}
.header-container .header-body-avatar .pending_el {
    border-radius: 50px;
}

.header-container .header-body-title {
    font-size: 20px;
}
.header-container .header-body-title.crop {
    max-width: 230px;
}

.header-container .header-body-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
    gap: 8px;
    width: 100%;
    margin-left: 0;
}
.header-container .header-body-user-status {
    display: flex;
    gap: 8px;
    justify-content: space-between;
    align-items: flex-start;
    width: inherit;
}
.header-container .header-body-user-status-left {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    width: inherit;
}
.header-container .header-body-user-status-right {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}
.header-container .header-body-user-status-right .user-types {
    display: inline-flex;
    align-items: center;
}
.header-container .header-body-user-status-right .tooltip .tooltip-inner {
    width: max-content;
    text-align: left;
}
.header-container .header-body-user-status-right .tooltip .tooltip-inner ul {
    padding-left: 8px;
    margin: 0;
}

.header-container .header-body-right .header-body-user-infos {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    width: inherit;
}
.header-container .header-body-user-infos-left {
    width: inherit;
}
.header-container .header-body-user-infos-left .user-email {
    display: flex;
    gap: 8px;
}
.header-container .header-body-user-infos-left .user-email a {
    color: inherit;
}
.header-container .header-body-user-infos-right {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    width: inherit;
}
.header-container .header-body-user-infos-right .lifetime-value,
.header-container .header-body-user-infos-right .rgpd,
.header-container .header-body-user-infos-right .rgpd-aff {
    display: flex;
    gap: 4px;
    width: max-content;
}

.header-container .header-body-user-dates {
    display: flex;
    gap: 8px;
}

@media (max-width: 992px) {
    .header-container .header-body-user-status {
        flex-direction: column;
        align-items: flex-start;
    }
    .header-container .header-body-user-status-right,
    .header-container .header-body-user-status-left {
        flex-wrap: wrap;
    }
}
@media (max-width: 920px) {
    .header-container .header-body-right {
        align-items: flex-start;
    }
    .header-container .header-body-user-infos {
        flex-direction: column;
    }
    .header-container .header-body-user-infos-right {
        justify-content: flex-start;
        flex-wrap: wrap;
    }
}