.offre {
    margin: 0 auto;
    margin-bottom: 0px;
    display: block;
    border: 1px solid #DDDDE1;
    padding: 10px;
}

.offre.theme1 {
    border-radius: 4px;
    background: linear-gradient(315deg, #517FA4 0%, #243949 100%);
    max-width: 380px;
}
.offre .offre-accroche {
    text-align: center;
    color: white;
}
.offre .offre-accroche h2 {
    text-align: center;
    color: white;
    font-size: 18px;
    font-weight: bold;
    margin-top: 15px;
}
.offre.theme1 .offre-texte {
    color: white;
    margin-bottom: 20px;
}
.offre.theme1 .offre-texte p {
    color: white;
}
.offre-orders {
    margin-bottom: 20px;
}
.offre .orders {
    text-align: center;
}
.offre .orders h4 {
    color: #FFFFFF;
    font-size: 13px;
    font-weight: 500;
}
.offre.theme1 .offre-countdown {
    margin-bottom: 20px;
}
.offre .orders.terminee {
    color: #CCC;
    background: transparent;
}
.offre .orders.terminee h4 {
    color: #CCC;
}
.offre form {
    margin-bottom: 0px;
}
.offre-btn a {
    text-decoration: none !important;
}
.offre-btn .btn {
    border-radius: 4px;
    box-shadow: none;
    color: white !important;
    background: var(--app-color-primary) !important;
    border: none;
    font-size: 16px;
    outline: none;
    display: block;
    width: 100%;
}
.offre-btn .btn.btn-small {
    padding: 3px 10px!important;
}
.offre-btn .btn.btn-lg {
    padding: 10px 16px!important;
    font-size: 18px!important;
}
.offre-btn .btn.btn-xlarge {
    padding: 15px 19px!important;
    font-size: 24px!important;
    line-height: 28px;
}

.offre.theme1 .offre-btn {
    margin: 10px;
}

.offre-theme-2 {
    width: 100%;
    bottom: 0;
    z-index: 1000;
    flex-grow: 0 !important;
}
.offre.theme2 {
    padding: 16px;
    background: linear-gradient(315deg, #517FA4 0%, #243949 100%);
    border: none;
    display: flex;
    flex-direction: row;
}
#broadcastRoom.is-full-width .offre.theme2 .offre-container {
    max-width: 1070px;
}
.offre.theme2 .offre-container {
    display: flex;
    flex-direction: row;
    margin: 0 auto;
    width: 100%;
}
.offre.theme2 .offre-container > div {
    flex: 1 1 auto;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: stretch;
    align-content: stretch;
    padding: 0 20px;
}
.offre.theme2 .offre-titles {
    justify-content: center;
}
.offre.theme2 .offre-accroche h2 {
    color: white;
    font-size: 18px;
    font-weight: bold;
    margin-top: 0;
    margin-bottom: 0;
    text-align: left;
}
.offre.theme2 .offre-texte,
.offre.theme2 .offre-texte p {
    color: white;
    font-size: 13px;
    margin-bottom: 0;
    text-align: left !important;
}
.offre.theme2 .offre-texte {
    margin-top: 10px;
}
.offre.theme2 .orders h4 {
    margin-top: 0;
}
.offre.theme2 .offre-options {
    flex-grow: 0;
    flex-direction: column;
    flex-shrink: 0;
}
.offre.theme2 .offre-orders {
    margin-bottom: 0;
}
.offre.theme2 .offre-container .offre-right-content {
    flex-grow: 0;
}
.offre.theme2 .offre-btn .btn {
    padding: 8px 20px !important;
    font-size: 11px !important;
    min-width: 184px;
}


.countdown-timer {
    margin: 0 auto
}
.offre-countdown {
    text-align: center;
}
.countdown-timer-style-6 {
    text-align: center;
    background: white;
    border: 1px solid #DDDDE1;
    border-radius: 4px;
    padding: 12px 15px;
    display: inline-block;
}
.offre.theme2 .countdown-timer-style-6 {
    padding: 8px 5px;
}
.countdown-timer-style-6 .hasCountdown {
    display: inline-block
}
.countdown-timer-style-6 .countdown_rtl {
    direction: rtl
}
.countdown-timer-style-6 .countdown_row {
    clear: both
}
.countdown-timer-style-6 .countdown-amount {
    position: relative;
    display: block;
    padding: 5px 5px 0 5px;
    line-height: 1em;
    font-weight: 300 !important;
    font-size: 24px !important;
    color: #34364B !important;
}
.offre.theme2 .countdown-timer-style-6 .countdown-amount {
    padding-top: 0;
}

.countdown-timer-style-6 .countdown-section br {
    display: none
}
.countdown-timer-style-6 .countdown-section {
    display: inline-block;
    text-align: left;
    vertical-align: center;
    color: inherit;
    text-align: center;
    margin: 0;
    border: none;
    padding: 0px 15px !important;
}
.countdown-timer-style-6 .countdown-section:first-child {
    margin-left: 0;
}
.countdown-timer-style-6 .countdown-section:last-child {
    margin-right: 0;
}
.countdown-timer-style-6 .countdown-period {
    opacity: 1;
    color: #34364B !important;
    font-size: 11px;
}

@media only screen and (min-width: 480px) and (max-width: 767px) {
    .countdown-timer-style-6 .countdown-amount {
        font-size: 30px;
        padding: 15px 15px 5px 15px;
    }
    .countdown-timer-style-6 .countdown-section {
        font-size: 16px;
        width: 85px;
        height: 85px;
    }
    .countdown-timer-style-6 .countdown-period {
        font-size: 12px;
        font-weight: normal;
    }
}

@media only screen and (max-width: 479px) {
    .countdown-timer-style-6 .countdown-amount {
        display: block;
        font-size: 24px;
    }
    .countdown-timer-style-6 .countdown-section br {
        display: block
    }
    .countdown-timer-style-6 .countdown-section {
        margin: 0 3px;
        font-size: 11px;
        font-weight: 400;
    }
    .countdown-timer-style-6 .countdown-section:first-child {
        margin-left: 0
    }
    .countdown-timer-style-6 .countdown-section:last-child {
        margin-right: 0
    }
    .countdown-timer-style-6 .countdown-period {
        font-size: 10px;
        font-weight: normal;
    }
}

@media(max-width:991px) {
    .offre.theme2,
    .offre.theme2 .offre-container {
        flex-direction: column;
    }
    .offre.theme2 .countdown-timer-style-6 {
        white-space: nowrap;
    }
    .page-container .countdown-timer-style-6 .countdown-section {
        height: auto;
        width: auto;
    }
    .offre.theme2 .countdown-timer-style-6 .countdown-amount {
        padding: 0 !important;
    }
    .offre.theme2 .offre-container > div {
        padding-bottom: 20px;
    }
}

body.replay .offre-countdown {
    display: none;
}

.theme {
    margin-bottom: 30px;
    background: white;
    border-radius: 6px;
    box-shadow: 0px 2px 10px 0 rgba(0, 0, 0, 0.08);
    padding: 20px 30px;
    -webkit-transition: border .2s ease-in-out;
    -o-transition: border .2s ease-in-out;
    transition: border .2s ease-in-out;
    border: 1px solid transparent;
}
.theme .theme-header {
    padding: 10px 0;
    display: block;
    position: relative;
}
.theme .theme-header h4 {
    color: #403d50;
    font-weight: bold;
    font-size: 24px;
}
.theme .theme-header span.label-warning {
    font-size: 10px;
    vertical-align: middle;
    position: relative;
    top: -2px;
    margin-left: 5px;
}
.theme .theme-header span.label-info.star {
    position: absolute;
    right: 0;
    top: 12px;
    padding: 4px 7px;
    font-size: 13px;
}

.theme-content {
    display: block;
    position: relative;
    padding: 0px;
    margin-bottom: 20px;
    text-align: center;
}
.theme-content img.pic {
    width: 100%;
    border-radius: 10px;
}
.theme-content div.pic {
    height: 250px;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    background-position: top center !important;
    border-radius: 8px;
}
.themes-container .theme-content img.pic {
    max-height: 150px;
}
.theme-content i.fa-picture-o {
    padding: 40px 0;
}

.theme-content button,
.theme-content a.btn {
    display: none;
    position: absolute;
    top:50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    font-size: 14px;
}
.theme-content:hover button,
.theme-content:hover a.btn {
    display: block;
}

.theme-content #overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background: black;
    opacity: .7;
    z-index: 1;
    display: none;
    top: 0;
    left: 0;
}
.theme-content:hover #overlay {
    display: block;
}
.theme .theme-footer {
    text-align: center;
    margin-bottom: 10px;
}
/*.theme .theme-footer .btn-success {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #eea236;
    font-size: 14px;
}*/
.theme .theme-footer .btn-violet {
    font-size: 14px;
    width: 100%;
    padding: 12px 0;
}
/*.theme .theme-footer .btn-warning {
    font-size: 14px;
    color: #fff !important;
    background-color: #f0ad4e !important;
    border-color: #eea236 !important;
}*/
.theme .theme-footer .theme-description {
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    display: -webkit-box;
}
