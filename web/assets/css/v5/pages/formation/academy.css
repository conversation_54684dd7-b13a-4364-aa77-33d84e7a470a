:root {
    --academy-main-color: var(--app-color-primary);
    --academy-bg-color: #fdedec;
    --academy-hover-color: #FF8574;
    --academy-button-text-color: #FFFFFF;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}
body > .container {
    display: flex;
    flex-direction: column;
    flex: 1;
}
div.index {
    background: white !important;
    margin: 0;
}
div.index-container {
    background: white !important;
}

/** Old formations */
#header, #stream {
    display: none !important;
}
#main-content {
    display: flex;
    flex-direction: column;
    flex: 1;
}
/** theme splash */
#main-container > .row > .col-md-3 {
    display: none;
}
#main-container > .row > .col-md-9 {
    width: 100% !important;
    margin-top: 0 !important;
}


.academy-container a,
.academy-container a:visited,
.academy-container a:active,
.academy-container a:focus {
    color: var(--academy-main-color);
}
.academy-container a:hover {
    color: var(--academy-hover-color);
}
.academy-container .btn-primary,
.academy-container .btn-primary:visited,
.academy-container .btn-primary:active,
.academy-container .btn-primary:focus,
.academy-container .btn-primary:active:focus,
.academy-container .btn-academy-primary,
.academy-container .btn-academy-primary:visited,
.academy-container .btn-academy-primary:active,
.academy-container .btn-academy-primary:focus,
.academy-container .btn-academy-primary:active:focus {
    background: var(--academy-main-color);
    border: none;
    color: var(--academy-button-text-color);
    transition: all .3s ease;
}
.academy-container .btn-primary:not(.disabled, .btn-light):hover,
.academy-container .btn-academy-primary:not(.disabled, .btn-light):hover {
    background: var(--academy-main-color);
    border: none;
    color: var(--academy-button-text-color);
    box-shadow: 4px 4px 0 0 var(--academy-bg-color) !important;
}
.academy-container .fc-event {
    background: var(--academy-bg-color);
    color: var(--academy-main-color);
    border-color: var(--academy-main-color);
}
.blank-state-page-image svg .main-color {
    fill: var(--academy-main-color);
}
.blank-state-page-image svg .bg-color {
    fill: var(--academy-bg-color);
}
.blank-state-page-image svg .text-color {
    fill: var(--academy-button-text-color);
}
.blank-state-page-image svg .hover-color {
    fill: var(--academy-hover-color);
}
.blank-state-image svg {
    color: var(--academy-main-color);
}
.blank-state-image svg .main-color {
    fill: var(--academy-main-color);
}
.blank-state-image svg .bg-color {
    fill: var(--academy-bg-color);
}
.blank-state-image svg .text-color {
    fill: var(--academy-button-text-color);
}
.blank-state-image svg .hover-color {
    fill: var(--academy-hover-color);
}

@media (min-width: 1200px) {
    .container {
        width: 1400px;
    }
}
body {
    background: white !important;
}
.container {
    width: 100% !important;
    padding: 0 !important;
}
.m-r-5 {
    margin-right: 5px;
}
.d-none {
    display: none !important;
}
.academy-container {
    background: white;
    width: 100%;
    display: flex;
    flex-direction: row;
}
.academy-container .academy-left-container {
    width: 360px;
    padding: 24px;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    border-right: var(--app-input-border);
}
#adminbar + .container .academy-left-container {
    height: calc(100vh - 76px);
    top: 76px;
}
.col-academy-left {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}
.card-academy {
    border-radius: 8px;
    border: 1px solid #CBD5E1;
    overflow: hidden;
    margin-bottom: 24px;
    padding: 0;
    flex-shrink: 0;
}
.card-academy .card-academy-image {
    height: 166px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
}
.card-academy .card-body {
    padding: 16px 12px;
}
.card-academy .card-body .panel .panel-title {
    margin-bottom: 0;
    font-size: 18px !important;
    line-height: 20px !important;
}
.card-academy .card-body .panel .panel-title a {
    color: var(--app-color-dark);
    text-transform: none;
}
.card-academy .card-body .panel .panel-heading {
    padding: 0;
}
.card-academy .card-body .panel .panel-heading .icon {
    margin-left: auto;
    margin-right: 0;
}
.card-academy .card-body h3 {
    margin: 0;
}
.card-academy .card-body .panel .panel-body {
    padding-top: 10px;
}
.card-academy .card-body .panel .panel-body p {
    margin-bottom: 0;
}

ul.navigation {
    list-style: none;
    margin: 0;
    padding: 0;
}
ul.navigation li a {
    padding: 8px 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
    color: var(--app-color-dark);
    border-radius: 8px;
    margin-bottom: 4px;
}
ul.navigation li a:hover {
    text-decoration: none;
    background: var(--academy-bg-color);
    color: var(--app-color-dark);
}
ul.navigation li a.active {
    text-decoration: none;
    background: var(--academy-bg-color);
    color: var(--academy-main-color);
}
ul.navigation li a .icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    text-align: center;
}
ul.navigation li a .icon i {
    font-size: 18px;
}
ul.navigation li a .name {
    font-weight: 500;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
ul.navigation li a .topic-icon {
    font-size: 20px;
    margin-right: 10px;
}
ul.navigation li a .badge {
    margin-left: auto;
    min-width: 18px;
    padding: 3px 5px;
}
ul.navigation li a.active .name,
ul.navigation li a.active .icon {
    color: var(--academy-main-color);
}
ul.navigation li a.btn-add-topic i {
    font-size: 14px;
}
ul.navigation li.separator {
    height: 36px;
}

.academy-container .col-academy-left .main-menu {
    display: none;
    margin-bottom: 20px;
}
.academy-container .col-academy-left .main-menu li a {
    font-weight: 500;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
.academy-container .col-academy-left .main-menu li a i {
    margin-right: 8px;
}
@media (max-width: 767px) {
    .academy-container .col-academy-left .main-menu {
        display: block;
    }
}

.academy-user {
    margin-top: auto;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    gap: 10px;
    padding-top: 20px;
}
.academy-user .academy-user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
}
.academy-user .academy-user-name {
    color: #000000;
}
.academy-user .btn-group {
    margin-left: auto;
}
.academy-user .btn-group .btn {
    background: transparent;
    border: none;
    color: var(--app-color-dark);
}


.academy-right-container {
    width: calc(100% - 360px);
    margin-left: 360px;
    padding: 48px;
}

.academy-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 24px;
    width: 100%;
}
.academy-header h2,
.user-interface .academy-header h2 {
    font-size: 40px !important;
    font-weight: 700 !important;
    margin: 0 !important;
    margin-right: 10px !important;
    line-height: 44px !important;
}
.academy-header .academy-header-actions {
    margin-left: auto;
}
.user-interface .academy-header .academy-header-actions a.btn {
    background: var(--academy-main-color) !important;
    color: var(--academy-button-text-color) !important;
    border: none !important;
}
.academy-header .academy-header-actions a.btn i {
    margin-left: 10px;
}
.academy-sub-header {
    margin-bottom: 24px;
}
.academy-sub-header .nav {
    margin-bottom: 16px;
    border-bottom: 1px solid var(--app-color-default-light);
    white-space: nowrap;
    overflow-x: auto;
}
.academy-sub-header .nav-tabs>li {
    padding: 10px;
    float: none;
    display: inline-block;
}
.academy-sub-header .nav-tabs>li>a {
    padding: 10px 18px 8px 18px;
    font-size: 14px;
    font-weight: 400;
    color: var(--app-color-dark);
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    background: transparent;
    margin-right: 0;
}
.academy-sub-header .nav-tabs>li.active {
    border-bottom: 2px solid var(--academy-main-color);
}
.academy-sub-header .nav-tabs>li.active>a,
.academy-sub-header .nav-tabs>li.active>a:focus,
.academy-sub-header .nav-tabs>li.active>a:hover {
    font-weight: 600;
    color: var(--academy-main-color) !important;
    border: none;
}
.academy-sub-header .nav-tabs>li:hover {
    border-bottom: 2px solid var(--academy-main-color);
}
.academy-sub-header .nav-tabs>li:hover>a {
    border: none;
}
.academy-sub-header .nav-tabs>li>a:hover {
    color: var(--academy-main-color) !important;
    border: none;
}

.academy-sub-header .nav-tabs>li.li-btn {
    padding: 10px 0 0 0;
    margin: 0 10px;
}
.user-interface .academy-sub-header .nav-tabs>li>a.btn {
    border-radius: 8px !important;
    background: var(--academy-main-color);
    padding: 12px 16px !important;
    height: 38px !important;
    font-weight: 600 !important;
    margin-bottom: 10px;
    color: var(--academy-button-text-color);
    line-height: 1;
}
.user-interface .academy-sub-header .nav-tabs>li.active>a.btn,
.user-interface .academy-sub-header .nav-tabs>li>a.btn:hover,
.user-interface .academy-sub-header .nav-tabs>li:hover>a.btn {
    color: var(--academy-button-text-color) !important;
    margin-bottom: 0;
    padding-bottom: 20px !important;
    height: 47px !important;
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}


@media (max-width: 767px) {
    .academy-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 8px;
    }
    .academy-container .academy-header-filters {
        padding-top: 12px;
        margin-left: 0;
    }
}
.academy-header-filters {
    display: flex;
    flex-direction: row;
    width: 100%;
    align-items: center;
    gap: 8px;
}
.academy-header-filters .form-group-search {
    flex: 1;
    width: 100%;
    margin-bottom: 0;
}
.user-interface .academy-header-filters .input-group {
    width: 100%;
}
.user-interface .academy-header-filters .input-group .input-group-addon {
    background: transparent !important;
    border-right: none !important;
    height: 40px !important;
    width: 50px !important;
    float: none;
}
.user-interface .academy-header-filters .input-group input {
    box-shadow: none !important;
    height: 40px !important;
    padding-left: 0 !important;
    border: 1px solid var(--app-color-grey-light) !important;
    border-left: none !important;
}
.user-interface .academy-header-filters .btn-sort.active {
    border-color: var(--academy-main-color) !important;
    color: var(--academy-main-color) !important;
}
.user-interface .academy-header-filters .btn-sort i {
    margin-left: 6px;
}
.user-interface .academy-header-filters .btn-group .btn {
    height: 40px !important;
    padding: 9px 19px !important;
    font-size: 14px !important;
    line-height: 18px !important;
    background: transparent;
    color: var(--app-color-dark) !important;
    border: 1px solid #CBD5E1 !important;
}
.user-interface .academy-header-filters .btn-group .btn.active {
    background: var(--academy-bg-color);
    box-shadow: none;
}
.user-interface .academy-header-filters .btn-group .btn:first-child {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}
.user-interface .academy-header-filters .btn-group .btn:last-child {
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
}
.user-interface .academy-header-filters .btn-group .btn i {
    margin-right: 0 !important;
}

.academy-container .btn.btn-outline-white,
.user-interface .btn.btn-outline-white {
    border: 1px solid var(--app-color-grey-light);
    background: transparent;
    color: var(--app-color-dark) !important;
    transition: all .3s ease;
}
.academy-container .btn.btn-outline-white:hover,
.user-interface .btn.btn-outline-white:hover {
    box-shadow: 4px 4px 0 0 var(--academy-bg-color) !important;
}
.academy-container .btn.btn-sm,
.user-interface .btn.btn-sm {
    padding: 9px 12px !important;
    font-size: 14px !important;
    height: 40px !important;
    border-radius: 8px !important;
    display: flex;
    align-items: center;
}

.academy-container .btn.navbar-toggle {
    position: absolute;
    top: -52px;
    z-index: 100;
    left: 10px;
    border: none;
    padding: 0;
    font-size: 20px !important;
}
@media (min-width: 768px) {
    .academy-container .btn.navbar-toggle {
        display: none;
    }
}
.m-r-10 {
    margin-right: 10px;
}
.academy-header-filters ul li a {
    cursor: pointer;
}
.academy-header-filters .btn i {
    margin-right: 5px;
}
@media (max-width: 767px) {
    .academy-header-filters .btn.main-btn-filter i {
        margin-right: 0;
    }
    .academy-header-filters .btn .btn-filter-label {
        display: none;
    }
}

#formations {
    position: relative;
}
.academy-card-container {
    border: 1px solid var(--app-color-default-light);
    border-radius: 8px;
    padding: 0 !important;
}
.academy-formation-label {
    background: var(--academy-bg-color);
    color: var(--academy-main-color);
    padding: 6px 24px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    flex-direction: row;
    justify-content: center;
}
.academy-formation-label-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
}


#academy-formations {

}
#academy-formations .tab-pane .formations-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
}
#mobile-app-container #academy-formations .tab-pane .formations-cards {
    grid-template-columns: 1fr;
}
#mobile-app-container .formation-card-overlay {
    display: none !important;
}

.formation-card {
    border-radius: 8px;
    border: 1px solid var(--app-color-default-light);
    background: #FFFFFF;
    position: relative;
    transition: all .3s ease;
    text-decoration: none !important;
}
.formation-card.is-member {
    cursor: pointer;
}
.formation-card.with-label {
    border: 1px solid #5628A5;
}
.formation-card:hover {
    box-shadow: 4px 4px 0 0 var(--academy-bg-color) !important;
}
.formation-card .formation-label {
    position: absolute;
    top: -13px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 4px;
    background: #5628A5;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: white;
    font-weight: 600;
    line-height: 1;
    padding: 8px;
    font-size: 12px;
}
.formation-card-container {
    display: flex;
    flex-direction: column;
}
.formation-card-image {
    height: 166px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    border-radius: 8px 8px 0 0;
}
.formation-card-content {
    display: flex;
    flex-direction: column;
    padding: 16px;
}
.formation-card-content-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 11px;
    color: var(--app-color-grey-medium);
}
.formation-card-last-vue i,
.formation-card-members > i {
    margin-right: 4px;
}
.formation-card-name {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-size: 16px;
    line-height: 1.2;
    font-weight: 700;
    color: var(--app-color-dark);
    margin-bottom: 8px;
    height: 38px;
}
.formation-card-name.owned {
    margin-bottom: 25px;
}
.formation-card-infos {
    display: flex;
    flex-direction: row;
    margin-bottom: 8px;
    color: var(--app-color-grey-medium);
    font-size: 12px;
    gap: 8px;
}
.formation-card-description {
    color: var(--app-color-grey-medium);
    font-size: 12px;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.formation-card-price:empty {
    display: none;
}
.formation-card-price:empty + .formation-card-infos-separator {
    display: none;
}
.formation-card-progression {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    margin-top: 20px;
}
.user-interface .formation-card-progression .progress {
    background: transparent !important;
    border: 1px solid var(--app-color-default-light) !important;
    flex: 1;
    margin-bottom: 0;
    box-shadow: none;
    height: 8px;
}
.user-interface .formation-card-progression .progress-bar {
    background-color: var(--academy-main-color) !important;
}
.formation-card-actions {
    text-align: center;
}
.user-interface .formation-card-actions .btn.btn-primary,
.formation-card .formation-card-actions .btn.btn-primary {
    border-radius: 8px !important;
    background: var(--academy-main-color) !important;
    padding: 12px 16px !important;
    height: 38px !important;
    font-weight: 600 !important;
    color: var(--academy-button-text-color) !important;
    border: none !important;
    line-height: 1;
}
.formation-card .progress-value {
    font-size: 12px;
    font-weight: 400;
    color: var(--app-color-grey-medium);
}

#academy-formations .tab-pane .formations-cards.list {
    grid-template-columns: 1fr;
}
.formations-cards.list .formation-card {
    height: 166px;
}
.formations-cards.list .formation-card .formation-card-container {
    flex-direction: row;
    gap: 16px;
    height: 100%;
}
.formations-cards.list .formation-card .formation-card-image {
    width: 280px;
    height: auto;
    border-radius: 8px 0 0 8px;
    flex: 0 0 auto;
}
.formations-cards.list .formation-card .formation-card-content {
    padding: 16px;
}
.formation-card-content-header {
    justify-content: flex-start;
    gap: 12px;
}
.formations-cards.list .formation-card .formation-card-content .formation-card-actions {
    text-align: left;
}
.formations-cards.list .formation-card .formation-label {
    left: 72px;
}
.formations-cards.list .formation-card .formation-card-progression {
    width: 240px;
}


body #line-footer,
body > #footer {
    background: white !important;
    border-top: 1px solid #CBD5E1 !important;
    margin-left: 360px;
    box-shadow: none;
    text-shadow: none;
    padding: 20px;
}
body #line-footer #footer {
    background: white !important;
}
#footer p,
#footer a {
    font-size: 14px !important;
    color: var(--app-color-dark) !important;
}
#footer .copyright .footer-socials {
    float: none;
}
.footer-social .social {
    padding: 12px !important;
    margin: 0 10px !important;
    background: var(--academy-bg-color) !important;
}
.footer-social .social i {
    color: var(--app-color-dark) !important;
}



#adminbar {
    margin: 0;
    background: white;
    height: 72px;
    position: fixed;
    width: 100%;
    z-index: 1010;
    top: 0;
    left: 0;
    border-bottom: 1px solid var(--app-color-default-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    font-size: var(--app-font-size);
    color: var(--app-color-grey-medium);
}
#adminbar .adminbar-preview-actions {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    gap: 16px;
}
#adminbar .adminbar-preview-logo {
    display: flex;
    flex-direction: row;
    padding: 0;
    align-items: center;
}
#adminbar .adminbar-preview-logo a {
    height: 32px;
    padding: 6px 12px;
    margin-right: 16px;
}
#adminbar .adminbar-preview-logo i {
    color: var(--app-color-dark);
    font-size: 12px;
}
#adminbar .adminbar-preview-logo a:hover i {
    color: black;
}
#adminbar .adminbar-preview-logo img {
    height: 24px;
    margin-right: 10px;
}
#adminbar .adminbar-preview-logo span {
    font-weight: 600;
    font-size: 16px;
    color: var(--app-color-dark);
}
.user-interface .adminbar-preview-btn.btn-primary {
    background: var(--app-bg-secondary-gradient) !important;
    border: 1px solid var(--app-color-secondary) !important;
    color: #FFFFFF !important;
    border-radius: 8px !important;
    height: 48px !important;
    padding: 12px 20px !important;
    line-height: 1 !important;
    display: flex;
    align-items: center;
    font-weight: 500 !important;
}
.user-interface .adminbar-preview-btn.btn-primary i {
    margin-right: 12px;
}
.user-interface .adminbar-preview-btn.btn-primary:hover {
    background: var(--app-bg-secondary-gradient-reverse) !important;
    border: 1px solid var(--app-color-secondary) !important;
    color: #FFFFFF !important;
    height: 48px !important;
    padding: 12px 20px !important;
    line-height: 1 !important;
    opacity: 1 !important;
}
@media (max-width: 767px) {
    .user-interface .adminbar-preview-btn.btn-primary i {
        margin-right: 0;
    }
    .user-interface .adminbar-preview-btn.btn-primary span {
        display: none;
    }
}


.navbar.navbar-top {
    display: none !important;
}


.panel-group {
    margin-bottom: 0;
}
.panel-group .panel {
    border: none;
    box-shadow: none;
}
.panel-group .panel-heading {
    border: none;
    background: none;
    padding: 8px 12px;
}
.panel-group .panel-title a {
    padding: 0;
    text-transform: uppercase;
    display: flex;
    align-items: center;
}
.panel-group .panel-title a:hover {
    text-decoration: none;
}
.panel-group .panel-title a .icon {
    margin-right: 10px;
}
.panel-group .panel-title a .icon i {
    font-size: 18px;
    transition: all .3s;
}
.panels .panel-group .panel-title a.collapsed .icon i {
    transform: rotate(-90deg);
}
.panel-group .panel-body {
    padding: 0;
    border: none !important;
}

.panels .panel-group {
    margin-top: 24px;
}

.blank-state {
    display: flex;
    flex-direction: column;
    padding: 25px;
    gap: 24px;
    text-align: center;
}
.user-interface .blank-state h4 {
    color: var(--app-color-grey-medium);
    font-size: 14px;
    font-style: italic;
    margin: 0;
    font-weight: 300 !important;
    line-height: 1.3 !important;
}
.blank-state .images {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    max-width: 500px;
    margin: 0 auto;
}
.blank-state .actions {

}


@media (max-width: 767px) {
    .academy-container .academy-left-container {
        position: fixed;
        top: 0;
        left: -100%;
        background: white;
        width: calc(100% - 50px);
        z-index: 1010;
        padding: 20px;
        overflow-y: auto;
        height: 100vh !important;
        transition: all .3s ease;
        display: block !important;
    }
    .academy-container .academy-left-container.in {
        left: 0;
    }
    #adminbar + .container .academy-container .academy-left-container {
        top: 72px;
        height: calc(100vh - 72px) !important;
    }

    .academy-left-container-mobile-bg {
        display: none;
        background: rgba(0, 0, 0, .7);
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1009;
    }
    .academy-container .academy-left-container.in + .academy-left-container-mobile-bg {
        display: block;
    }

    .academy-right-container {
        width: 100%;
        margin-left: 0;
        padding: 20px;
    }
    #formationsTab {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
    }
    #formationsTab li,
    #formationsTab li a,
    #formationsTab li a .text {
        white-space: nowrap;
    }
    .adminbar-preview-logo span {
        display: none !important;
    }
    .navbar-top {
        display: block !important;
        border-bottom: 1px solid #CBD5E1 !important;
        height: 60px;
        box-shadow: none !important;
        padding: 10px;
    }
    .user-interface .btn.navbar-toggle {
        float: none;
        margin-bottom: 0;
        color: var(--app-color-dark) !important;
        border: none;
        font-size: 20px !important;
    }
    .academy-container .academy-header-filters .btn-sort-group.btn-sort-by-name,
    .academy-container .academy-header-filters .btn-sort-group.btn-sort-by-activity {
        display: none;
    }
    body #line-footer,
    body > #footer {
        margin-left: 0;
    }
}


#page-editor .editor-header-content {
    margin-left: auto;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 16px;
    flex: 1 1 0;
}
#page-editor .editor-header .editor-header-content + .editor-header-actions {
    flex-grow: 0;
}
.adminbar-preview-access-level {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    padding-right: 20px;
    margin-right: 4px;
    border-right: 1px solid #F4F4F4;
}
.adminbar-preview-access-level span {
    font-size: 12px;
    color: var(--app-color-grey-medium);
}
@media (max-width: 767px) {
    .adminbar-preview-access-level span {
        display: none;
    }
}
.user-interface .adminbar-preview-access-level .btn,
#page-editor .adminbar-preview-access-level .btn {
    background: transparent !important;
    border: 1px solid #EEF !important;
    color: var(--app-color-secondary) !important;
}
#page-editor .adminbar-preview-access-level .btn {
    height: var(--app-btn-sm-height);
    padding: var(--app-btn-sm-padding);
    line-height: calc(var(--app-btn-sm-height) / 2);
}
.adminbar-preview-access-level .btn i {
    margin-left: 16px;
}
.adminbar-preview-access-level ul.dropdown-menu {
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 10px 15px 0 rgba(95, 98, 190, 0.12);
    width: 320px;
}
.adminbar-preview-access-level ul.dropdown-menu li {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
}
.adminbar-preview-access-level ul.dropdown-menu li > a {
    padding: 14px 10px;
    font-weight: 400;
    border-radius: 8px;
    display: flex;
    align-items: center;
    width: 100%;
    color: var(--app-color-grey-medium);
    gap: 0;
}
.adminbar-preview-access-level ul.dropdown-menu li > a.with-label {
    padding: 12px 10px;
    width: calc(100% - 30px);
}
.adminbar-preview-access-level ul.dropdown-menu li > a i {
    width: 20px;
    text-align: center;
    margin-right: 5px;
}
.adminbar-preview-access-level ul.dropdown-menu li > a i.fa-check {
    margin-left: auto;
    margin-right: 0;
    text-align: right;
}
.adminbar-preview-access-level ul.dropdown-menu li.active > a {
    font-weight: 600;
    background: transparent;
    color: var(--app-color-dark);
}
.adminbar-preview-access-level ul.dropdown-menu li.active > a i {
    color: var(--academy-main-color);
}
.adminbar-preview-access-level ul.dropdown-menu li > a:hover {
    background: var(--academy-bg-color);
    font-weight: 600;
    border-bottom-right-radius: var(--app-dropdown-radius) !important;
    border-bottom-left-radius: var(--app-dropdown-radius) !important;
}
.adminbar-preview-access-level ul.dropdown-menu li > a:hover i {
    color: var(--academy-main-color);
}
.adminbar-preview-access-level ul.dropdown-menu li > a .label {
    margin-left: 12px;
    padding: 5px 10px !important;
    height: 24px !important;
}
.adminbar-preview-access-level ul.dropdown-menu li .dropdown {
    margin-left: auto;
    margin-right: 10px;
}
#page-editor .adminbar-preview-access-level ul.dropdown-menu li > a.with-label {
    width: calc(100% - 35px);
}
#page-editor .adminbar-preview-access-level ul.dropdown-menu li > a.btn {
    border: none !important;
    box-shadow: none;
    padding: 0 !important;
    width: 20px;
    height: 20px;
}


#academy-container.public .academy-user {
    display: none;
}
#academy-container.public .formation-card.is-private {
    display: none;
}
#academy-container.public #formationsTab {
    display: none;
}

.d-none,
.user-interface .d-none,
.user-interface .label.d-none {
    display: none !important;
}


.modal:not(#ModalUnsplash) .modal-dialog .modal-content {
    box-shadow: none;
}
.modal-backdrop, .modal-backdrop.fade.in {
    opacity: .9;
    background: #35364B;
}

.modal.in:not(#ModalUnsplash) .modal-dialog.modal-loader {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    max-width: 500px;
    height: 100%;
}
.modal.in:not(#ModalUnsplash) .modal-dialog.modal-loader .modal-content {
    width: 500px;
    box-shadow: 0 2px 10px 0 rgba(20, 27, 58, 0.3), 0 10px 30px 0 rgba(20, 27, 58, 0.14);
    border-radius: 15px;
    overflow: hidden;
    border: none;
}
.modal-loader .modal-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 35px;
}
.modal-loader .modal-body img {
    max-width: 220px;
    margin-bottom: 25px;
}
.modal-loader .modal-body i {
    font-size: 24px;
    color: var(--app-color-dark);
    margin-bottom: 10px;
}
.modal-loader .modal-body h3 {
    color: var(--app-color-dark);
    margin: 0;
    font-weight: 700;
    font-size: 25px;
}


.academy-header-title {
    font-size: 40px !important;
    font-weight: 700 !important;
    margin: 10px 0 0 0 !important;
    line-height: 44px !important;
    color: var(--app-color-dark) !important;
    display: none;
}
@media (max-width: 767px) {
    .academy-header-title {
        display: block;
        order: 2;
    }
    .academy-header h2 {
        display: none;
    }
}
