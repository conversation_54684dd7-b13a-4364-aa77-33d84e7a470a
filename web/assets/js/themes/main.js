import Vue from 'vue'
import Vuex from 'vuex'
import axios from 'axios'
import VueAxios from 'vue-axios'
import router from './router'
import storePlugin from './plugins/storePlugin'
import App from './App.vue'
import VueQrcode from '@chenfengyuan/vue-qrcode'
import VueClipboard from 'vue-clipboard2'
import VueTour from 'vue-tour'
import VueSession from 'vue-session'
import PopupComponent from './components/PopupComponent.vue'
import GetTextPlugin from 'vue-gettext'
import translations from './translations.json'
import TunnelTutorialComponent from "./components/tunnels_tutorial/TunnelTutorialComponent.vue";

require('vue-tour/dist/vue-tour.css')

Vue.use(Vuex)
Vue.use(VueAxios, axios)
Vue.use(storePlugin)
Vue.use(VueClipboard)
Vue.use(VueSession, { persist: true })
Vue.use(VueTour)
Vue.component(VueQrcode.name, VueQrcode)
Vue.component('popup-component', PopupComponent)
Vue.component('tunnel-tutorial-component', TunnelTutorialComponent)
Vue.use(GetTextPlugin, {
    availableLanguages: {
        de_DE: 'Deutsche',
        en_US: 'English',
        es_ES: 'Español',
        fr_FR: 'Français',
    },
    defaultLanguage: 'fr_FR',
    translations: translations,
    silent: true,
})

Vue.config.language = locale

// @todo: export in mixin.js ?
Vue.mixin({
    data: function() {
        return {
            get app() {
                let availableApps = ['dev', 'prospect', 'client']
                return typeof viewerApp !== 'undefined' && availableApps.indexOf(viewerApp) !== -1 ? viewerApp : 'prospect'
            }
        }
    },
    methods: {
        goToLb () {
            window.open(lbDir, '_blank')
        },
        getPageTypeLabel: function(type) {
            let types = {
                optin: this.$gettext('Capture'),
                content: this.$gettext('Contenu'),
                sale: this.$gettext('Vente'),
                commande: this.$gettext('Commande'),
                site: this.$gettext('Site & Blog'),
                formation: this.$gettext('Formation')
            }
            return types[type]
        }
    },
    mounted() {

    }
})

router.push({name: 'default'});

new Vue({
    router,
    render: createEle => createEle(App)
}).$mount('#app')
