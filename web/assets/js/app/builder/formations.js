function initMultiLevelMenu()
{
	$('#menu').multilevelpushmenu({
		backText: __('Retour'),
		backItemIcon: 'fa fa-angle-left',
		groupIcon: 'fa fa-angle-right',
		direction: 'ltr',
		menuWidth: '100%',
		mode: 'cover',
		preventItemClick: false,
		onMenuReady: function() {
			$('#multilevelmenu-overlay').css('display', 'none');
            $('.levelHolderClass').off('mousedown');
		},
	});
}

$(document).ready(function(){
	initMultiLevelMenu();

	//button arborescence
    $('#btn-change-arborescence button').on('click', function(e) {
	    e.preventDefault();
	    $(this).parent().find('button').removeClass('btn-primary').addClass('btn-default');
	    $(this).addClass('btn-primary');

	    $('.formation-main-menu').removeClass('list arborescence').addClass($(this).data('value'));
    });

    //Handle Pages Position
    if ($('.builder-title-menu').length > 0) {
		$('.formation-main-menu').nestedSortable({
			handle: 'div.element-handle',
			forcePlaceholderSize: true,
			items: 'li',
			listType: 'ul',
			helper:	'clone',
			//toleranceElement: '> div',
			doNotClear: true,
			isTree: true,
			isAllowed: function (placeholder, placeholderParent, currentItem) {
				if (typeof(placeholderParent) !== 'undefined' && currentItem.hasClass('element-page') && placeholderParent.hasClass('element-page')) {
					$('body').css('cursor', 'not-allowed');
					$('body a').css('cursor', 'not-allowed');
					return false;
				}
				$('body').css('cursor', 'default');
				$('body a').css('cursor', 'pointer');
				return true;
			},
			relocate: function(event,ui) {
				if (handled) return false;
				handled = true;

				var datas_pages = $(this).sortable('toArray');

				var datas = { CSRFGuard_token:CSRFGuard_token, async: false };
				var length = datas_pages.length;

                var pages = [];
                if (length > 10) {
                    var i,temparray,chunk = 10;

                    for (i=0, j=datas_pages.length; i<j; i+=chunk) {
                        temparray = datas_pages.slice(i,i+chunk);
                        pages[i] = temparray;
                    }
                } else {
                    pages[0] = datas_pages;
                }

                datas.formDatas = JSON.stringify(pages);
				$.post(
		    		baseDir+'ajax/builder/formation_pages_position',
		    		datas,
		    		function (data) {
			            handled = false;
		    			if(data.status) {
							$('.formation-main-menu .element-module').each(function() {
								var nb = $(this).find('li.element-page').length;
								$(this).find('.nb_elements').first().html('<i class="fa fa-file-text-o"></i> ' + nb);
							});
		    			} else {
		    				TriggerGritter('error', data.message);
		    			}
		    		},
		    	'json');
			}
		});
	}

    $('.absolute-menu-header .btn').on('click', function(event) {
        event.preventDefault();
        $('.absolute-menu-header').toggleClass('collapsed');
        if ($('.absolute-menu-header').hasClass('collapsed')) {
            $('#menu_multilevelpushmenu a').each(function() {
                $(this).attr('data-rel', 'tooltip').attr('data-title', $(this).text());
            });
            $('.absolute-menu-header a[data-rel="tooltip"]').tooltip(
                {
                    'placement':'right',
                    'container': 'body',
                    'template': '<div class="tooltip absolute-menu-tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>'
                }
            );
        } else {
            $('#menu_multilevelpushmenu a[data-rel="tooltip"]').tooltip('destroy');
            if ($('#menu').length > 0) {
                setTimeout(function() {
                    $('#menu').multilevelpushmenu('redraw');
                }, 400);
            }
            $('#menu_multilevelpushmenu a').removeAttr('data-rel').removeAttr('data-title');
        }
    });

    if ($('.menu-search').length > 0) {
        $('.menu-search form').on('submit', function (e) {
            e.preventDefault();
        });
    }

});


function AddNavigationElement(type, element_parent, idformation) {
    if (handled) return false;
    handled = true;

	var selector = '#'+type+element_parent;
    $(selector).append('<div id="loader"><img src="'+cdnDir+'assets/images/ajax-loader.gif"></div>');

	$.post(
		baseDir+'ajax/builder/formation_add_navigation',
		{ type, parent:element_parent, idformation, CSRFGuard_token, async: false },
		function(data) {
            handled = false;
			if (data.status) {

    		    CreateModalBuild(__('Création d\'un élément de navigation'), data.html, 'SaveNavigationElement(\''+type+'\', '+element_parent+', '+idformation+'); return false;', __('Valider'));
    		    $('#ModalBuild .top-page-title').hide();
    		    $('#ModalBuild .form-actions').hide();
				$('#modalfooter a.btn:not(#submit)').addClass('btn-secondary m-r-10');
				$('#modalfooter a.btn:not(#submit) i.fa-times').hide();
    		    if (type == 'module') Raty();
    		    $('#loader').remove();

        		$("#ModalBuild .datepicker").datetimepicker({
                    language: language,
                    minuteStep: 5,
                    format: 'yyyy-mm-dd hh:ii:ss',
                    autoclose: true,
                    todayBtn: true,
                    todayHighlight: true,
                });
				$('.checkbox').find("input:checkbox, input:radio").not('[data-no-uniform="true"],#uniform-is-ajax').uniform();
				$('#ModalBuild [data-rel="select2"], #ModalBuild [rel="select2"]').select2({language: language});
			} else {
    			TriggerGritter('error', data.message);
            }
		},
	'json');
    return false;
}

function SaveNavigationElement(type, module_parent, idformation) {
    if (handled) return false;
    handled = true;
	$('#ModalBuild #submit').html('<i class="fa fa-spinner fa-spin"></i> ' + __('Traitement...')).attr('disabled', true);
	$('#ModalBuild #error').html('');

	$.post(
	    baseDir+'ajax/builder/formation_insert_navigation',
        { data: $('#ModalBuild form').serialize(), type, parent:module_parent, idformation, in_formation:true, CSRFGuard_token },
    	function(msg) {
            handled = false;
        	$('#ModalBuild #submit').html(__('Valider')).attr('disabled', false);

    		if (msg.status) {
    		    if (msg.redirect) {
        		    window.top.location.href = msg.redirect;
    		    } else {

    		    	//nouveau menu
    		    	if ($('#menu').length > 0) {
	    		    	if (msg.emplacement == 'm0') {
	                    	$('.formation-main-menu').append(msg.html_menu);
	                    } else {
	                    	$('.formation-main-menu #menuItem_'+msg.emplacement+' .submenu').append(msg.html_menu);
	                    }

	                    if (msg.emplacement == 'm0') {
	                    	var target_menu = $( '#menu' ).multilevelpushmenu( 'findmenusbyId' , 'menu_multilevelpushmenu' ).first();
	                    } else {
	                    	var target_menu = $( '#menu' ).multilevelpushmenu( 'findmenusbyId' , msg.emplacement ).first();
	                    }

	                    var menu = $( '#menu' ).multilevelpushmenu( 'additems' , msg.html , target_menu , msg.position );
	                    if (menu) {
		                    var target_module = $( '#menu' ).multilevelpushmenu( 'findmenusbyId' , 'm'+msg.idmodule );
		                    if (target_module) {
			                    var target_module = target_module.first();
								$( '#menu' ).multilevelpushmenu( 'expand' , target_module );
							}
		                }
					}

                    //TriggerGritter('success', __('L\'élément a bien été ajouté'));
                    $('#ModalBuild').modal('hide');
                }
    		} else {
    			$('#ModalBuild form').prepend('<div id="error"><div class="alert alert-danger">'+msg.message+'</div></div>');
    		}
    	},
    'json');

    return false;
}


function ShowNavigationElement(type, module_parent, element) {
    if (handled) return false;
    handled = true;

    if (element.emplacement == 'm0') {
    	var target_menu = $('#menu').multilevelpushmenu('findmenusbyId', 'menu_multilevelpushmenu').first();
    } else {
    	var target_menu = $('#menu').multilevelpushmenu('findmenusbyId', element.emplacement).first();
    }

    $('#menu').multilevelpushmenu('additems', element.html, target_menu, element.position);

    var target_module = $('#menu').multilevelpushmenu('findmenusbyId', 'm'+element.idmodule).first();
    $('#menu').multilevelpushmenu('expand', target_module);

    handled = false;
    return false;
}


function EditNavigationElement(type, idelement, redirect, idformation) {
    if (handled) return false;
    handled = true;

	if (typeof redirect === 'undefined') var redirect = '';

	if (type == 'page') {
	    var selector = 'p'+idelement;
	    var element_selected = $(selector).find('#edit:first');
	} else if (type == 'module') {
	    var selector = 'm'+idelement;
	    var element_selected = $("li[id^='"+selector+"']").find('#edit:first');
	} else if (type == 'formation' || type == 'formation_modules') {
	    var selector = 'formation'+idelement;
	    var element_selected = $("li[id^='"+selector+"']").find('#edit:first');
	}
	var text = element_selected.text();
    element_selected.html('<i id="loader" class="fa fa-spinner fa-spin"></i>');

    $.post(
		baseDir+'ajax/builder/formation_edit_navigation',
		{ type:type, idelement:idelement, idformation, CSRFGuard_token:CSRFGuard_token, async: false },
		function(data) {
            handled = false;
			if (data.status) {
    		    CreateModalBuild(__('Modification d\'un élément de navigation'), data.html, 'UpdateNavigationElement(\''+type+'\', '+idelement+', \''+redirect+'\', ' + idformation + '); return false;', __('Valider'));

                $('#ModalBuild .form-actions').hide();
            	$('#ModalBuild .top-page-title').hide();
                $('#ModalBuild .form-actions input[name="redirect"]').remove();

        		$("#ModalBuild .datepicker").datetimepicker({
                    language: language,
                    minuteStep: 5,
                    format: 'yyyy-mm-dd hh:ii:ss',
                    autoclose: true,
                    todayBtn: true,
                    todayHighlight: true,
                });

                if (type == 'module') {
            		$('#search-module').hideseek();
            		Raty();
        		}

        		SortableComposition();

    		    $('#loader').remove();
    		    element_selected.html('<i class="fa fa-edit"> '+text+'</i>');
		    } else {
    			TriggerGritter('error', data.message);
            }
		},
	'json');
    return false;
}


function UpdateNavigationElement(type, idelement, redirect, idformation) {
    if (handled) return false;
    handled = true;
	$('#ModalBuild #submit').html('<i class="fa fa-spinner fa-spin"></i> ' + __('Traitement...')).attr('disabled', true);
	$('#ModalBuild #error').html('');

	$.post(
	    baseDir+'ajax/builder/formation_update_navigation',
        { data: $('#ModalBuild form').serialize(), type:type, idelement:idelement, idformation, CSRFGuard_token:CSRFGuard_token },
    	function(msg) {
            handled = false;
        	$('#ModalBuild #submit').html(__('Valider')).attr('disabled', false);
    		if (msg.status) {
                if (msg.redirect) {
        		    window.location = msg.redirect;
    		    } else if (redirect) {
        		    window.location.reload();
    		    } else if (msg.reload) {
        		    window.location.reload();
    		    } else {
        		    //titre affiché sur la page
        		    if (type == 'page') {
	        		    $('#formation-menu #menuItem_p'+idelement+' span.page-title').html(msg.titre);
						$('#formation-menu #menuItem_p'+idelement+' .element-handle span.label-multilevelpushmenu').html('+' + msg.delay + 'j');
	        		    $('li#p'+msg.idmodule+'-'+idelement).addClass('active');
	        		    if (idelement == id_objet) {
							$('.builder-title-right h3 a').html(msg.titre);
						}
                    } else if (type == 'module') {
	                    $('#menuItem_m'+idelement+' .element-handle > a').first().html(msg.header_main_menu);
                    }

                    //remove all items in parent module
                    if ($('#menu').length > 0) {
	                    if (msg.emplacement == 'm0') {
	                    	var target_menu = $( '#menu' ).multilevelpushmenu( 'findmenusbyId' , 'menu_multilevelpushmenu' ).first();
	                    } else {
	                    	var target_menu = $( '#menu' ).multilevelpushmenu( 'findmenusbyId' , msg.emplacement ).first();
	                    }
	                    var target_elements = target_menu.find('ul > li').not('.header').not('.action');
	                    $.each(target_elements, function() {
	                        $('#menu').multilevelpushmenu('removeitems', $(this));
	                    });
						$('#menu').multilevelpushmenu('additems', msg.html, target_menu, 0);
					}

                	handled = false;
                    $('#ModalBuild').modal('hide');
                }

    		} else {
    			$('#ModalBuild form').prepend('<div id="error"><div class="alert alert-danger">'+msg.message+'</div></div>');
    		}
    	},
    'json');

    return false;
}


function AskDuplicateNavigationElement(type, idelement, redirect) {
    if (handled) return false;
    handled = true;

	if (typeof redirect === 'undefined') var redirect = '';

	$.post(
	    baseDir+'ajax/builder/formation_ask_duplicate_navigation',
        { type:type, idelement:idelement, CSRFGuard_token:CSRFGuard_token },
    	function(data) {
            handled = false;

    		CreateModalBuild(__('Duplication d\'un élément de navigation'), data.html, 'DuplicateNavigationElement(\''+type+'\', '+idelement+'); return false;', __('Valider'));
            $('#ModalBuild .form-actions').hide();
            $('#ModalBuild .form-actions input[name="redirect"]').remove();
            $('#loader').remove();
    	},
    'json');

    return false;
}


function DuplicateNavigationElement(type, idelement) {
    if (handled) return false;
    handled = true;

	var params = '';
	if ($('#ModalBuild #FormPage').length > 0) {
    	var params = $('#ModalBuild #FormPage').serialize();
	}

    if (type == 'module') {
        $.ModalHelper.createModal({
            title: __('Duplication d\'un module'),
            content: __('Traitement en cours...') + '<br><img src="' + cdnDir + 'assets/images/ajax-loader.gif">'
        });
    }

	$.post(
	    baseDir+'ajax/builder/formation_duplicate_navigation',
        { type:type, idelement:idelement, params:params, CSRFGuard_token:CSRFGuard_token },
    	function(msg) {
            handled = false;

    		if(msg.status) {
                if (msg.redirect) {
        		    window.top.location.href = msg.redirect;
    		    } else {

                    //remove all items in parent module
                    if (msg.emplacement == 'm0') {
                    	var target_menu = $('#menu').multilevelpushmenu('findmenusbyId', 'menu_multilevelpushmenu').first();
                    } else {
                    	var target_menu = $('#menu').multilevelpushmenu('findmenusbyId', msg.emplacement).first();
                    }
                    var target_elements = target_menu.find('ul > li').not('.header').not('.action');

                    $.each(target_elements, function() {
                        $('#menu').multilevelpushmenu('removeitems', $(this));
                    });

                    $('#menu').multilevelpushmenu('additems', msg.html, target_menu, 0);


                    if (msg.idmodule == 0) {
                    	var new_target_menu = $('#menu').multilevelpushmenu('findmenusbyId', 'menu_multilevelpushmenu').first();
                    } else {
                        var new_target_menu = $('#menu').multilevelpushmenu('findmenusbyId', 'm'+msg.idmodule).first();
                    }
                    $( '#menu' ).multilevelpushmenu( 'expand' , new_target_menu );

	    		    if (type == 'module') {
                        $('#ModalBuild').modal('hide');
                    }

                    //TriggerGritter('success', __('L\'élément a bien été dupliqué'));
                }
    		} else {
                TriggerGritter('error', msg.message);
    		}
    	},
    'json');

    return false;
}


function ShowDuplicateNavigationElement(type, idelement, element) {
    if (handled) return false;
    handled = true;

    //remove all items in parent module
    if (element.emplacement == 'm0') {
    	var target_menu = $('#menu').multilevelpushmenu('findmenusbyId', 'menu_multilevelpushmenu').first();
    } else {
    	var target_menu = $('#menu').multilevelpushmenu('findmenusbyId', element.emplacement).first();
    }
    var target_elements = target_menu.find('ul > li').not('.header').not('.action');

    $.each(target_elements, function() {
        $('#menu').multilevelpushmenu('removeitems', $(this));
    });

    $('#menu').multilevelpushmenu('additems', element.html, target_menu, 0);


    if (element.idmodule == 0) {
    	var new_target_menu = $('#menu').multilevelpushmenu('findmenusbyId', 'menu_multilevelpushmenu').first();
    } else {
        var new_target_menu = $('#menu').multilevelpushmenu('findmenusbyId', 'm'+element.idmodule).first();
    }
    $('#menu').multilevelpushmenu('expand', new_target_menu);

    handled = false;
    return false;
}


function DeleteNavigationElementConfirmation(type, idelement, redirect) {
    if (handled) return false;

	if (typeof redirect === 'undefined') var redirect = '';

    handled = true;
    $.post(
		baseDir+'ajax/builder/formation_delete_navigation_confirmation',
		{ type:type, idelement:idelement, CSRFGuard_token:CSRFGuard_token, async: false },
		function(data) {
            handled = false;
			if (data.status) {
			    if (data.confirmation) {
                    $.ModalHelper.createModal({
                        title: __('Suppression d\'un élément de navigation'),
                        content: data.message,
                        action: 'DeleteNavigationElement(\'' + type + '\', ' + idelement + '); return false;',
                        button: data.button,
                        type: 'error',
                        blocker: __('Oui, je confirme la suppression.')
                    });
                } else {
                    DeleteNavigationElement(type, idelement);
                }
		    } else {
    			TriggerGritter('error', data.message);
            }
		},
	'json');
    return false;
}


function DeleteNavigationElement(type, idelement) {
    if (handled) return false;
    handled = true;
	$('#ModalCreated').modal('hide');

    if (type == 'page') {
        //get next link
        if ($('a.btn-next').length > 0) {
            var href = $('a.btn-next').attr('href');
        } else if ($('a.btn-prev').length > 0) {
            var href = $('a.btn-prev').attr('href');
        } else {
            var href = baseDir+'app/build/';
        }
        var idpage_encours = idelement;
    } else {
        var idpage_encours = $('.sortable-pages').data('idpage');
    }

	$.post(
	    baseDir+'ajax/builder/formation_delete_navigation',
        { idelement:idelement, type:type, idpage_encours:idpage_encours, CSRFGuard_token:CSRFGuard_token },
    	function(msg) {
            handled = false;
    		if (msg.status) {

    		    if (type == 'page') {
        		    window.top.location.href = href;
    		    } else {

    		        if (msg.redirect) {
        		        window.top.location.href = baseDir+'app/build/';
    		        }

        		    //remove all items in parent module
                    if (msg.emplacement == 'm0') {
                    	var target_menu = $('#menu').multilevelpushmenu('findmenusbyId', 'menu_multilevelpushmenu').first();
                    } else {
                    	var target_menu = $('#menu').multilevelpushmenu('findmenusbyId', msg.emplacement).first();
                    }
                    var target_elements = target_menu.find('ul > li').not('.header').not('.action');

                    $.each(target_elements, function() {
                        $('#menu').multilevelpushmenu('removeitems', $(this));
                    });

                    $('#menu').multilevelpushmenu('additems', msg.html, target_menu, 0);
                    $('#menu').multilevelpushmenu('expand', target_menu);
                }

                //TriggerGritter('success', __('L\'élément a bien été supprimé'));
    		} else {
                TriggerGritter('error', msg.message);
    		}
    	},
    'json');
    return false;
}


function EditFormationTheme() {
    if (handled) return false;
    handled = true;

    if ($('#sidebarTheme').hasClass('open')) {
        $('body').animate({ left: '0px'}, 500);
        $('#sidebarTheme').removeClass('open').toggle('slide', { direction: 'left' }, 500);
        handled = false;
    } else {
        $.post(
    		baseDir+'ajax/builder/formation_edit_theme',
    		{ CSRFGuard_token:CSRFGuard_token, async: false },
    		function(data) {
                handled = false;
    			if (data.status) {
        		    $('#sidebarTheme').html(data.html);
                    $('#sidebarTheme .js-modal-handler').modalHandler();
        		    $("#sidebarTheme .color").each(function() {
                        $(this).minicolors({
            				opacity: false,
                            theme: "bootstrap",
                            position: $(this).attr('data-position') || 'bottom left',
                        });
        			});
        			$('#sidebarTheme').addClass('open').toggle('slide', { direction: 'left' }, 500);
        			var largeur = $('#sidebarTheme').outerWidth();
        			largeur = largeur/2;
                    $('body').animate({ left: largeur+'px'}, 500);
        			$('#sidebarTheme').initTheme();

        			var url = window.location.href;
        			$('#sidebarTheme .form-buttons').append('<input type="hidden" name="redirect" value="'+url+'">');
        			$('#sidebarTheme #reinit').append('<input type="hidden" name="redirect" value="'+url+'">');
    		    } else {
        			TriggerGritter('error', data.message);
                }
    		},
    	'json');
    }

    return false;

}


function getBrowserPrefix() {
    if ($.browser.chrome) {
       return '-webkit-';
    } else if ($.browser.mozilla) {
       return '-moz-';
    } else if ($.browser.opera) {
       return '-o-';
    } else if ($.browser.msie) {
       return '-ms-';
    }
    return;
}


$.fn.initTheme = function(id_element) {
    var $container = $(this);
    var prefix = getBrowserPrefix();

    $container.find('.color').on('change', function() {
        var target = $(this).data('target');
        if (target != undefined) {
            var color = $(this).val();
            $(target).each(function () {
                this.style.setProperty('color', color, 'important');
            });

            var target2 = $(this).data('target');
            if (target2 != undefined) {
                var color2 = $(this).val();
                $(target).each(function () {
                    this.style.setProperty('color', color2, 'important');
                });
            }
        }
    });

    $container.find('input[type="number"]').on('change', function() {
        var target = $(this).data('target');
        if (target != undefined) {
            var fontsize = $(this).val();
            $(target).each(function () {
                this.style.setProperty('font-size', fontsize+'px', 'important');
            });
        }
    });

    $container.find('input[name="logo"]').on('input', function() {
        $('a.name img').attr('src', $(this).val());
    });

    $container.find('input[name="hide_logo"]').on('change', function() {
        if ($(this).attr('checked')) {
            $('a.name img').hide();
        } else {
            $('a.name img').show();
        }
    });

    $container.find('input[name="banniere"]').on('input', function() {
        if ($('.banniere-container').length > 0) {
            $('.banniere-container img').attr('src', $(this).val());
        } else {
            $('body').prepend('<div class="banniere-container"><div class="banniere"><img src="'+$(this).val()+'"></div></div>');
            if ($('input[name="banniere-bgcolor"]').val() != '')
                $('.banniere-container').css('background', $('input[name="banniere-bgcolor"]').val());
        }
    });

    $container.find('input[name="banniere-bgcolor"]').on('change', function() {
        if ($('.banniere-container').length > 0)
            $('.banniere-container').css('background', $(this).val());
    });

    $container.find('#bgimage').on('input', function() {
        if ($(this).val() == '') {
            $('body').css('background', $('#bgcolor1').val());
        } else {
            var background = $(this).val().replace(/'/g, "\\'");
            $('body').css('background', 'url(\''+background+'\') no-repeat center top');
            $('body').css('background-attachment', 'fixed');
            $('body').css('background-color', 'transparent');
            $('body').css('-webkit-background-size', 'cover');
            $('body').css('-moz-background-size', 'cover');
            $('body').css('-o-background-size', 'cover');
            $('body').css('background-size', 'cover');
            $('body').css('width', '100%');
            $('body').css('height', '100%');
        }
    });

    $container.find('#bgcolor1').on('change', function() {
        if ($('#bgimage').val() == '') {
            if ($('#bgcolor2').val() == '') {
                $('body').css('background', $(this).val());
            } else {
                $('body').css('background', prefix+'linear-gradient(top, '+$(this).val()+' 0%, '+$('#bgcolor2').val()+' 100%)');
            }
        }
    });

    $container.find('#bgcolor2').on('change', function() {
        if ($('#bgimage').val() == '') {
            $('body').css('background', prefix+'linear-gradient(top, '+$('#bgcolor1').val()+' 0%, '+$(this).val()+' 100%)');
        }
    });


    //header
    $container.find('#header-bgcolor1').on('change', function() {
        if ($('#header-bgcolor2').val() == '') {
            var color = $(this).val();
            $('.navbar-inner')[0].style.setProperty('background', color, 'important');
        } else {
            $('.navbar-inner')[0].style.setProperty('background', prefix+'linear-gradient(top, '+$(this).val()+' 0%, '+$('#header-bgcolor2').val()+' 100%)', 'important');
        }
    });

    $container.find('#header-bgcolor2').on('change', function() {
        $('.navbar-inner')[0].style.setProperty('background', prefix+'linear-gradient(top, '+$('#header-bgcolor1').val()+' 0%, '+$(this).val()+' 100%)', 'important');
    });


    //menu
    $container.find('#mainmenu-color').on('change', function() {
        $('.navbar-menu')[0].style.setProperty('background', $(this).val(), 'important');
        $('.navbar-menu ul.nav')[0].style.setProperty('background', $(this).val(), 'important');
    });

    //sous-menu
    $container.find('#mainsubmenu-color').on('change', function() {
        var color = $(this).val();
        $('.navbar-menu li.dropdown > ul.dropdown-menu').each(function() {
            this.style.setProperty('background', color, 'important');
        });
        $('.dropdown-menu .arrow:after').css('background', $(this).val()+' !important');
        $('.navbar .nav > li > .dropdown-menu:after').css('background', $(this).val()+' !important');
    });

    $container.find('#mainsubmenu-hover-color').on('change', function() {
        $('.navbar-menu li.dropdown > ul.dropdown-menu > li:hover > a')[0].style.setProperty('background', $(this).val(), 'important');
        $('.navbar-menu li.dropdown > ul.dropdown-menu > li > a:hover')[0].style.setProperty('background', $(this).val(), 'important');
        $('.navbar-menu li.dropdown > ul.dropdown-menu > li:focus > a')[0].style.setProperty('background', $(this).val(), 'important');
        $('.navbar-menu li.dropdown > ul.dropdown-menu > li > a:focus')[0].style.setProperty('background', $(this).val(), 'important');
    });

    //postheader
    $container.find('#postheader-bgcolor').on('change', function() {
        $('ul.postheader')[0].style.setProperty('background', $(this).val(), 'important');
        $('ul.postheader li')[0].style.setProperty('background', $(this).val(), 'important');
    });

    //postheader
    $container.find('#postheader-text-hover-color').on('change', function() {
        $('ul.postheader li a:hover').css('cssText', 'color:'+$(this).val()+' !important');
    });

    //container
    var body_container = 'body > .container';
    var theme = '';
    if (theme == 'formation') body_container = '.scroll.con';
    if (theme == 'application') body_container = 'body > .container-fluid';

    $container.find('#container-bgcolor1').on('change', function() {
        if ($('#container-bgcolor2').val() == '') {
            var color = $(this).val();
            $(body_container).each(function() {
                this.style.setProperty('background', color, 'important');
            });
        } else {
            $(body_container).each(function() {
                this.style.setProperty('background', prefix+'linear-gradient(top, '+color+' 0%, '+$('#container-bgcolor2').val()+' 100%)', 'important');
            });
        }
    });

    $container.find('#container-bgcolor2').on('change', function() {
        var color = $(this).val();
        $(body_container).each(function() {
            this.style.setProperty('background', prefix+'linear-gradient(top, '+$('#container-bgcolor1').val()+' 0%, '+color+' 100%)', 'important');
        });
    });


    //menu de gauche
    $container.find('#multilevelmenu-titre-bgcolor').on('change', function() {
        var color = $(this).val();
        $('.multilevelpushmenu_wrapper h2').each(function() {
            this.style.setProperty('background', color, 'important');
        });
    });

    $container.find('#multilevelmenu-links-bgcolor').on('change', function() {
        var color = $(this).val();
        $('.multilevelpushmenu_wrapper .levelHolderClass').each(function() {
            this.style.setProperty('background', color, 'important');
        });
        $('.multilevelpushmenu_wrapper .backItemClass').each(function() {
            this.style.setProperty('background', color, 'important');
        });
    });

    $container.find('#multilevelmenu-links-active-bgcolor').on('change', function() {
        $('.multilevelpushmenu_wrapper li.active')[0].style.setProperty('background', $(this).val(), 'important');
    });

    $container.find('#multilevelmenu-links-hover-bgcolor').on('change', function() {
        $('.multilevelpushmenu_wrapper li:hover').css('cssText', 'background:'+$(this).val()+' !important');
        $('.multilevelpushmenu_wrapper .backItemClass:hover').css('cssText', 'background:'+$(this).val()+' !important');
    });

    $container.find('#sequencemenu-color').on('change', function() {
        $('.menu > .box-header-nav')[0].style.setProperty('background', $(this).val(), 'important');
    });

    $container.find('#sequencemenu-btn-bgcolor').on('change', function() {
        $('.menu .btn')[0].style.setProperty('background', $(this).val(), 'important');
    });

    $container.find('input[name="position_menu"]').on('change', function() {
        var position = $(this).val();
        if (position == 'left') {
            $('.mainmenu').removeClass('pull-right').show();
            $('.composition').removeClass('col-md-10 col-md-offset-1').addClass('col-md-9').removeClass('composition-left').show();
        } else if (position == 'right') {
            $('.mainmenu').addClass('pull-right').show();
            $('.composition').removeClass('col-md-10 col-md-offset-1').addClass('col-md-9').addClass('composition-left').show();
        } else if (position == 'hide') {
            $('.mainmenu').removeClass('pull-right').hide();
            $('.composition').addClass('col-md-10 col-md-offset-1').removeClass('composition-left').show();
        }
    });

    $container.find('#formation-bgimage').on('input', function() {
        if ($(this).val() == '') {
            $('.sequence').css('background', $('#bgcolor1').val());
        } else {
            var background_url = $(this).val().replace(/'/g, "\\'");
            $('.sequence').css('background', 'url(\''+background_url+'\') no-repeat center top');
            $('.sequence').css('background-color', 'transparent');
            $('.sequence').css('-webkit-background-size', 'cover');
            $('.sequence').css('-moz-background-size', 'cover');
            $('.sequence').css('-o-background-size', 'cover');
            $('.sequence').css('background-size', 'cover');
            $('.sequence').css('width', '100%');
            $('.sequence').css('height', '100%');
        }
    });

    $container.find('#formation-bgcolor1').on('change', function() {
        if ($('#formation-bgimage').val() == '') {
            if ($('#formation-bgcolor2').val() == '') {
                $('.sequence')[0].style.setProperty('background', $(this).val(), 'important');
            } else {
                $('.sequence')[0].style.setProperty('background', prefix+'linear-gradient(top, '+$(this).val()+' 0%, '+$('#formation-bgcolor2').val()+' 100%)', 'important');
            }

        }
    });

    $container.find('#formation-bgcolor2').on('change', function() {
        if ($('#formation-bgimage').val() == '') {
            $('.sequence')[0].style.setProperty('background', prefix+'linear-gradient(top, '+$('#formation-bgcolor1').val()+' 0%, '+$(this).val()+' 100%)', 'important');
        }
    });

    $container.find('#footer-color').on('change', function() {
        $('#footer')[0].style.setProperty('background', $(this).val(), 'important');
    });


};


function UpdateFormationTheme() {
    if (handled) return false;
    handled = true;
	$('#ModalBuild #submit').html('<i class="fa fa-spinner fa-spin"></i> ' + __('Traitement...')).attr('disabled', true);
	$('#ModalBuild #error').html('');

	$.post(
	    baseDir+'ajax/builder/formation_update_theme',
        { data: $('#ModalBuild form').not('#regenerate').serialize(), CSRFGuard_token:CSRFGuard_token },
    	function(msg) {
            handled = false;
    		if(msg.status) {
                window.location.reload();
    		} else {
    			$('#ModalBuild form').prepend('<div id="error"><div class="alert alert-danger">'+msg.message+'</div></div>');
    		}
    	},
    'json');

    return false;
}


function goToModule(idmodule) {
	if ($('#menu').length > 0) {
	    if (idmodule == 0) {
	    	var target_menu = $('#menu').multilevelpushmenu('findmenusbyId', 'menu_multilevelpushmenu').first();
	    } else {
	    	var target_menu = $('#menu').multilevelpushmenu('findmenusbyId', 'm'+idmodule).first();
	    }
	    $('#menu').multilevelpushmenu('expand', target_menu);
	}
}

function Raty() {
    if ($('#raty').length > 0) {
		$('#raty').each(function() {
    		$(this).raty({
                readOnly: false,
                score: function() {
                    return $(this).attr('data-score');
                },
                size     : 24,
                starHalf : 'star-half-big.png',
                starOff  : 'star-off-big.png',
                starOn   : 'star-on-big.png'
            });
        });
	}
}




function CreateFormationHeader(idformation) {
    if (handled) return false;
    handled = true;

    $.post(
		baseDir+'ajax/formation/line/frame/create/',
		{ idformation:idformation, objet:objet, CSRFGuard_token:CSRFGuard_token, async: false, type: 'header' },
		function(data) {
            handled = false;
			if (data.status) {
    		    window.location.reload();
		    } else {
    			TriggerGritter('error', data.message);
            }
		},
	'json');
    return false;
}


function CreateFormationFooter(idformation) {
    if (handled) return false;
    handled = true;

	$.post(
		baseDir+'ajax/formation/line/frame/create/',
		{ idformation:idformation, objet:objet, CSRFGuard_token:CSRFGuard_token, async: false, type: 'footer' },
		function(data) {
            handled = false;
			if (data.status) {
    		    window.location.reload();
		    } else {
    			TriggerGritter('error', data.message);
            }
		},
	'json');
    return false;
}



function SortableComposition() {

    //changement du curseur pour drag and drop and sort
    $("#dragBox, #dropBox").sortable({
        cursor: "move"
    });

    //on deactive la possibilité de selection dans les li
    $("#dragBox, #dropBox").disableSelection();

    // on associe la liste des li à une liste draggable
    $("#dragBox li").draggable({
        connectToSortable: '#dropBox',
        helper: 'clone',
        revert: 'invalid'
    });

    //on associe #dropBox à une liste sortable
    $("#dropBox").sortable({
        axis: "y",
        cursor: "move",
        placeholder: "ui-sortable-placeholder",

        update : function (event, ui) {

            if(!ui.item.hasClass('deletable')){
                ui.item.children('h4').prepend('<i class="fa fa-times delete pull-right"></i>');
                ui.item.addClass('deletable');
                ui.item.children('h4').children('.fa-th-list').removeClass('fa-th-list').addClass('fa-arrows-v');
            }

            var serial = $('#dropBox').sortable('toArray', { attribute: 'itemid' });
            $('#sort').val(serial);

            $('.delete').click(function() {
                $(this).parent().parent().remove();
                var serial = $('#dropBox').sortable('toArray');
                $('#sort').val(serial);
            });
        }

    });

    var serial = $('#dropBox').sortable('toArray', { attribute: 'itemid' });
    $('#sort').val(serial);

    $('#dropBox li').children('h4').prepend('<i class="fa fa-times delete pull-right"></i>');
    $('#dropBox li').addClass('deletable');
    $('#dropBox li').children('h4').children('.fa-th-list').removeClass('fa-th-list').addClass('fa-arrows-v');


    $('.delete').click(function() {
        $(this).parent().parent().remove();
        var serial = $('#dropBox').sortable('toArray');
        $('#sort').val(serial);
    });

}
