function deleteUserFormationAudio(formationId, userId, fileId) {
  $.ajax({
    url: `${baseDir}ajax/app/user/${userId}/formation/${formationId}/file/${fileId}/delete/`,
    headers: {
      'X-CSRF-TOKEN': $('#CSRFGuard_token').val()
    },
    dataType: 'json',
    type: 'POST',
    success : function(result) {
      if (result.status) {
        TriggerGritter('success', __("L'audio a été supprimé avec succès."));
      } else {
        TriggerGritter('error', result?.message || __("Une erreur est survenue lors de la suppression de l'audio."));
      }
    },
    error: function() {
      TriggerGritter('error', result?.responseJSON.message || __("Une erreur est survenue lors de la suppression de l'audio."));
    },
    complete: function() {
      $('.modal.fade.in').modal('hide');
      HandleTab('formation');
    }
  });
}