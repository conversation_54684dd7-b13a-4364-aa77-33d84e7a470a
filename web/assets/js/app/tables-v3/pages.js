$(document).ready(function() {
    $('#table_pages tbody').css('height', '100px');
    $.AjaxContentHelper.displayAjaxLoader($('#table_pages tbody'));

    var timerPages;
    var options = {
        stateSave: true,
        sDom: '<"content"t<"row table-footer"<"col-md-12 pagi-bottom"lp>>>',
        language: {
            url: cdnDir + "assets/js/common/jquery/jquery.dataTables." + language + ".json",
            searchPlaceholder: __("Rechercher"),
            sLengthMenu: "_MENU_ " + __("Éléments par page")
        },
        bDestroy: true,
        bAutoWidth: false,
        aaSorting: [[ 0, "asc" ]],
        buttons: [ ],
        fnPreDrawCallback: function() {
            $("#table_pages").addClass('has-filters');
            $("#table_pages").parent('div.content').addClass('has-filters');
        },
        fnDrawCallback: function() {
            $('.dataTables_paginate ul.pagination .paginate_button:not(.disabled, .active) a').on('click', function() {
                $.AjaxContentHelper.displayAjaxLoader($('#table_pages tbody'));
            });

             if (Math.ceil((this.fnSettings().fnRecordsDisplay()) / this.fnSettings()._iDisplayLength) > 1) {
                 $('.table-footer').show();
                 $('.dataTables_paginate').css("display", "inline-block");
                 $('.dataTables_length').css("display", "inline-block");
                 $("#table_pages").addClass('has-pagination');
             } else {
                 $('.dataTables_paginate').css("display", "none");

                 if (this.fnSettings().fnRecordsDisplay() <= 10) {
                     $('.table-footer').hide();
                     $('.dataTables_length').css("display", "none");
                     $("#table_pages").removeClass('has-pagination');
                 }

                 //if no action button --> hide top actions
                 if ($('#table_pages').find('.btn-action').length == 0) {
                     $('#table_pages').find('.content .row').first().css('display', 'none');
                     $('#table_pages').find('.content').css('border-top', 'none');
                 }
             }

            if (table.page.info().recordsDisplay === 0) {
                $("#table_pages").addClass('no-result');
            } else {
                $("#table_pages").removeClass('no-result');
            }

            if ($('.filters-container.filters-table').length > 0) {
                $("#table_pages").addClass('has-filters');
                $("#table_pages").parent('div.content').addClass('has-filters');
            } else if ($('div.table-search:not(.filters-search)').length > 0) {
                $("#table_pages").addClass('has-filters');
                $("#table_pages").parent('div.content').removeClass('has-filters');
            } else {
                $("#table_pages").removeClass('has-filters');
                $("#table_pages").parent('div.content').removeClass('has-filters');
            }
        },
        initComplete: function() {
            let state = table.state.loaded();
            if (state !== null && typeof state.search !== undefined && typeof state.search.search !== undefined && state.search.search.length > 0) {
                $('#search').val(state.search.search);
                $('#search').addClass('active');
            }

            $('#search').on('keyup change', function() {
                var search_string = $(this).val();
                if (search_string) {
                    $(this).addClass('active');
                } else {
                    $(this).removeClass('active');
                }

                clearTimeout(timerPages);
                timerPages = setTimeout(function () {
                    table.search(search_string).draw();
                }, 300);
            });
        }
    };

    var table = $("#table_pages").DataTable(options);
});
