$(document).ready(function() {
    var CSRFGuard_token = $('#CSRFGuard_token').val();

    var options = {
        stateSave: true,
        //sDom: "<\'row\'<\'col-md-12\'f>><\'row\'<\'col-md-12\'p>r><\'row\'<\'col-md-6\'B><\'col-md-6\'l>>t<\'row\'<\'col-md-12\'i>><\'row\'<\'col-md-12\'p>>",
        sDom: "<\'row\'<\'col-md-12\'f>><\'content\'<\'row\'<\'col-md-6 col-sm-6 col-xs-6 table-info\'iB>>t<\'row table-footer\'<\'col-md-12 pagi-bottom\'lp>>>",
        language: {
            "url": cdnDir + "assets/js/common/jquery/jquery.dataTables." + language + ".json",
            searchPlaceholder: __("Rechercher"),
            sLengthMenu: "_MENU_ " + __("Éléments par page")
        },
        bDestroy: true,
        bAutoWidth: false,
        aaSorting: [[ 0, "desc" ]],
        buttons: [ ],
        columnDefs: [
           { visible: "false", className: "hidden", "targets": [2] },
        ],
        aoColumns: [
          {"bSortable": true},
          {"bSortable": true},
          {"bSortable": false},
          {"iDataSort": 2},
          {"bSortable": true},
          {"bSortable": true},
        ],
        "fnDrawCallback": function() {
            if (Math.ceil((this.fnSettings().fnRecordsDisplay()) / this.fnSettings()._iDisplayLength) > 1) {
                $('.dataTables_paginate').css("display", "inline-block");
                $('.dataTables_length').css("display", "inline-block");
            } else {
                $('.dataTables_length').css("display", "none");
            }
        },
        initComplete: function() {

            var header = $(table.table().header()).find('th:last-child');
            header.html(table.buttons(1).container()[1]);
        }
    };

    var table = $("#table_customers").DataTable(options);

});
