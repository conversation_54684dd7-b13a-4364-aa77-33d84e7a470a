$(document).ready(function() {

    if ($('#logo').length > 0) {
        $('input[name="logo"]').on('input', function() {
            var logo = $('#logo').val();
            $('#preview').html('<div id="preview-logo"><img src="'+logo+'"></div>');
        });
    }

    if ($('.methodologies').length > 0) {
        $('input[name="methodologie"]').on("click", function() {
            $(".methodologie").removeClass("active");
            $(this).closest(".methodologie").addClass("active");
        });

        $('.methodologie .icone').on("click", function() {
            var input = $(this).parent().find('input').attr('checked', true);
            $.uniform.update();
            input.trigger('click');
        });
    }

    if ($('.elements').length > 0) {
        $('input[name="elements[]"]').on("click", function() {
            if ($(this).attr('checked')) {
                $(this).closest(".methodologie").addClass("active");
            } else {
                $(this).closest(".methodologie").removeClass("active");
            }
        });
    }

    if ($('.thumbnail').length > 0) {
        $('input[name="theme"]').on("click", function() {
            $(".thumbnail").removeClass("active");
            $(this).closest(".thumbnail").addClass("active");
        });
    }

    $('.theme .btn-specific-theme').click(function (e) {
        e.preventDefault();
        var specific_theme = $(this).closest('.theme').data('specific-theme');
        $('#specific_theme').val(specific_theme);
        $('#form-timeline').submit();
    });

    $('.theme .btn-master-theme').click(function (e) {
        e.preventDefault();
        var master_theme = $(this).closest('.theme').data('master-theme');
        $('#master_theme').val(master_theme);
        $('#form-timeline').submit();
    });

    $('#nb_modules, #nb_pages').on('mouseup keyup', function () {
        $(this).val(Math.min(15, Math.max(0, $(this).val())));
    });

});
