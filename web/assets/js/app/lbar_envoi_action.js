function VerifDate() {
    var date = $('#date_envoi').val();
    var today = new Date();
    var date = new Date(date);

    if (date <= today) {
        $.ModalHelper.createModal({
            title: __('Confirmation'),
            content: __('Attention : si vous envoyez cet email maintenant, vous ne pourrez pas modifier le contenu de l\'email envoyé.'),
            action: '$(\'#envoi_action\').submit(); return true;',
            button: __('Envoyer maintenant'),
            type: 'warning',
        });
    } else {
        $('#envoi_action').submit();
    }
}
