$(document).ready(function(){

    /* ---------- Uniform ---------- */
    $("input:checkbox, input:radio").not('[data-no-uniform="true"],#uniform-is-ajax').uniform();

    /* ---------- Select2 ---------- */
    $('[data-rel="select2"],[rel="select2"]').select2({language: language});

    /* ---------- Tooltip ---------- */
    $('[rel="tooltip"],[data-rel="tooltip"]').tooltip({"placement": "bottom", delay: {show: 400, hide: 200}});

    /* ---------- Popover ---------- */
    $('[rel="popover"],[data-rel="popover"]').popover({trigger: "hover"});

    /* ---------- ckeditor ---------- */
    CKEDITOR.timestamp = 'LB0.88';
    if ($("#ckeditorsmall").size() > 0) {
       CKEDITOR.replace('ckeditorsmall', { height: '200', toolbar: 'Light' });
    }

	if ($("#ckeditorsmall2").size() > 0) {
       CKEDITOR.replace('ckeditorsmall2', { height: '200', toolbar: 'Basic' });
    }

    if ($("#ckeditor-light").size() > 0) {
    	CKEDITOR.replace('ckeditor-light', {
    		height: '150',
    		toolbar:
    			[
					{ name: 'basicstyles', groups: [ 'basicstyles', 'cleanup' ], items: [ 'Bold', 'Italic', 'Strike' ] },
					{ name: 'styles', items: [ 'Styles', 'Format' ] },
					{ name: 'insert', items: [ 'Smiley' ] }
				]
    	});
    }


    /* ---------- MediaElement ---------- */
    var mode = 'auto';
    if(navigator.appName.indexOf("Internet Explorer")!=-1) mode = 'shim';

    $('video').not('.video-audio').not('#bgvid').mediaelementplayer({
        mode: mode,
		features: ['playpause', 'current', 'progress', 'duration', 'tracks', 'speed', 'volume', 'fullscreen'],
        success: function(mediaElement, domObject) {
            if (mediaElement.pluginType == 'flash' && mediaElement.attributes.hasOwnProperty('autoplay')) {
                mediaElement.addEventListener('canplay', function() {
                    mediaElement.play();
                }, false);
            }
        }
    });
    $('.video-audio').mediaelementplayer({
        renderers: ['flash_video'],
		pluginPath: '/swf/',
    });
    $('audio').not('.playlist,.no-mediaelementplayer').mediaelementplayer({ features: ['playpause','stop','current','progress','duration','volume','fullscreen'] });
    $('.video-block-center').bind('contextmenu', function(e) {
        return false;
    });

    /* ------------ Raty ------------ */
	if ($('#raty').length > 0) {
		$('#raty').raty({
		  score: function() {
		    return $(this).attr('data-score');
		  },
          hints: [__('Mauvais'), __('Assez bien'), __('Moyen'), __('Bien'), __('Excellent')],
          noRatedMsg : __('Aucune note pour l\'instant'),

		  click: function(score, evt) {
		      var datas = $(this).attr('data-input');
		      var CSRFGuard_token = $('#CSRFGuard_token').val();

              $.post(
        		baseDir+'ajax/raty/',
        		{ datas: datas, score:score, CSRFGuard_token:CSRFGuard_token, async: false },
        		function(data) {
        		    if (data.status) {
            			$('#raty').html('<span style="color:#468847"><i class="fa fa-check"></i> ' + __('Enregistré') + ' !</span>');
                    } else {
            			$('#raty').html('<div class="alert alert-danger">'+data.message+'</div>');
        			}
        		},
        	'json');
          }
		});
	}

	if ($('#raty-infos').length > 0) {
		$('#raty-infos').raty({
		  readOnly: true,
		  score: function() {
		    return $(this).attr('data-score');
		  }
        });
	}

	if ($('.raty').length > 0) {
		$('.raty').each(function() {
		    $(this).raty({
                readOnly: true,
                score: function() {
                    return $(this).attr('data-score');
                },
                hints: [__('Facile'), __('Assez facile'), __('Moyen'), __('Dur'), __('Très dur')],
                noRatedMsg : __('Aucun niveau de difficulté spécifié'),
            });
        });
	}


    /* ---------- Notifications ---------- */
	if ($(".notifications .alert-success").size() > 0) {
        $(".notifications .alert-success").each(function() {
            var alert = $(this);
            setTimeout(function() {
                  alert.slideUp('slow');
            }, 5000);
       	});
    }

    /* ---------- Datable ---------- */
	$('.datatable').dataTable({
		"sDom": "<'row'<'col-md-6'l><'col-md-6'f>r>t<'row'<'col-md-12'i><'col-md-12 center'p>>",
		"sPaginationType": "bootstrap",
		"language": {
            "url": cdnDir + "assets/js/common/jquery/jquery.dataTables." + language + ".json"
        },
    });

    /* ---------- Top right menu ---------- */
    $('.dropdown-menu-user .formation .formation-icon-right').on('click', function() {
        $('.dropdown-menu-user .formation-icon-right').toggleClass('active');
        $('.dropdown-menu-user .user-formations').toggleClass('active');
    });

	/* waypoint */
	if (typeof oldIE == 'undefined' && $('.waypoint').length > 0) {
		startWaypoints();
	}

});


/* ---------- FUNCTIONS ---------- */
function TriggerGritter(type, message, click) {
	if (type == 'error') {
		$.gritter.add({
			title: __('Erreur !'),
			text: message,
			image: '<i class="fa fa-warning"></i>',
			sticky: true,
			time: '',
			class_name: 'gritter-error',
			click: click
		});
	} else if (type == 'success') {
		$.gritter.add({
			title: __('Information'),
			text: message,
			image: '<i class="fa fa-check"></i>',
			sticky: false,
			time: 5000,
			class_name: 'gritter-success',
			click: click
		});
	} else if (type == 'notice') {
		$.gritter.add({
			title: __('Information'),
			text: message,
			image: '<i class="fa fa-info-circle"></i>',
			sticky: false,
			time: 5000,
			class_name: '',
			click: click
		});
	}
}

function FullTriggerGritter(title, message, image, classname, timeout, sticky) {
	if (image == undefined) image = '<i class="fa fa-info-circle"></i>';
	if (sticky == undefined) sticky = false;
	if (timeout == undefined) timeout = 10000;
	$.gritter.add({
		title: title,
		text: message,
		image: image,
		sticky: sticky,
		time: timeout,
		class_name: classname+' animated fadeInDown',
	});
}



function showFlashPlayer(fileid) {
    if (fileid == '') {
        $('#flashParcours').hide();
    } else {
        var type = $('#file'+fileid).attr('type');
        var filename = $('#file'+fileid).attr('file');
        var path = $('#file'+fileid).attr('path');
        $('#flashParcours').hide();

        if (type == 'flv') {
            $('#flashParcours').html('<video class="video-audio" controls preload="auto" width="300" height="30" poster=""><source type="video/flv" src="'+path+filename+'"><object width="300 height="30" type="application/x-shockwave-flash" data="'+baseDir+'swf/flashmediaelement.swf"><param name="movie" value="'+baseDir+'mswf/flashmediaelement.swf" /><param name="flashvars" value="controls=true&amp;file='+path+filename+'" /></object></video>');
            $('.video-audio').mediaelementplayer({
                renderers: ['flash_video'],
        		pluginPath: '/swf/',
            });
        } else if (type == 'mp3') {
        	$('#flashParcours').html('<audio type="audio/mp3" src="'+path+filename+'" controls width="300"></audio>');
            $('audio').mediaelementplayer();
        }
        $('#flashParcours').show();
    }
}

function DisplayCoach(id_coach) {
	$('#coach_description').html('');
	$('#calendrier').html('');
	if (id_coach) {
    	var CSRFGuard_token = $('#CSRFGuard_token').val();
    	$.post(
    		baseDir+'ajax/show_coach/',
    		{ id_coach:id_coach, CSRFGuard_token:CSRFGuard_token },
    		function (data) {
                $('#coach_description').html(data.content);
                DisplayCalendar(id_coach);
    		},
    	'json');
    }
}


function DisplayCalendar(id_coach) {
	var CSRFGuard_token = $('#CSRFGuard_token').val();

    $('#calendrier').fullCalendar({
		header: {
			left: 'title',
			right: 'prev,next today,month,agendaWeek,agendaDay'
		},
        timezone: 'local',
		defaultView: 'agendaWeek',
		editable: false,
		buttonText: {
		    today:	'Aujourd\'hui',
		    month:	'Mois',
		    week:	'Semaine',
		    day:	'Jour',
		},
		monthNames: ['Janvier','Février','Mars','Avril','Mai','Juin','Juillet','Août','Septembre','Octobre','Novembre','Décembre'],
		monthNamesShort:['janv.','févr.','mars','avr.','mai','juin','juil.','août','sept.','oct.','nov.','déc.'],
		dayNames: ['Dimanche','Lundi','Mardi','Mercredi','Jeudi','Vendredi','Samedi'],
		dayNamesShort: ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'],
		titleFormat: {
			month: 'MMMM YYYY', // ex : Janvier 2010
			week: 'D MMMM YYYY',
			day: 'dddd D MMMM YYYY' // ex : Jeudi 14 Janvier 2010
    	},
    	allDaySlot: false,
		columnFormat: {
			month: 'ddd', // Ven.
			week: 'ddd D', // Ven. 15
			day: 'ddd D'
		},
		selectable: true,
		selectHelper: true,
		select: function(start, end) {
    	    var date_start = new Date(start._d.toString());
    	    var date_fr_start = moment(date_start).format('YYYY-MM-DD HH:mm:ss');
    	    $('#rdv_date').val(date_fr_start);
        },
		axisFormat: 'H(:mm)',
		timeFormat: 'H:mm', // uppercase H for 24-hour clock
		firstDay:1, // Lundi premier jour de la semaine

        events: function(start, end, timezone, callback) {
            $.post(
                baseDir+'ajax/show_coach_calendar/',
                { id_coach:id_coach, CSRFGuard_token:CSRFGuard_token, start:start.unix(), end:end.unix() },
                function (data) {
                    $('#calendrier').fullCalendar('removeEvents');
                    $('#calendrier').fullCalendar('addEventSource', data);
                    $('#calendrier').fullCalendar('rerenderEvents');
        		},
        	'json');
        }

	});

}


function closeModalBadges(id) {
	if (!$('#menu-trophy').length) {
		$('#ModalBadges'+id).modal('hide');
		return;
	}

	$('.modal-backdrop').fadeOut('slow');

	var height = $('#menu-trophy').outerHeight();
	var width = $('#menu-trophy').outerWidth();
	var top = $('#menu-trophy').offset().top - $(window).scrollTop();
	var left = $('#menu-trophy').offset().left;

	var left2 = $('#ModalBadges'+id+' .modal-dialog').offset().left;

	$('#ModalBadges'+id+' .modal-content').animate({
		top: top-30,
		left: left-left2,
		width: width,
		height: height,
	}, 700, 'linear', function() {
		$('#ModalBadges'+id).modal('hide');
		$('#ModalBadges'+id).remove();
	});

}


//Fix links in ckeditor
$.fn.modal.Constructor.prototype.enforceFocus = function() {
  modal_this = this
  $(document).on('focusin.modal', function (e) {
    if (modal_this.$element[0] !== e.target && !modal_this.$element.has(e.target).length
    && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_select')
    && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_text')) {
      modal_this.$element.focus()
    }
  })
};

function startWaypoints()
{
	$('.waypoint').css({opacity: '0'});
	$('.waypoint').each(function() {
		var id = $(this).attr('id');

		new Waypoint({
			element: document.getElementById(id),
			handler: function () {
				var id = this.element.id;

				var jElement = $(this.element);
				var delayTime = jElement.data('delay');
				if (typeof delayTime == 'undefined')
					delayTime = 100;
				else
					delayTime = delayTime*1000;

				var fadeClass = jElement.data('class');
				if (typeof fadeClass == 'undefined')
					var fadeClass = 'fadeInUp';

				jElement.delay(delayTime).queue(function(next) {
					jElement.css('opacity', 1);
					jElement.css('max-height', '').css('height', '');
					jElement.addClass('animated '+fadeClass);
					delayTime = 0;
					refreshWaypoints();
					next();
				});

				this.destroy();
			},
			offset: '90%'
		});

	});
}

function refreshWaypoints()
{
	Waypoint.refreshAll();
}
