$(document).ready(function () {
    ForumModalAddAudio.init();
});

let ForumModalAddAudio = {
    origin: '',
    elementId: 0,
    parentCommentId: 0,
    mediaRecorder: null,
    audioChunks: [],

    init: function() {
        initWaveSurfer();

        $(document).on('click', '#ModalAddMessage .add-post-audio', (e) => {
            e.preventDefault();
            ForumModalAddAudio.display('post');
        });

        $(document).on('click', '.message-add-comment-container .add-comment-audio', (e) => {
            e.preventDefault();
            let messageId = $(e.currentTarget).closest('.message-container').data('message-id');
            let parentCommentId = null;
            if ($(e.currentTarget).closest('.comment').length) {
                parentCommentId = $(e.currentTarget).closest('.comment').data('comment-id');
            }
            ForumModalAddAudio.display('comment', messageId, parentCommentId);
        });

        $(document).on('click', '#ModalAddAudio .btn-save', (e) => {
            e.preventDefault();
            $(e.currentTarget).attr('disabled', true);
            $(e.currentTarget).find('i').removeClass('fa-save').addClass('fa-circle-o-notch fa-spin');
            let url = $(e.currentTarget).closest('.file').find('audio').attr('src');
            ForumModalAddAudio.saveAudio(url);
        });
    },

    display: function (origin, elementId, parentCommentId) {
        this.origin = origin;
        this.elementId = elementId;
        this.parentCommentId = parentCommentId;

        $('#ModalAddAudio').trigger('reset');
        this.resetRecorder();

        $('#ModalAddAudio').unbind('hide.bs.modal');

        if (this.origin === 'post') {
            ForumModalPost.hide();
            $('#ModalAddAudio').bind('hide.bs.modal', function () {
                ForumModalPost.show();
            });
        }

        $('#ModalAddAudio').modal('show');
    },

    saveAudio: async function (audioUrl) {
        const self = this;

        let audioBlob = await fetch(audioUrl).then(r => r.blob());

        const formData = new FormData();
        formData.append('audio', audioBlob, 'recording.wav');
        formData.append('CSRFGuard_token', CSRFGuard_token);

        $.ajax({
            url: baseDir + 'ajax/formation/forum/upload-audio/',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                $('#ModalAddAudio #recorder0 .stopButton').hide();
                $('#ModalAddAudio #recorder0 .recordButton').show();
                if (response.status) {
                    self.addNewAudio(response.url);
                    self.hide();
                    self.resetRecorder();
                } else {
                    TriggerGritter('error', response.message);
                }
            }
        });
    },

    resetRecorder: function () {
        $('#ModalAddAudio #files').html('');
    },

    addNewAudio: function (audioUrl, origin, elementId, parentCommentId) {
        if (origin) {
            this.origin = origin;
        }
        if (elementId) {
            this.elementId = elementId;
        }
        if (parentCommentId) {
            this.parentCommentId = parentCommentId;
        }
        let uniqId = uniqid('audio_');
        let audioElement = '<div class="audio">' +
            '    <div class="wavesurfer message-audio" id="wavesurfer-message-' + uniqId + '" data-audio-file="' + audioUrl + '">\n' +
            '        <button class="play-button">\n' +
            '            <i class="fa-solid fa-play"></i>\n' +
            '        </button>\n' +
            '        <div class="waveform"></div>\n' +
            '        <div class="controls">\n' +
            '            <div class="timecode">\n' +
            '                <span class="current-time">00:00</span>\n' +
            '            </div>\n' +
            '            <div class="volume">\n' +
            '                        <i class="fa-solid fa-volume"></i>\n' +
            '                        <input class="volume-slider" type="range" name="volume-slider" min="0" max="100" value="50">\n' +
            '            </div>\n' +
            '        </div>\n' +
            '        <button type="button" class="btn-remove-audio">\n' +
            '            <i class="fa-solid fa-xmark"></i>\n' +
            '        </button>\n' +
            '    </div>\n' +
            '    <input type="hidden" name="audio" value="' + audioUrl + '">\n' +
            '</div>';

        if (this.origin === 'post') {
            $('#ModalAddMessage .audios-container').append(audioElement).removeClass('d-none');
            //$('#ModalAddMessage .form-audio-container .add-post-audio').addClass('d-none');
            //$('#ModalAddMessage #audio').val(audioUrl);
        } else if (this.origin === 'comment') {
            if (this.parentCommentId) {
                $('#message' + this.elementId + ' #comment' + this.parentCommentId + ' .message-add-comment-container .audios-container').html(audioElement).removeClass('d-none');
            } else {
                $('#message' + this.elementId + ' .message-add-comment-container').first().find('.audios-container').html(audioElement).removeClass('d-none');
            }
            //$('.message-add-comment-container .form-audio-container .add-post-audio').addClass('d-none');
            //$('.message-add-comment-container #audio').val(audioUrl);
        }
        $('#wavesurfer-message-' + uniqId).initAudioFile();
    },

    hide: function () {
        $('#ModalAddAudio').modal('hide');
    }
}

window.ForumModalAddAudio = ForumModalAddAudio;
