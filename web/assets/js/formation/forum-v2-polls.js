$(document).ready(function () {
    ForumModalAddPoll.init();
});

let ForumModalAddPoll = {
    origin: '',
    elementId: 0,
    optionCount: 2,

    init: function() {
        if ($.fn.datepicker) {
            $('#poll-expiration').datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true,
                todayHighlight: true,
                startDate: new Date()
            });
        }

        if ($.fn.sortable) {
            this.initSortable();
        }

        $(document).on('click', '#ModalAddMessage .add-post-poll', (e) => {
            e.preventDefault();
            ForumModalAddPoll.display('post');
        });

        $(document).on('click', '.btn-add-option', (e) => {
            e.preventDefault();
            ForumModalAddPoll.addOption();
        });

        $(document).on('click', '.btn-remove-option', (e) => {
            e.preventDefault();
            if ($('.poll-option-row').length > 2) {
                $(e.currentTarget).closest('.poll-option-row').remove();
            } else {
                alert(__('Un sondage doit avoir au moins 2 options'));
            }
        });

        $(document).on('click', '.btn-create-poll', (e) => {
            e.preventDefault();
            ForumModalAddPoll.createPoll();
        });

        $(document).on('click', '.btn-remove-poll', function(e) {
            e.preventDefault();
            $(this).closest('.poll-preview').remove();
            $('#ModalAddMessage .poll-container').addClass('d-none');
        });
    },

    display: function (origin, elementId) {
        this.origin = origin;
        this.elementId = elementId;

        // Réinitialiser le formulaire
        $('#poll-question').val('');
        $('#poll-multiple-choice').prop('checked', false);
        $('#poll-expiration').val('');

        $('#poll-options-container').html('');
        this.optionCount = 0;
        this.addOption();
        this.addOption();

        if ($.fn.sortable) {
            this.initSortable();
        }

        $('#ModalAddPoll').unbind('hide.bs.modal');

        if (this.origin === 'post') {
            ForumModalPost.hide();
            $('#ModalAddPoll').bind('hide.bs.modal', function () {
                ForumModalPost.show();
            });
        }
        $('#ModalAddPoll').modal('show');
    },

    initSortable: function() {
        $('.sortable-options').sortable({
            handle: '.poll-option-handle',
            axis: 'y',
            placeholder: 'poll-option-placeholder',
            forcePlaceholderSize: true,
            opacity: 0.7,
            tolerance: 'pointer',
            cursor: 'move',
        });
    },

    addOption: function() {
        this.optionCount++;
        let optionHtml = `
            <div class="poll-option-row mb-2">
                <div class="poll-option-handle"><i class="fa-solid fa-grip-dots-vertical"></i></div>
                <input type="text" class="form-control poll-option" tabindex="500" placeholder="${__('Option')} ${this.optionCount}">
                <button class="btn btn-remove-option" type="button"><i class="fa-regular fa-trash"></i></button>
            </div>
        `;
        $('#poll-options-container').append(optionHtml);

        if ($.fn.sortable) {
            $('#poll-options-container').sortable('refresh');
        }
    },

    createPoll: function() {
        let question = $('#poll-question').val().trim();
        if (!question) {
            alert(__('Veuillez saisir une question pour le sondage'));
            return;
        }

        let options = [];
        $('.poll-option').each(function() {
            let optionText = $(this).val().trim();
            if (optionText) {
                options.push(optionText);
            }
        });

        if (options.length < 2) {
            alert(__('Veuillez saisir au moins 2 options pour le sondage'));
            return;
        }

        let multipleChoice = $('#poll-multiple-choice').is(':checked');
        let expirationDate = $('#poll-expiration').val();

        let pollData = {
            question: question,
            options: options,
            multiple_choice: multipleChoice,
            end_date: expirationDate
        };
        this.addPollToForm(pollData);
        this.hide();
    },

    addPollToForm: function(pollData) {
        let optionsHtml = '';
        pollData.options.forEach(option => {
            optionsHtml += `<div class="poll-option-item">${option}</div>`;
        });

        let pollHtml = `
            <div class="poll-preview">
                <div class="poll-question">${pollData.question}</div>
                <div class="poll-options">${optionsHtml}</div>
                <div class="poll-info">
                    <span class="poll-type">${pollData.multiple_choice ? __('Choix multiples') : __('Choix unique')}</span>
                    ${pollData.end_date ? `<span class="poll-expiration">${__('Expire le')} ${ForumModalAddPoll.formatDateFr(pollData.end_date)}</span>` : ''}
                </div>
                <button type="button" class="btn-remove-poll">
                    <i class="fa-solid fa-circle-xmark"></i>
                </button>
                <input type="hidden" name="poll_data" value='${JSON.stringify(pollData)}'>
            </div>
        `;

        if (this.origin === 'post') {
            $('#ModalAddMessage .poll-container').html(pollHtml).removeClass('d-none');
        }
    },

    hide: function() {
        $('#ModalAddPoll').modal('hide');
    },

    removePoll: function() {
        if (this.origin === 'post') {
            $('#ModalAddMessage .poll-container').html('').addClass('d-none');
        }
    },

    formatDateFr: function(dateStr) {
        if (!dateStr) return '';

        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return dateStr;

        const months = [
            'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
            'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
        ];

        const day = date.getDate();
        const month = months[date.getMonth()];
        const year = date.getFullYear();

        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');

        return `${day} ${month} ${year} à ${hours}h${minutes}`;
    }
};

window.ForumModalAddPoll = ForumModalAddPoll;
