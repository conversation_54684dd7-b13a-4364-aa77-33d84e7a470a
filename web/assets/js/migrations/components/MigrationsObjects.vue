<template>
    <div class="Objects">
        <div class="Objects-objectsContent">
            <div class="ObjectsContent" :class="(type === 'mail_formulaires' ? 'noThemeBox' : '')">
                <p class="ObjectsContent-title">{{ objectsTitle }}</p>
                <div class="ObjectsContent-carousel">
                    <MigrationsObjectsCarousel/>
                </div>
            </div>
            <MigrationsThemeComponent v-if="type !== 'mail_formulaires'"/>
        </div>
    </div>
</template>

<script>
    import MigrationsObject from "./MigrationsObject.vue";
    import MigrationsObjectsCarousel from "./MigrationsObjectsCarousel.vue";
    import MigrationsThemeComponent from "./MigrationsThemeComponent.vue";

    export default {
        name: 'MigrationsObjects',
        components: {
            MigrationsObject,
            MigrationsObjectsCarousel,
            MigrationsThemeComponent
        },
        data() {
            return {
                type: type
            }
        },
        computed: {
            objects() {
                return this.$store.getters.objects
            },
            objectsTitle() {
                let text = '';
                switch (this.type) {
                    case 'popups': text = this.$gettext('Sélectionnez un popup à prévisualiser :'); break;
                    case 'tunnels': text = this.$gettext('Sélectionnez un tunnel pour prévisualiser les pages :'); break;
                    case 'sites': text = this.$gettext('Sélectionnez un site à prévisualiser :'); break;
                    case 'formations': text = this.$gettext('Sélectionnez une formation à prévisualiser :'); break;
                }
                return text;
            },
        },
    }
</script>

<style>
    .Objects {
        padding-top: 8px;
        background: #f8f9fa;
        position: relative;
        width: auto;
        height: 243px;
        color: black;
    }
    .Objects-objectsContent {
        width: 90%;
        margin: 0 auto;
        padding-right: 15px;
        padding-left: 15px;
    }
    .ObjectsContent {
        float: left;
        width: calc(100% - 300px);
        padding: 0;
        height: 100%;
    }
    .noThemeBox {
        width: 100%;
    }
    .ObjectsContent-title {
        color: black;
        font-size: 16px;
        margin-bottom: 12px;
        padding-top: 18px;
    }
    .ObjectsContent-carousel {
        position: relative;
        width: auto;
        height: 170px;
        max-height: 170px;
    }
</style>
