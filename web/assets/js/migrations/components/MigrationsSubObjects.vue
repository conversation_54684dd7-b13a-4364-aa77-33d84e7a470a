<template>
    <div class="SubObjectsList isDark">
        <div class="container" :class="(((subObjects && subObjects.length > 1) || selectedSubObjectsType == 'mails') ? 'hasSubObjects' : '')">
            <div class="SubObjectsList-list">
                <p class="Objects__container__title">{{ subObjectsTitle }}</p>
                <div class="SubObjectsContainer">
                    <div class="SubObjectsContainer-objectsContent">
                        <template v-for="subObject in subObjects">
                            <div class="SubObjectsContainer-objectsContent-objects" v-for="object in subObject.children" v-if="!selectedSubSubObjectsType && subObject.id === selectedSubObjectsType && !object.children">
                                <MigrationsSubObject :subObject="object"/>
                            </div>
                            <template v-for="subObject in subObject.children" v-if="selectedSubSubObjectsType && selectedSubSubObjectsType === subObject.id">
                                <div class="SubObjectsContainer-objectsContent-objects" v-for="object in subObject.children">
                                    <MigrationsSubObject :subObject="object"/>
                                </div>
                            </template>
                        </template>
                    </div>
                </div>
            </div>
            <template v-if="((subObjects && subObjects.length > 1) || selectedSubObjectsType == 'mails')">
                <div class="ObjectsList__theme" style="float: left; width: 300px; height: 228px; padding: 0px 0px 15px 24px; border-left: 2px solid #abb0b5;">
                    <p class="Objects__container__title" v-translate>Choisissez un outil à prévisualiser :</p>
                    <div class="form-group" style="margin-bottom: 20px;">
                        <div class="controls">
                            <select style="width: 100%!important;" v-model="selectedSubObjectsType" @change="changeSelectedSubObjectsType" class="form-control input-small">
                                <option v-for="subObject in subObjects" :value="subObject.id" v-html="subObject.name"></option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group" v-if="selectedSubObjectsType === 'mails'">
                        <div class="controls">
                            <select style="width: 100%!important;" v-model="selectedSubSubObjectsType" @change="(e) => changeSelectedSubSubObjectsType(e)" class="form-control input-small">
                                <template v-for="subObject in subObjects" v-if="subObject.id === selectedSubObjectsType">
                                    <option v-for="subSubObject in subObject.children" :value="subSubObject.id" v-html="subSubObject.name"></option>
                                </template>
                            </select>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
    import MigrationsSubObject from "./MigrationsSubObject.vue";

    export default {
        name: 'MigrationsSubObjects',
        components: {
            MigrationsSubObject
        },
        data() {
            return {
                type: type
            }
        },
        created() {
            if (!this.selectedSubObjectsType && this.subObjects) {
                this.$store.commit('updateSelectedSubObjectsType', this.subObjects[0].id)

                if (this.subObjects[0].id == 'mails') {
                    this.$store.commit("updateSelectedSubSubObjectsType", this.subObjects[0].children[0].id)
                }
            }
        },
        computed: {
            masterTheme() {
                return this.$store.getters.masterTheme;
            },
            objects() {
                return this.$store.getters.objects
            },
            popupPreviewError() {
                return this.$store.getters.popupPreviewError
            },
            selectedObject() {
                return this.$store.getters.selectedObject
            },
            selectedSubObject() {
                return this.$store.getters.selectedSubObject
            },
            selectedSubObjectsType: {
                get: function () {
                    return this.$store.getters.selectedSubObjectsType
                },
                set: function (subObjectType) {
                    this.$store.commit('updateSelectedSubObjectsType', subObjectType);
                }
            },
            selectedSubSubObjectsType: {
                get: function () {
                    return this.$store.getters.selectedSubSubObjectsType
                },
                set: function (subSubObjectType) {
                    this.$store.commit('updateSelectedSubSubObjectsType', subSubObjectType);
                }
            },
            specificTheme() {
                return this.$store.getters.specificTheme;
            },
            subObjects() {
                return this.$store.getters.subObjects
            },
            subObjectsTitle() {
                let text = this.$gettext('Sélectionnez une page à prévisualiser :');
                switch (this.selectedSubObjectsType) {
                    case 'mails': text = this.$gettext('Sélectionnez un mail à prévisualiser :'); break;
                    case 'articles': text = this.$gettext('Sélectionnez un article à prévisualiser :'); break;
                }
                return text;
            }
        },
        methods: {
            changeSelectedSubObjectsType() {
                let object = null;

                if (this.selectedSubObjectsType == 'mails') {
                    this.selectedSubSubObjectsType = 'undefined';
                } else {
                    this.selectedSubSubObjectsType = null;
                }

                for (let subObject of this.subObjects) {
                    if (subObject.id == this.selectedSubObjectsType) {
                        object = this.selectedSubObjectsType !== 'mails' ? subObject.children[0] : subObject.children[0].children[0];
                    }
                }

                this.$store.commit('updateSelectedSubObject', object);
                this.updateSubObject(object)
            },
            changeSelectedSubSubObjectsType(e) {
                let object = null;

                let subObjects = this.subObjects
                this.subObjects.forEach(function (element, index) {
                    if (element.id == 'mails') {
                        object = subObjects[index].children[e.target.options.selectedIndex].children[0];
                    }
                })

                this.$store.commit('updateSelectedSubObject', object);
                this.updateSubObject()
            },
            async updateSubObject() {
                this.$store.commit('updatePopupPreviewError', false);
                this.$store.commit('updateLoadingPreview', true);

                if (this.type == 'formations' || this.selectedSubObject.id_migrated == null || this.selectedSubObject.id_migrated == '') {
                    await this.$store.dispatch('updateMigration')
                }

                if (!this.popupPreviewError && this.type !== 'formations' && (this.selectedSubObject.specificTheme != this.specificTheme.id_specific_theme || this.selectedSubObject.masterTheme != this.masterTheme.id_master_theme)) {
                    await this.$store.dispatch('updateThemes')
                }
            }
        }
    }
</script>

<style scoped>
    .container {
        width: 90%;
        margin: 0 auto;
    }
    .container.hasSubObjects {
        display: flex;
    }
    .Objects__container__title {
        color: white;
        font-size: 16px;
        margin-bottom: 12px;
        padding-top: 18px;
    }
    .SubObjectsList {
        padding-top: 0px;
        background: #f8f9fa;
        position: relative;
        width: auto;
        height: 243px;
        color: black;
    }
    .SubObjectsList.isDark {
        background: rgb(23, 28, 34);
        color: white;
    }
    .SubObjectsList > .container {
        padding-top: 5px;
    }
    .SubObjectsList-list {
        float: left;
        padding: 0px;
        height: 100%;
        width: 100%;
    }
    .container.hasSubObjects .SubObjectsList-list {
        width: calc(100% - 300px);
    }
    .SubObjectsContainer {
        position: relative;
        width: auto;
        height: 170px;
        max-height: 170px;
    }
    .SubObjectsContainer-objectsContent {
        height: 183px;
        overflow:auto hidden;
        position: absolute;
        width: 100%;
        white-space: nowrap;
        padding-right: 10px;
    }
    .SubObjectsContainer-objectsContent::-webkit-scrollbar {
        width: 10px;
        height: 10px;
    }
    .SubObjectsContainer-objectsContent::-webkit-scrollbar-thumb {
        background-color: #c5ccd6;
        border: none;
        border-radius: 12px;
    }
    .SubObjectsContainer-objectsContent::-webkit-scrollbar-track {
        background: transparent;
    }
    .SubObjectsContainer-objectsContent-objects {
        display:inline-block;
        background: transparent;
        padding: 0px 0px 20px 0px;
    }
</style>
