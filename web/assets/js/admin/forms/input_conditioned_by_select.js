$(document).ready(function(){
    $('select[name="select-conditional-input"]').on('change', function() {
        let value = $(this).val().toString();
        let showInput = $(this).data("show-input");
        let $formGroup = $('#form-group-' + $(this).data("name"));

        $formGroup.find('input').val(value);
        if (showInput.includes(value)) {
            $formGroup.show();
        } else {
            $formGroup.hide();
        }
    });
});
