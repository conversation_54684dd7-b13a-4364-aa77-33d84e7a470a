$(function () {
    "use strict";
    var url = baseDir+"lib/upload/",
        uploadButton = $("<button id=\"buttonUpload\"/>")
            .addClass("btn btn-primary")
            .prop("disabled", true)
            .text("Traitement...")
            .on("click", function () {
                var $this = $(this), data = $this.data();
                $this
                    .off("click")
                    .text("Abort")
                    .on("click", function () {
                        $this.remove();
                        data.abort();
                    });
                data.submit().always(function () {
                    $this.remove();
                });
            });
    $("#fileupload").fileupload({
        url: url,
        dataType: "json",
        autoUpload: true,
        disableImageResize: /Android(?!.*Chrome)|Opera/.test(window.navigator.userAgent),
        previewMaxWidth: 100,
        previewMaxHeight: 100,
        previewCrop: true
    }).on("fileuploadadd", function (e, data) {
        data.context = $("<div/>").appendTo("#files");
        $.each(data.files, function (index, file) {
            var node = $("<p/>").append($("<span/>").text(file.name));
            if (!index) {
                node.append("<br>").append(uploadButton.clone(true).data(data));
            }
            node.appendTo(data.context);
        });
    }).on("fileuploadprocessalways", function (e, data) {
        var index = data.index,
            file = data.files[index],
            node = $(data.context.children()[index]);
        if (file.preview) {
            node.prepend("<br>").prepend(file.preview);
        }
        if (file.error) {
            node.append("<br>").append(file.error);
        }
        if (index + 1 === data.files.length) {
            data.context.find("button").text("Upload").prop("disabled", !!data.files.error);
        }
    }).on("fileuploadprogressall", function (e, data) {
        var progress = parseInt(data.loaded / data.total * 100, 10);
        $("#progress .progress-bar").css("width", progress + "%");

    }).on("fileuploaddone", function (e, data) {
        $.each(data.result.files, function (index, file) {
            if (file.error) {
                $('#files').append('<div class="alert alert-danger">' + __('Une erreur est survenue, vérifiez les permissions du dossier "medias/upload"') + '</div>');
            } else {
                var link = $("<a>").attr("target", "_blank").prop("href", file.url);
                $(data.context.children()[index]).wrap(link);
                $("#buttonUpload").remove();            
                $("#attachments").append("<input type=\"hidden\" name=\"attachments[]\" value=\""+file.name+"\">");
            }
        });
    }).on("fileuploadfail", function (e, data) {
        if (typeof data.result !== "undefined" && data.result) {
            $.each(data.result.files, function (index, file) {
                var error = $("<span/>").text(file.error);
                $(data.context.children()[index]).append("<br>").append(error);
            });
        } else {
            $('#files').append('<div class="alert alert-danger">' + __('Erreur') + ' : '+data.errorThrown.message+'</div>');
        }
    }).prop("disabled", !$.support.fileInput)
        .parent().addClass($.support.fileInput ? undefined : "disabled");
});