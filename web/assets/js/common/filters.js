let sortSelect = $('.filters-inputs #sort')
let orderBy = $('#filtres input#orderBy');

sortSelect.change(function() {
    if($(this).val() === 'f.nomformation' || $(this).val() === 't.nom') {
        orderBy.val('asc');
    } else {
        orderBy.val('desc');
    }
})

$(".datepicker-date").datetimepicker({
    language: language,
    minuteStep: 5,
    format: "yyyy-mm-dd",
    autoclose: true,
    todayBtn: true,
    todayHighlight: true,
    fontAwesome: true,
    viewSelect: "month",
    minView: "month",
    maxView: "year",
}).on('show', function (ev) {
    $('.datetimepicker table thead').show();
});
