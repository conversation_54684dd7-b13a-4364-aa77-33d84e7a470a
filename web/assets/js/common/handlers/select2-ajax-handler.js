/** select2 AJAX Handler
 * Enables the Select2 to get the search responses via an Ajax call
 * Options :
 *
 * ajaxUrl : URL to the ajax file
 * returnText : the return text that will be shown. Must use a property of the returned item ex "item.name"
 *              Can also pass an array to concatenate multiple item properties ex "['nom', 'prenom'] will return item.nom + ' ' + item.prenom
 * returnId : The Id of the returned object. Must use a property of the returned item ex "item.id_element". Used after in the form
 * minInputLength : the minimum input before the ajax request fires. Default 2
 * quietMilliseconds: After input, number of milliseconds before firing the Ajax request. Default 50
 *
 ***/
(($) => {
    function Select2AjaxHandler(el, options) {
        this.$el = $(el);
        this.defaultOptions = {
            ajaxUrl: '',
            minInputLength: 2,
            quietMilliseconds: 50
        };
        this.options = $.extend(this.defaultOptions, options);
        this.init();
    }

    $.extend(Select2AjaxHandler.prototype, {
        init() {
            if ('' === this.options.ajaxUrl || '' === this.options.returnText || '' === this.options.returnId || !Number.isInteger(this.options.quietMilliseconds)) {
                console.error('Select2Ajax option error');
                return;
            }
            let minInputLength = this.options.minInputLength;
            let returnText = this.options.returnText;
            let returnId = this.options.returnId;

            this.$el.select2({
                minimumInputLength: minInputLength,
                language: {
                    searching: function () {
                        return __('Recherche en cours…');
                    },
                    inputTooShort: function () {
                        return __('Saisissez au moins %s caractères.', minInputLength);
                    },
                    noResults: function () {
                        return __('Aucun résultat trouvé.');
                    },
                    errorLoading: function () {
                        return __('Les résultats sont en cours de chargement ou ne peuvent pas être chargés.');
                    },
                },
                ajax: {
                    url: baseDir + this.options.ajaxUrl,
                    dataType: 'json',
                    type: "POST",
                    quietMillis: this.options.quietMilliseconds,
                    data: function (params) {
                        return {
                            search: params.term,
                            CSRFGuard_token: $('#CSRFGuard_token').val()
                        };
                    },
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (item) {
                                let returnTextFormatted = '';
                                if (Array.isArray(returnText)) {
                                    returnText.forEach(function (returnTextElement) {
                                        returnTextFormatted += item[returnTextElement] + ' ';
                                    });
                                } else {
                                    returnTextFormatted = item[returnText]
                                }
                                return {
                                    text: returnTextFormatted,
                                    id: item[returnId]
                                }
                            })
                        };
                    }
                }
            });
        }
    })

    $.extend($.fn, {
        select2AjaxHandler(options = {}) {
            this.each((index, el) => new Select2AjaxHandler(el, options));
        },
    });
})(jQuery);
