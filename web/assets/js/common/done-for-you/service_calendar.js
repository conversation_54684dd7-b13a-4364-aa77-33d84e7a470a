(function ($) {
    $(document).ready(function () {
        $('#service_calendar').fullCalendar({
            defaultView: 'month',
            header: {
                left: 'prev,next today',
                center: 'title',
                right: 'month,agendaWeek,agendaDay'
            },
            handleWindowResize: true,
            height: 700,
            allDayDefault: true,
            firstDay: 1,
            lazyFetching: true,
            displayEventTime: true,
            minTime:'08:00:00',
            maxTime:'22:00:00',
            displayEventEnd: true,
            events: function(start, end, timezone, callback) {
                let coachId = $('#service_calendar').attr('data-coachId') ?? null;

                let data = {
                    start: start.format(),
                    end: end.format(),
                    CSRFGuard_token:CSRFGuard_token,
                    coachId: coachId
                };

                $.ajax({
                    url: '/ajax/done_for_you/offer/json/fullcalendar/',
                    data: data,
                    success: function(events) {
                        callback(events);
                    }
                });
            },
            eventClick: function(event) {
                window.location = event.serviceUrl;
            },
            eventRender: function(event, element) {
                $(element).tooltip({
                    title: event.description ?? event.title,
                    delay: {
                        show: 500,
                        hide: 100
                    }
                });
            },
            eventDataTransform: function(eventData){
                if (eventData.allDay != false && eventData.end !== eventData.start) {
                    eventData.end = moment(eventData.end).add(1, 'days').format();
                }
                return eventData;
            },
            editable: false,
            droppable: false
        });
    });
})(jQuery);
