$(function () {

    //Commentaires
	var working = false;

	$('#addCommentForm').submit(function(e){
		e.preventDefault();
		if(working) return false;
		working = true;

		$('#addCommentForm #submit').html(__('Traitement...')).attr('disabled', true);
		$('#addCommentForm #error').html('');
		$('#addCommentForm span.help-inline').remove();

    	var CSRFGuard_token = $('#addCommentForm input[name="CSRFGuard_token"]').val();

		$.post(baseDir+'ajax/commentaire/',
		{ action: 'add_comment', data: $(this).serialize(), CSRFGuard_token:CSRFGuard_token },
		function(msg) {
			working = false;
			$('#addCommentForm #submit').html(__('Valider')).attr('disabled', false);
			$('.control-group').removeClass('error');

			if(msg.status) {
				if (msg.redirection) {
    			    window.location.href = msg.redirection;
    			}

    			$('#alert-nocomment').remove();
				$('#comments').prepend(msg.html);
				$('#'+msg.id).slideDown();
				$('#addCommentForm')[0].reset();
				$('#addCommentForm .form-group').removeClass('has-error');
			} else {
				if (msg.message) {
					$('#addCommentForm #error').append('<div class="alert alert-danger">'+msg.message+'</div>');
				} else if (msg.errors) {
					$.each(msg.errors,function(k,v){
						$('#group'+k).addClass('has-error');
						$('#control'+k).after('<span class="help-inline" style="font-size:14px">'+v+'</span>');
					});
				}
			}
		},'json');

	});


	$('#addReponseForm').submit(function(e){
		e.preventDefault();
		if(working) return false;
		working = true;

		$('#addReponseForm #submit-reponse').html(__('Traitement..')).attr('disabled', true);
		$('#addReponseForm span.help-inline').remove();

		var id_orig_reponse = $('#orig_comment').val();
    	var CSRFGuard_token = $('#addReponseForm input[name="CSRFGuard_token"]').val();

		$.post(baseDir+'ajax/commentaire/',
		{ action: 'add_comment', data: $(this).serialize(), CSRFGuard_token:CSRFGuard_token },
		function(msg) {
			working = false;
			$('#submit-reponse').html('<i class="fa fa-comment"></i> ' + __('Répondre')).attr('disabled', false);
			$('.control-group').removeClass('error');

			if(msg.status) {
				if (msg.redirection) {
    			    window.location.href = msg.redirection;
    			}

				if ($('#'+id_orig_reponse+' .childrens').length > 0) {
					$('#'+id_orig_reponse+' .childrens').first().append(msg.html);
					$('#'+id_orig_reponse+' .childrens').css('display', 'block');
					$('#'+msg.id).slideDown();
				} else {
					$('#'+id_orig_reponse).append('<div class="childrens" style="display:block">'+msg.html+'</div>');
					$('#'+msg.id).slideDown();
				}

				CancelReply();
			} else {
				if (msg.message) {
					$('#addReponseForm #error').append('<div class="alert alert-danger">'+msg.message+'</div>');
				} else if (msg.errors) {
                    $.each(msg.errors,function(k,v){
					    $('#group-reponse-'+k).addClass('has-error');
                        $('#control-reponse-'+k).append('<span class="help-inline">'+v+'</span>');
                    });
                }
			}
		},'json');

	});

});


function DisplayComments(idArticle, start, idElement = null) {
    let $element = $('.comments');
    if (idElement !== null) {
        let $elementById = $('#comments'+ idElement).find('.comments');
        if ($elementById.length > 0) {
            $element = $elementById;
        }
    }
    $element.append('<img id="loader" src="'+cdnDir+'assets/images/ajax-loader.gif">');

	let CSRFGuard_token = $('#CSRFGuard_token').val();
	$.post(
		baseDir+'ajax/update_comments/',
		{ id_article:idArticle, start:start, CSRFGuard_token:CSRFGuard_token },
		function (data) {
            if (idElement === null) {
                $('#loader').remove();
                $('#shownextelements').remove();
            } else {
                $element.find('#loader').remove();
                $element.find('#shownextelements').remove();
            }

			if (data.status) {
				if (data.content !== '') {
                    $element.append(data.content);
                    let lastCommentId = $element.find(".comment:not(.reply_to)").last().attr('id');

                    $element.append('<div id="shownextelements"><a href="#" class="btn" onclick="DisplayComments(\''+idArticle+'\', '+lastCommentId+', '+idElement+'); return false;">' + __('Afficher les commentaires suivants') + '</a></div>');
				} else {
                    $element.append('<div class="alert alert-info" id="alert-nocomment">' + __('Aucun commentaire pour l\'instant') + '</div>');
				}
			} else {
                $element.append('<div class="alert alert-danger">'+data.message+'</div>');
			}
		},
	'json');
}


function VerifEmail() {
	var email = $('input[type=email]').val();
	$('#controlemail').append('<span class="input-group-addon" id="loader"><img id="loader" src="'+cdnDir+'assets/images/ajax-loader2.gif"></span>');
	var CSRFGuard_token = $('#addCommentForm input[name="CSRFGuard_token"]').val();

	$.post(
		baseDir+'ajax/commentaire/',
		{ action: 'verif_email', email:email, CSRFGuard_token:CSRFGuard_token },
		function (data) {
			setTimeout(function() { $('#loader').remove(); }, 300);

			$('.control-group').removeClass('error');
			$('#erroremail').remove();

			if(data.status == 'invalidemail') {
				$('#groupemail').addClass('error');
				$('#controlemail').append('<span class="help-inline" style="font-size:14px" id="erroremail">' + __('Adresse email invalide') + '</span>');
			} else if(data.status == 'usernotshow') {
				$('#groupprenom').hide();
            } else if(data.status == 'usernotexists') {
				$('#groupprenom').show();
			} else if(data.status == 'userexists') {
				$('#groupprenom').hide();
			} else {
				$('#groupemail').addClass('error');
				if (data.message) {
    				$('#controlemail').append('<span class="help-inline" style="font-size:14px" id="erroremail">'+data.message+'</span>');
    				TriggerGritter('error', data.message);
				} else if (data.errors) {
    				$('#controlemail').append('<span class="help-inline" style="font-size:14px" id="erroremail">'+data.errors+'</span>');
    				TriggerGritter('error', data.errors);
				} else {
    				$('#controlemail').append('<span class="help-inline" style="font-size:14px" id="erroremail">'+data.status+'</span>');
    				TriggerGritter('error', data.status);
				}
			}
		},
	'json');

}

function VerifEmailReponse() {
	var email = $('#addReponseForm input[type=email]').val();
	var CSRFGuard_token = $('#addReponseForm input[name="CSRFGuard_token"]').val();

	$('#addReponseForm #control-reponse-email').append('<span class="input-group-addon" id="loader"><img id="loader" src="'+cdnDir+'assets/images/ajax-loader2.gif"></span>');

	$.post(
		baseDir+'ajax/commentaire/',
		{ action: 'verif_email', email:email, CSRFGuard_token:CSRFGuard_token },
		function (data) {

			setTimeout(function() { $('#loader').remove(); }, 300);

			$('#addReponseForm .control-group').removeClass('error');
			$('#addReponseForm #erroremail').remove();

			if(data.status == 'invalidemail') {
				$('#addReponseForm #group-reponse-email').addClass('error');
				$('#addReponseForm #control-reponse-email').append('<span class="help-inline" style="font-size:14px" id="erroremail">' + __('Adresse email invalide') + '</span>');
			} else if(data.status == 'usernotshow') {
				$('#groupprenom').hide();
			} else if(data.status == 'usernotexists') {
				$('#addReponseForm #group-reponse-prenom').show();
			} else if(data.status == 'userexists') {
				$('#addReponseForm #group-reponse-prenom').hide();
			} else {
				$('#addReponseForm #group-reponse-email').addClass('error');
				if (data.message) {
    				$('#addReponseForm #control-reponse-email').append('<span class="help-inline" style="font-size:14px" id="erroremail">'+data.message+'</span>');
    				TriggerGritter('error', data.message);
				} else if (data.errors) {
    				$('#addReponseForm #control-reponse-email').append('<span class="help-inline" style="font-size:14px" id="erroremail">'+data.errors+'</span>');
    				TriggerGritter('error', data.errors);
				} else {
    				$('#addReponseForm #control-reponse-email').append('<span class="help-inline" style="font-size:14px" id="erroremail">'+data.status+'</span>');
    				TriggerGritter('error', data.status);
				}
			}
		},
	'json');

}



function Reply(id_commentaire) {
    $('#orig_comment').val(id_commentaire);
    $('.comments #'+id_commentaire+' .childrens').first().before($('#ContentReponse'));
    $('.comments #'+id_commentaire+' #ContentReponse').css('display', 'block');
}

function CancelReply() {
    $('.comments #ContentReponse').css('display', 'none');
    $('#ContentReponse #error').html('');
    $('#addReponseForm')[0].reset();
    $('#ContentReponse .form-group').removeClass('has-error');
}

function DisplayAnswers(id_commentaire) {
	$('#'+id_commentaire+' .childrens').show();
	$('#'+id_commentaire+' .btn-show-answers').attr('onclick', 'HideAnswers('+id_commentaire+')').html('<i class="fa fa-caret-up"></i> <u class="main_color0">'+ __('Cacher les réponses')+'</u>');
	Masonry();
}
function HideAnswers(id_commentaire) {
	$('#'+id_commentaire+' .childrens').hide();
	$('#'+id_commentaire+' .btn-show-answers').attr('onclick', 'DisplayAnswers('+id_commentaire+')').html('<i class="fa fa-caret-down"></i> <u class="main_color0">'+ __('Afficher les réponses')+'</u>');
	Masonry();
}
