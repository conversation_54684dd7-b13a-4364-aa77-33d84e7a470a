$(document).ready(function() {

	/* ---------- Uniform ---------- */
	$("input:checkbox, input:radio, input:file").not('[data-no-uniform="true"],#uniform-is-ajax').uniform();

    /* ---------- Select2 ---------- */
    $('[data-rel="select2"],[rel="select2"]').select2({language: language});

	/* ---------- Tooltip ---------- */
	$('[rel="tooltip"],[data-rel="tooltip"]').tooltip({"placement":"bottom",delay: { show: 400, hide: 200 }});

	/* ---------- Popover ---------- */
	$('[rel="popover"],[data-rel="popover"]').popover();

    /* ---------- MediaElement ---------- */
    var startvolume = 1;
    if (window!=window.top) { startvolume = 0; }

    var mode = 'auto';
    if(navigator.appName.indexOf("Internet Explorer")!=-1) mode = 'shim';

    $('audio,video').not('#bgvid').mediaelementplayer({
        mode: mode,
        startVolume: startvolume,
        success: function(mediaElement, domObject) {
            if (mediaElement.pluginType == 'flash' && mediaElement.attributes.hasOwnProperty('autoplay')) {
                mediaElement.addEventListener('canplay', function() {
                    mediaElement.play();
                }, false);
            }
        }
    });
    $('video').bind('contextmenu', function(e) {
        return false;
    });


    // fade in for elements only on frontend!
    $("[data-delay]").each(function() {
    	var element = $(this);
    	setTimeout(function () {

        	var CSRFGuard_token = $('#CSRFGuard_token').val();
        	var elements_infos = $(element).data('el');

    		$.ajax({
                type: 'POST',
                url: baseDir+'ajax/page_display_element/',
                data: { elements_infos:elements_infos, CSRFGuard_token:CSRFGuard_token },
                dataType: 'json',
                async:true,
                success: function(data) { }
            });

            var fadeClass = element.data('class');

    		element.removeClass('hidden');
    		element.css('max-height', '').css('height', '');
    		//element.animate({ opacity: 1 });
    		element.addClass('animated '+fadeClass);

            //recreate mediaelementplayers
            if (element.find('.videoplayer video').length > 0) {
                element.find('.videoplayer').each(function() {
                    $(this).find('video').get(0).player.remove();
                    $(this).find('video').mediaelementplayer({
                        mode: mode,
                        startVolume: startvolume,
                        success: function(mediaElement, domObject) {
                            if (mediaElement.pluginType == 'flash' && mediaElement.attributes.hasOwnProperty('autoplay')) {
                                mediaElement.addEventListener('canplay', function() {
                                    mediaElement.play();
                                }, false);
                            }
                        }
                    });
                });
            }

            Masonry();

    	}, parseInt(element.data('delay'), 10) * 1000);
    });

    // fade in for elements only on frontend!
    $("[data-z-delay]").each(function() {
    	var element = $(this);
    	setTimeout(function () {

        	var CSRFGuard_token = $('#CSRFGuard_token').val();
        	var elements_infos = $(element).data('el');

    		$.ajax({
                type: 'POST',
                url: baseDir+'ajax/page_display_line/',
                data: { elements_infos:elements_infos, CSRFGuard_token:CSRFGuard_token },
                dataType: 'json',
                async:true,
                success: function(data) { }
            });

    		element.removeClass('hidden');
    		element.css('opacity', 0).css('max-height', '').css('height', '');
    		element.animate({ opacity: 1 });

            var id = element.attr('id');

            //recreate mediaelementplayers
            var mejs_players = new Array();
            var player;
            $('#'+id+' video').each(function() {
                player = $(this)[0].player;
                player.remove();
            });

            $('#'+id+' video, #'+id+' audio').not('#bgvid').mediaelementplayer();

            Masonry();

    	}, parseInt(element.data('z-delay'), 10) * 1000);
    });


    // fade out for elements only on frontend!
    $("[data-d-delay]").each(function() {
    	var element = $(this);
    	setTimeout(function () {
    		var fadeOutClass = element.data('d-class');

    		element.addClass('animated '+fadeOutClass);

    		setTimeout(function () {
        		element.remove();
                Masonry();
            }, 1000);

    	}, parseInt(element.data('d-delay'), 10) * 1000);
    });

    //set videos to fixed position
    if (window.matchMedia('(min-width: 768px)').matches) {
        $("[data-affix]").each(function() {
            var smallWidth = $(this).data('affix-width');
            var position = $(this).data('affix-position');
            var id_element = $(this).attr('id').replace('blocvideo_', '');

            var originalWidth = $("#blocvideo_" + id_element).width();
            var originalHeight = $("#blocvideo_" + id_element).height();
            const navbarHeight = $('.nav.navbar-nav').height();
            const adminbarHeight = $('#adminbar').height();

            //If the height was set to auto, all is calculated via padding, so forcing a height based on 16/9
            if (originalHeight <= 1) {
                originalHeight = originalWidth * 9 / 16;
            }

            var ratio = originalHeight / originalWidth;
            var smallHeight = smallWidth * ratio;

            $("#blocvideo_" + id_element).css("width", originalWidth + "px");
            $("#blocvideo_" + id_element).css("height", originalHeight + "px");

            //set element's height to keep it when video will be fixed positioned
            $('.box-masonry[data-id="' + id_element + '"]').css("height", originalHeight + "px");

            var elementTop = $('.box-masonry[data-id="' + id_element + '"]').offset().top;
            elementTop = elementTop + originalHeight;

            var is_affixed = false;
            $('.box-masonry[data-id="' + id_element + '"] .videoplayer').addClass('affix_' + position);
            $('.box-masonry[data-id="' + id_element + '"] .videoplayer').affix({offset: {top: elementTop}});

            $('.box-masonry[data-id="' + id_element + '"] .videoplayer').on("affix.bs.affix", function () {
                $('.box-masonry[data-id="' + id_element + '"] .videoplayer.affix_top_right').css("top", navbarHeight + adminbarHeight + 20 + "px");
                $('.box-masonry[data-id="' + id_element + '"] .videoplayer.affix_top_left').css("top", navbarHeight + adminbarHeight + 20 + "px");
                setVideoWidth(id_element, smallWidth, smallHeight, true);
                is_affixed = true;
            });

            $('.box-masonry[data-id="' + id_element + '"] .videoplayer').on("affix-top.bs.affix", function () {
                $('.box-masonry[data-id="' + id_element + '"] .videoplayer.affix_top_right').css("top", "unset");
                $('.box-masonry[data-id="' + id_element + '"] .videoplayer.affix_top_left').css("top", "unset");
                setVideoWidth(id_element, originalWidth, originalHeight, false);
            });

            //on page refresh, video can be affixed but on('affix.bs.affix') is not called
            setTimeout(function() {
                if (!is_affixed && $('.box-masonry[data-id="' + id_element + '"] .videoplayer').hasClass("affix")) {
                    setVideoWidth(id_element, smallWidth, smallHeight, true);
                    is_affixed = true;
                }
            }, 100);
        });
    }

    // FAQs
    $("#faq .faq").click(function() {
        $(this).find(".reponse").slideToggle('fast');
        setTimeout(function() { Masonry(); }, 500);
    });


    if ($('.form-staf').length > 0) {

        $('.form-staf').each(function() {
            $(this).submit(function(event) {
                event.preventDefault();
                var form = $(this);

                $('#STAFerror').remove();
                var CSRFGuard_token = $('#CSRFGuard_token').val();
                $.ajax({
                    type: 'POST',
                    url: baseDir+'ajax/sendtoafriend/',
                    data: { data: form.serialize(), CSRFGuard_token:CSRFGuard_token },
                    dataType: 'json',
                    async:false,
                    success: function(data) {
                        if (data.status) {
                            window.location.reload();
                        } else {
                            form.prepend('<div class="alert alert-danger" id="STAFerror">'+data.message+'</div>');
                            return false;
                        }
                    }
                });
            });
        });

    }

});


function Masonry() { }


function ShowIframe(idelement, module_name) {
    window.setDocumentDomain();
    window.removeIframeResizerStyling(idelement, module_name);

    setTimeout(function () {
        iFrameResize({
            log: false,
            checkOrigin: false,
            onResized: function () {
                setTimeout(function () {
                    Masonry();
                }, 100);
            }
        });

    }, 100);
}


function Subscribe(id_page, id_element) {
    $('selector').css('cursor', 'loading');
	var CSRFGuard_token = $('#CSRFGuard_token').val();
    $.ajax({
        type: 'POST',
        url: baseDir+'ajax/page_subscribe/',
        data: { id_page:id_page, data: $('#formcontainer'+id_element+' form').serialize(), CSRFGuard_token:CSRFGuard_token },
        dataType: 'json',
        async:false,
        success: function(data) {
    		$('selector').css('cursor', 'auto');
    		if (data.status && $('#formcontainer'+id_element+' form input[name="token"]').length > 0) {
        	   $('#formcontainer'+id_element+' form input[name="token"]').val(data.token);
    		}
        }
    });
    return true;
}


function ExecuteActionLearnyMail(id_element, type, random_id) {
    var CSRFGuard_token = $('#CSRFGuard_token').val();
    $.ajax({
        type: 'POST',
        url: baseDir+'ajax/lbar_get_actions_v2',
        data: { id_element:id_element, type:type, random_id:random_id, CSRFGuard_token:CSRFGuard_token },
        dataType: 'json',
        async:false,
        success: function(data) {
    		if (data.link) {
        		if (data.open_window) {
            		window.open(data.link);
                } else {
                    window.top.location = data.link;
                }
    		}
        }
    });
    return true;
}

function setVideoWidth(id_element, width, height, small) {
    $("#blocvideo_"+id_element).css("width", width + "px").css("height", height.toFixed()+"px");

    var affix_close = $("#blocvideo_"+id_element).data('affix-hide');
    $('.box-masonry[data-id="' + id_element + '"] .videoplayer .affix-close').remove();

    if (small) {
        $('.box-masonry[data-id="' + id_element + '"] .videoplayer').css("width", width + "px").css("height", height.toFixed() + "px");
    } else {
        $('.box-masonry[data-id="' + id_element + '"] .videoplayer').css("width", '').css("height", '');
    }

    if (affix_close && small) {
        $('.box-masonry[data-id="' + id_element + '"] .videoplayer').prepend('<div class="affix-close"><i class="fa fa-remove"></div>');
        $('.box-masonry[data-id="' + id_element + '"] .videoplayer .affix-close').on('click', function () {
            $('.box-masonry[data-id="' + id_element + '"] .videoplayer').removeClass('affix');
        });
    }
    if ($('.box-masonry[data-id="' + id_element + '"] video').length) {
        var player = $('.box-masonry[data-id="' + id_element + '"] video')[0].player;
        player.setPlayerSize(width, height.toFixed());
        $("#video_" + id_element + "_html5").css("width", width + "px").css("height", height.toFixed() + "px");
        $('.box-masonry[data-id="' + id_element + '"] .mejs__container').css("width", width + "px").css("height", height.toFixed() + "px");
        setTimeout(function () {
            player.setControlsSize();
        }, 100);
    }
}

if (typeof oldIE == 'undefined' && $('.waypoint').length > 0) {

    startWaypoints();
}

function startWaypoints()
{
    $('.waypoint').css({opacity: '0'});
    $('.waypoint').each(function() {
        var id = $(this).attr('id');

        new Waypoint({
            element: document.getElementById(id),
            handler: function () {
                var id = this.element.id;

                var jElement = $(this.element);
                var delayTime = jElement.data('delay');
                if (typeof delayTime == 'undefined')
                    delayTime = 100;
                else
                    delayTime = delayTime*1000;

                var fadeClass = jElement.data('class');
                if (typeof fadeClass == 'undefined')
                    var fadeClass = 'fadeInUp';

                jElement.delay(delayTime).queue(function(next) {
                    jElement.css('opacity', 1);
                    jElement.css('max-height', '').css('height', '');
                    jElement.addClass('animated '+fadeClass);
                    delayTime = 0;
                    refreshWaypoints();
                    next();
                });

                this.destroy();
            },
            offset: '90%'
        });

    });
}
