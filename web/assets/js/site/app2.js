function template_functions() {
    $("input:checkbox, input:radio").not('[data-no-uniform="true"],#uniform-is-ajax').uniform();
    if (typeof language == 'undefined') {
        var language = 'fr';
    }
    $('[data-rel="select2"],[rel="select2"]').select2({
        language: language
    });

    $('[rel="tooltip"],[data-rel="tooltip"]').tooltip({
        delay: {
            show: 400,
            hide: 200
        }
    });
    $('[rel="popover"],[data-rel="popover"]').popover();
    $("audio,video").not('#bgvid,.no-mediaelementplayer').mediaelementplayer({
        startVolume: 1,
        success: function (t, e) {
            "flash" == t.pluginType && t.attributes.hasOwnProperty("autoplay") && t.addEventListener("canplay", function () {
                t.play()
            }, !1)
        }
    })
    $("video").bind("contextmenu", function(t) {
        return !1
    });

    CKEDITOR.timestamp = "LB0.86";
    if ($("#ckeditor-light").length > 0) {
        CKEDITOR.replace("ckeditor-light", {
            height: "150",
            toolbar: [
                {
                    name: "basicstyles",
                    groups: ["basicstyles", "cleanup"],
                    items: ["Bold", "Italic", "Strike"]
                }, {
                    name: "styles",
                    items: ["Styles", "Format"]
                }
            ]
        });
    }
}

function TriggerGritter(t, e) {
    if (t == "error") {
        $.gritter.add({
            title: __("Erreur !"),
            text: e,
            image: '<i class="fa fa-warning"></i>',
            sticky: !0,
            time: "",
            class_name: "gritter-error"
        });
    } else if (t == "success") {
        $.gritter.add({
            title: __("Information"),
            text: e,
            image: '<i class="fa fa-check"></i>',
            sticky: !1,
            time: 5e3,
            class_name: "gritter-success"
        });
    } else if (t == "notice") {
        $.gritter.add({
            title: __("Information"),
            text: e,
            image: '<i class="fa fa-info-circle"></i>',
            sticky: !1,
            time: 5e3,
            class_name: ""
        });
    }
}

function FullTriggerGritter(t, e, a, s, i, n) {
    void 0 == a && (a = '<i class="fa fa-info-circle"></i>'), void 0 == n && (n = !1), void 0 == i && (i = 1e4), $.gritter.add({
        title: t,
        text: e,
        image: a,
        sticky: n,
        time: i,
        class_name: s
    })
}

function widthFunctions(t) {
    var e = $(window).height(),
        a = $(window).width();
    e && $("#content").css("min-height", e), a < 980 && a > 767 ? ($(".main-menu-span").hasClass("col-md-2") && ($(".main-menu-span").removeClass("col-md-2"), $(".main-menu-span").addClass("col-md-1")), $("#content").hasClass("col-md-10") && ($("#content").removeClass("col-md-10"), $("#content").addClass("col-md-11")), $("a").each(function() {
        $(this).hasClass("quick-button-small col-md-1") && ($(this).removeClass("quick-button-small col-md-1"), $(this).addClass("quick-button col-md-2 changed"))
    }), $(".circleStatsItem").each(function() {
        var t = $(this).parent().attr("onTablet"),
            e = $(this).parent().attr("onDesktop");
        t && ($(this).parent().removeClass(e), $(this).parent().addClass(t))
    }), $(".box").each(function() {
        var t = $(this).attr("onTablet"),
            e = $(this).attr("onDesktop");
        t && ($(this).removeClass(e), $(this).addClass(t))
    })) : ($(".main-menu-span").hasClass("col-md-1") && ($(".main-menu-span").removeClass("col-md-1"), $(".main-menu-span").addClass("col-md-2")), $("#content").hasClass("col-md-11") && ($("#content").removeClass("col-md-11"), $("#content").addClass("col-md-10")), $("a").each(function() {
        $(this).hasClass("quick-button col-md-2 changed") && ($(this).removeClass("quick-button col-md-2 changed"), $(this).addClass("quick-button-small col-md-1"))
    }), $(".circleStatsItem").each(function() {
        var t = $(this).parent().attr("onTablet"),
            e = $(this).parent().attr("onDesktop");
        t && ($(this).parent().removeClass(t), $(this).parent().addClass(e))
    }), $(".box").each(function() {
        var t = $(this).attr("onTablet"),
            e = $(this).attr("onDesktop");
        t && ($(this).removeClass(t), $(this).addClass(e))
    }))
}
$(document).ready(function() {
    template_functions();
    widthFunctions();
});
$(window).bind("resize", widthFunctions);
