import main from './main';

export const clientLimitApiSlice = main.injectEndpoints({
  endpoints: (builder) => ({
    getRedirectToAbonnementPage: builder.query<string, string>({
      query: (limit) => ({
        url: 'client/limit/redirect_to_abonnement_page/',
        method: 'POST',
        body: { limit },
      }),
      transformResponse: (response: any) => response.data.url,
    }),
  }),
});

export const { useLazyGetRedirectToAbonnementPageQuery } = clientLimitApiSlice;
