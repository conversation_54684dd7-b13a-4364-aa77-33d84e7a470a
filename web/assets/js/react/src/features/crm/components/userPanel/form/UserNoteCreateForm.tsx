import { InputTextarea } from 'primereact/inputtextarea';
import { classNames } from 'primereact/utils';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';

import { __ } from '../../../../../helpers/gettext';
import { useAppDispatch } from '../../../../../hooks/hook';
import { addSuccessNotification } from '../../../../../reducers/notificationSlice';
import { useCreateUserNoteMutation } from '../../../api/userNoteApiSlice';
import {
  USER_NOTE_COLORS,
  USER_NOTE_DANGER_COLOR,
  USER_NOTE_DEFAULT_COLOR,
  USER_NOTE_INFO_COLOR,
  USER_NOTE_SUCCESS_COLOR,
  USER_NOTE_WARNING_COLOR,
} from '../body/UserPanelNote';

export default function UserNoteCreateForm({ userId }: { userId: number }): JSX.Element {
  const dispatch = useAppDispatch();
  const [createUserNote, { isSuccess, isLoading }] = useCreateUserNoteMutation();
  const [textareaClass, setTextareaClass] = useState('textarea-default');
  const form = useForm({
    defaultValues: {
      content: '',
      label: USER_NOTE_DEFAULT_COLOR,
    },
  });

  const onSubmit = (data: { content: string; label: string }): void => {
    createUserNote({ userId, content: data.content, label: data.label });
    setTextareaClass('textarea-default ');
  };

  const handleTextareaClass = (color: string): void => {
    switch (true) {
      case color === USER_NOTE_DANGER_COLOR:
        setTextareaClass('textarea-danger ');
        break;
      case color === USER_NOTE_DEFAULT_COLOR:
        setTextareaClass('textarea-default ');
        break;
      case color === USER_NOTE_INFO_COLOR:
        setTextareaClass('textarea-info ');
        break;
      case color === USER_NOTE_SUCCESS_COLOR:
        setTextareaClass('textarea-success ');
        break;
      case color === USER_NOTE_WARNING_COLOR:
        setTextareaClass('textarea-warning ');
        break;
    }
  };

  useEffect(() => {
    if (isSuccess) {
      dispatch(addSuccessNotification({ summary: __('Succes'), detail: __('La note a été crée avec succès.') }));

      form.reset();
    }
  }, [isSuccess]);

  return (
    <form className="d-flex flex-direction-col flex-gap-16" onSubmit={form.handleSubmit(onSubmit)}>
      <Controller
        name="content"
        control={form.control}
        rules={{ required: __('Veuillez rédiger une note') }}
        render={({ field, fieldState }) => (
          <>
            <div className="d-flex flex-direction-col flex-gap-4">
              <InputTextarea
                id={field.name}
                value={field.value}
                className={classNames(textareaClass, { 'p-invalid': fieldState.error })}
                onChange={(e) => field.onChange(e.target.value)}
                rows={5}
                placeholder={__('Rédiger une note privée')}
                disabled={isLoading}
                invalid={fieldState.invalid}
              />
              {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
            </div>
          </>
        )}
      />
      <div>
        <Controller
          name="label"
          control={form.control}
          rules={{ required: __('Veuillez sélectionner une couleur') }}
          render={({ field, fieldState }) => (
            <>
              <div className="d-flex flex-direction-col flex-gap-4">
                <div className="user-note-labels-list">
                  {USER_NOTE_COLORS.map((color: string, index) => (
                    <span
                      key={index}
                      className={`user-note-icon-selectable user-note-icon user-note-icon-${color} ${color === field.value && 'selected'}`}
                      data-label={color}
                      onClick={() => {
                        if (!isLoading) {
                          field.onChange(color);
                          handleTextareaClass(color);
                        }
                      }}
                    ></span>
                  ))}
                </div>
                {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
              </div>
            </>
          )}
        />
      </div>
      <button style={{ maxWidth: '28%' }} disabled={isLoading} className={`btn btn-default btn-xs ${isLoading && 'disabled'}`} type="submit">
        {isLoading ? __('Traitement...') : __('Enregistrer')} {isLoading && <i className="fa-regular fa-circle-notch fa-spin"></i>}
      </button>
    </form>
  );
}
