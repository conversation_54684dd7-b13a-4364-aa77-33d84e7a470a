.user-panel-header {
  display: flex;
  gap: 16px;
  padding: 16px 24px;

  .user-panel-left {
    .user-avatar {
      width: 88px;
      height: 88px;
      background-size: cover;
    }
  }

  .user-panel-right {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 8px;

    .user-info-container {
      display: flex;
      gap: 16px;
      align-items: center;
      .user-fullname {
        font-size: 20px;
        font-weight: 600;
      }
    }

    .user-email {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;
      max-width: 300px
    }

    .user-types {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      .p-tag {
        background: var(--app-color-default);
        color: var(--app-color-dark);
        font-weight: var(--app-font-weight);
        font-size: var(--app-font-size);
        border: 1px solid var(--app-color-grey-light);
        height: 22px;
        padding: 4px 8px;
      }
    }

    .user-edit {
      margin-top: 8px;
    }
  }
}