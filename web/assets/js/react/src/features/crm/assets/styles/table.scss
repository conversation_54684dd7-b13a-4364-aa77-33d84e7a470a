.crm-datatable {
  .p-datatable-wrapper {
    .p-datatable-table {
      margin-bottom: 0;
    }
    .p-datatable-thead {
      .p-frozen-column:last-child {
        right: -1px !important;
      }
    }
    .p-datatable-tbody {
      tr > td:first-child {
        padding-right: var(--app-layout-padding);
      }

      .left-column-frozen {
        &-shadow::after {
          content: "";
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          width: 10px;
          box-shadow: 4px 0 10px -3px rgba(0, 0, 0, 0.1);
        }
      }

      .right-column-frozen {
        right: -1px !important;
        &-shadow::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          width: 10px;
          box-shadow: -4px 0 10px -3px rgba(0, 0, 0, 0.1);
        }
      }

      .cell-dropdown-info {
        display: flex;
        justify-content: end;
        padding: 0;
        &-button:hover {
          background-color: #ebe7df;
          border-radius: 8px;
        }
        &-clickable {
          display: block;
          padding: 14px 20px;
          text-align: center;
          min-width: 54px;
          min-height: 48px;
        }
      }
    }

    .empty-result-container {
      max-width: 912px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      img {
        width: 10%;
      }

      @media (max-width: 992px) {
        justify-content: left;
      }
    }

    .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
      border-color: var(--app-color-primary);
    }
  }

  .p-paginator-bottom {
    border-top: var(--app-panel-border);
    border-bottom-left-radius: var(--app-panel-radius);
    border-bottom-right-radius: var(--app-panel-radius);
  }
}
