import { MultiSelect } from 'primereact/multiselect';
import { Tooltip } from 'primereact/tooltip';
import React, { useContext, useEffect } from 'react';
import { Controller } from 'react-hook-form';

import { useGetObjectListQuery } from '../../../../api/objectApiSlice';
import { useUpdateUsersTagsMutation } from '../../../../api/tagApiSlice';
import { __, n__ } from '../../../../helpers/gettext';
import { useAppDispatch } from '../../../../hooks/hook';
import { closeModal, startLoading, startSuccess, stopLoading, stopSuccess } from '../../../../reducers/modalSlice';
import { addSuccessNotification } from '../../../../reducers/notificationSlice';
import Truncate from '../../../Truncate';
import { ModalContext } from '../../Modal';

export default function ModalToggleTagForm({ userIds }: { userIds: number[] }): React.JSX.Element {
  const dispatch = useAppDispatch();
  const { modal, handleSubmit, control, getValues, reset } = useContext(ModalContext);
  const { data: tags, isFetching, isSuccess: areTagsSuccess, isError: areTagsError } = useGetObjectListQuery({ slug: 'tags' });
  const [updateUsersTags, { isSuccess: isSuccessUpdate, isError: isErrorUpdate }] = useUpdateUsersTagsMutation();

  useEffect(() => {
    if (modal.isOpen && !areTagsSuccess) {
      dispatch(startLoading(modal.id));
    }

    return () => {
      dispatch(stopSuccess(modal.id));
      reset({ tagsToAdd: [], tagsToRemove: [] });
    };
  }, []);

  useEffect(() => {
    if (!isFetching && areTagsSuccess) {
      dispatch(stopLoading(modal.id));
    }
  }, [tags]);

  useEffect(() => {
    if (modal.isSubmitting) {
      handleSubmit(({ tagsToAdd, tagsToRemove }: { tagsToAdd: []; tagsToRemove: [] }) => {
        updateUsersTags({
          users: userIds,
          tagsToAdd: tagsToAdd,
          tagsToRemove: tagsToRemove,
        });
      })();
    }
  }, [modal.isSubmitting]);

  useEffect(() => {
    if (isSuccessUpdate) {
      let successMessage = '';
      if (userIds.length === 1) {
        successMessage = __("Les tags de l'utilisateur ont été mis à jour avec succès.");
      } else {
        successMessage = __('Les tags des utilisateurs vont être mis à jour dans un instant.');
      }

      dispatch(startSuccess(modal.id));
      dispatch(
        addSuccessNotification({
          summary: __('Succès'),
          detail: successMessage,
        })
      );

      if (modal.isOpen) {
        dispatch(closeModal(modal.id));
      }
    }
  }, [isSuccessUpdate]);

  useEffect(() => {
    if (isErrorUpdate || areTagsError) {
      if (modal.isOpen) {
        dispatch(closeModal(modal.id));
      }
    }
  }, [isErrorUpdate, areTagsError]);

  return (
    <>
      <Controller
        name="tagsToAdd"
        control={control}
        rules={{
          validate: (value) => {
            if (
              (!value && !getValues('tagsToRemove')) ||
              (value && value.length === 0 && getValues('tagsToRemove') && getValues('tagsToRemove').length === 0)
            ) {
              return __('Veuillez sélectionner au moins un tag à ajouter.');
            }
            return true;
          },
        }}
        render={({ field, fieldState }) => (
          <>
            <div className="d-flex flex-direction-col flex-gap-4 m-b-24">
              <label>
                {n__("Ajouter des tags à l'utilisateur", 'Ajouter des tags aux utilisateurs', userIds.length)}
                &nbsp;
                <Tooltip target=".tag-tooltip" autoHide={false} />
                <i
                  className="fa fa-info-circle tag-tooltip cursor-pointer"
                  data-pr-tooltip={n__(
                    "Si l'utilisateur sélectionné n'est pas déjà contact LearnyMail, celui-ci sera créé dans la limite des seuils de votre abonnement.",
                    'Si les utilisateurs sélectionnés ne sont pas déjà contacts LearnyMail, ceux-ci seront créés dans la limite des seuils de votre abonnement.',
                    userIds.length
                  )}
                  data-pr-position="bottom"
                ></i>
              </label>

              <MultiSelect
                filter
                inputId={field.name}
                value={field.value}
                options={tags}
                onChange={(e) => {
                  field.onChange(e.value);
                  e.stopPropagation();
                }}
                optionLabel="label"
                optionValue="id"
                placeholder={__('Tags à ajouter')}
                display="chip"
                itemTemplate={(option) => <Truncate text={option.label} maxLength={70} />}
                emptyFilterMessage={__('Aucun résultat')}
                invalid={fieldState.invalid}
              />
              {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
            </div>
          </>
        )}
      />

      <Controller
        name="tagsToRemove"
        control={control}
        rules={{
          validate: (value) => {
            if ((!value && !getValues('tagsToAdd')) || (value && value.length === 0 && getValues('tagsToAdd') && getValues('tagsToAdd').length === 0)) {
              return __('Veuillez sélectionner au moins un tag à retirer.');
            }
            return true;
          },
        }}
        render={({ field, fieldState }) => (
          <>
            <div className="d-flex flex-direction-col flex-gap-4">
              <label>{n__("Retirer des tags à l'utilisateur", 'Retirer des tags aux utilisateurs', userIds.length)}</label>
              <MultiSelect
                filter
                inputId={field.name}
                value={field.value}
                options={tags}
                onChange={(e) => {
                  field.onChange(e.value);
                  e.stopPropagation();
                }}
                optionLabel="label"
                optionValue="id"
                placeholder={__('Tags à retirer')}
                display="chip"
                itemTemplate={(option) => <Truncate text={option.label} maxLength={70} />}
                emptyFilterMessage={__('Aucun résultat')}
                invalid={fieldState.invalid}
              />
              {fieldState.error && <small className="p-error">{fieldState.error.message}</small>}
            </div>
          </>
        )}
      />
    </>
  );
}
