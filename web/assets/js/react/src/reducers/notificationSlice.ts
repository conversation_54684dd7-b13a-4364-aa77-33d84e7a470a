import { createEntityAdapter, createSlice, EntityState, nanoid } from '@reduxjs/toolkit';
import { ToastMessage } from 'primereact/toast';

import { RootState } from '../stores/store';

const notificationAdapter = createEntityAdapter<ToastMessage>();

const notificationSlice = createSlice({
  name: 'notifications',
  initialState: notificationAdapter.getInitialState() as EntityState<ToastMessage>,
  reducers: {
    addSuccessNotification(state, { payload }) {
      notificationAdapter.addOne(state, { id: nanoid(), severity: 'success', summary: payload.summary, detail: payload.detail });
    },
    addInfoNotification(state, { payload }) {
      notificationAdapter.addOne(state, { id: nanoid(), severity: 'info', summary: payload.summary, detail: payload.detail });
    },
    addWarnNotification(state, { payload }) {
      notificationAdapter.addOne(state, { id: nanoid(), severity: 'warn', summary: payload.summary, detail: payload.detail });
    },
    addErrorNotification(state, { payload }) {
      notificationAdapter.addOne(state, { id: nanoid(), severity: 'error', summary: payload.summary, detail: payload.detail });
    },
    removeNotifications: notificationAdapter.removeAll,
  },
});

export const { addSuccessNotification, addInfoNotification, addWarnNotification, addErrorNotification, removeNotifications } = notificationSlice.actions;

export const { selectAll: selectAllNotifications } = notificationAdapter.getSelectors((state: RootState) => state.notifications);

export default notificationSlice.reducer;
