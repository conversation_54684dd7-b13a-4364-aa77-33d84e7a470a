/* turn.js 4.1.0 | Copyright (c) 2012 <PERSON> | turnjs.com | turnjs.com/license.txt */

(function(f){function s(a,b,c){if(!c[0]||"object"==typeof c[0])return b.init.apply(a,c);if(b[c[0]])return b[c[0]].apply(a,Array.prototype.slice.call(c,1));throw k(c[0]+" is an invalid value");}function q(a,b,c,d){return{css:{position:"absolute",top:a,left:b,overflow:d||"hidden","z-index":c||"auto"}}}function t(a,b,c,d,e){var g=1-e,f=g*g*g,l=e*e*e;return m(Math.round(f*a.x+3*e*g*g*b.x+3*e*e*g*c.x+l*d.x),Math.round(f*a.y+3*e*g*g*b.y+3*e*e*g*c.y+l*d.y))}function m(a,b){return{x:a,y:b}}function j(a,b){return Object.prototype.hasOwnProperty.call(b,
a)}function k(a){function b(a){this.name="TurnJsError";this.message=a}b.prototype=Error();b.prototype.constructor=b;return new b(a)}function v(){return""}var r="",n={down:"mousedown",move:"mousemove",up:"mouseup",over:"mouseover",out:"mouseout"},p={backward:["l"],forward:["r"],all:["l","r"]},x=["single","double"],y=["ltr","rtl"],z={acceleration:!0,display:"double",duration:600,page:1,gradients:!0,when:null},A={acceleration:!0,corners:"forward",cornerSize:100,duration:600,gradients:!0},i={init:function(a){if(1<
this.length)throw k("This selector has more than 1 element");for(var b=["Moz","Webkit","Khtml","O","ms"],c=b.length,d="";c--;)b[c]+"Transform"in document.body.style&&(d="-"+b[c].toLowerCase()+"-");r=d;var e,b=0,g=this.data(),c=this.children(),a=f.extend({width:this.width(),height:this.height(),direction:this.attr("dir")||this.css("direction")||"ltr"},z,a);g.opts=a;g.pageObjs={};g.pages={};g.pageWrap={};g.pagePlace={};g.pageMv=[];g.zoom=1;g.totalPages=a.pages||0;g.docEvents={mouseStart:function(a){for(var b in g.pages)if(j(b,
g.pages)&&!1===h._eventStart.call(g.pages[b],a))return!1},mouseMove:function(a){for(var b in g.pages)j(b,g.pages)&&h._eventMove.call(g.pages[b],a)},mouseEnd:function(a){for(var b in g.pages)j(b,g.pages)&&h._eventEnd.call(g.pages[b],a)}};if(a.when)for(e in a.when)j(e,a.when)&&this.bind(e,a.when[e]);this.css({position:"relative",width:a.width,height:a.height});this.turn("display",a.display);""!==a.direction&&this.turn("direction",a.direction);for(e=0;e<c.length;e++)"1"!=f(c[e]).attr("ignore")&&this.turn("addPage",
c[e],++b);f(this).bind(n.down,g.docEvents.mouseStart).bind("start",i._start).bind("end",i._end).bind("pressed",i._pressed).bind("released",i._released).bind("flip",i._flip);f(document).bind(n.move,g.docEvents.mouseMove).bind(n.up,g.docEvents.mouseEnd);this.turn("page",a.page);g.done=!0;return this},addPage:function(a,b){var c,d=!1,e=this.data(),g=e.totalPages+1;if(e.destroying)return!1;if(c=/\bp([0-9]+)\b/.exec(f(a).attr("class")))b=parseInt(c[1],10);if(b)if(b==g)d=!0;else{if(b>g)throw k('Page "'+
b+'" cannot be inserted');}else b=g,d=!0;1<=b&&b<=g&&(c="double"==e.display?b%2?" odd":" even":"",e.done&&this.turn("stop"),b in e.pageObjs&&i._movePages.call(this,b,1),d&&(e.totalPages=g),e.pageObjs[b]=f(a).css({"float":"left"}).addClass("page p"+b+c),i._addPage.call(this,b),e.done&&this.turn("update"),i._removeFromDOM.call(this));return this},_addPage:function(a){var b=this.data(),c=b.pageObjs[a];if(c)if(i._necessPage.call(this,a)){if(!b.pageWrap[a]){var d=i._pageSize.call(this,a,!0);c.css({width:d.width,
height:d.height});b.pagePlace[a]=a;b.pageWrap[a]=f("<div/>",{"class":"turn-page-wrapper",page:a,css:{position:"absolute",overflow:"hidden"}}).css(d);this.append(b.pageWrap[a]);b.pageWrap[a].prepend(b.pageObjs[a])}(!a||1==i._setPageLoc.call(this,a))&&i._makeFlip.call(this,a)}else b.pagePlace[a]=0,b.pageObjs[a]&&b.pageObjs[a].remove()},hasPage:function(a){return j(a,this.data().pageObjs)},center:function(a){var b=this.data(),c=f(this).turn("size"),d=c.width/(2*b.zoom)-c.width/2;"double"==b.display&&
(a=this.turn("view",a||b.tpage||b.page),"ltr"==b.direction?a[0]?a[1]||(d+=c.width/4):d-=c.width/4:a[0]?a[1]||(d-=c.width/4):d+=c.width/4);f(this).css({marginLeft:d});return this},destroy:function(){var a=this.data();a.destroying=!0;f(this).unbind(n.down).unbind("end").unbind("first").unbind("flip").unbind("last").unbind("pressed").unbind("released").unbind("start").unbind("turning").unbind("turned").unbind("zooming");for(f(document).unbind(n.move,a.docEvents.mouseMove).unbind(n.up,a.docEvents.mouseEnd);0!==
a.totalPages;)this.turn("removePage",a.totalPages);a.fparent&&a.fparent.remove();a.shadow&&a.shadow.remove();this.removeData();return this},is:function(){return"object"==typeof this.data().pages},zoom:function(a){var b=this.data();if("number"==typeof a){if(0.0010>a||100<a)throw k(a+" is not a value for zoom");var c=f.Event("zooming");this.trigger(c,[a,b.zoom]);if(c.isDefaultPrevented())return this;var c=f(this).turn("size"),d=1/b.zoom,e=Math.round(c.width*d*a),g=Math.round(c.height*d*a);b.zoom=a;
f(this).turn("stop").turn("size",e,g).css({marginTop:c.height*d/2-g/2});b.opts.autoCenter?this.turn("center"):f(this).css({marginLeft:c.width*d/2-e/2});i._updateShadow.call(this);return this}return b.zoom},_pageSize:function(a,b){var c=this.data(),d={};if("single"==c.display)d.width=this.width(),d.height=this.height(),b&&(d.top=0,d.left=0,d.right="auto");else{var e=this.width()/2,g=this.height();c.pageObjs[a].hasClass("own-size")?(d.width=c.pageObjs[a].width(),d.height=c.pageObjs[a].height()):(d.width=
e,d.height=g);if(b){var f=a%2;d.top=(g-d.height)/2;"ltr"==c.direction?(d[f?"right":"left"]=e-d.width,d[f?"left":"right"]="auto"):(d[f?"left":"right"]=e-d.width,d[f?"right":"left"]="auto")}}return d},_makeFlip:function(a){var b=this.data();if(!b.pages[a]&&b.pagePlace[a]==a){var c="single"==b.display,d=a%2;b.pages[a]=b.pageObjs[a].css(i._pageSize.call(this,a)).flip({page:a,next:d||c?a+1:a-1,turn:this,duration:b.opts.duration,acceleration:b.opts.acceleration,gradients:b.opts.gradients}).flip("disable",
b.disabled)}return b.pages[a]},_makeRange:function(){var a,b;if(!(1>this.data().totalPages)){b=this.turn("range");for(a=b[0];a<=b[1];a++)i._addPage.call(this,a)}},range:function(a){var b,c,d,e=this.data(),a=a||e.tpage||e.page||1;d=i._view.call(this,a);if(1>a||a>e.totalPages)throw k('"'+a+'" is not a page for range');d[1]=d[1]||d[0];1<=d[0]&&d[1]<=e.totalPages?(a=Math.floor(2),e.totalPages-d[1]>d[0]?(b=Math.min(d[0]-1,a),c=2*a-b):(c=Math.min(e.totalPages-d[1],a),b=2*a-c)):c=b=5;return[Math.max(1,d[0]-
b),Math.min(e.totalPages,d[1]+c)]},_necessPage:function(a){if(0===a)return!0;var b=this.data(),c=this.turn("range");return b.pageObjs[a].hasClass("fixed")||a>=c[0]&&a<=c[1]},_removeFromDOM:function(){var a,b=this.data();for(a in b.pageWrap)j(a,b.pageWrap)&&!i._necessPage.call(this,a)&&i._removePageFromDOM.call(this,a)},_removePageFromDOM:function(a){var b=this.data();if(b.pages[a]){var c=b.pages[a].data();h._moveFoldingPage.call(b.pages[a],!1);c.f&&c.f.fwrapper&&c.f.fwrapper.remove();b.pages[a].removeData();
b.pages[a].remove();delete b.pages[a]}b.pageObjs[a]&&b.pageObjs[a].remove();b.pageWrap[a]&&(b.pageWrap[a].remove(),delete b.pageWrap[a]);delete b.pagePlace[a]},removePage:function(a){var b=this.data();if(1>a||a>b.totalPages)throw k("The page "+a+" doesn't exist");b.pageObjs[a]&&(this.turn("stop"),i._removePageFromDOM.call(this,a),delete b.pageObjs[a]);i._movePages.call(this,a,-1);b.totalPages-=1;b.page>b.totalPages?this.turn("page",b.totalPages):i._makeRange.call(this);return this},_movePages:function(a,
b){var c,d=this,e=this.data(),g="single"==e.display,f=function(a){var c=a+b,f=c%2,h=f?" odd ":" even ";e.pageObjs[a]&&(e.pageObjs[c]=e.pageObjs[a].removeClass("p"+a+" odd even").addClass("p"+c+h));e.pagePlace[a]&&e.pageWrap[a]&&(e.pagePlace[c]=c,e.pageWrap[c]=e.pageObjs[c].hasClass("fixed")?e.pageWrap[a].attr("page",c):e.pageWrap[a].css(i._pageSize.call(d,c,!0)).attr("page",c),e.pages[a]&&(e.pages[c]=e.pages[a].flip("options",{page:c,next:g||f?c+1:c-1,corners:g?"all":f?"forward":"backward"})),b&&
(delete e.pages[a],delete e.pagePlace[a],delete e.pageObjs[a],delete e.pageWrap[a],delete e.pageObjs[a]))};if(0<b)for(c=e.totalPages;c>=a;c--)f(c);else for(c=a;c<=e.totalPages;c++)f(c)},display:function(a){var b=this.data(),c=b.display;if(a){if(-1==f.inArray(a,x))throw k('"'+a+'" is not a value for display');"single"==a?b.pageObjs[0]||(this.turn("stop").css({overflow:"hidden"}),b.pageObjs[0]=f("<div />",{"class":"page p-temporal"}).css({width:this.width(),height:this.height()}).appendTo(this)):b.pageObjs[0]&&
(this.turn("stop").css({overflow:""}),b.pageObjs[0].remove(),delete b.pageObjs[0]);b.display=a;c&&(a=this.turn("size"),i._movePages.call(this,1,0),this.turn("size",a.width,a.height).turn("update"));return this}return c},direction:function(a){var b=this.data();if("undefined"==typeof a)return b.direction;a=a.toLowerCase();if(-1==f.inArray(a,y))throw k('"'+a+'" is not a value for direction');"rtl"==a&&f(this).attr("dir","ltr").css({direction:"ltr"});b.direction=a;b.done&&this.turn("size",f(this).width(),
f(this).height());return this},animating:function(){return 0<this.data().pageMv.length},disable:function(a){var b,c=this.data(),d=this.turn("view");c.disabled=void 0===a||!0===a;for(b in c.pages)j(b,c.pages)&&c.pages[b].flip("disable",a?f.inArray(b,d):!1);return this},disabled:function(a){return void 0===a?!0===this.data().disabled:this.turn("disable",a)},size:function(a,b){if(a&&b){var c,d,e=this.data();d="double"==e.display?a/2:a;this.css({width:a,height:b});e.pageObjs[0]&&e.pageObjs[0].css({width:d,
height:b});for(c in e.pageWrap)j(c,e.pageWrap)&&(d=i._pageSize.call(this,c,!0),e.pageObjs[c].css({width:d.width,height:d.height}),e.pageWrap[c].css(d),e.pages[c]&&e.pages[c].css({width:d.width,height:d.height}));this.turn("resize");return this}return{width:this.width(),height:this.height()}},resize:function(){var a,b=this.data();b.pages[0]&&(b.pageWrap[0].css({left:-this.width()}),b.pages[0].flip("resize",!0));for(a=1;a<=b.totalPages;a++)b.pages[a]&&b.pages[a].flip("resize",!0)},_removeMv:function(a){var b,
c=this.data();for(b=0;b<c.pageMv.length;b++)if(c.pageMv[b]==a)return c.pageMv.splice(b,1),!0;return!1},_addMv:function(a){var b=this.data();i._removeMv.call(this,a);b.pageMv.push(a)},_view:function(a){var b=this.data(),a=a||b.page;return"double"==b.display?a%2?[a-1,a]:[a,a+1]:[a]},view:function(a){var b=this.data(),a=i._view.call(this,a);return"double"==b.display?[0<a[0]?a[0]:0,a[1]<=b.totalPages?a[1]:0]:[0<a[0]&&a[0]<=b.totalPages?a[0]:0]},stop:function(){if(this.turn("animating")){var a,b,c,d=this.data(),
e=d.pageMv;d.pageMv=[];d.tpage&&(d.page=d.tpage,delete d.tpage);for(a=0;a<e.length;a++)c=d.pages[e[a]],b=c.data().f.opts,c.flip("hideFoldedPage",!1),h._moveFoldingPage.call(c,!1),d.pagePlace[b.next]=b.next,b.force&&(b.next=0===b.page%2?b.page-1:b.page+1,delete b.force)}this.turn("update");return this},pages:function(a){var b=this.data();if(a){if(a<b.totalPages){for(var c=a+1;c<=b.totalPages;c++)this.turn("removePage",c);this.turn("page")>a&&this.turn("page",a)}b.totalPages=a;return this}return b.totalPages},
_missing:function(a){for(var b=this.data(),c=this.turn("range",a),d=[],a=c[0];a<=c[1];a++)b.pageObjs[a]||d.push(a);0<d.length&&this.trigger("missing",[d])},_fitPage:function(a){var b=this.data(),c=this.turn("view",a);i._missing.call(this,a);b.pageObjs[a]&&(b.page=a,this.turn("stop"),i._removeFromDOM.call(this),i._makeRange.call(this),i._updateShadow.call(this),this.trigger("turned",[a,c]),b.opts.autoCenter&&this.turn("center"))},_turnPage:function(a,b){var c,d,e=this.data(),g=e.pagePlace[a],h=this.turn("view"),
l=this.turn("view",a);if(e.page!=a){var w=f.Event("turning");this.trigger(w,[a,l]);if(w.isDefaultPrevented())return;-1!=f.inArray(1,l)&&this.trigger("first");-1!=f.inArray(e.totalPages,l)&&this.trigger("last")}if(b)this.turn("stop",g);else{i._missing.call(this,a);if(!e.pageObjs[a])return;this.turn("stop");e.page=a}i._makeRange.call(this);"single"==e.display?(c=h[0],d=l[0]):h[1]&&a>h[1]?(c=h[1],d=l[0]):h[0]&&a<h[0]&&(c=h[0],d=l[1]);e.pages[c]&&(g=e.pages[c].data().f.opts,e.tpage=d,g.next!=d&&(g.next=
d,e.pagePlace[d]=g.page,g.force=!0),"single"==e.display?"ltr"==e.direction?e.pages[c].flip("turnPage",l[0]>h[0]?"r":"l"):e.pages[c].flip("turnPage",l[0]>h[0]?"l":"r"):e.pages[c].flip("turnPage"))},page:function(a){var a=parseInt(a,10),b=this.data();return 0<a&&a<=b.totalPages?(!b.done||-1!=f.inArray(a,this.turn("view"))?i._fitPage.call(this,a):i._turnPage.call(this,a),this):b.page},next:function(){return this.turn("page",i._view.call(this,this.data().page).pop()+1)},previous:function(){return this.turn("page",
i._view.call(this,this.data().page).shift()-1)},peel:function(){return this},_addMotionPage:function(){var a=f(this).data().f.opts,b=a.turn,c=b.data();i._addMv.call(b,a.page);c.pagePlace[a.next]=a.page;b.turn("update")},_start:function(a,b,c){var d=b.turn.data();a.isDefaultPrevented()||("single"==d.display&&c&&("l"==c&&"ltr"==d.direction||"r"==c&&"rtl"==d.direction?(b.next=b.next<b.page?b.next:b.page-1,b.force=!0):b.next=b.next>b.page?b.next:b.page+1),i._addMotionPage.call(a.target));i._updateShadow.call(b.turn)},
_end:function(a,b,c){f(a.target).data();var a=b.turn,d=a.data();if(c||d.tpage){if(d.tpage==b.next||d.tpage==b.page)delete d.tpage,i._fitPage.call(a,d.tpage||b.next,!0)}else i._removeMv.call(a,b.page),i._updateShadow.call(a),a.turn("update")},_pressed:function(a){a.stopPropagation();var b,a=f(a.target).data().f,c=a.opts.turn.data().pages;for(b in c)b!=a.opts.page&&c[b].flip("disable",!0);return a.time=(new Date).getTime()},_released:function(a,b){a.stopPropagation();var c,d=f(a.target),e=d.data().f,
g=e.opts.turn,u=g.data();c="single"==u.display?"r"==b.corner?b.x<d.width()/2:b.x>d.width()/2:0>b.x||b.x>d.width();if(200>(new Date).getTime()-e.time||c)a.preventDefault(),i._turnPage.call(g,e.opts.next,!1===h._cornerActivated.call(d,b,1));u.mouseAction=!1},_flip:function(a){a.stopPropagation();a=f(a.target).data().f.opts;a.turn.trigger("turn",[a.next]);a.turn.data().opts.autoCenter&&a.turn.turn("center",a.next)},calculateZ:function(a){var b,c,d,e,g=this,f=this.data();b=this.turn("view");var h=b[0]||
b[1],i={pageZ:{},partZ:{},pageV:{}},j=function(a){a=g.turn("view",a);a[0]&&(i.pageV[a[0]]=!0);a[1]&&(i.pageV[a[1]]=!0)};for(b=0;b<a.length;b++)c=a[b],d=f.pages[c].data().f.opts.next,e=f.pagePlace[c],j(c),j(d),c=f.pagePlace[d]==d?d:c,i.pageZ[c]=f.totalPages-Math.abs(h-c),i.partZ[e]=2*f.totalPages+Math.abs(h-c);return i},update:function(){var a,b=this.data();if(b.pageMv.length&&0!==b.pageMv[0]){var c,d=this.turn("calculateZ",b.pageMv);this.turn("view",b.tpage);for(a in b.pageWrap)if(j(a,b.pageWrap)&&
(c=b.pageObjs[a].hasClass("fixed"),b.pageWrap[a].css({display:d.pageV[a]||c?"":"none","z-index":d.pageZ[a]||(c?-1:0)}),c=b.pages[a]))c.flip("z",d.partZ[a]||null),d.pageV[a]&&c.flip("resize"),b.tpage&&c.flip("disable",!0)}else for(a in b.pageWrap)j(a,b.pageWrap)&&(d=i._setPageLoc.call(this,a),b.pages[a]&&b.pages[a].flip("disable",b.disabled||1!=d).flip("z",null))},_updateShadow:function(){var a,b,c=this.data(),d=this.width(),e=this.height(),g="single"==c.display?d:d/2;a=this.turn("view");c.shadow||
(c.shadow=f("<div />",{"class":"shadow",css:q(0,0,0).css}).appendTo(this));for(var h=0;h<c.pageMv.length&&a[0]&&a[1];h++)a=this.turn("view",c.pages[c.pageMv[h]].data().f.opts.next),b=this.turn("view",c.pageMv[h]),a[0]=a[0]&&b[0],a[1]=a[1]&&b[1];switch(a[0]?a[1]?3:"ltr"==c.direction?2:1:"ltr"==c.direction?1:2){case 1:c.shadow.css({width:g,height:e,top:0,left:g});break;case 2:c.shadow.css({width:g,height:e,top:0,left:0});break;case 3:c.shadow.css({width:d,height:e,top:0,left:0})}},_setPageLoc:function(a){var b=
this.data(),c=this.turn("view");if(a==c[0]||a==c[1])return b.pageWrap[a].css({zIndex:b.totalPages,display:""}),1;if("single"==b.display&&a==c[0]+1||"double"==b.display&&a==c[0]-2||a==c[1]+2)return b.pageWrap[a].css({zIndex:b.totalPages-1,display:""}),2;b.pageWrap[a].css({zIndex:0,display:b.pageObjs[a].hasClass("fixed")?"":"none"});return 0},options:function(a){if(void 0===a)return this.data().opts;var b=this.data();f.extend(b.opts,a);a.pages&&this.turn("pages",a.pages);a.page&&this.turn("page",a.page);
a.display&&this.turn("display",a.display);a.direction&&this.turn("direction",a.direction);a.width&&a.height&&this.turn("size",a.width,a.height);if(a.when)for(var c in a.when)j(c,a.when)&&this.unbind(c).bind(c,a.when[c]);return this},version:function(){return"4.1.0"}},h={init:function(a){this.data({f:{effect:"r"==a.corners||"l"==a.corners?"hard":"sheet"}});this.flip("options",a);h._addPageWrapper.call(this);return this},setData:function(a){var b=this.data();b.f=f.extend(b.f,a);return this},options:function(a){var b=
this.data().f;return a?(h.setData.call(this,{opts:f.extend({},b.opts||A,a)}),this):b.opts},z:function(a){var b=this.data().f;b.fwrapper&&(b.opts["z-index"]=a,b.fwrapper.css({"z-index":a||parseInt(b.parent.css("z-index"),10)||0}));return this},_cAllowed:function(){var a=this.data().f,b=a.opts.turn.data(),a=a.opts.page,c=a%2;return"single"==b.display?1==a?"ltr"==b.direction?p.forward:p.backward:a==b.totalPages?"ltr"==b.direction?p.backward:p.forward:p.all:"ltr"==b.direction?p[c?"forward":"backward"]:
p[c?"backward":"forward"]},_cornerActivated:function(a){var b=this.data().f,c=b.parent.offset(),d=this.width(),e=this.height(),a={x:Math.max(0,a.pageX-c.left),y:Math.max(0,a.pageY-c.top)},b=b.opts.cornerSize;if(0>=a.x||0>=a.y||a.x>=d||a.y>=e)return!1;e=h._cAllowed.call(this);if(a.x>d-b)a.corner="r";else if(a.x<b)a.corner="l";else return!1;return-1==f.inArray(a.corner,e)?!1:a},_c:function(a,b){b=b||0;switch(a){case "l":return m(b,0);case "r":return m(this.width()-b,0)}},_c2:function(a){switch(a){case "l":return m(2*
this.width(),0);case "r":return m(-this.width(),0)}},_foldingPage:function(){var a=this.data().f,b=a.opts;if(a.folding)return a.folding;if(b.turn)return a=b.turn.data(),"single"==a.display?a.pageObjs[b.next]?a.pageObjs[0]:null:a.pageObjs[b.next]},_backGradient:function(){var a=this.data().f,b=a.opts.turn;return a.opts.gradients&&(!b||"single"==b.data().display||2!=a.opts.page&&a.opts.page!=b.data().totalPages-1)},resize:function(a){var b=this.data().f,c=this.width(),d=this.height();a&&(b.wrapper.css({width:c,
height:d}),b.fpage.css({width:c,height:d}))},_addPageWrapper:function(){var a=this.data().f,b=this.parent();a.parent=b;if(!a.wrapper){var c={};a.wrapper=f("<div/>",q(0,0,2)).css(c).appendTo(b).prepend(this);a.fpage=f("<div/>",q(0,0,1)).css(c).appendTo(b)}h.resize.call(this,!0)},_fold:function(a){var b=this.data().f,c=h._c.call(this,a.corner),c=c.x?c.x-a.x:a.x,d=this.width();this.height();c=Math.min(2*d,Math.max(0,c));switch(a.corner){case "r":b.wrapper.css({width:Math.max(0,d-c)});this.css({position:"relative",
left:-c});b.fpage.css({left:-c+d,width:Math.max(0,c-d)});break;case "l":b.wrapper.css({width:d}),this.css({position:"relative",left:c}),b.fpage.css({left:d,width:Math.max(0,c-d)}),b.folding&&b.folding.css({position:"relative",left:2*-d+c})}b.parent.css({overflow:"visible"});b.point=a},_moveFoldingPage:function(a){var b=this.data().f;if(a){var a=h._foldingPage.call(this),c=b.opts.turn;if(a){if(b.folding){if(b.folding===a)return;h._moveFoldingPage.call(this,!1)}h.setData.call(this,{backParent:a.parent(),
folding:a});b.fpage.prepend(a)}c.turn("update")}else b.backParent&&(b.backParent.prepend(b.folding),delete b.backParent,delete b.folding)},_showFoldedPage:function(a,b){var c=h._foldingPage.call(this),d=this.data(),e=d.f,g=e.visible;if(!g||!e.point||e.point.corner!=a.corner){e.opts.turn.data();var i=f.Event("start");this.trigger(i,[e.opts,a.corner]);g=!1;if(i.isDefaultPrevented())return!1}if(c){if(b){var l=this,c=e.point&&e.point.corner==a.corner?e.point:h._c.call(this,a.corner,1);this.animatef({from:[c.x,
c.y],to:[a.x,a.y],duration:500,frame:function(b){a.x=Math.round(b[0]);a.y=Math.round(b[1]);h._fold.call(l,a)}})}else h._fold.call(this,a),d.effect&&!d.effect.turning&&this.animatef(!1);g||(e.visible=!0,h._moveFoldingPage.call(this,!0),e.fpage.show());return!0}return!1},hide:function(){var a=this.data().f;h._foldingPage.call(this);this.css({position:"",left:"auto"});a.wrapper.css({width:this.width()});a.fpage.css({width:this.width()});a.folding&&a.folding.css({position:"",left:"auto"});a.fpage.hide();
a.visible=!1;return this},hideFoldedPage:function(a){var b=this.data().f;if(b.point){var c=this,d=b.point,e=function(){b.point=null;c.flip("hide");c.trigger("end",[b.opts,!1])};if(a){var g=h._c.call(this,d.corner),a="t"==d.corner.substr(0,1)?Math.min(0,d.y-g.y)/2:Math.max(0,d.y-g.y)/2,f=m(d.x,d.y+a),i=m(g.x,g.y-a);this.animatef({from:0,to:1,frame:function(a){a=t(d,f,i,g,a);d.x=a.x;d.y=a.y;h._fold.call(c,d)},complete:e,duration:800,hiding:!0})}else this.animatef(!1),e()}},turnPage:function(a){var b=
this,c=this.data().f,a={corner:c.corner?c.corner.corner:a||h._cAllowed.call(this)[0]},d=c.point||h._c.call(this,a.corner,c.opts.turn?c.opts.turn.data().opts.elevation:0),e=h._c2.call(this,a.corner);this.trigger("flip").animatef({from:0,to:1,frame:function(c){c=t(d,d,e,e,c);a.x=c.x;a.y=c.y;h._showFoldedPage.call(b,a)},complete:function(){b.trigger("end",[c.opts,!0])},duration:c.opts.duration,turning:!0});c.corner=null},moving:function(){return"effect"in this.data()},isTurning:function(){return this.flip("moving")&&
this.data().effect.turning},_eventStart:function(a){var b=this.data().f;if(!b.disabled&&!this.flip("isTurning")){b.corner=h._cornerActivated.call(this,a);if(b.corner&&h._foldingPage.call(this,b.corner))return h._showFoldedPage.call(this,b.corner)&&this.trigger("pressed",[b.point]),!1;b.corner=null}},_eventMove:function(a){var b=this.data().f;if(!b.disabled)if(a=[a],b.corner){var c=b.parent.offset();b.corner.x=a[0].pageX-c.left;b.corner.y=a[0].pageY-c.top;h._showFoldedPage.call(this,b.corner)}else!this.data().effect&&
this.is(":visible")&&((a=h._cornerActivated.call(this,a[0]))?(b=h._c.call(this,a.corner,b.opts.cornerSize/2),a.x=b.x,a.y=b.y,h._showFoldedPage.call(this,a,!0)):h.hideFoldedPage.call(this,!0))},_eventEnd:function(){var a=this.data().f;if(!a.disabled&&a.point){var b=f.Event("released");this.trigger(b,[a.point]);b.isDefaultPrevented()||h.hideFoldedPage.call(this,!0)}a.corner=null},disable:function(a){h.setData.call(this,{disabled:a});return this}};window.requestAnim=function(a){window.setTimeout(a,1E3/
60)};f.extend(f.fn,{flip:function(a,b){return s(this,h,arguments)},turn:function(a){return s(this,i,arguments)},transform:function(a,b){var c={};b&&(c[r+"transform-origin"]=b);c[r+"transform"]=a;return this.css(c)},animatef:function(a){var b=this.data();b.effect&&b.effect.stop();if(a){a.to.length||(a.to=[a.to]);a.from.length||(a.from=[a.from]);for(var c=[],d=a.to.length,e=!0,g=this,h=(new Date).getTime(),i=function(){if(b.effect&&e){for(var f=[],j=Math.min(a.duration,(new Date).getTime()-h),k=0;k<
d;k++)f.push(b.effect.easing(1,j,a.from[k],c[k],a.duration));a.frame(1==d?f[0]:f);j==a.duration?(delete b.effect,g.data(b),a.complete&&a.complete()):window.requestAnim(i)}},j=0;j<d;j++)c.push(a.to[j]-a.from[j]);b.effect=f.extend({stop:function(){e=!1},easing:function(a,b,c,d,e){return d*Math.sqrt(1-(b=b/e-1)*b)+c}},a);this.data(b);i()}else delete b.effect}});f.isTouch=!1;f.mouseEvents=n;f.cssPrefix=v;f.cssTransitionEnd=v;f.findPos=function(a){var b={top:0,left:0};do b.left+=a.offsetLeft,b.top+=a.offsetTop;
while(a=a.offsetParent);return b}})(jQuery);
