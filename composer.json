{"name": "learnybox/learnybox", "license": "proprietary", "type": "project", "version": "4.0.0", "autoload": {"classmap": ["src/Classes/", "src/Controllers/"], "psr-4": {"Learnybox\\": "src/"}}, "autoload-dev": {"psr-4": {"Learnybox\\Controllers\\App\\Tests\\": "src/controllers/App/Tests/"}}, "scripts": {"post-install-cmd": ["if [ $COMPOSER_DEV_MODE = \"1\" ]; then npm install; fi"]}, "repositories": [{"type": "vcs", "url": "**************:learnybox/eden.git"}, {"type": "vcs", "url": "**************:learnybox/expertsender.git"}, {"type": "vcs", "url": "**************:learnybox/monolog-firehose-handler.git"}, {"type": "vcs", "url": "**************:learnybox/mailchimpapi.git"}], "require": {"php": ">=8.2.4", "symfony/var-dumper": "^6.0", "learnybox/eden": "dev-develop", "learnybox/expertsender": "dev-master", "viicslen/anim-gif": "^1.4", "braintree/braintree_php": "^6.16.0", "okwinza/cloudflare-api": "^1.0", "tijsverkoyen/css-to-inline-styles": "^2.2", "james-heinrich/getid3": "^1.9.22", "gocardless/gocardless-pro": "^5.7.0", "spipu/html2pdf": "^5.0", "html2text/html2text": "^4.1", "giggsey/libphonenumber-for-php": "^8.8", "mangopay/php-sdk-v2": "3.40.0", "opentok/opentok": "^4.4", "swiftmailer/swiftmailer": "^v6.3", "twilio/sdk": "^7.13.1", "wickedreports/php-sdk": "^1.4", "google/apiclient": "^2.12", "google/apiclient-services": "^0.270", "payplug/payplug-php": "^3.0", "pusher/pusher-php-server": "^5.0", "stripe/stripe-php": "^17.0", "aweber/aweber": "^1.1", "getresponse/getresponse": "^0.0.2", "wickedreports/old-php-isdk": "^1.9", "robmorgan/phinx": "^0.13", "mobiledetect/mobiledetectlib": "^2.8", "monolog/monolog": "^1.23", "symfony/error-handler": "^6.1", "wazaari/monolog-mysql": "1.0.4", "learnybox/monolog-firehose-handler": "dev-master", "aws/aws-sdk-php": "^3.0", "dragonmantank/cron-expression": "^2.2", "symfony/process": "^6.0", "supervisorphp/supervisor": "^3.0", "lstrojny/fxmlrpc": "^0.22.0", "php-http/guzzle7-adapter": "^1.0.0", "php-http/message-factory": "^1.0", "laminas/laminas-diactoros": "^2.19", "php-http/message": "^1.6", "guzzlehttp/guzzle": "7.8.1", "php-di/php-di": "^7", "seld/signal-handler": "^1.1", "shortpixel/shortpixel-php": "^1.4", "doctrine/annotations": "^1.6", "checkout/checkout-sdk-php": "^3.0", "symfony/routing": "^6.0", "symfony/http-foundation": "^6.0", "symfony/finder": "^6.0", "phpclassic/php-shopify": "^1.0", "jms/serializer": "^3.18", "doctrine/collections": "^1.5", "zircote/swagger-php": "^3.0", "symfony/yaml": "^6.0", "campaignmonitor/createsend-php": "^6.0", "drewm/mailchimp-api": "^2.5", "segmentio/analytics-php": "^3.7", "symfony/cache": "^6.0", "doctrine/orm": "^2.5.11", "doctrine/migrations": "^3.5", "doctrine/common": "^3.4", "sentry/sentry": "^4", "gedmo/doctrine-extensions": "^3.9", "symfony/validator": "^6.0", "symfony/translation": "^6.0", "onelogin/php-saml": "^3.1", "symfony/event-dispatcher": "^6.0", "digitick/sepa-xml": "^2.2", "gettext/gettext": "^4.6", "unsplash/unsplash": "^3.2.1", "knplabs/knp-menu": "^3.3", "sendgrid/sendgrid": "^7.8", "getresponse/sdk-php": "^3.0", "phpoffice/phpspreadsheet": "^1.29", "stevenmaguire/trello-php": "^1.0.0", "umpirsky/country-list": "^2.0", "intercom/intercom-php": "^4.4", "sendinblue/api-v3-sdk": "^8.4.2", "league/oauth2-client": "^2.6", "symfony/workflow": "^6.0", "symfony/string": "^6.0", "lincanbin/white-html-filter": "^1.4", "renoki-co/php-k8s": "^3.8", "league/climate": "^3.8", "ifsnop/mysqldump-php": "^2.9", "janu-software/facebook-php-sdk": "^0.1.11", "twig/twig": "^3.0", "paypal/paypal-checkout-sdk": "^1.0", "knplabs/knp-components": "v4.0", "symfony/http-kernel": "^6.0", "spatie/browsershot": "^5.0", "symfony/mime": "^6.3", "symfony/serializer": "^6.3", "guzzlehttp/psr7": "^2.0", "kreait/firebase-php": "7.15.0", "symfony/intl": "^7.0", "facebook/php-business-sdk": "^18.0", "getbrevo/brevo-php": "^2.0", "ramsey/uuid": "^4.7", "jaybizzle/crawler-detect": "^1.2", "tecnickcom/tcpdf": "^6.8", "maximebf/debugbar": "^1.23", "endroid/qr-code": "^6.0", "robthree/twofactorauth": "^3.0", "cleantalk/php-antispam": "^3.2", "br33f/php-ga4-mp": "^0.1.5"}, "require-dev": {"phpunit/phpunit": "^9.5", "wyrihaximus/list-classes-in-directory": "^1.5", "nette/php-generator": "^3.6", "squizlabs/php_codesniffer": "^3.9", "rector/rector": "^1.2"}, "config": {"allow-plugins": {"symfony/flex": true, "php-http/discovery": true}}}