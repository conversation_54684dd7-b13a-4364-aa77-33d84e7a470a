<?php

namespace Learnybox\Renderers;

use Learnybox\Helpers\Assets;

class MediasRenderer extends AbstractRenderer
{
    public function renderMediasBackups(array $files = [], array $folders = [], string $errorMessage = null): string
    {
        Assets::addJs('app/tables-v2/medias_backups_files.js');
        Assets::addJs('app/tables-v2/medias_backups_folders.js');

        return $this->renderTemplate(VIEWS_PATH . '/app/medias_backups.php', [
            'files' => $files,
            'folders' => $folders,
            'errorMessage' => $errorMessage,
        ]);
    }
}
