<?php

namespace Learnybox\Renderers\Menus\App;

use Learnybox\Menus\MainMenu;
use Learnybox\Renderers\Menus\MenuRenderer;

class MainMenuRenderer
{
    private MenuRenderer $menuRenderer;
    private MainMenu $mainMenu;

    public function __construct(
        MenuRenderer $menuRenderer,
        MainMenu $mainMenu
    ) {
        $this->menuRenderer = $menuRenderer;
        $this->mainMenu = $mainMenu;
    }


    public function render($isResponsive = false): string
    {
        return $this->menuRenderer->render($this->mainMenu->createMenu($isResponsive));
    }
}
