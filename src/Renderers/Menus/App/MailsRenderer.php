<?php

namespace Learnybox\Renderers\Menus\App;

use Learnybox\Helpers\TemplateHelper;
use <PERSON>rnybox\Helpers\ViewHelper;
use Learnybox\Menus\MailsMenu;
use Learnybox\Renderers\AbstractRenderer;
use Learnybox\Renderers\Menus\MenuRendererInterface;
use Learnybox\Renderers\Menus\MenuRenderer;

class MailsRenderer extends AbstractRenderer implements MenuRendererInterface
{
    private MailsMenu $mailsMenu;

    private MenuRenderer $menuRenderer;

    public function __construct(
        MailsMenu $mailsMenu,
        MenuRenderer $menuRenderer
    ) {
        parent::__construct();

        $this->mailsMenu = $mailsMenu;
        $this->menuRenderer = $menuRenderer;
    }

    public function render(): string
    {
        $menuOutput = $this->menuRenderer->render($this->mailsMenu->createMenu());

        $headerOutput = TemplateHelper::render('app/layouts/headers/base/default_submenu.html.twig', [
            'menu' => $menuOutput,
            'icon' => 'auto'
        ]);

        return $headerOutput;
    }
}
