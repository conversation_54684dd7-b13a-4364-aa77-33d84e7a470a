<?php

namespace Learnybox\Renderers\Menus\App\Reglages;

use Learnybox\Components\HttpFoundation\Request;
use Learnybox\Menus\NotificationsWebReglagesMenu;
use Learnybox\Renderers\AbstractRenderer;
use Learnybox\Renderers\Menus\MenuRenderer;
use Learnybox\Renderers\Menus\MenuRendererInterface;

class NotificationsWebReglagesRenderer extends AbstractRenderer implements MenuRendererInterface
{
    private NotificationsWebReglagesMenu $notificationsWebReglagesMenu;

    private \NotificationswebCampaigns $notificationswebCampaigns;

    private Request $request;

    private MenuRenderer $menuRenderer;

    public function __construct(
        NotificationsWebReglagesMenu $notificationsWebReglagesMenu,
        \NotificationswebCampaigns $notificationswebCampaigns,
        Request $request,
        MenuRenderer $menuRenderer
    ) {
        parent::__construct();

        $this->notificationsWebReglagesMenu = $notificationsWebReglagesMenu;
        $this->notificationswebCampaigns = $notificationswebCampaigns;
        $this->request = $request;
        $this->menuRenderer = $menuRenderer;
    }

    public function render(): string
    {
        $campaignId = $this->request->attributes->getInt('campaignId');
        if (!$campaignId && isset($_SESSION['notificationsweb']['id_campaign'])) {
            $campaignId = $_SESSION['notificationsweb']['id_campaign'];
        }
        if (!$campaignId) {
            return '';
        }

        $campaign = $this->notificationswebCampaigns->getCampaignById($campaignId);
        return $this->menuRenderer->render($this->notificationsWebReglagesMenu->createMenu($campaign));
    }
}
