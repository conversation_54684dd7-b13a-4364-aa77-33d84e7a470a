<?php

namespace Learnybox\Renderers\Menus\App;

use Doctrine\ORM\EntityManager;
use Learnybox\Components\HttpFoundation\Request;
use Learnybox\Entity\DoneForYou\DoneForYouService;
use Learnybox\Helpers\TemplateHelper;
use Learnybox\Helpers\ViewHelper;
use Learnybox\Renderers\AbstractRenderer;
use Learnybox\Renderers\DoneForYou\HeaderActionsRenderer;
use Learnybox\Renderers\Menus\MenuRendererInterface;
use Learnybox\Services\DoneForYou\DoneForYouTransitionLogService;

class DoneForYouServiceRenderer extends AbstractRenderer implements MenuRendererInterface
{
    private EntityManager $em;
    private DoneForYouTransitionLogService $transitionLogService;
    private Request $request;
    private HeaderActionsRenderer $headerActionsRenderer;

    public function __construct(
        EntityManager $em,
        Request $request,
        DoneForYouTransitionLogService $transitionLogService,
        HeaderActionsRenderer $headerActionsRenderer
    ) {
        parent::__construct();
        $this->em = $em;
        $this->request = $request;
        $this->transitionLogService = $transitionLogService;
        $this->headerActionsRenderer = $headerActionsRenderer;
    }

    public function render(): string
    {
        $serviceId = $this->request->attributes->getInt('id');
        $service = $this->em->getRepository(DoneForYouService::class)->find($serviceId);

        if ($service === null) {
            return '';
        }

        $statusRendered = ViewHelper::renderTemplate(VIEWS_PATH . '/app/done_for_you/service/_badge.php', [
            'service' => $service,
            'transitionLogRepository' => $this->transitionLogService->getRepository()
        ]);

        return TemplateHelper::render('app/layouts/headers/done_for_you_service.html.twig', [
            'service' => $service,
            'actions' => $this->headerActionsRenderer->getActions($service),
            'statusRendered' => $statusRendered
        ]);
    }
}
