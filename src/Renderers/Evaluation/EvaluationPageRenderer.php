<?php

namespace Learnybox\Renderers\Evaluation;

use Learnybox\Helpers\RouterHelper;
use Learnybox\Renderers\HtmlSnippetsRenderer;
use Learnybox\Repositories\Evaluation\EvaluationPageRepository;
use Learnybox\Renderers\AbstractRenderer;

class EvaluationPageRenderer extends AbstractRenderer
{
    private EvaluationPageRepository $evaluationPageRepository;
    private EvaluationRenderer $evaluationRenderer;
    private HtmlSnippetsRenderer $htmlSnippetsRenderer;

    public function __construct(
        EvaluationPageRepository $evaluationPageRepository,
        EvaluationRenderer $evaluationRenderer,
        HtmlSnippetsRenderer $htmlSnippetsRenderer
    ) {
        parent::__construct();
        $this->evaluationPageRepository = $evaluationPageRepository;
        $this->evaluationRenderer = $evaluationRenderer;
        $this->htmlSnippetsRenderer = $htmlSnippetsRenderer;
    }

    public function displayPageForm(array $evaluation = [], int $idPage = null): string
    {
        $cleanedPost = clean_post($_POST);

        if (!$evaluation) {
            return $this->evaluationRenderer->displayErrors(__('Erreur fatale : cette évaluation n\'existe pas') . '<br><a href="' . RouterHelper::generate('app_evaluations') . '">&laquo;&nbsp;' . __('Retour') . '</a>');
        }
        $idEvaluation = $evaluation['id_evaluation'];

        $fatalError = '';
        if ($idPage) {
            $page = $this->evaluationPageRepository->getPageById($idEvaluation, $idPage);
            if (!$page) {
                $fatalError = __("Cette page n'existe pas");
            }
        } else {
            $fatalError = __("Cette page n'existe pas");
        }

        if ($fatalError) {
            return $this->evaluationRenderer->displayErrors($fatalError . '<br><a href="' . RouterHelper::generate('app_evaluation_questions', ['slug' => $evaluation['random_id']]) . '">&laquo;&nbsp;' . __('Retour') . '</a>');
        }

        $nom = $page['nom'];
        $description = $page['description'];
        $timer = $page['timer'];
        $random = $page['random'];

        if (isset($_POST['nom'])) {
            if (isset($cleanedPost['nom'])) {
                $nom = $cleanedPost['nom'];
            }
            if (isset($cleanedPost['description'])) {
                $description = $cleanedPost['description'];
            }
            if (isset($cleanedPost['timer'])) {
                $timer = $cleanedPost['timer'];
            }
            if (isset($cleanedPost['random'])) {
                $random = $cleanedPost['random'];
            }
        }

        return $this->renderTemplate(FORMS_PATH . '/app/evaluations/page.php', [
            'evaluation' => $evaluation,
            'nom' => $nom,
            'description' => stripslashes($description),
            'timer' => $timer,
            'random' => $random,
            'idPage' => $idPage,
        ]);
    }

    public function generateSelectEvaluationPage(int $idEvaluation, int $idPage): string
    {
        $output = '';
        $pages = $this->evaluationPageRepository->getPages($idEvaluation);
        if ($pages) {
            foreach ($pages as $id => $page) {
                $output .= $this->htmlSnippetsRenderer->generateSelectOptions([$page['id_page'] => $page['nom']], $idPage);
            }
        }

        return $output;
    }
}
