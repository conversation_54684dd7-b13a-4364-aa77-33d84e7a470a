<?php

namespace Learnybox\Renderers\Layouts\Coachs;

use Learnybox\Menus\Coachs\AccountMenu;
use Learnybox\Renderers\AbstractRenderer;
use Learnybox\Renderers\Menus\MenuRenderer;

class AccountRenderer extends AbstractRenderer
{
    private MenuRenderer $menuRenderer;
    private AccountMenu $accountMenu;

    public function __construct(MenuRenderer $menuRenderer, AccountMenu $accountMenu)
    {
        parent::__construct();

        $this->menuRenderer = $menuRenderer;
        $this->accountMenu = $accountMenu;
    }

    public function render(): string
    {
        return $this->menuRenderer->render($this->accountMenu->createMenu(), ['allow_safe_labels' => true]);
    }
}
