<?php

namespace Learnybox\Renderers\Sondage;

use Learnybox\Helpers\ViewHelper;
use Learnybox\Renderers\AbstractRenderer;
use Learnybox\Repositories\Sondage\Participant\SondageParticipantRepository;

class SondageMailRenderer extends AbstractRenderer
{
    const BASE_PATH = '/app/sondage/mail/';

    private SondageParticipantRepository $sondageParticipantRepository;

    public function __construct(
        SondageParticipantRepository $sondageParticipantRepository
    ) {
        $this->sondageParticipantRepository = $sondageParticipantRepository;
    }

    public function renderMails(
        string $randomId,
        array $sondage,
        ?array $mails
    ): string {
        return $this->checkFileAndRenderTemplate(ViewHelper::getPath(self::BASE_PATH, 'mails'), [
            'random_id' => $randomId,
            'sondage' => $sondage,
            'mails' => $mails
        ]);
    }

    public function renderMail(
        int $idSondage,
        string $randomId,
        ?array $mail
    ): string {
        return $this->checkFileAndRenderTemplate(ViewHelper::getPath(self::BASE_PATH, 'mail'), [
            'sondageParticipantRepository' => $this->sondageParticipantRepository,
            'id_sondage' => $idSondage,
            'random_id' => $randomId,
            'mail' => $mail
        ]);
    }
}
