<?php

namespace Learnybox\Forms\DoneForYou;

use Learnybox\Components\Workflow\WorkflowRegistry;
use Learnybox\Entity\DoneForYou\DoneForYouService;
use Learnybox\Enums\DoneForYou\DoneForYouTransitionEnum;
use Learnybox\Event\DoneForYou\DoneForYouServiceTransitionSavedEvent;
use Learnybox\Event\EntityEvent;
use Learnybox\Event\EntityEvents;
use Learnybox\Forms\AbstractForm;
use Learnybox\Services\DoneForYou\DoneForYouServiceService;
use Symfony\Component\EventDispatcher\EventDispatcher;
use Symfony\Component\Validator\Constraints\NotBlank;

class DoneForYouRequestRefuseForm extends AbstractDoneForYouForm
{
    private WorkflowRegistry $workflowRegistry;

    private EventDispatcher $eventDispatcher;

    public function __construct(
        DoneForYouServiceService $doneForYouServiceService,
        WorkflowRegistry $workflowRegistry,
        EventDispatcher $eventDispatcher
    ) {
        parent::__construct($doneForYouServiceService);

        $this->workflowRegistry = $workflowRegistry;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function validateRefuse(array $postArray): string
    {
        $rules = [
            'refusalReason' => [
                new NotBlank(['message' => __('Veuillez spécifier le motif de refus.')])
            ],
            'serviceId' => [
                new NotBlank(['message' => __('Veuillez spécifier un service.')])
            ],
        ];

        $errors = $this->validateForm($rules, $postArray);

        return $this->sendResponse($errors, true);
    }

    private function getParamsFromPost(array $cleanedPost): array
    {
        return [
            'refusalReason' => filter_var($cleanedPost['refusalReason'], FILTER_SANITIZE_SPECIAL_CHARS)
        ];
    }

    public function refuse(array $cleanedPost): array
    {
        $service = $this->findService($cleanedPost['serviceId']);

        if ($service === null) {
            $response = $this->sendErrorResponse([__('Impossible de récupérer le service.')], true);
            return json_decode($response, true);
        }

        $params = $this->getParamsFromPost($cleanedPost);

        $this->getObjectHydrator()->hydrate($service->getRequest(), $params);

        // Apply transition
        $workflow = $this->workflowRegistry->get($service);
        $workflow->apply($service, DoneForYouTransitionEnum::REFUSE_REQUEST);

        $save = $this->save($service);
        if (false === $save['valid']) {
            return $save;
        }

        // Dispatch event
        $event = new DoneForYouServiceTransitionSavedEvent($service, DoneForYouTransitionEnum::REFUSE_REQUEST);
        $this->eventDispatcher->dispatch($event, EntityEvents::DONE_FOR_YOU_SERVICE_TRANSITION_SAVED);

        return $this->sendSuccessResponse();
    }

    public function save(DoneForYouService $service): array
    {
        try {
            $this->doneForYouServiceService->persistAndFlush($service);
        } catch (\Exception $e) {
            $response = $this->sendErrorResponse([__('Une erreur est survenue lors de la sauvegarde.')], true);
            return json_decode($response, true);
        }

        return $this->sendSuccessResponse();
    }
}
