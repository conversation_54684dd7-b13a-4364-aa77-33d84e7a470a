<?php

namespace Learnybox\Forms\DoneForYou;

use Learnybox\Components\Workflow\WorkflowRegistry;
use Learnybox\Entity\DoneForYou\DoneForYouOffer;
use Learnybox\Entity\DoneForYou\DoneForYouRequest;
use Learnybox\Entity\DoneForYou\DoneForYouService;
use Learnybox\Enums\DoneForYou\DoneForYouHosterEnum;
use Learnybox\Enums\DoneForYou\DoneForYouRequestTypeEnum;
use Learnybox\Enums\DoneForYou\DoneForYouStateEnum;
use Learnybox\Enums\DoneForYou\DoneForYouTransitionEnum;
use Learnybox\Event\DoneForYou\DoneForYouServiceTransitionSavedEvent;
use Learnybox\Event\EntityEvent;
use Learnybox\Event\EntityEvents;
use Learnybox\Forms\AbstractForm;
use Learnybox\Services\ClientService;
use Learnybox\Services\DoneForYou\DoneForYouAttachedFileService;
use Learnybox\Services\DoneForYou\DoneForYouOfferService;
use Learnybox\Services\DoneForYou\DoneForYouRequestService;
use Learnybox\Services\DoneForYou\DoneForYouServiceService;
use Symfony\Component\EventDispatcher\EventDispatcher;
use Symfony\Component\Validator\Constraints\GreaterThanOrEqual;
use Symfony\Component\Validator\Constraints\NotBlank;

class DoneForYouOfferForm extends AbstractDoneForYouForm
{
    private ClientService $clientService;

    private DoneForYouOfferService $doneForYouOfferService;

    private EventDispatcher $eventDispatcher;

    private WorkflowRegistry $workflowRegistry;

    public function __construct(
        ClientService $clientService,
        DoneForYouServiceService $doneForYouServiceService,
        DoneForYouOfferService $doneForYouOfferService,
        EventDispatcher $eventDispatcher,
        WorkflowRegistry $workflowRegistry
    ) {
        parent::__construct($doneForYouServiceService);

        $this->clientService = $clientService;
        $this->doneForYouOfferService = $doneForYouOfferService;
        $this->eventDispatcher = $eventDispatcher;
        $this->workflowRegistry = $workflowRegistry;
    }

    public function validateMade(array $postArray): string
    {
        $dateStart = new \DateTime();
        $dateStart->modify('+2 weekday');

        $rules = [
            'workStartAt' => [
                new NotBlank(['message' => __('Veuillez spécifier une date de début .')]),
                new GreaterThanOrEqual([
                    'value' => $dateStart->format('Y-m-d'),
                    'message' => __('La date de début doit être supérieure à la date d\'aujourd\'hui + 2 jours ouvrés (temps d\'acceptation du client).')
                ])
            ],
            'workEndAt' => [
                new NotBlank(['message' => __('Veuillez spécifier une date de livraison.')]),
                new GreaterThanOrEqual([
                    'value' => $postArray['workStartAt'],
                    'message' => __('La date de livraison doit être supérieure à la date de début.')
                ])
            ],
            'creditCost' => [
                new NotBlank(['message' => __('Veuillez spécifier un nombre de crédits.')]),
                new GreaterThanOrEqual([
                    'value' => 1,
                    'message' => __('Le coût en crédits doit être supérieur ou égal à 1.')
                ])
            ],
            'description' => [
                new NotBlank(['message' => __('Veuillez spécifier des précisions.')])
            ],
            'serviceId' => [
                new NotBlank(['message' => __('Veuillez spécifier un service.')])
            ],
        ];

        $errors = $this->validateForm($rules, $postArray);

        return $this->sendResponse($errors, true);
    }

    private function getMadeParamsFromPost(array $cleanedPost): array
    {
        $params = [
            'workStartAt' => filter_var($cleanedPost['workStartAt'], FILTER_SANITIZE_SPECIAL_CHARS),
            'workEndAt' => filter_var($cleanedPost['workEndAt'], FILTER_SANITIZE_SPECIAL_CHARS),
            'creditCost' => filter_var($cleanedPost['creditCost'], FILTER_VALIDATE_INT),
            'description' => filter_var($cleanedPost['description'], FILTER_SANITIZE_SPECIAL_CHARS),
        ];

        $params['workStartAt'] = \DateTime::createFromFormat('Y-m-d', $params['workStartAt']);
        $params['workEndAt'] = \DateTime::createFromFormat('Y-m-d', $params['workEndAt']);

        return $params;
    }

    public function createMade(array $cleanedPost): array
    {
        $service = $this->findService($cleanedPost['serviceId']);

        if ($service === null) {
            $response = $this->sendErrorResponse([__('Impossible de récupérer le service.')], true);
            return json_decode($response, true);
        }

        $params = $this->getMadeParamsFromPost($cleanedPost);

        // Create Offer
        $offer = new DoneForYouOffer();
        $this->getObjectHydrator()->hydrate($offer, $params);

        $service->setOffer($offer);

        // Apply transition
        $workflow = $this->workflowRegistry->get($service);
        $workflow->apply($service, DoneForYouTransitionEnum::MAKE_OFFER);

        // Save
        $save = $this->save($service);
        if (false === $save['valid']) {
            return $save;
        }

        // Dispatch event
        $event = new DoneForYouServiceTransitionSavedEvent($service, DoneForYouTransitionEnum::MAKE_OFFER);
        $this->eventDispatcher->dispatch($event, EntityEvents::DONE_FOR_YOU_SERVICE_TRANSITION_SAVED);

        return $this->sendSuccessResponse();
    }

    public function save(DoneForYouService $service): array
    {
        try {
            $this->doneForYouServiceService->persistAndFlush($service);
        } catch (\Exception $e) {
            $response = $this->sendErrorResponse([__('Une erreur est survenue lors de la sauvegarde.')], true);
            return json_decode($response, true);
        }

        return $this->sendSuccessResponse();
    }
}
