<?php

namespace Learnybox\Forms\Reseller;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Client\Produit\ClientProduit;
use Learnybox\Forms\AbstractForm;
use Learnybox\Services\ClientService;
use Learnybox\Services\Reseller\ResellerLicenseService;
use Symfony\Component\Validator\Constraints\GreaterThan;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Email;

/**
 * Class ClientForm
 * @package Learnybox\Forms\Reseller
 */
class ClientForm extends AbstractForm
{
    /**
     * @var EntityManager
     */
    private $em;

    /**
     * @var ClientService
     */
    private $clientService;

    /**
     * @var ResellerLicenseService
     */
    private $resellerLicenseService;

    /**
     * @var \Clients
     */
    private $clientsClass;

    /**
     * ClientForm constructor.
     * @param EntityManager $em
     * @param ClientService $clientService
     * @param ResellerLicenseService $resellerLicenseService
     * @param \Clients $clientsClass
     */
    public function __construct(
        EntityManager $em,
        ClientService $clientService,
        ResellerLicenseService $resellerLicenseService,
        \Clients $clientsClass
    ) {
        $this->em = $em;
        $this->clientService = $clientService;
        $this->resellerLicenseService = $resellerLicenseService;
        $this->clientsClass = $clientsClass;
    }

    /**
     * @param array $postArray
     * @return array
     */
    public function validatepostAdd(array $postArray): array
    {
        $rules = [
            'nom_client' => [
                new NotBlank(['message' => __('Veuillez indiquer le nom du client.')])
            ],
            'uniqid' => [
                new NotBlank(['message' => __('Veuillez indiquer le lien du client.')])
            ],
            'id_license' => [
                new NotBlank(['message' => __('Veuillez sélectionner une licence.')]),
                new GreaterThan(['value' => 0, 'message' => __('Veuillez sélectionner une licence.')])
            ],
            'fname' => [
                new NotBlank(['message' => __('Veuillez indiquer le prénom de l\'administrateur.')])
            ],
            'lname' => [
                new NotBlank(['message' => __('Veuillez indiquer le nom de l\'administrateur.')])
            ],
            'email' => [
                new NotBlank(['message' => __('Veuillez indiquer l\'adresse email de l\'administrateur.')]),
                new Email(['message' => __('Veuillez indiquer une adresse email valide.')])
            ],
        ];

        $errors = $this->validateForm($rules, $postArray);

        $uniqid = filter_var($postArray['uniqid'], FILTER_SANITIZE_SPECIAL_CHARS);
        if ($uniqid) {
            $client = $this->clientService->getRepository()->findBy(['uniqid' => $uniqid]);
            if ($client) {
                array_push($errors, __('Un client existe déjà avec ce lien'));
            }
        }

        $idLicense = filter_var($postArray['id_license'], FILTER_VALIDATE_INT);
        if ($idLicense) {
            $license = $this->resellerLicenseService->getRepository()->findOneBy(['id' => $idLicense, 'client' => $_SESSION['id_client']]);
            $nbLicenseLeft = $license->getAllowed() - $license->getUsed();
            if (!$nbLicenseLeft) {
                array_push($errors, __('Cette licence n\'est plus disponible'));
            }
        }

        return $this->sendResponse($errors);
    }

    /**
     * @param array $postArray
     * @return array
     */
    public function validatePostUpdate(array $postArray): array
    {
        $idClient = filter_var($postArray['id_client'], FILTER_VALIDATE_INT);
        if (!$idClient) {
            return $this->sendErrorResponse([__('Ce client n\'existe pas.')]);
        }

        $client = $this->clientService->getRepository()->find($idClient);
        if (!$client or $client->getClientReseller()->getIdClient() != $_SESSION['id_client']) {
            return $this->sendErrorResponse([__('Ce client n\'existe pas.')]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $cleanedPost
     * @return array
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function insert(array $cleanedPost): array
    {
        $params = $this->getParamsFromPost($cleanedPost);
        $params = $this->addDefaultParams($params);

        $license = $this->resellerLicenseService->getRepository()->findOneBy(['id' => $params['id_license'], 'client' => $_SESSION['id_client']]);
        $params['abonnement'] = $license->getClientProduit()->getNom();

        $insertClient = $this->clientsClass->insert_client($params);
        if (!$insertClient['valid']) {
            return $insertClient;
        }

        $idClient = $insertClient['id_client'];

        $client = $this->clientService->getRepository()->find($idClient);

        //add product params
        $productParams = $this->getProductParams($license->getClientProduit());
        $this->getObjectHydrator()->hydrate($client, $productParams);
        $client->setDateFinAbonnement(new \DateTime('2100-12-31'));
        $client->setClientReseller($this->clientService->getRepository()->find($_SESSION['id_client']));
        $client->setLicense($license);
        $this->clientService->persistAndFlush($client);

        //decrement license
        $license->setUsed($license->getUsed() + 1);
        $this->resellerLicenseService->persistAndFlush($license);

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $cleanedPost
     * @return array
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function update(array $cleanedPost): array
    {
        $idClient = filter_var($cleanedPost['id_client'], FILTER_VALIDATE_INT);
        $client = $this->clientService->getRepository()->find($idClient);

        $nomClient = filter_var($cleanedPost['nom_client'], FILTER_SANITIZE_SPECIAL_CHARS);
        $client->setNomClient($nomClient);

        $idLicense = filter_var($cleanedPost['id_license'], FILTER_VALIDATE_INT);
        $license = $this->resellerLicenseService->getRepository()->findOneBy(['id' => $idLicense, 'client' => $_SESSION['id_client']]);
        if ($license->getId() != $client->getLicense()->getId()) {
            $nbLicenseLeft = $license->getAllowed() - $license->getUsed();
            if (!$nbLicenseLeft) {
                return ['valid' => false, 'message' => __('Cette licence n\'est plus disponible')];
            }

            //increment previous license
            $previousLicense = $client->getLicense();
            $previousLicense->setUsed($previousLicense->getUsed() - 1);
            $this->resellerLicenseService->persistAndFlush($previousLicense);

            //decrement new license
            $license->setUsed($license->getUsed() + 1);
            $this->resellerLicenseService->persistAndFlush($license);

            $productParams = $this->getProductParams($license->getClientProduit());
            $this->getObjectHydrator()->hydrate($client, $productParams);
            $client->setLicense($license);
        }

        $this->clientService->persistAndFlush($client);

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $cleanedPost
     * @return array
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function active(array $cleanedPost): array
    {
        $idClient = filter_var($cleanedPost['id_client'], FILTER_VALIDATE_INT);
        $client = $this->clientService->getRepository()->find($idClient);
        $client->setActive(true);
        $this->clientService->persistAndFlush($client);

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $cleanedPost
     * @return array
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function desactive(array $cleanedPost): array
    {
        $idClient = filter_var($cleanedPost['id_client'], FILTER_VALIDATE_INT);
        $client = $this->clientService->getRepository()->find($idClient);
        $client->setActive(false);
        $this->clientService->persistAndFlush($client);

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $cleanedPost
     * @return array
     */
    public function getParamsFromPost(array $cleanedPost): array
    {
        $params = [
            'nom_client' => filter_var($cleanedPost['nom_client'], FILTER_SANITIZE_SPECIAL_CHARS),
            'uniqid' => filter_var($cleanedPost['uniqid'], FILTER_SANITIZE_SPECIAL_CHARS),
            'id_license' => filter_var($cleanedPost['id_license'], FILTER_VALIDATE_INT),
            'fname' => filter_var($cleanedPost['fname'], FILTER_SANITIZE_SPECIAL_CHARS),
            'lname' => filter_var($cleanedPost['lname'], FILTER_SANITIZE_SPECIAL_CHARS),
            'email' => filter_var($cleanedPost['email'], FILTER_VALIDATE_EMAIL),
        ];
        return parent::filterParamsFromPost($params, $cleanedPost);
    }

    /**
     * @param array $params
     * @return array
     */
    private function addDefaultParams(array $params): array
    {
        $params['logo'] = '';
        $params['password'] = '';
        $params['maxusers'] = 100;
        $params['maxformations'] = 1;

        return $params;
    }

    /**
     * @param ClientProduit $product
     * @return array
     */
    private function getProductParams(ClientProduit $product): array
    {
        $productParams = [];
        $productParams['abonnement'] = $product->getNom();
        $productParams['maxformations'] = $product->getMaxformations();
        $productParams['maxusers'] = $product->getMaxusers();
        $productParams['stockage'] = $product->getStockage();
        $productParams['maxconferences'] = $product->getMaxconferences();
        $productParams['maxconferences_participants'] = $product->getMaxconferencesParticipants();
        $productParams['maxconferences_duration'] = $product->getMaxconferencesDuration();
        $productParams['maxautowebinaires'] = $product->getMaxautowebinaires();
        $productParams['maxautowebinaires_participants'] = $product->getMaxautowebinairesParticipants();
        $productParams['maxtransactions'] = $product->getMaxtransactions();
        $productParams['maxcampaigns'] = $product->getMaxcampaigns();
        $productParams['maxtunnels'] = $product->getMaxtunnels();
        $productParams['maxtunnels_visites'] = $product->getMaxtunnelsVisites();
        $productParams['max_tunnels_pages'] = $product->getMaxTunnelsPages();
        $productParams['max_lbar_users'] = $product->getMaxLbarUsers();
        $productParams['max_lbar_mails'] = $product->getMaxLbarMails();
        $productParams['lbar_mails_left'] = $product->getMaxLbarMails();
        $productParams['mail_domain'] = $product->hasMailDomain();
        $productParams['max_sequences'] = $product->getMaxSequences();
        $productParams['max_tags'] = $product->getMaxTags();
        $productParams['max_popups'] = $product->getMaxPopups();
        $productParams['max_split_tests'] = $product->getMaxSplitTests();
        $productParams['max_articles_blog'] = $product->getMaxArticlesBlog();
        $productParams['max_domaines'] = $product->getMaxDomaines();
        $productParams['max_pages'] = $product->getMaxPages();
        $productParams['max_pages_visites'] = $product->getMaxPagesVisites();
        $productParams['app_mobile'] = $product->hasAppMobile();

        return $productParams;
    }
}
