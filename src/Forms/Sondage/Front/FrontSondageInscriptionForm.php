<?php

namespace Learnybox\Forms\Sondage\Front;

use Learnybox\Entity\Sondage\Renseignement\Renseignement;
use Learnybox\Forms\AbstractForm;
use Learnybox\Repositories\Sondage\Participant\SondageParticipantDetailPersonnaliseRepository;
use Learnybox\Repositories\Sondage\Participant\SondageParticipantDetailRepository;
use Learnybox\Repositories\Sondage\Participant\SondageParticipantRepository;
use Learnybox\Repositories\Sondage\Renseignement\RenseignementRepository;
use Learnybox\Repositories\Sondage\Renseignement\SondageRenseignementPersonnaliseRepository;
use Learnybox\Repositories\Sondage\Renseignement\SondageRenseignementRepository;
use Learnybox\Repositories\Sondage\SondageRepository;
use Learnybox\Repositories\UsersRepository;
use Learnybox\Services\Sondage\SondageService;
use Learnybox\Services\Logger\LoggerService;
use Monolog\Logger;

class FrontSondageInscriptionForm extends AbstractForm
{
    private \Utilisateurs $utilisateurs;

    private \Utilisateursrgpd $utilisateursrgpd;

    private \Password $password;

    private \Autorepondeurs_Base $autorepondeursBase;

    private UsersRepository $usersRepository;

    private SondageRepository $sondageRepository;

    private RenseignementRepository $renseignementRepository;

    private SondageRenseignementRepository $sondageRenseignementRepository;

    private SondageRenseignementPersonnaliseRepository $sondageRenseignementPersonnaliseRepository;

    private SondageParticipantRepository $sondageParticipantRepository;

    private SondageParticipantDetailRepository $sondageParticipantDetailRepository;

    private SondageParticipantDetailPersonnaliseRepository $sondageParticipantDetailPersonnaliseRepository;

    private SondageService $sondageService;

    public function __construct(
        \Utilisateurs $utilisateurs,
        \Utilisateursrgpd $utilisateursrgpd,
        \Password $password,
        \Autorepondeurs_Base $autorepondeursBase,
        UsersRepository $usersRepository,
        SondageRepository $sondageRepository,
        RenseignementRepository $renseignementRepository,
        SondageRenseignementRepository $sondageRenseignementRepository,
        SondageRenseignementPersonnaliseRepository $sondageRenseignementPersonnaliseRepository,
        SondageParticipantRepository $sondageParticipantRepository,
        SondageParticipantDetailRepository $sondageParticipantDetailRepository,
        SondageParticipantDetailPersonnaliseRepository $sondageParticipantDetailPersonnaliseRepository,
        SondageService $sondageService
    ) {
        $this->utilisateurs = $utilisateurs;
        $this->utilisateursrgpd = $utilisateursrgpd;
        $this->password = $password;
        $this->autorepondeursBase = $autorepondeursBase;
        $this->usersRepository = $usersRepository;
        $this->sondageRepository = $sondageRepository;
        $this->renseignementRepository = $renseignementRepository;
        $this->sondageRenseignementRepository = $sondageRenseignementRepository;
        $this->sondageRenseignementPersonnaliseRepository = $sondageRenseignementPersonnaliseRepository;
        $this->sondageParticipantRepository = $sondageParticipantRepository;
        $this->sondageParticipantDetailRepository = $sondageParticipantDetailRepository;
        $this->sondageParticipantDetailPersonnaliseRepository = $sondageParticipantDetailPersonnaliseRepository;
        $this->sondageService = $sondageService;
    }

    public function validatepost_inscription(array $postarray)
    {
        $errors = [];

        $random_id = filter_var($postarray['random_id'], FILTER_SANITIZE_SPECIAL_CHARS);
        if (!$random_id) {
            LoggerService::log(Logger::ERROR, "Erreur sur la page d'inscription à un sondage : pas de random_id");
            return $this->sendResponse([__("Erreur critique : impossible de continuer, veuillez nous excuser. L'administrateur a été prévenu.")]);
        } else {
            $sondage = $this->sondageRepository->getSondageByRandomId($random_id);
            if (!$sondage) {
                LoggerService::log(Logger::ERROR, "Erreur sur la page d'inscription à un sondage : ce sondage n'existe pas. RandomId : $random_id");
                return $this->sendResponse([__("Erreur critique : ce sondage n'existe pas")]);
            }
        }

        $user = $this->utilisateurs->getUser();
        if (!$user and !$sondage['anonymous']) {
            $prenom = filter_var($postarray['prenom'], FILTER_SANITIZE_SPECIAL_CHARS);
            $email = filter_var($postarray['email'], FILTER_VALIDATE_EMAIL);

            if (!$prenom) {
                $errors[] = __("Veuillez entrer votre prénom");
            }
            if (trim($postarray['email']) == '') {
                $errors[] = __("Veuillez entrer votre adresse email");
            } elseif (!$email) {
                $errors[] = __("Veuillez entrer une adresse email valide");
            }
        }

        //verification des informations complémentaires
        $renseignements = $this->sondageRenseignementRepository->getRenseignementsBySondageAndType($sondage['id_sondage'], 'pre');
        if ($renseignements) {
            foreach ($renseignements as $id => $_renseignement) {
                $renseignement_infos = $this->sondageRenseignementRepository->getRenseignementBySondage($sondage['id_sondage'], $_renseignement['id_rens']);
                if ($renseignement_infos['obligatoire'] == "1") {
                    if (isset($postarray[$_renseignement['nom']])) {
                        ${'rens-' . $_renseignement['nom']} = $this->sondageService->filterByType($_renseignement['type'], $postarray[$_renseignement['nom']]);
                        if (${'rens-' . $_renseignement['nom']} == false) {
                            $errors[] = __("Veuillez remplir le champ") . " \"" . Renseignement::getI18nLibelles($_renseignement['nom_affiche']) . "\"";
                        }
                    } else {
                        $errors[] = __("Veuillez remplir le champ") . " \"" . Renseignement::getI18nLibelles($_renseignement['nom_affiche']) . "\"";
                    }
                }
            }
        }

        $renseignements_personnalises = $this->sondageRenseignementPersonnaliseRepository->getDistinctRenseignementsPersonnalisesBySondage($sondage['id_sondage'], 'pre');
        if ($renseignements_personnalises) {
            foreach ($renseignements_personnalises as $id => $_renseignement_personnalise) {
                if ($_renseignement_personnalise['obligatoire'] == "1") {
                    if (isset($postarray[$_renseignement_personnalise['nom']])) {
                        ${'rens-' . $_renseignement_personnalise['nom']} = $this->sondageService->filterByType($_renseignement_personnalise['type'], $postarray[$_renseignement_personnalise['nom']]);
                        if (${'rens-' . $_renseignement_personnalise['nom']} == false) {
                            $errors[] = __("Veuillez remplir le champ") . " \"" . $_renseignement_personnalise['nom_affiche'] . "\"";
                        }
                    } else {
                        $errors[] = __("Veuillez remplir le champ") . " \"" . $_renseignement_personnalise['nom_affiche'] . "\"";
                    }
                }

                if ($_renseignement_personnalise['type'] == "input_checkbox" and isset($postarray[$_renseignement_personnalise['nom']])) {
                    $nb_reponses = count($postarray[$_renseignement_personnalise['nom']]);
                    if ($_renseignement_personnalise['min'] > 0 and $_renseignement_personnalise['max'] > 0) {
                        if ($_renseignement_personnalise['min'] == $_renseignement_personnalise['max'] and $_renseignement_personnalise['min'] != $nb_reponses) {
                            $errors[] = __("Veuillez sélectionner") . " " . $_renseignement_personnalise['min'] . " " . __("réponses à la question") . " \"" . $_renseignement_personnalise['nom_affiche'] . "\"";
                        } else {
                            if ($nb_reponses < $_renseignement_personnalise['min']) {
                                $errors[] = __("Veuillez sélectionner") . " " . $_renseignement_personnalise['min'] . " " . __("réponses minimum à la question") . " \"" . $_renseignement_personnalise['nom_affiche'] . "\"";
                            }
                            if ($nb_reponses > $_renseignement_personnalise['max']) {
                                $errors[] = __("Veuillez sélectionner") . " " . $_renseignement_personnalise['max'] . " " . __("réponses maximum à la question") . " \"" . $_renseignement_personnalise['nom_affiche'] . "\"";
                            }
                        }
                    } else {
                        if ($_renseignement_personnalise['min'] > 0 and $nb_reponses < $_renseignement_personnalise['min']) {
                            $errors[] = __("Veuillez sélectionner") . " " . $_renseignement_personnalise['min'] . " " . __("réponses minimum à la question") . " \"" . $_renseignement_personnalise['nom_affiche'] . "\"";
                        }
                        if ($_renseignement_personnalise['max'] > 0 and $nb_reponses > $_renseignement_personnalise['max']) {
                            $errors[] = __("Veuillez sélectionner") . " " . $_renseignement_personnalise['max'] . " " . __("réponses maximum à la question") . " \"" . $_renseignement_personnalise['nom_affiche'] . "\"";
                        }
                    }
                }
            }
        }

        return $this->sendResponse($errors);
    }

    public function insert_inscription($cleaned_post): array
    {
        $sondage = $this->sondageRepository->getSondageByRandomId($cleaned_post['random_id']);
        $id_sondage = $sondage['id_sondage'];

        $nom = '';

        // RGPD
        $rgpd = $rgpd_aff = 0;
        $rgpd_notice = $rgpd_aff_notice = '';
        $rgpd_date = $rgpd_aff_date = date('Y-m-d H:i:s');

        if (isset($cleaned_post['rgpd'])) {
            $rgpd = 1;
        }
        if (isset($cleaned_post['rgpd_aff'])) {
            $rgpd_aff = 1;
        }
        if (isset($cleaned_post['rgpd_notice'])) {
            $rgpd_notice = filter_var($cleaned_post['rgpd_notice'], FILTER_SANITIZE_SPECIAL_CHARS);
        }
        if (isset($cleaned_post['rgpd_aff_notice'])) {
            $rgpd_aff_notice = filter_var($cleaned_post['rgpd_aff_notice'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        //verification si l'utilisateur est connecté
        $user = $this->utilisateurs->getUser();
        if ($user) {
            $id_user = $user['user_id'];
            $user_random_id = $user['random_id'];
            $prenom = $user['fname'];
            $nom = $user['lname'];
            $email = $user['email'];

            //RGPD
            if ($rgpd or $rgpd_aff) {
                $array_update = [];
                if (!$user['rgpd'] and $rgpd) {
                    $array_update['rgpd'] = $rgpd;
                }
                if (!$user['rgpd_aff'] and $rgpd_aff) {
                    $array_update['rgpd_aff'] = $rgpd_aff;
                }

                if ($array_update) {
                    try {
                        $this->usersRepository->update($array_update, "user_id='" . $user['user_id'] . "' AND id_client='" . $_SESSION['id_client'] . "'");
                    } catch (\Eden_Error $e) {
                        return ["valid" => false, "message" => __("Erreur lors de la mise à jour de l'utilisateur.")];
                    }
                }

                $array_insert = [
                    'rgpd' => $rgpd,
                    'rgpd_date' => $rgpd_date,
                    'rgpd_notice' => $rgpd_notice,
                    'rgpd_aff' => $rgpd_aff,
                    'rgpd_aff_date' => $rgpd_aff_date,
                    'rgpd_aff_notice' => $rgpd_aff_notice,
                ];
                $this->utilisateursrgpd->insertHistory($id_user, $array_insert);
            }
        } elseif (!$sondage['anonymous']) {
            $prenom = filter_var($cleaned_post['prenom'], FILTER_SANITIZE_SPECIAL_CHARS);
            $email = filter_var($cleaned_post['email'], FILTER_VALIDATE_EMAIL);

            if (isset($cleaned_post['nom'])) {
                $nom = filter_var($cleaned_post['nom'], FILTER_SANITIZE_SPECIAL_CHARS);
            }

            //verification de l'utilisateur par email
            $verif_user = $this->utilisateurs->getUserByEmail($email);
            if ($verif_user) {
                $id_user = $verif_user['user_id'];
                $user_random_id = $verif_user['random_id'];
                $prenom = $verif_user['fname'];
                $nom = $verif_user['lname'];
                $email = $verif_user['email'];

                //RGPD
                if ($rgpd or $rgpd_aff) {
                    $array_update = [];
                    if (!$verif_user['rgpd'] and $rgpd) {
                        $array_update['rgpd'] = $rgpd;
                    }
                    if (!$verif_user['rgpd_aff'] and $rgpd_aff) {
                        $array_update['rgpd_aff'] = $rgpd_aff;
                    }

                    if ($array_update) {
                        try {
                            $this->usersRepository->update($array_update, "user_id='" . $verif_user['user_id'] . "' AND id_client='" . $_SESSION['id_client'] . "'");
                        } catch (\Eden_Error $e) {
                            return ["valid" => false, "message" => __("Erreur lors de la mise à jour de l'utilisateur.")];
                        }
                    }

                    $array_insert = [
                        'rgpd' => $rgpd,
                        'rgpd_date' => $rgpd_date,
                        'rgpd_notice' => $rgpd_notice,
                        'rgpd_aff' => $rgpd_aff,
                        'rgpd_aff_date' => $rgpd_aff_date,
                        'rgpd_aff_notice' => $rgpd_aff_notice,
                    ];
                    $this->utilisateursrgpd->insertHistory($id_user, $array_insert);
                }
            } else {
                if (!$nom) {
                    $nom = $prenom;
                }

                $array_insert = [
                    'fname' => $prenom,
                    'lname' => $nom,
                    'email' => $email,
                    'restricted' => 0,
                    'validated' => 1,
                    'newsletter' => 1,
                    'rgpd' => $rgpd,
                    'rgpd_aff' => $rgpd_aff,
                    'rgpd_notice' => $rgpd_notice,
                    'rgpd_aff_notice' => $rgpd_aff_notice,
                    'rgpd_date' => $rgpd_date,
                    'rgpd_aff_date' => $rgpd_aff_date,
                ];

                $insert = $this->utilisateurs->insert_user($array_insert);
                if ($insert['valid'] == false) {
                    LoggerService::log(Logger::ERROR, "Erreur insert_inscription : impossible d'enregistrer l'utilisateur.<br><br>" . $insert['message']);
                    return ["valid" => false, "message" => __("Une erreur est survenue, veuillez nous en excuser.")];
                } else {
                    //récupération de l'id utilisateur
                    $verif_user = $this->utilisateurs->getUserByEmail($email);
                    if ($verif_user) {
                        $id_user = $verif_user['user_id'];
                        $user_random_id = $verif_user['random_id'];
                    } else {
                        //erreur
                        LoggerService::log(Logger::ERROR, "Erreur insert_inscription : impossible de récupérer l'utilisateur après enregistrement.");
                        return ["valid" => false, "message" => __("Une erreur est survenue, veuillez nous en excuser.")];
                    }
                }
            }
        } else {
            $id_user = null;

            //user_random_id
            $valid_random_id = false;
            while (!$valid_random_id) {
                $user_random_id = createRandomID();
                $verif = $this->utilisateurs->getUserByRandomid($user_random_id);
                if (!$verif) {
                    $valid_random_id = true;
                }
            }
        }


        $page = 1;
        $subscribed = false;

        if (!$sondage['anonymous']) {
            $participant = $this->sondageParticipantRepository->getParticipantBySondageAndUser($id_sondage, $id_user);
            if ($participant) {
                $subscribed = true;
                $id_participant = $participant['id_participant'];
                $user_random_id = $participant['random_id'];
                $page = $participant['etat'];

                //déjà inscrit au sondage
                if ($participant['etat'] == 'termine') {
                    $_SESSION['success'] = true;

                    return ['valid' => true, 'user_random_id' => $participant['random_id'], 'page' => 1];
                } else {
                    $page = $participant['etat'];
                }
            }
        }

        if (!$subscribed) {
            //inscription comme participant
            $array_insert = [
                'id_client' => $_SESSION['id_client'],
                'id_sondage' => $id_sondage,
                'id_user' => $id_user ?? null,
                'etat' => 1,
                'ip' => \Tools::get_real_IP() ?? '::1',
                'disabled' => 0,
                'random_id' => $user_random_id,
                'date' => date('Y-m-d H:i:s'),
            ];
            try {
                $this->sondageParticipantRepository->insertOne($array_insert);
            } catch (\Eden_Error $e) {
                return ["valid" => false, "message" => __("Erreur lors de l'enregistrement du sondage.")];
            }

            $id_participant = $this->sondageParticipantRepository->getLastInsertedId();
            if (!$id_participant) {
                return ["valid" => false, "message" => __("Erreur lors de l'enregistrement du participant")];
            }
        }


        //ajout des renseignements
        $renseignements = $this->sondageRenseignementRepository->getRenseignementsBySondageAndType($id_sondage, 'pre');
        if ($renseignements) {
            foreach ($renseignements as $id => $_renseignement) {
                if (isset($cleaned_post[$_renseignement['nom']])) {
                    //filtre
                    ${'rens-' . $_renseignement['nom']} = $this->sondageService->filterByType($_renseignement['type'], $cleaned_post[$_renseignement['nom']]);

                    if (is_array(${'rens-' . $_renseignement['nom']})) {
                        $temp_rens = '';
                        foreach (${'rens-' . $_renseignement['nom']} as $value) {
                            $temp_rens .= $value . ',';
                        }
                        ${'rens-' . $_renseignement['nom']} = substr($temp_rens, 0, -1);
                    }

                    if (${'rens-' . $_renseignement['nom']} != false) {
                        //verification
                        $verif_reponse = $this->sondageParticipantDetailRepository->getRenseignementReponseBySondageAndParticipant($id_sondage, $_renseignement['id_rens'], $id_participant);
                        if ($verif_reponse) {
                            //mise à jour
                            $array_update = [
                                'value' => ${'rens-' . $_renseignement['nom']},
                                'date' => date('Y-m-d H:i:s'),
                            ];
                            try {
                                $this->sondageParticipantDetailRepository->update($array_update, "id_sondage='$id_sondage' AND id_rens='" . $_renseignement['id_rens'] . "' AND id_participant='$id_participant'");
                            } catch (\Eden_Error $e) {
                                return ["valid" => false, "message" => __("Erreur lors de la mise à jour des renseignements")];
                            }
                        } else {
                            //insertion
                            $array_insert = [
                                'id_sondage' => $id_sondage,
                                'id_participant' => $id_participant,
                                'id_rens' => $_renseignement['id_rens'],
                                'value' => ${'rens-' . $_renseignement['nom']},
                                'date' => date('Y-m-d H:i:s'),
                            ];
                            try {
                                $this->sondageParticipantDetailRepository->insertOne($array_insert);
                            } catch (\Eden_Error $e) {
                                return ["valid" => false, "message" => __("Erreur lors de l'enregistrement des renseignements")];
                            }
                        }
                    }
                }
            }
        }

        //ajout des renseignements personnalises
        $renseignements_personnalises = $this->sondageRenseignementPersonnaliseRepository->getDistinctRenseignementsPersonnalisesBySondage($id_sondage, 'pre');
        if ($renseignements_personnalises) {
            foreach ($renseignements_personnalises as $id => $_renseignement_personnalise) {
                if (isset($cleaned_post[$_renseignement_personnalise['nom']])) {
                    //filtre
                    ${'rens-' . $_renseignement_personnalise['nom']} = $this->sondageService->filterByType($_renseignement_personnalise['type'], $cleaned_post[$_renseignement_personnalise['nom']]);

                    if (is_array(${'rens-' . $_renseignement_personnalise['nom']})) {
                        ${'rens-' . $_renseignement_personnalise['nom']} = implode(',', ${'rens-' . $_renseignement_personnalise['nom']});
                    }

                    if (${'rens-' . $_renseignement_personnalise['nom']} != false) {
                        //verification
                        $verif_reponse = $this->sondageParticipantDetailPersonnaliseRepository->getRenseignementPersonnaliseReponseBySondageAndParticipant($id_sondage, $_renseignement_personnalise['id_rens_p'], $id_participant);
                        if ($verif_reponse) {
                            //mise à jour
                            $array_update = [
                                'value' => (string)${'rens-' . $_renseignement_personnalise['nom']},
                                'date' => date('Y-m-d H:i:s'),
                            ];

                            try {
                                $this->sondageParticipantDetailPersonnaliseRepository->update($array_update, "id_sondage='$id_sondage' AND id_rens_p='" . $_renseignement_personnalise['id_rens_p'] . "' AND id_participant='$id_participant'");
                            } catch (\Eden_Error $e) {
                                return ["valid" => false, "message" => __("Erreur lors de la mise à jour des renseignements personnalisés")];
                            }
                        } else {
                            //insertion
                            $array_insert = [
                                'id_sondage' => $id_sondage,
                                'id_participant' => $id_participant,
                                'id_rens_p' => $_renseignement_personnalise['id_rens_p'],
                                'value' => ${'rens-' . $_renseignement_personnalise['nom']},
                                'date' => date('Y-m-d H:i:s'),
                            ];

                            try {
                                $this->sondageParticipantDetailPersonnaliseRepository->insertOne($array_insert);
                            } catch (\Eden_Error $e) {
                                return ["valid" => false, "message" => __("Erreur lors de l'enregistrement des renseignements personnalisés")];
                            }
                        }
                    }
                }
            }
        }


        //inscriptions autorépondeur
        if (!$sondage['anonymous']) {
            $autorepondeurs = $this->autorepondeursBase->getAutorepondeursBySondage($id_sondage);
            if ($autorepondeurs) {
                $datas_inscription = [
                    'id_sondage' => $id_sondage,
                    'fname' => $prenom,
                    'lname' => $nom,
                    'email' => $email
                ];

                if ($rgpd or $rgpd_aff) {
                    $datas_inscription['rgpd'] = $rgpd;
                    $datas_inscription['rgpd_date'] = $rgpd_date;
                    $datas_inscription['rgpd_notice'] = $rgpd_notice;
                    $datas_inscription['rgpd_aff'] = $rgpd_aff;
                    $datas_inscription['rgpd_aff_date'] = $rgpd_aff_date;
                    $datas_inscription['rgpd_aff_notice'] = $rgpd_aff_notice;
                }

                if (isset($cleaned_post['tel'])) {
                    $phone = filter_var($cleaned_post['tel'], FILTER_SANITIZE_SPECIAL_CHARS);
                    $datas_inscription['tel'] = $phone;
                }

                foreach ($autorepondeurs as $autorepondeur) {
                    $inscription = $this->autorepondeursBase->main_subscribe_sondages($autorepondeur['type'], $datas_inscription);
                    /*if (!$inscription['valid'])
                        LoggerService::log(Logger::WARNING, '<div class="alert alert-danger">Inscription effecutée avec succès mais un autorépondeur a renvoyé une erreur :<br>'.$inscription['message'].'</div>');*/
                }
            }
        }

        return ["valid" => true, "user_random_id" => $user_random_id, "page" => $page];
    }
}
