<?php

namespace Learnybox\Forms\News;

use Learnybox\Entity\News\News;
use Learnybox\Entity\News\NewsContent;
use Learnybox\Entity\News\NewsTarget;
use Learnybox\Enums\NewsTypeEnum;
use Learnybox\Forms\AbstractForm;
use Learnybox\Services\News\NewsService;
use DateTime;

/**
 * Class LinksForm
 * @package Learnybox\Forms\Formation
 */
class NewsForm extends AbstractForm
{
    /**
     * @var NewsService
     */
    private $newsService;

    /**
     * LinksForm constructor.
     * @param NewsService $newsService
     */
    public function __construct(NewsService $newsService)
    {
        $this->newsService = $newsService;
    }

    /**
     * @param array $postArray
     * @return array
     */
    public function validatePostAdd(array $postArray): array
    {
        $errors = $this->checkContent($postArray);
        return $this->sendResponse($errors);
    }

    /**
     * @param array $postArray
     * @return array
     */
    public function validatePostUpdate(array $postArray): array
    {
        $errors = $this->checkContent($postArray);

        $idNews = filter_var($postArray['id_news'], FILTER_VALIDATE_INT);
        $news = $this->newsService->getRepository()->find($idNews);
        if ($news === null) {
            array_push($errors, __('Erreur fatale : la news n\'a pas été trouvée'));
        }

        return $this->sendResponse($errors);
    }

    private function checkContent(array $postArray): array
    {
        $errors = [];

        $hasTitle = false;
        $hasContent = false;
        $hasImage = false;

        if (isset($postArray['newscontents'])) {
            foreach ($postArray['newscontents'] as $newsContent) {
                if (filter_var($newsContent['title'], FILTER_SANITIZE_SPECIAL_CHARS)) {
                    $hasTitle = true;
                }
                if (filter_var($newsContent['content'], FILTER_SANITIZE_SPECIAL_CHARS)) {
                    $hasContent = true;
                }
                if (filter_var($newsContent['image'], FILTER_VALIDATE_URL)) {
                    $hasImage = true;
                }
            }
        }

        if (!$hasTitle && !$hasContent && !$hasImage) {
            $errors[] = __('Veuillez entrer au moins un titre, une image ou un texte.');
        }

        return $errors;
    }

    /**
     * @param array $cleanedPost
     * @return array
     */
    public function insert(array $cleanedPost): array
    {
        $params = $this->getParamsFromPost($cleanedPost);

        $news = new News();
        $this->getObjectHydrator()->hydrate($news, $params);
        $news->setDate(new DateTime());

        $news->setDatePublication(new DateTime('0000-00-00 00:00:00'));
        if ($news->getActive()) {
            $news->setDatePublication(new DateTime());
        }

        $news = $this->addContent($cleanedPost, $news);
        $news = $this->addTarget($cleanedPost, $news);

        try {
            $this->newsService->persistAndFlush($news);
        } catch (\Exception $e) {
            return $this->sendErrorResponse([__("Erreur lors de l'enregistrement de la news.")]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $cleanedPost
     * @return array
     */
    public function update(array $cleanedPost): array
    {
        $idNews = filter_var($cleanedPost['id_news'], FILTER_VALIDATE_INT);
        $news = $this->newsService->getRepository()->find($idNews);
        if (null === $news) {
            return $this->sendErrorResponse([__('Cette news n\'existe pas.')]);
        }

        $currentDatePublication = clone $news->getDatePublication();

        $params = $this->getParamsFromPost($cleanedPost);
        $this->getObjectHydrator()->hydrate($news, $params);

        if ($news->getActive() && $currentDatePublication->format('Y') <= 0) {
            $news->setDatePublication(new DateTime());
        } elseif (!$news->getActive()) {
            $news->setDatePublication(new DateTime('0000-00-00 00:00:00'));
        }

        $newsContents = $news->getNewsContents();
        foreach ($newsContents as $newsContent) {
            $news->removeNewsContent($newsContent);
        }

        $news = $this->addContent($cleanedPost, $news);

        $newsTargets = $news->getNewsTarget();
        foreach ($newsTargets as $newsTarget) {
            $news->removeNewsTarget($newsTarget);
        }

        $news = $this->addTarget($cleanedPost, $news);

        try {
            $this->newsService->persistAndFlush($news);
        } catch (\Exception $e) {
            return $this->sendErrorResponse([__("Erreur lors de l'enregistrement de la news.")]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $cleanedPost
     * @param News $news
     * @return News
     */
    private function addContent(array $cleanedPost, News $news): News
    {
        if (isset($cleanedPost['newscontents'])) {
            foreach ($cleanedPost['newscontents'] as $newsContentPost) {
                if (empty($newsContentPost['title']) && empty($newsContentPost['content']) && empty($newsContentPost['image'])) {
                    continue;
                }

                $lang = filter_var($newsContentPost['lang'], FILTER_SANITIZE_SPECIAL_CHARS);
                $title = filter_var($newsContentPost['title'], FILTER_SANITIZE_SPECIAL_CHARS);
                $content = filter_var($newsContentPost['content'], FILTER_UNSAFE_RAW);

                $image = '';
                if (isset($newsContentPost['image']) and $newsContentPost['image']) {
                    $image = filter_var($newsContentPost['image'], FILTER_VALIDATE_URL);
                }

                $link = '';
                if (isset($newsContentPost['link']) and $newsContentPost['link']) {
                    $link = filter_var($newsContentPost['link'], FILTER_VALIDATE_URL);
                }

                $newsContent = new NewsContent();
                $newsContent->setLang($lang);
                $newsContent->setTitle($title);
                $newsContent->setImage($image);
                $newsContent->setLink($link);
                $newsContent->setContent($content);

                $news->addNewsContent($newsContent);
            }
        }

        return $news;
    }

    public function addTarget(array $cleanedPost, News $news): News
    {
        if (isset($cleanedPost['target'])) {
            foreach ($cleanedPost['target'] as $newsTargetPost) {
                $newsTargetPost = json_decode($newsTargetPost, true);
                $newsTarget = new NewsTarget();
                $newsTarget->setType($newsTargetPost['type']);
                $newsTarget->setValue($newsTargetPost['value']);

                $news->addNewsTarget($newsTarget);
            }
        }
        return $news;
    }

    public function publish(int $idNews): array
    {
        $publishNews = $this->newsService->publish($idNews);
        if (!$publishNews['valid']) {
            return $this->sendErrorResponse([$publishNews['message']]);
        }

        return $this->sendSuccessResponse();
    }

    public function unpublish(int $idNews): array
    {
        $unpublishNews = $this->newsService->unpublish($idNews);
        if (!$unpublishNews['valid']) {
            return $this->sendErrorResponse([$unpublishNews['message']]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param int $idNews
     * @return array
     */
    public function delete(int $idNews): array
    {
        $news = $this->newsService->getRepository()->find($idNews);
        if (null === $news) {
            return $this->sendErrorResponse([__('Cette news n\'existe pas.')]);
        }

        try {
            $this->newsService->deleteAndFlush($news);
        } catch (\Exception $e) {
            return $this->sendErrorResponse([__('Une erreur est survenue lors de la suppression de la news.')]);
        }

        return $this->sendSuccessResponse();
    }

    /**
     * @param array $cleanedPost
     * @return array
     */
    public function getParamsFromPost(array $cleanedPost): array
    {
        $params = [
            'priorite' => 0,
            'type' => NewsTypeEnum::PUSH_LEFT,
            'date_fin' => new DateTime('0000-00-00 00:00:00'),
            'date_auto_publication' => new DateTime('0000-00-00 00:00:00'),
            'active' => 0,
        ];

        if (isset($cleanedPost['priorite'])) {
            $params['priorite'] = filter_var($cleanedPost['priorite'], FILTER_VALIDATE_INT);
        }

        if (isset($cleanedPost['type']) and $cleanedPost['type']) {
            $params['type'] = filter_var($cleanedPost['type'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        if (isset($cleanedPost['date_fin']) and $cleanedPost['date_fin']) {
            $dateFin = filter_var($cleanedPost['date_fin'], FILTER_SANITIZE_SPECIAL_CHARS);
            if ($dateFin and $dateFin != '0000-00-00 00:00:00') {
                $params['date_fin'] = new DateTime($dateFin);
            }
        }

        if (isset($cleanedPost['date_auto_publication']) and $cleanedPost['date_auto_publication']) {
            $dateAutoPublication = filter_var($cleanedPost['date_auto_publication'], FILTER_SANITIZE_SPECIAL_CHARS);
            if ($dateAutoPublication and $dateAutoPublication != '0000-00-00 00:00:00') {
                $params['date_auto_publication'] = new DateTime($dateAutoPublication);
            }
        }

        if (isset($cleanedPost['active']) and $cleanedPost['active']) {
            $params['active'] = 1;
        }

        return $params;
    }
}
