<?php

namespace Learnybox\Repository\Citation;

use Learnybox\Entity\Citation\Citation;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method Citation|null find($id, $lockMode = null, $lockVersion = null)
 * @method Citation|null findOneBy(array $criteria, array $orderBy = null)
 * @method Citation[]    findAll()
 * @method Citation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CitationRepository extends AbstractEntityRepository
{
}
