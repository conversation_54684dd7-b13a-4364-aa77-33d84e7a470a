<?php

namespace Learnybox\Repository\Conference\Signing;

use Learnybox\Entity\Conference\Signing\ConferenceSigningParticipant;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method ConferenceSigningParticipant|null find($id, $lockMode = null, $lockVersion = null)
 * @method ConferenceSigningParticipant|null findOneBy(array $criteria, array $orderBy = null)
 * @method ConferenceSigningParticipant[]    findAll()
 * @method ConferenceSigningParticipant[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ConferenceSigningParticipantRepository extends AbstractEntityRepository
{
    public function deleteByConference(int $idConference)
    {
        return $this->createQueryBuilder('csp')
            ->where('csp.conference = :id_conference')
            ->setParameter('id_conference', $idConference)
            ->delete()
            ->getQuery()
            ->execute();
    }
}
