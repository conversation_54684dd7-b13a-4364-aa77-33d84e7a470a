<?php

namespace Learnybox\Repository\Conference\Inscrit;

use Learnybox\Entity\Conference\Inscrit\ConferenceInscritDetail;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method ConferenceInscritDetail|null find($id, $lockMode = null, $lockVersion = null)
 * @method ConferenceInscritDetail|null findOneBy(array $criteria, array $orderBy = null)
 * @method ConferenceInscritDetail[]    findAll()
 * @method ConferenceInscritDetail[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ConferenceInscritDetailRepository extends AbstractEntityRepository
{
    public function getPostAnswersByConferenceAndUser(int $idConference, int $idUser, int $idClient = null): array
    {
        if (null === $idClient) {
            $idClient = $_SESSION['id_client'];
        }
        $qb = $this->createQueryBuilder('t')
            ->innerJoin('Learnybox\Entity\Conference\Renseignement\ConferenceRenseignement', 'r', 'WITH', 'r.idRens = t.idRens')
            ->where('t.conference = :conference')
            ->andWhere('t.client = :idClient')
            ->andWhere('t.conferenceUser = :idUser')
            ->andWhere('r.typeRens = :typeRens')
            ->setParameter('conference', $idConference)
            ->setParameter('idClient', $idClient)
            ->setParameter('idUser', $idUser)
            ->setParameter('typeRens', 'post');

        return $qb->getQuery()->getResult();
    }
}
