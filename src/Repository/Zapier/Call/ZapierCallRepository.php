<?php

namespace Learnybox\Repository\Zapier\Call;

use Learnybox\Entity\Zapier\Call\ZapierCall;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method ZapierCall|null find($id, $lockMode = null, $lockVersion = null)
 * @method ZapierCall|null findOneBy(array $criteria, array $orderBy = null)
 * @method ZapierCall[]    findAll()
 * @method ZapierCall[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ZapierCallRepository extends AbstractEntityRepository
{
}
