<?php

namespace Learnybox\Repository\Evaluation\Action;

use Learnybox\Entity\Evaluation\Action\EvaluationActionData;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method EvaluationActionData|null find($id, $lockMode = null, $lockVersion = null)
 * @method EvaluationActionData|null findOneBy(array $criteria, array $orderBy = null)
 * @method EvaluationActionData[]    findAll()
 * @method EvaluationActionData[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EvaluationActionDataRepository extends AbstractEntityRepository
{
}
