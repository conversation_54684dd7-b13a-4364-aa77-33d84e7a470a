<?php

namespace Learnybox\Repository\Builder\Modele;

use Learnybox\Entity\Builder\Modele\BuilderModeleCol;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method BuilderModeleCol|null find($id, $lockMode = null, $lockVersion = null)
 * @method BuilderModeleCol|null findOneBy(array $criteria, array $orderBy = null)
 * @method BuilderModeleCol[]    findAll()
 * @method BuilderModeleCol[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class BuilderModeleColRepository extends AbstractEntityRepository
{
}
