<?php

namespace Learnybox\Repository\Application;

use Learnybox\Entity\Application\Application;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method Application|null find($id, $lockMode = null, $lockVersion = null)
 * @method Application|null findOneBy(array $criteria, array $orderBy = null)
 * @method Application[]    findAll()
 * @method Application[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ApplicationRepository extends AbstractEntityRepository
{
}
