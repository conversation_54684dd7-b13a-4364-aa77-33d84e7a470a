<?php

namespace Learnybox\Repository\Role;

use Learnybox\Entity\Role\Role;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method Role|null find($id, $lockMode = null, $lockVersion = null)
 * @method Role|null findOneBy(array $criteria, array $orderBy = null)
 * @method Role[]    findAll()
 * @method Role[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RoleRepository extends AbstractEntityRepository
{
}
