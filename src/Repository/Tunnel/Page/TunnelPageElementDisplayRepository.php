<?php

namespace Learnybox\Repository\Tunnel\Page;

use Learnybox\Entity\Tunnel\Page\TunnelPageElementDisplay;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method TunnelPageElementDisplay|null find($id, $lockMode = null, $lockVersion = null)
 * @method TunnelPageElementDisplay|null findOneBy(array $criteria, array $orderBy = null)
 * @method TunnelPageElementDisplay[]    findAll()
 * @method TunnelPageElementDisplay[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TunnelPageElementDisplayRepository extends AbstractEntityRepository
{
}
