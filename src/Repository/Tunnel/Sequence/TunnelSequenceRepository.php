<?php

namespace Learnybox\Repository\Tunnel\Sequence;

use Learnybox\Entity\Tunnel\Sequence\TunnelSequence;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method TunnelSequence|null find($id, $lockMode = null, $lockVersion = null)
 * @method TunnelSequence|null findOneBy(array $criteria, array $orderBy = null)
 * @method TunnelSequence[]    findAll()
 * @method TunnelSequence[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TunnelSequenceRepository extends AbstractEntityRepository
{
}
