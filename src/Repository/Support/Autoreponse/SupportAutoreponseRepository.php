<?php

namespace Learnybox\Repository\Support\Autoreponse;

use Learnybox\Entity\Support\Autoreponse\SupportAutoreponse;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method SupportAutoreponse|null find($id, $lockMode = null, $lockVersion = null)
 * @method SupportAutoreponse|null findOneBy(array $criteria, array $orderBy = null)
 * @method SupportAutoreponse[]    findAll()
 * @method SupportAutoreponse[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SupportAutoreponseRepository extends AbstractEntityRepository
{
}
