<?php

namespace Learnybox\Repository\Quiz\Vue;

use Learnybox\Entity\Quiz\Vue\QuizVue;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method QuizVue|null find($id, $lockMode = null, $lockVersion = null)
 * @method QuizVue|null findOneBy(array $criteria, array $orderBy = null)
 * @method QuizVue[]    findAll()
 * @method QuizVue[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class QuizVueRepository extends AbstractEntityRepository
{
}
