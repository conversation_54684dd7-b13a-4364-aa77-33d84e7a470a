<?php

namespace Learnybox\Repository\Formation\Autorepondeur;

use Learnybox\Entity\Formation\Autorepondeur\FormationAutorepondeur;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method FormationAutorepondeur|null find($id, $lockMode = null, $lockVersion = null)
 * @method FormationAutorepondeur|null findOneBy(array $criteria, array $orderBy = null)
 * @method FormationAutorepondeur[]    findAll()
 * @method FormationAutorepondeur[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FormationAutorepondeurRepository extends AbstractEntityRepository
{
}
