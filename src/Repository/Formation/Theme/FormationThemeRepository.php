<?php

namespace Learnybox\Repository\Formation\Theme;

use Learnybox\Entity\Formation\Theme\FormationTheme;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method FormationTheme|null find($id, $lockMode = null, $lockVersion = null)
 * @method FormationTheme|null findOneBy(array $criteria, array $orderBy = null)
 * @method FormationTheme[]    findAll()
 * @method FormationTheme[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FormationThemeRepository extends AbstractEntityRepository
{
}
