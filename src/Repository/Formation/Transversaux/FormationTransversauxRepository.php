<?php

namespace Learnybox\Repository\Formation\Transversaux;

use Learnybox\Entity\Formation\Transversaux\FormationTransversaux;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method FormationTransversaux|null find($id, $lockMode = null, $lockVersion = null)
 * @method FormationTransversaux|null findOneBy(array $criteria, array $orderBy = null)
 * @method FormationTransversaux[]    findAll()
 * @method FormationTransversaux[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FormationTransversauxRepository extends AbstractEntityRepository
{
}
