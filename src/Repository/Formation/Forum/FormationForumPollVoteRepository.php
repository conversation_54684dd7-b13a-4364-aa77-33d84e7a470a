<?php

namespace Learnybox\Repository\Formation\Forum;

use Learnybox\Entity\Formation\Forum\FormationForumPollVote;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method FormationForumPollVote|null find($id, $lockMode = null, $lockVersion = null)
 * @method FormationForumPollVote|null findOneBy(array $criteria, array $orderBy = null)
 * @method FormationForumPollVote[]    findAll()
 * @method FormationForumPollVote[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FormationForumPollVoteRepository extends AbstractEntityRepository
{
    public function hasUserVoted(int $pollId, int $userId): bool
    {
        $result = $this->createQueryBuilder('v')
            ->select('COUNT(v.id)')
            ->join('v.option', 'o')
            ->where('o.poll = :pollId')
            ->andWhere('v.user = :userId')
            ->setParameter('pollId', $pollId)
            ->setParameter('userId', $userId)
            ->getQuery()
            ->getSingleScalarResult();

        return $result > 0;
    }

    public function getUserVotesByPoll(int $pollId, int $userId): array
    {
        return $this->createQueryBuilder('v')
            ->join('v.option', 'o')
            ->where('o.poll = :pollId')
            ->andWhere('v.user = :userId')
            ->setParameter('pollId', $pollId)
            ->setParameter('userId', $userId)
            ->getQuery()
            ->getResult();
    }
}
