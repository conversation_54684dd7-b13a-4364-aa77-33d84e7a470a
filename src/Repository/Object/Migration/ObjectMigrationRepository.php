<?php

namespace Learnybox\Repository\Object\Migration;

use Learnybox\Entity\Object\Migration\ObjectMigration;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method ObjectMigration|null find($id, $lockMode = null, $lockVersion = null)
 * @method ObjectMigration|null findOneBy(array $criteria, array $orderBy = null)
 * @method ObjectMigration[]    findAll()
 * @method ObjectMigration[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ObjectMigrationRepository extends AbstractEntityRepository
{
    public function findOneByStatusAndUserId(string $status, int $userId)
    {
        $qb = $this->createQueryBuilder('m')
            ->where('m.status = :status')
            ->andWhere('m.client = :clientId')
            ->andWhere('m.user = :userId')
            ->setParameter('status', $status)
            ->setParameter('clientId', $_SESSION['id_client'])
            ->setParameter('userId', $userId);

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function findByStatus(string $status)
    {
        $qb = $this->createQueryBuilder('m')
            ->where('m.status = :status')
            ->andWhere('m.client = :clientId')
            ->setParameter('status', $status)
            ->setParameter('clientId', $_SESSION['id_client']);

        return $qb->getQuery()->execute();
    }

    public function findExceptStatus(array $status)
    {
        $qb = $this->createQueryBuilder('m')
            ->where('m.status NOT IN (:status)')
            ->andWhere('m.client = :clientId')
            ->setParameters([
                'status' => $status,
                'clientId' => $_SESSION['id_client']
            ]);

        return $qb->getQuery()->execute();
    }

    public function findOneById(int $idMigration): ?ObjectMigration
    {
        return $this->createQueryBuilder('m')
            ->where('m.id = :idMigration')
            ->setParameter('idMigration', $idMigration)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function findByStatusAndClient(string $status, ?int $clientId = null): array
    {
        return $this->createQueryBuilder('m')
            ->where('m.client = :client')
            ->andWhere('m.status = :status')
            ->setParameters([
                'client' => $clientId ?? $_SESSION['id_client'],
                'status' => $status
            ])
            ->getQuery()
            ->getArrayResult()
        ;
    }
}
