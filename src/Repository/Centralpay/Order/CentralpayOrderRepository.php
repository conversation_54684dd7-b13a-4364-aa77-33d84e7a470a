<?php

namespace Learnybox\Repository\Centralpay\Order;

use Learnybox\Entity\Centralpay\Order\CentralpayOrder;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method CentralpayOrder|null find($id, $lockMode = null, $lockVersion = null)
 * @method CentralpayOrder|null findOneBy(array $criteria, array $orderBy = null)
 * @method CentralpayOrder[]    findAll()
 * @method CentralpayOrder[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CentralpayOrderRepository extends AbstractEntityRepository
{
}
