<?php

namespace Learnybox\Repository\News;

use Learnybox\Entity\News\NewsTarget;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method NewsTarget|null find($id, $lockMode = null, $lockVersion = null)
 * @method NewsTarget|null findOneBy(array $criteria, array $orderBy = null)
 * @method NewsTarget[]    findAll()
 * @method NewsTarget[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class NewsTargetRepository extends AbstractEntityRepository
{
}
