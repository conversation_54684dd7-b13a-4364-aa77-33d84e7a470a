<?php

namespace Learnybox\Repository\Boxidea\Status;

use Learnybox\Entity\Boxidea\Status\BoxideaStatus;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method BoxideaStatus|null find($id, $lockMode = null, $lockVersion = null)
 * @method BoxideaStatus|null findOneBy(array $criteria, array $orderBy = null)
 * @method BoxideaStatus[]    findAll()
 * @method BoxideaStatus[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class BoxideaStatusRepository extends AbstractEntityRepository
{
}
