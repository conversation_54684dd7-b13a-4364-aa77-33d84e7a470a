<?php

namespace Learnybox\Repository\Client\Cancel;

use Learnybox\Entity\Client\Cancel\ClientCancel;
use Learnybox\Entity\Client\Client;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method ClientCancel|null find($id, $lockMode = null, $lockVersion = null)
 * @method ClientCancel|null findOneBy(array $criteria, array $orderBy = null)
 * @method ClientCancel[]    findAll()
 * @method ClientCancel[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ClientCancelRepository extends AbstractEntityRepository
{
    public function getAbonnementsCancelByDate(\DateTime $dateStart, \DateTime $dateEnd)
    {
        return $this->createQueryBuilder('cc')
            ->select('
            cc.idAbonnementCancel id_abonnement_cancel,
            cc.idClient id_client,
            cc.nomClient nom_client,
            cc.uniqid,
            cc.nom,
            cc.prenom,
            cc.email,
            cc.cancelReason
            cancel_reason,
            cc.reason,
            cc.abonnement,
            cc.etat,
            cc.dateDemande date_demande,
            cc.dateValidation date_validation,
            cc.automatic,
            c.dateDesactivation date_desactivation
            ')
            ->join(Client::class, 'c', 'WITH', 'cc.idClient = c.idClient')
            ->where('cc.dateDemande >= :dateStart')
            ->andWhere('cc.dateDemande <= :dateEnd')
            ->setParameters([
                'dateStart' => $dateStart,
                'dateEnd' => $dateEnd
            ])
            ->getQuery()
            ->getArrayResult();
    }
}
