<?php

namespace Learnybox\Repository\Mangopay\Order;

use Learnybox\Entity\Mangopay\Order\MangopayOrder;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method MangopayOrder|null find($id, $lockMode = null, $lockVersion = null)
 * @method MangopayOrder|null findOneBy(array $criteria, array $orderBy = null)
 * @method MangopayOrder[]    findAll()
 * @method MangopayOrder[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MangopayOrderRepository extends AbstractEntityRepository
{
    public function getLastOrdersInErrorByCustom(string $email, string $custom)
    {
        $date10min = date('Y-m-d H:i:s', strtotime('-10 minutes'));

        $qb = $this->createQueryBuilder('o')
            ->andWhere("o.client = :client")
            ->andWhere('o.status = :status')
            ->andWhere('o.date >= :date')
            ->setParameter('date', $date10min)
            ->setParameter('status', 'FAILED')
            ->setParameter('client', $_SESSION['id_client']);

        if ($email) {
            $qb->andWhere('o.email = :email')
                ->setParameter('email', $email);
        } else {
            $qb->andWhere('o.custom = :custom')
                ->setParameter('custom', $custom);
        }

        return $qb->getQuery()
            ->getResult();
    }

    public function findTransactionsNotCreated(string $date = '2024-01-01')
    {
        $query = "SELECT o.id, o.id_order, o.amount, o.id_client, c.uniqid, IF(op.mangopay_id_paiement IS NOT NULL, op.mangopay_id_paiement , o.mangopay_id_paiement) mangopay_id_paiement, o.date
                FROM mp_orders o
                JOIN lb_clients c ON o.id_client=c.id_client
                LEFT JOIN lb_transactions t ON o.id_order=t.id_trans
                LEFT JOIN mp_orders_payments op ON o.id_order=op.id_order
                WHERE t.id_trans IS NULL AND o.date > ? AND o.status = ? AND (o.mangopay_id_paiement <> 0 OR op.mangopay_id_paiement IS NOT NULL);
                ORDER BY date DESC
                ";

        return  $this->_em->getConnection()->executeQuery($query, [$date, 'SUCCEEDED'])->fetchAllAssociative();
    }

    private function getTransactionWithNoSubscriptionQuery(): string
    {
        return "
            SELECT o.id, c.id_client, c.uniqid, o.id_order, o.amount, o.mangopay_id_paiement, t.date, o.checkout, o.id_account
            FROM mp_orders o
            INNER JOIN lb_clients c ON o.id_client = c.id_client
            RIGHT JOIN lb_transactions t ON t.id_trans = o.id_order
            WHERE o.checkout LIKE '%\"payment\":\"subscription\"%' AND o.id_abonnement=0 AND c.active=1 AND t.valid=1
        ";
    }

    public function findTransactionsWithNoSubscription(): array
    {
        $query = $this->getTransactionWithNoSubscriptionQuery();
        return $this->_em->getConnection()->executeQuery($query)->fetchAllAssociative();
    }

    public function findTransactionWithNoSubscription(string $transactionId): array|false
    {
        $query = $this->getTransactionWithNoSubscriptionQuery();
        $query .= " AND o.id_order = :transactionId AND o.date >= :date";

        return $this->_em
            ->getConnection()
            ->executeQuery($query, [
                'transactionId' => $transactionId,
                'date' => '2024-01-01 00:00:00'
            ])
            ->fetchAssociative();
    }

    public function findTransactionsWithNoSubscriptionByAccountId(int $accountId): array
    {
        $query = "
            SELECT o.id, o.id_order, u.user_id, u.lname, u.fname, t.descriptor, o.amount, o.date, o.mangopay_id_paiement, o.currency, o.checkout
            FROM mp_orders o
            RIGHT JOIN lb_transactions t ON t.id_trans = o.id_order
            LEFT JOIN lb_users u ON u.user_id = t.user_id
            WHERE o.checkout LIKE '%\"payment\":\"subscription\"%'
            AND o.id_abonnement=0
            AND t.valid=1
            AND o.id_account = :accountId
            AND o.date >= '2024-01-01 00:00:00'
        ";

        return $this->_em->getConnection()->executeQuery($query, ['accountId' => $accountId])->fetchAllAssociative();
    }

    public function getOrderById(int $id): ?MangopayOrder
    {
        return $this->createQueryBuilder('o')
            ->andWhere('o.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
