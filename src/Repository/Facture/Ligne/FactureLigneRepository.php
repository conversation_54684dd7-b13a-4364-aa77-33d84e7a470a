<?php

namespace Learnybox\Repository\Facture\Ligne;

use Learnybox\Entity\Facture\Ligne\FactureLigne;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method FactureLigne|null find($id, $lockMode = null, $lockVersion = null)
 * @method FactureLigne|null findOneBy(array $criteria, array $orderBy = null)
 * @method FactureLigne[]    findAll()
 * @method FactureLigne[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FactureLigneRepository extends AbstractEntityRepository
{
}
