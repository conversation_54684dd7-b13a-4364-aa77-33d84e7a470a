<?php

namespace Learnybox\Repository\Status;

use Learnybox\Entity\Status\Status;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method Status|null find($id, $lockMode = null, $lockVersion = null)
 * @method Status|null findOneBy(array $criteria, array $orderBy = null)
 * @method Status[]    findAll()
 * @method Status[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class StatusRepository extends AbstractEntityRepository
{
}
