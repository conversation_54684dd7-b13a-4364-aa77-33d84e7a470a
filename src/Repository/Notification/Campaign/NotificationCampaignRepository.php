<?php

namespace Learnybox\Repository\Notification\Campaign;

use Learnybox\Entity\Notification\Campaign\NotificationCampaign;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method NotificationCampaign|null find($id, $lockMode = null, $lockVersion = null)
 * @method NotificationCampaign|null findOneBy(array $criteria, array $orderBy = null)
 * @method NotificationCampaign[]    findAll()
 * @method NotificationCampaign[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class NotificationCampaignRepository extends AbstractEntityRepository
{
}
