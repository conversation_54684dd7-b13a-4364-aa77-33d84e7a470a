<?php

namespace Learnybox\Repository\Popup\Autorepondeur;

use Learnybox\Entity\Popup\Autorepondeur\PopupAutorepondeur;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method PopupAutorepondeur|null find($id, $lockMode = null, $lockVersion = null)
 * @method PopupAutorepondeur|null findOneBy(array $criteria, array $orderBy = null)
 * @method PopupAutorepondeur[]    findAll()
 * @method PopupAutorepondeur[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PopupAutorepondeurRepository extends AbstractEntityRepository
{
}
