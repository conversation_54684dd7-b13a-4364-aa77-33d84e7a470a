<?php

namespace Learnybox\Repository\Mail\Col;

use Learnybox\Entity\Mail\Col\MailCol;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method MailCol|null find($id, $lockMode = null, $lockVersion = null)
 * @method MailCol|null findOneBy(array $criteria, array $orderBy = null)
 * @method MailCol[]    findAll()
 * @method MailCol[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MailColRepository extends AbstractEntityRepository
{
    public function getColsWithMobileContent(int $idMail): array
    {
        return $this->createQueryBuilder('mc')
            ->andWhere('mc.mail = :mail')
            ->setParameter('mail', $idMail)
            ->andWhere("mc.contenuMobile != ''")
            ->getQuery()
            ->getArrayResult();
    }
}
