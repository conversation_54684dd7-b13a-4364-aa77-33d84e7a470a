<?php

namespace Learnybox\Repository\Mail\Sms;

use Learnybox\Entity\Mail\Sms\MailSms;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method MailSms|null find($id, $lockMode = null, $lockVersion = null)
 * @method MailSms|null findOneBy(array $criteria, array $orderBy = null)
 * @method MailSms[]    findAll()
 * @method MailSms[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MailSmsRepository extends AbstractEntityRepository
{
    public function countBySubjectAndMessageLike(string $search, ?int $clientId = null): int
    {
        return $this->createQueryBuilder('ms')
            ->select('count(ms.idSms)')
            ->where('ms.client = :clientId')
            ->andWhere('ms.sujet LIKE :search OR ms.message LIKE :search')
            ->setParameters([
                'clientId' => $clientId ?? $_SESSION['id_client'],
                'search' => '%' . $search . '%'
            ])
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function findBySubjectAndMessageLike(string $search, ?int $clientId = null): array
    {
        return $this->createQueryBuilder('ms')
            ->where('ms.client = :clientId')
            ->andWhere('ms.sujet LIKE :search OR ms.message LIKE :search')
            ->setParameters([
                'clientId' => $clientId ?? $_SESSION['id_client'],
                'search' => '%' . $search . '%'
            ])
            ->getQuery()
            ->getResult();
    }
}
