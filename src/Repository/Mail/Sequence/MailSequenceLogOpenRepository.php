<?php

namespace Learnybox\Repository\Mail\Sequence;

use Learnybox\Entity\Mail\Sequence\MailSequenceLogOpen;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method MailSequenceLogOpen|null find($id, $lockMode = null, $lockVersion = null)
 * @method MailSequenceLogOpen|null findOneBy(array $criteria, array $orderBy = null)
 * @method MailSequenceLogOpen[]    findAll()
 * @method MailSequenceLogOpen[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MailSequenceLogOpenRepository extends AbstractEntityRepository
{
    /**
     * @param int $idStep
     * @param int $idClient
     * @return int
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function countDistinctUserIdByIdStepAndIdClient(int $idStep, int $idClient = null): int
    {
        if (null === $idClient) {
            $idClient = $_SESSION['id_client'];
        }

        $qb = $this->createQueryBuilder('t');
        $query = $qb
            ->select($qb->expr()->countDistinct('t.user'))
            ->andWhere('t.step = :idStep')
            ->andWhere('t.client = :idClient')
            ->setParameter('idClient', $idClient)
            ->setParameter('idStep', $idStep)
            ->getQuery()
            ->getSingleScalarResult();

        return $query;
    }
}
