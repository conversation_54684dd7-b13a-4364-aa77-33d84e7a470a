<?php

namespace Learnybox\Repository\Log\Action;

use Learnybox\Entity\Log\Action\LogAction;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method LogAction|null find($id, $lockMode = null, $lockVersion = null)
 * @method LogAction|null findOneBy(array $criteria, array $orderBy = null)
 * @method LogAction[]    findAll()
 * @method LogAction[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LogActionRepository extends AbstractEntityRepository
{
}
