<?php

namespace Learnybox\Repository\Affiliation\Invoice;

use Learnybox\Entity\Affiliation\Invoice\AffiliationInvoice;
use Learnybox\Repository\AbstractEntityRepository;

/**
 * @method AffiliationInvoice|null find($id, $lockMode = null, $lockVersion = null)
 * @method AffiliationInvoice|null findOneBy(array $criteria, array $orderBy = null)
 * @method AffiliationInvoice[]    findAll()
 * @method AffiliationInvoice[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AffiliationInvoiceRepository extends AbstractEntityRepository
{
    /**
     * @param \DateTime|null $uploadedAtStart
     * @param \DateTime|null $uploadedAtEnd
     * @return array|null
     */
    public function findAllByDateAndStatus(?\DateTime $uploadedAtStart = null, ?\DateTime $uploadedAtEnd = null, ?array $statuses = null): ?array
    {
        $qb = $this->createQueryBuilder('ai');

        if (null !== $uploadedAtStart) {
            $qb->andWhere('ai.uploadedAt >= :uploadedAtStart')
                ->setParameter('uploadedAtStart', $uploadedAtStart->format('Y-m-d H:i:s'));
        }
        if (null !== $uploadedAtEnd) {
            $qb->andWhere('ai.uploadedAt <= :uploadedAtEnd')
                ->setParameter('uploadedAtEnd', $uploadedAtEnd->format('Y-m-d H:i:s'));
        }
        if (!empty($statuses)) {
            $qb->andWhere('ai.status IN (:statuses)')
                ->setParameter('statuses', $statuses);
        }
        $qb->orderBy('ai.uploadedAt', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * @param int $idAffilie
     * @param array|null $status
     * @return mixed
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function getTotalAmountByStatus(int $idAffilie, array $status = null)
    {
        if ($status === null) {
            $status = [
                AffiliationInvoice::STATUS_SENT_TO_PAYMENT,
                AffiliationInvoice::STATUS_SENT,
                AffiliationInvoice::STATUS_VALIDATED
            ];
        }
        $qb = $this->createQueryBuilder('ai');
        $qb->select('SUM(ai.preTaxAmount) as sum');
        $qb->where("ai.affiliationAffilie = :idAffilie")
            ->setParameter('idAffilie', $idAffilie);
        $statusFilters = [];
        foreach ($status as $key => $s) {
            $statusFilters[] = "ai.status = '$s'";
        }
        $qb->andWhere(implode(' OR ', $statusFilters));

        return $qb->getQuery()->getOneOrNullResult();
    }
}
