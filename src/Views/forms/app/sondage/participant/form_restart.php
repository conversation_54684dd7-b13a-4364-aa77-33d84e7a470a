<?php

/** @var array $sondage */
/** @var string $random_id */
/** @var array $participant */

use Learnybox\Services\RightsService;

$isEditor = RightsService::isEditor();

echo __('<PERSON><PERSON><PERSON><PERSON>, vous avez déjà répondu à ce sondage.');

if ($isEditor or (!$sondage['nb_times'] or ($sondage['nb_times'] >= 1 and $participant['nb_times'] < $sondage['nb_times']))) { ?>
    <br>
    <form method="post" action="" style="margin:0; padding:0;">
        [[CSRF]]
        <input type="hidden" name="form_action" value="front_sondage_restart_sondage">
        <input type="hidden" name="random_id" value="<?php echo $random_id; ?>">
        <input type="hidden" name="user_random_id" value="<?php echo $participant['random_id']; ?>">
        <button type="submit" class="btn btn-warning"><i class="fa fa-refresh"></i> <?php echo __('Recommencer le sondage'); ?></button>
    </form>
    <?php
} ?>
