<?php

use Learnybox\Helpers\RouterHelper;

$fatal_error = '';
if (isset($id_sondage) and $id_sondage) {
    $sondage = eden()->EverWebinar_Sondages()->getSondage($id_sondage, $id_webinaire);
    if (!$sondage) {
        $fatal_error = __("Erreur : ce sondage n'existe pas");
    } else {
        if (!isset($_POST['question'])) {
            $question = $sondage['question'];
            $type = $sondage['type'];
            $etat = $sondage['etat'];
            $afficher_resultats = $sondage['afficher_resultats'];
            $heure_publication = explode(':', $sondage['heure_publication']);
            $heure_publication_fin = explode(':', $sondage['heure_publication_fin']);

            //récupération des réponses
            $reponses_sondage = eden()->EverWebinar_Sondages()->getSondageDetails($id_sondage);
            $reponses_input = eden()->EverWebinar_Sondages()->generate_reponses_input($reponses_sondage);
        } else {
            $reponses_input = eden()->EverWebinar_Sondages()->generate_reponses_input(false, $_POST['reponses'], $_POST['resultats']);
            $question = filter_input(INPUT_POST, 'question', FILTER_SANITIZE_SPECIAL_CHARS);
            if (isset($cleaned_post['type'])) {
                $type = $cleaned_post['type'];
            }
            if (isset($cleaned_post['etat'])) {
                $etat = $cleaned_post['etat'];
            }
            if (isset($cleaned_post['afficher_resultats'])) {
                $afficher_resultats = $cleaned_post['afficher_resultats'];
            }
        }
    }
} else {
    if (!isset($_POST['question'])) {
        $question = '';
        $type = '';
        $etat = 'active';
        $afficher_resultats = '';
        $reponses_sondage = false;
        $reponses_input = eden()->EverWebinar_Sondages()->generate_reponses_input();
    } else {
        $reponses_input = eden()->EverWebinar_Sondages()->generate_reponses_input(false, $_POST['reponses'], $_POST['resultats']);
        $question = filter_input(INPUT_POST, 'question', FILTER_SANITIZE_SPECIAL_CHARS);
        if (isset($cleaned_post['type'])) {
            $type = $cleaned_post['type'];
        }
        if (isset($cleaned_post['etat'])) {
            $etat = $cleaned_post['etat'];
        }
        if (isset($cleaned_post['afficher_resultats'])) {
            $afficher_resultats = $cleaned_post['afficher_resultats'];
        }
    }
}

if ($fatal_error) {
    echo '
	    <div class="alert alert-danger">
			<button type="button" class="close" data-dismiss="alert">×</button>
			<h4 class="alert-heading">' . $fatal_error . '</h4>
			<a href="' . RouterHelper::generate('app_webinaire_sondages', ['webinaireId' => $id_webinaire]) . '"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a>
		</div>';

    return;
}
?>

<div class="top-page-title">
    <h3><?php echo (isset($id_sondage) and $id_sondage) ? __('Modification d\'un sondage') : __('Création d\'un sondage'); ?></h3>
</div>

<div class="panel panel-default">
    <div class="panel-body">
        <form class="form" method="post" action="">

            <div class="form-group">
                <label class="control-label" for="question"><?php echo __('Question'); ?> *</label>
                <span class="help-block"><?php echo __('Indiquez ici le titre de votre question, par exemple « Quelle est votre profession ? ».'); ?></span>
                <div class="controls">
                    <input class="form-control input-xxlarge focused" name="question" id="question" type="text" value="<?php if (isset($question)) {
                        echo $question;
                                                                                                                       } ?>"/>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="type"><?php echo __('Type'); ?> *</label>
                <span
                        class="help-block"><strong><?php echo __('Réponse unique'); ?></strong> : <?php echo __('vos visiteurs ne pourront choisir qu’une seule réponse parmi celles proposées'); ?><br><strong><?php echo __('Réponse multiple'); ?></strong> : <?php echo __('vos visiteurs pourront cocher plusieurs réponses parmi celles proposées'); ?></span>
                <div class="controls">
                    <div class="checkbox">
                        <label for="type1">
                            <input type="radio" name="type" id="type1" value="radio" <?php if (isset($type) and 'radio' == $type) {
                                echo 'checked="checked"';
                                                                                     } ?> /> <?php echo __('Réponse unique'); ?>
                        </label>
                    </div>
                    <div class="checkbox">
                        <label for="type2">
                            <input type="radio" name="type" id="type2" value="multiple" <?php if (isset($type) and 'multiple' == $type) {
                                echo 'checked="checked"';
                                                                                        } ?> /> <?php echo __('Réponses multiples'); ?>
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label"><?php echo __('Heure du début d\'affichage du sondage'); ?> *</label>
                <span class="help-block"><?php echo __('Durée à partir du début du webinaire à partir de laquelle le sondage est affiché.'); ?></span>
                <div class="form-inline">
                    <div class="form-group">
                        <div class="input-group" style="padding-right: 10px">
                            <input class="form-control input-small" type="number" name="heure_publication_heures" min="0" max="23" size="2" value="<?php if (isset($heure_publication[0])) {
                                echo (int)$heure_publication[0];
                                                                                                                                                   } ?>">
                            <span class="input-group-addon"><?php echo __('heures'); ?></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="input-group" style="padding-right: 10px">
                            <input class="form-control input-small" type="number" name="heure_publication_minutes" min="0" max="59" size="2" value="<?php if (isset($heure_publication[1])) {
                                echo (int)$heure_publication[1];
                                                                                                                                                    } ?>">
                            <span class="input-group-addon"><?php echo __('minutes'); ?></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="input-group" style="padding-right: 10px">
                            <input class="form-control input-small" type="number" name="heure_publication_secondes" min="0" max="59" size="2" value="<?php if (isset($heure_publication[2])) {
                                echo (int)$heure_publication[2];
                                                                                                                                                     } ?>">
                            <span class="input-group-addon"><?php echo __('secondes'); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label"><?php echo __('Heure de fin d\'affichage du sondage'); ?></label>
                <span class="help-block"><?php echo __('Laissez les valeurs à 0 si vous voulez laisser le sondage jusqu\'à la fin du webinaire.'); ?></span>
                <div class="form-inline">
                    <div class="form-group">
                        <div class="input-group" style="padding-right: 10px">
                            <input class="form-control input-small" type="number" name="heure_publication_fin_heures" min="0" max="23" size="2" value="<?php if (isset($heure_publication_fin[0])) {
                                echo (int)$heure_publication_fin[0];
                                                                                                                                                       } ?>">
                            <span class="input-group-addon"><?php echo __('heures'); ?></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="input-group" style="padding-right: 10px">
                            <input class="form-control input-small" type="number" name="heure_publication_fin_minutes" min="0" max="59" size="2" value="<?php if (isset($heure_publication_fin[1])) {
                                echo (int)$heure_publication_fin[1];
                                                                                                                                                        } ?>">
                            <span class="input-group-addon"><?php echo __('minutes'); ?></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="input-group" style="padding-right: 10px">
                            <input class="form-control input-small" type="number" name="heure_publication_fin_secondes" min="0" max="59" size="2" value="<?php if (isset($heure_publication_fin[2])) {
                                echo (int)$heure_publication_fin[2];
                                                                                                                                                         } ?>">
                            <span class="input-group-addon"><?php echo __('secondes'); ?></span>
                        </div>
                    </div>
                </div>
            </div>


            <div class="form-group">
                <label class="control-label" for="reponses"><?php echo __('Réponses'); ?> *</label>
                <span
                        class="help-block"><?php echo __('Indiquez ici les différentes réponses possibles, par exemple : « infopreneur », « conférencier », etc.') . '<br>' . __('Si vous avez besoin d\'ajouter plus de réponses, cliquez simplement sur « Ajouter une réponse ».'); ?></span>

                <div id="reponses">
                    <?php echo $reponses_input; ?>
                </div>
                <a href="#" class="btn btn-secondary" onclick="AddQuestionSondage()"><?php echo __('Ajouter une réponse'); ?></a>
            </div>

            <div class="form-group">
                <div class="switch-left">
                    <div class="switch">
                        <input type="checkbox" name="afficher_resultats" id="afficher_resultats" class="ios-toggle ios-toggle-round ios-toggle-switch" value="oui"
                               data-no-uniform="true" <?php if (isset($afficher_resultats) and 'oui' == $afficher_resultats) {
                                    echo 'checked="checked"';
                                                      } ?>>
                        <label for="afficher_resultats"></label>
                    </div>
                </div>
                <div class="switch-right">
                    <div class="switch-infos">
                        <label class="control-label" for="afficher_resultats"><?php echo __('Afficher les résultats'); ?></label>
                        <span class="help-block" style="margin-bottom: 0px"><?php echo __('Vous pouvez choisir d\'afficher ou non les résultats à la fin du sondage.'); ?></span>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="switch-left">
                    <div class="switch">
                        <input type="checkbox" name="etat" id="etat" class="ios-toggle ios-toggle-round ios-toggle-switch" value="active" data-no-uniform="true" <?php if (isset($etat) and 'active' == $etat) {
                            echo 'checked="checked"';
                                                                                                                                                                 } ?>>
                        <label for="etat"></label>
                    </div>
                </div>
                <div class="switch-right">
                    <div class="switch-infos">
                        <label class="control-label" for="etat"><?php echo __('Activer le sondage'); ?></label>
                        <span class="help-block" style="margin-bottom: 0px"><?php echo __('Activez cette option pour que le sondage soit affiché pendant le webinaire.'); ?></span>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                [[CSRF]]
                <input type="hidden" name="form_action" id="form_action" value="<?php echo $action; ?>"/>
                <input type="hidden" name="id_webinaire" value="<?php echo $id_webinaire; ?>"/>
                <input type="hidden" name="redirect" value="<?php echo RouterHelper::generate('app_webinaire_sondages', ['webinaireId' => $id_webinaire]); ?>"/>
                <?php if (isset($id_sondage) and $id_sondage) {
                    echo '<input type="hidden" name="id_sondage" value="' . $id_sondage . '" />';
                } ?>
                <button type="submit" class="btn btn-primary btn-lg"><?php echo __('Valider'); ?></button>
                <a class="btn btn-default btn-lg" href="<?php echo RouterHelper::generate('app_webinaire_sondages', ['webinaireId' => $id_webinaire]); ?>"><?php echo __('Annuler'); ?></a>
            </div>

        </form>
    </div>
</div>
