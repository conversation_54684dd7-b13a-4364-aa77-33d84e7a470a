<?php

use Learnybox\Helpers\RouterHelper;

$vars = ['day', 'hour', 'minute', 'heure', 'date_fixe', 'conditions'];
foreach ($vars as $var) {
    ${$var} = '';
    if (isset($cleaned_post[$var])) {
        ${$var} = $cleaned_post[$var];
    } elseif (isset($_SESSION['lbar_stepsmstutoriel'][$var])) {
        ${$var} = $_SESSION['lbar_stepsmstutoriel'][$var];
    }
}

$jours = [];
if (isset($cleaned_post['jours'])) {
    $jours = filter_var($cleaned_post['jours'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
} elseif (isset($_SESSION['lbar_stepsmstutoriel']['jours'])) {
    $jours = $_SESSION['lbar_stepsmstutoriel']['jours'];
}

if (!isset($heure)) {
    $heure = 'immediat';
}
if (isset($date_fixe) and '0000-00-00 00:00:00' == $date_fixe) {
    $date_fixe = '';
}

$active = 'date_relative';
if (isset($date_fixe) and $date_fixe and '0000-00-00 00:00:00' != $date_fixe) {
    $active = 'date_fixe';
}

if (!isset($conditions)) {
    $conditions = [];
}

$selectHeure = eden()->Lbar_Sequence()->generateSelectHeure($heure);
$selectJours = eden()->Lbar_Sequence()->generateSelectJours($jours);

$timezone = '';
if (isset($cleaned_post['timezone'])) {
    $timezone = filter_var($cleaned_post['timezone'], FILTER_SANITIZE_SPECIAL_CHARS);
} elseif (isset($_SESSION['lbar_stepsmstutoriel']['timezone'])) {
    $timezone = $_SESSION['lbar_stepsmstutoriel']['timezone'];
}

if (!isset($timezone) or !$timezone) {
    $timezone = SERVER_TIMEZONE;
    $getDefaultTimezone = eden()->Reglages()->appGetParametreByName('default_timezone');
    if ($getDefaultTimezone) {
        $timezone = $getDefaultTimezone['value'];
    }
}
$selectTimeZones = Tools::generateSelectTimeZone($timezone);
?>
<div class="tutoriel-content">
    <div class="container">
        <form class="form" method="post" action="" id="form-timeline">

            <div class="btn-group" role="group" style="margin-bottom: 20px;">
                <button type="button" class="btn <?php echo('date_relative' == $active ? 'btn-primary' : 'btn-default'); ?>" id="btn-daterelative"
                        onclick="$('#bloc-daterelative').css('display', 'block'); $('#bloc-datefixe').css('display', 'none'); $('#type').val('date_relative'); $('#btn-daterelative').addClass('btn-primary').removeClass('btn-default'); $('#btn-datefixe').addClass('btn-default').removeClass('btn-primary');"><?php echo __('Programmer à une date relative'); ?></button>
                <button type="button" class="btn <?php echo('date_fixe' == $active ? 'btn-primary' : 'btn-default'); ?>" id="btn-datefixe"
                        onclick="$('#bloc-daterelative').css('display', 'none'); $('#bloc-datefixe').css('display', 'block'); $('#type').val('date_fixe'); $('#btn-daterelative').addClass('btn-default').removeClass('btn-primary'); $('#btn-datefixe').addClass('btn-primary').removeClass('btn-default');"><?php echo __('à une date fixe'); ?></button>
            </div>

            <div class="card">
                <div id="bloc-daterelative" style="display:<?php if ('date_relative' == $active) {
                    echo 'block';
                                                           } else {
                                                               echo 'none';
                                                           } ?>">

                    <div class="form-group">
                        <label class="control-label"><?php echo __('Délai'); ?> <span class="gray"><small><?php echo __('(après la dernière étape)'); ?></small></span></label>
                        <span class="help-block"><?php echo __('Cette option vous permet de définir au bout de combien de temps après la dernière étape de votre séquence, ce SMS doit être envoyé (par exemple : 1 jour après l\'étape précédente).'); ?></span>

                        <?php
                        //vérification si il n'y a pas déjà un sms dans la séquence
                        $nb_steps = eden()->Lbar_Sequence()->getCountSequenceSteps($sequence['id_sequence']);
                        if (!$nb_steps) {
                            echo '
                            <div class="checkbox">
                                <label for="send_after_subscribe">
                                    <input type="radio" name="delay" value="send_after_subscribe" id="send_after_subscribe" ' . ((!$day and !$hour and !$minute) ? 'checked' : '') . ' onchange="$(\'.delay_options\').hide();">
                                    ' . __('Envoyer juste après l\'inscription') . '
                                </label>
                            </div>
                            <div class="checkbox">
                                <label for="send_later">
                                    <input type="radio" name="delay" value="send_later" id="send_later" ' . (($day or $hour or $minute) ? 'checked' : '') . ' onchange="$(\'.delay_options\').show();">
                                    ' . __('Envoyer après l\'inscription avec un délai') . '
                                </label>
                                <div class="delay_options" style="margin-left:30px; display: ' . (($day or $hour or $minute) ? 'block' : 'none') . ';">';
                        }
                        ?>
                        <div class="form-inline">
                            <div class="input-group">
                                <div class="form-group">
                                    <input class="form-control input-small" type="number" name="day" min="0" max="999" size="2" value="<?php if (isset($day) and $day) {
                                        echo $day;
                                                                                                                                       } else {
                                                                                                                                           echo '1';
                                                                                                                                       } ?>">
                                    <span class="input-group-addon"><?php echo __('jours'); ?></span>
                                </div>
                            </div>
                            <div class="input-group">
                                <div class="form-group">
                                    <input class="form-control input-small" type="number" name="hour" min="0" max="23" size="2" value="<?php if (isset($hour) and $hour) {
                                        echo $hour;
                                                                                                                                       } else {
                                                                                                                                           echo '0';
                                                                                                                                       } ?>">
                                    <span class="input-group-addon"><?php echo __('heures'); ?></span>
                                </div>
                            </div>
                            <div class="input-group">
                                <div class="form-group">
                                    <input class="form-control input-small" type="number" name="minute" min="0" max="59" size="2" value="<?php if (isset($minute) and $minute) {
                                        echo $minute;
                                                                                                                                         } else {
                                                                                                                                             echo '0';
                                                                                                                                         } ?>">
                                    <span class="input-group-addon"><?php echo __('minutes'); ?></span>
                                </div>
                            </div>
                            <div class="form-group">
                                &nbsp;&nbsp;<?php echo !$nb_steps ? __('après l\'inscription') : __('après la dernière étape'); ?>
                            </div>
                        </div>
                        <?php
                        if (!$nb_steps) {
                            echo '
                                </div>
                            </div>';
                        }
                        ?>
                    </div>


                    <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">

                        <div class="panel panel-default">
                            <div class="panel-heading" role="tab">
                                <a role="button" data-toggle="collapse" href="#collapseActions" aria-expanded="true">
                                    <strong><?php echo __('Personnaliser la date d\'envoi'); ?></strong><br>
                                    <span><?php echo __('Vous permet de définir un horaire d\'envoi, et de sélectionner des jours spécifiques d\'envoi.'); ?></span>
                                </a>
                            </div>

                            <div id="collapseActions" class="panel-collapse collapse" role="tabpanel">
                                <div class="panel-body">

                                    <div class="form-group">
                                        <label class="control-label"><?php echo __('Horaire') . ' <span class="h5">(' . __('Facultatif') . ')</span>'; ?></label>
                                        <span class="help-block"><?php echo __('Relatif à la date d\'inscription : si un contact s\'inscrit aujourd\'hui à 16h et que vous programmez ce SMS pour être envoyé 1 jour après l\'inscription, il sera envoyé demain à 16h. Si vous souhaitez l\'envoyer à un horaire précis, sélectionnez-le ci-dessous.'); ?></span>
                                        <div class="controls">
                                            <select name="heure" data-rel="select2">
                                                <?php echo $selectHeure; ?>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label"><?php echo __('Jours de la semaine') . ' <span class="h5">(' . __('Facultatif') . ')</span>'; ?></label>
                                        <span class="help-block"><?php echo __('Si vous souhaitez exécuter cette étape uniquement certains jours de la semaine, sélectionnez-les ci-dessous.'); ?></span>
                                        <div class="controls">
                                            <select name="jours[]" multiple data-rel="select2" style="min-width: 300px" data-placeholder="<?php echo __('Sélectionnez un jour'); ?>">
                                                <?php echo $selectJours; ?>
                                            </select>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                    </div>

                </div>


                <div id="bloc-datefixe" style="display: <?php if ('date_fixe' == $active) {
                    echo 'block';
                                                        } else {
                                                            echo 'none';
                                                        } ?>;">

                    <div class="form-group">
                        <label class="control-label"><?php echo __('Indiquez une date ci-dessous'); ?></label>
                        <span class="help-block"><?php echo __('Ce SMS sera envoyé à la date indiquée ci-dessous. Toute personne qui s\'inscrit après cette date ne recevra pas ce SMS mais continuera la séquence normalement.'); ?></span>
                        <div class="input-group">
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                            <input class="input-large form-control datepicker" type="text" name="date_fixe" value="<?php if (isset($date_fixe) and $date_fixe and '0000-00-00 00:00:00' != $date_fixe) {
                                echo $date_fixe;
                                                                                                                   } ?>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label" for="timezone"><?php echo __('Fuseau horaire'); ?> *</label>
                        <div class="controls">
                            <select id="timezone" name="timezone" data-rel="select2">
                                <?php echo $selectTimeZones; ?>
                            </select>
                        </div>
                    </div>

                </div>


                <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">

                    <div class="panel panel-default">
                        <div class="panel-heading" role="tab">
                            <a role="button" data-toggle="collapse" href="#collapseConditions" aria-expanded="true">
                                <strong><?php echo __('Ajouter des conditions'); ?></strong><br>
                                <span><?php echo __('Permet d\'exécuter cette étape sous certaines conditions'); ?></span>
                            </a>
                        </div>

                        <div id="collapseConditions" class="panel-collapse collapse" role="tabpanel">
                            <div class="panel-body">

                                <div class="form-group">
                                    <label class="control-label"><?php echo __('Conditions') . ' <span class="h5">(' . __('Facultatif') . ')</span>'; ?></label>
                                    <span class="help-block"><?php echo __('Cette option vous permet d\'envoyer ce SMS sous certaines conditions. Par exemple : si le contact est inscrit dans une autre séquence, si le contact posséde un certain tag, si le contact à ouvert l\'envoi précédent, etc.'); ?></span>
                                    <div class="controls">
                                        <div id="conditions">
                                            <?php
                                            if ($conditions) {
                                                $i = 0;
                                                foreach ($conditions as $condition) {
                                                    if (!isset($condition['id_condition'])) {
                                                        $condition['id_condition'] = $i;
                                                    }
                                                    echo eden()->Lbar_Conditions()->generateSelectConditions($condition['id_condition'], $condition, $i);
                                                    ++$i;
                                                }
                                            }
                                            ?>
                                        </div>
                                        <a class="btn btn-secondary" onclick="AddCondition(); return false;"><i class="fa fa-plus"></i> <?php echo __('Ajouter une condition'); ?></a>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            [[CSRF]]
            <input type="hidden" name="form_action" value="<?php echo $action; ?>"/>
            <input type="hidden" name="step" value="<?php echo $step; ?>"/>
            <input type="hidden" name="id_sequence" value="<?php echo $sequence['id_sequence']; ?>"/>
            <input type="hidden" name="type" id="type" value="<?php if ('date_fixe' == $active) {
                echo 'date_fixe';
                                                              } else {
                                                                  echo 'date_relative';
                                                              } ?>"/>
            <input type="hidden" name="redirect" value="<?php echo RouterHelper::generate('app_autorepondeur_stepsms_tutoriel_step', [
                'sequenceId' => $sequence['id_sequence'],
                'step' => 'step3'
                                                              ]); ?>"/>

        </form>
    </div>
</div>
