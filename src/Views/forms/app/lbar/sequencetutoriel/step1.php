<?php
$nomsequence = '';
if (isset($cleaned_post['nomsequence'])) {
    $nomsequence = $cleaned_post['nomsequence'];
} elseif (isset($_SESSION['lbar_sequencetutoriel']['nomsequence'])) {
    $nomsequence = $_SESSION['lbar_sequencetutoriel']['nomsequence'];
}
?>
<div class="tutoriel-content">
    <div class="container">
        <form class="form" method="post" action="" id="form-timeline">

            <div class="btn-group" role="group" style="margin-bottom: 20px;">
                <button type="button" class="btn btn-primary" id="btn-new"
                        onclick="$('#new').css('display', 'block'); $('#import').css('display', 'none'); $('#btn-new').addClass('btn-primary').removeClass('btn-default'); $('#btn-import').addClass('btn-default').removeClass('btn-primary');"><?php echo __('Créer une nouvelle séquence'); ?></button>
                <button type="button" class="btn btn-default" id="btn-import"
                        onclick="$('#new').css('display', 'none'); $('#import').css('display', 'block'); $('#btn-new').addClass('btn-default').removeClass('btn-primary'); $('#btn-import').addClass('btn-primary').removeClass('btn-default');"><?php echo __('Importer une séquence pré-écrite'); ?></button>
            </div>

            <div id="new" style="display:block">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="form-group">
                                <label for="nomsequence"><?php echo __('Nom de votre séquence'); ?></label>
                                <div class="form-inline-button">
                                    <input class="form-control input focused" name="nomsequence" id="nomsequence" type="text" value="<?php echo(isset($nomsequence) ? $nomsequence : ''); ?>" required
                                           placeholder="<?php echo __('Ma première séquence'); ?>">
                                    <a class="btn btn-default btn-small" onclick="$('#nomsequence').val('Ma première séquence'); $('#form-timeline').submit();" style="cursor:pointer"><?php echo __('Je ne sais pas encore'); ?></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="import" style="display: none;">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <p><?php echo LB . ' ' . __('vous propose plusieurs séquences pré-écrites, comportant un certain nombre d\'emails qui seront importés dans une séquence sur votre compte LearnyMail.') . '<br>' . __('Vous pourrez ensuite éditer chaque email, en ajouter d\'autres, les supprimer, etc.'); ?></p>
                            <a class="btn btn-primary" href="<?php echo Tools::makeLink('app', 'autorepondeur', 'sequence_tutoriel_import'); ?>"><i class="fa fa-upload"></i> <?php echo __('Importer une séquence pré-écrite'); ?></a>
                        </div>
                    </div>
                </div>
            </div>

            [[CSRF]]
            <input type="hidden" name="form_action" value="<?php echo $action; ?>"/>
            <input type="hidden" name="step" value="<?php echo $step; ?>"/>
            <input type="hidden" name="redirect" value="<?php echo Tools::makeLink('app', 'autorepondeur', 'sequence_tutoriel/step2'); ?>"/>

        </form>
    </div>
</div>