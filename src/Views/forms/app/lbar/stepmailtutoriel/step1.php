<?php

use Learnybox\Helpers\RouterHelper;

$id_mail = 0;
if (isset($cleaned_post['id_mail'])) {
    $id_mail = $cleaned_post['id_mail'];
} elseif (isset($_SESSION['lbar_stepmailtutoriel']['id_mail'])) {
    $id_mail = $_SESSION['lbar_stepmailtutoriel']['id_mail'];
}

if ($cleaned_post) {
    foreach ($cleaned_post as $key => $value) {
        if (!is_array($value)) {
            ${$key} = $value;
        }
    }
}
?>
<div class="tutoriel-content">
    <div class="container">
        <form class="form" method="post" action="" id="form-timeline">

            <div class="btn-group" role="group" style="margin-bottom: 20px;">
                <button type="button" class="btn <?php echo (((!isset($type) or 'create' == $type) and !$id_mail) ? 'btn-primary' : 'btn-default'); ?>" id="btn-create" onclick="$('#create').css('display', 'block'); $('#select').css('display', 'none'); $('#type').val('create'); $('#btn-create').addClass('btn-primary').removeClass('btn-default'); $('#btn-select').addClass('btn-default').removeClass('btn-primary');"><?php echo __('Créer un nouvel email'); ?></button>
                <button type="button" class="btn <?php echo ((isset($id_mail) and $id_mail) ? 'btn-primary' : 'btn-default'); ?>" id="btn-select" onclick="$('#create').css('display', 'none'); $('#select').css('display', 'block'); $('#type').val('select'); $('#btn-create').addClass('btn-default').removeClass('btn-primary'); $('#btn-select').addClass('btn-primary').removeClass('btn-default');"><?php echo __('Sélectionner un email'); ?></button>
            </div>

            <div id="create" style="display:<?php if (!isset($type) or 'create' == $type) {
                echo 'block';
                                            } else {
                                                echo 'none';
                                            } ?>">

                <div class="form-group" style="margin-bottom: 10px">
                    <label class="control-label" for="theme"><?php echo __('Thème'); ?></label>
                </div>

                <div class="row">

                    <div class="col-md-3">
                        <div class="theme card">
                            <div class="theme-content">
                                <img class="pic" src="<?php echo \Learnybox\Helpers\Assets::getImageUrl('mails/empty_mail.png') ?>" alt="<?php echo __('Mail vide'); ?>" />
                            </div>
                            <div class="theme-footer">
                                <button class="btn btn-primary btn-lg btn-theme" data-theme="0" data-submit="true"><?php echo __('Sélectionner'); ?></button>
                            </div>
                        </div>
                    </div>

                    <?php
                    $themes = eden()->Lbar_Mails_Themes()->getAllThemes();
                    if ($themes) {
                        $i = 1;
                        foreach ($themes as $_theme) {
                            if (0 == $i % 4) {
                                echo '<div class="row">';
                            }
                            ++$i;

                            echo '
                            <div class="col-md-3">
                                <div class="theme card ' . ((isset($theme) and $theme == $_theme['name']) ? ' active' : '') . '">
                                    <div class="theme-content">';

                            echo '
                                <a href="' . \Learnybox\Helpers\Assets::getImageUrl('mails/' . $_theme['name'] . '/preview_large.png') . '" title="" data-gallery="gallery">
                                    <img class="pic" src="' . \Learnybox\Helpers\Assets::getImageUrl('mails/' . $_theme['name'] . '/preview.png') . '" alt="' . $_theme['name'] . '" />
                                    <div id="overlay"></div>
                                    <button class="btn btn-success btn-lg"><i class="fa fa-eye"></i> ' . __('Aperçu') . '</button>
                                </a>';

                            echo '
                                    </div>
                                    <div class="theme-footer">
                                        <button class="btn btn-primary btn-lg btn-theme ' . ((isset($theme) and $theme == $_theme['name']) ? 'btn-warning' : '') . '" data-theme="' . $_theme['name'] . '" data-submit="true">' . __('Sélectionner') . '</button>
                                    </div>
                                </div>
                            </div>';

                            if (0 == $i % 4) {
                                echo '</div>';
                            }
                        }

                        if (0 != $i % 4) {
                            echo '</div>';
                        }
                    }
                    ?>

                <?php
                $themes_mails = eden()->Lbar_Mails_ThemesMails()->generateSelectThemeMail();
                if ($themes_mails) {
                    echo $themes_mails['output'];
                }
                ?>
            </div>

            <div id="select" style="display:<?php if (isset($id_mail) and $id_mail) {
                    echo 'block';
                                            } else {
                                                echo 'none';
                                            } ?>">
                <div class="row">
                    <div class="col-md-7">
                        <div class="card">
                            <p class="text-info" style="margin-bottom: 30px;"><i class="fa fa-info-circle"></i> <?php echo __('Cette fonctionnalité vous permet d\'envoyer un email déjà existant. Pour cela, sélectionnez un email dans la liste déroulante ci-dessous, et cochez la case pour le dupliquer si besoin.'); ?></p>

                            <?php
                            $selectEmail = eden()->Lbar_Mails()->generateSelectMail($id_mail);
                            if (!$selectEmail) {
                                echo '<div class="alert alert-info">' . __('Aucun email trouvé.') . '</div>';
                            } else {
                                ?>
                            <div class="form-group">
                                <label class="control-label"><?php echo __('Sélectionnez un email'); ?> *</label>
                                <div class="controls">
                                    <select name="id_mail" data-rel="select2" onchange="AfficherApercuMail(this.value);" style="width: 100%">
                                        <?php echo $selectEmail; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label" for="email_copy"><?php echo __('Créer une copie de cet email'); ?> *</label>
                                <div class="checkbox">
                                    <label for="email_copy">
                                        <input type="checkbox" name="email_copy" id="email_copy"> <?php echo __('Cochez cette case pour créer une copie de cet email. Dans le cas contraire, si l\'email sélectionné est inclus dans d\'autres séquences, il sera modifié pour toutes les séquences.'); ?>
                                    </label>
                                </div>
                            </div>
                                <?php
                            } ?>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div id="ApercuMail"></div>
                    </div>
                </div>
            </div>

                [[CSRF]]
                <input type="hidden" name="form_action" value="<?php echo $action; ?>"/>
                <input type="hidden" name="step" value="<?php echo $step; ?>"/>
                <input type="hidden" name="id_sequence" value="<?php echo $sequence['id_sequence']; ?>"/>
                <input type="hidden" name="type" id="type" value="<?php echo (isset($id_mail) and $id_mail) ? 'select' : 'create'; ?>"/>
                <input type="hidden" name="theme" id="theme" value=""/>
                <input type="hidden" name="redirect" value="<?php echo RouterHelper::generate('app_autorepondeur_stepmail_tutoriel_step', [
                    'sequenceId' => $sequence['id_sequence'],
                    'step' => 'step2'
                ]); ?>"/>

        </form>
    </div>
</div>

<?php
if (isset($themes_mails['forms'])) {
    echo $themes_mails['forms'];
}
?>
