<?php

use Learnybox\Helpers\RouterHelper;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Repositories\Evaluation\EvaluationReponseRepository;

$container = ContainerBuilderService::getInstance();

/** @var int $idCalcul */
/** @var string $nom */
/** @var array $questions */
/** @var array $evaluation */
/** @var array $details */
?>
<div class="top-page-title">
    <h3><?php echo($idCalcul ? __('Modification d\'un calcul') : __('Création d\'un calcul')); ?></h3>
</div>

<div class="panel panel-default">
    <div class="panel-body">
        <form class="form" method="post" action="" id="addCalculForm">
            <div class="form-group">
                <label class="control-label" for="nom"><?php echo __('Nom'); ?> *</label>
                <div class="controls">
                    <input class="form-control input-xxlarge" name="nom" id="nom" type="text" value="<?php echo(isset($nom) ? $nom : ''); ?>"/>
                </div>
            </div>

            <div class="dataTables_wrapper">
                <div class="content">
                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th width="60%"><?php echo __('Question'); ?></th>
                            <th width="20%"><?php echo __('Bonne réponse'); ?></th>
                            <th width="20%"><?php echo __('Points'); ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($questions as $question) { ?>
                            <tr>
                                <td><strong><?php echo $question['question']; ?></strong></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <?php
                            if ($question['type'] == 'echelle') {
                                $datas_echelle = json_decode($question['datas'], true);
                                $min_echelle = $datas_echelle['min_echelle'];
                                $max_echelle = $datas_echelle['max_echelle'];
                            }

                            if ($question['type'] == 'cliczone') {
                                $reponses = [];
                                $reponses[] = [
                                    'reponse' => __('Clic sur la bonne zone'),
                                    'correct' => true,
                                    'id_reponse' => 0
                                ];
                            } else {
                                $reponses = $container->get(EvaluationReponseRepository::class)->getReponsesByQuestion($evaluation['id_evaluation'], $question['id_question']);
                            }
                            ?>
                            <?php foreach ($reponses as $reponse) { ?>
                                <tr>
                                    <td style="padding-left:40px"><?php echo $reponse['reponse']; ?></td>
                                    <td>
                                        <?php if ($reponse['correct']) { ?>
                                            <span class="text-success"><i class="fa fa-check"></i> <?php echo __('Oui'); ?></span>
                                        <?php } else { ?>
                                            <span class="text-danger"><i class="fa fa-times"></i> <?php echo __('Non'); ?></span>
                                        <?php } ?>
                                    </td>
                                    <td>
                                        <?php if ($question['type'] == 'echelle') { ?>
                                            <div class="checkbox">
                                                <label for="calcul_<?php echo $question['id_question']; ?>_<?php echo $reponse['id_reponse']; ?>">
                                                    <input type="checkbox" id="calcul_<?php echo $question['id_question']; ?>_<?php echo $reponse['id_reponse']; ?>"
                                                           name="calcul[<?php echo $question['id_question']; ?>][<?php echo $reponse['id_reponse']; ?>][active]"
                                                           value="<?php echo $max_echelle; ?>" <?php echo(isset($details[$question['id_question']][$reponse['id_reponse']]) ? 'checked' : ''); ?>>
                                                    <em><?php echo $min_echelle; ?> à <?php echo n__('%d point', '%d points', $max_echelle, $max_echelle); ?></em>
                                                </label>
                                                <input type="hidden" name="calcul[<?php echo $question['id_question']; ?>][<?php echo $reponse['id_reponse']; ?>][exist]" value="oui">
                                            </div>
                                        <?php } else { ?>
                                            <div class="input-group" style="margin-bottom: 0;">
                                                <input type="number" class="form-control input-small" name="calcul[<?php echo $question['id_question']; ?>][<?php echo $reponse['id_reponse']; ?>]"
                                                       value="<?php echo(isset($details[$question['id_question']][$reponse['id_reponse']]) ? $details[$question['id_question']][$reponse['id_reponse']] : 0); ?>">
                                                <span class="input-group-addon"><?php echo __('point(s)'); ?></span>
                                            </div>
                                        <?php } ?>
                                    </td>
                                </tr>
                            <?php } ?>
                        <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="form-actions">
                [[CSRF]]
                <input type="hidden" name="random_id" value="<?php echo $evaluation['random_id']; ?>"/>
                <?php echo((isset($idCalcul) and $idCalcul) ? '<input type="hidden" name="id_calcul" value="' . $idCalcul . '" />' : ''); ?>
                <input type="hidden" name="form_action" value="<?php echo((isset($idCalcul) and $idCalcul) ? 'evaluation_calcul_edit' : 'evaluation_calcul_add'); ?>"/>
                <input type="hidden" name="redirect" value="<?php echo RouterHelper::generate('app_evaluation_calculs', ['slug' => $evaluation['random_id']]); ?>"/>
                <button type="submit" name="submit" class="btn btn-primary"><?php echo __('Valider'); ?></button>
                <a class="btn btn-default btn-lg" href="<?php echo RouterHelper::generate('app_evaluation_calculs', ['slug' => $evaluation['random_id']]); ?>"><?php echo __('Annuler'); ?></a>
            </div>
        </form>
    </div>
</div>
