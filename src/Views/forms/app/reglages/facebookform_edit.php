<div class="top-page-title">
    <h3><?php echo __('Modification d\'un formulaire de capture Facebook'); ?></h3>
</div>
<?php if (!$accessToken) : ?>
    <div class="alert alert-info">
        <?php echo __('Vous devez integrer Facebook Business pour créer des formulaires de capture Facebook.'); ?><br/>
        <a class="btn btn-primary" href="<?php echo Tools::makeLink('app', 'integration', 'facebook_leads'); ?>">
            <i class="fa fa-facebook"></i> <?php echo __('Intégrer Facebook Business'); ?>
        </a>
    </div>
    <?php return; ?>
<?php endif; ?>

<form class="form" method="post" action="" id="FormPage">
    <div class="form-group" style="padding-top:10px">
        <label class="control-label" for="sequences"><?php echo __('Inscrire les leads dans les séquences'); ?></label>
        <div class="controls">
            <select name="sequences[]" id="sequences" data-rel="select2" multiple data-placeholder="<?php echo __('Sélectionnez une séquence'); ?>" style="width: 50%">
                <?php echo $selectSequences; ?>
            </select>
        </div>
    </div>

    <div class="form-actions">
        [[CSRF]]
        <input type="hidden" name="form_action" value="<?php echo $action; ?>" />
        <input type="hidden" name="facebookFormId" value="<?php echo $facebookFormId; ?>" />
        <button type="submit" name="submit" class="btn btn-primary"><?php echo __('Valider'); ?></button>
        <a class="btn btn-default btn-lg" href="<?php echo Tools::makeLink('app', 'integration', 'facebook_leads'); ?>"><?php echo __('Annuler'); ?></a>
    </div>
</form>