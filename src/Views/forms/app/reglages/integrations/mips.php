<?php
$merchantId = $entityId = $operatorId = $operatorPassword = '';
$username = $password = $salt = $cipherKey = '';

if (isset($_POST['mips_merchant_id'])) {
    $merchantId = $cleaned_post['mips_merchant_id'];
    $entityId = $cleaned_post['mips_entity_id'];
    $operatorId = $cleaned_post['mips_operator_id'];
    $operatorPassword = $cleaned_post['mips_operator_password'];
    $username = $cleaned_post['mips_username'];
    $password = $cleaned_post['mips_password'];
    $salt = $cleaned_post['mips_salt'];
    $cipherKey = $cleaned_post['mips_cipher_key'];
} else {
    if (isset($reglages['mips_merchant_id'])) {
        $merchantId = $reglages['mips_merchant_id'];
    }
    if (isset($reglages['mips_entity_id'])) {
        $entityId = $reglages['mips_entity_id'];
    }
    if (isset($reglages['mips_operator_id'])) {
        $operatorId = $reglages['mips_operator_id'];
    }
    if (isset($reglages['mips_operator_password'])) {
        $operatorPassword = $reglages['mips_operator_password'];
    }
    if (isset($reglages['mips_username'])) {
        $username = $reglages['mips_username'];
    }
    if (isset($reglages['mips_password'])) {
        $password = $reglages['mips_password'];
    }
    if (isset($reglages['mips_salt'])) {
        $salt = $reglages['mips_salt'];
    }
    if (isset($reglages['mips_cipher_key'])) {
        $cipherKey = $reglages['mips_cipher_key'];
    }
}
?>

<div class="form-group" style="margin-top: 10px">
    <label class="control-label" for="mips_merchant_id"><?php echo __('Id marchand'); ?> *</label>
    <div class="controls">
        <input class="form-control input" name="mips_merchant_id" id="mips_merchant_id" type="text" value="<?php echo $merchantId; ?>" required="required">
    </div>
</div>

<div class="form-group">
    <label class="control-label" for="mips_entity_id"><?php echo __('Id entité'); ?> *</label>
    <div class="controls">
        <input class="form-control input" name="mips_entity_id" id="mips_entity_id" type="text" value="<?php echo $entityId; ?>" required="required">
    </div>
</div>

<div class="form-group">
    <label class="control-label" for="mips_operator_id"><?php echo __('Id opérateur'); ?> *</label>
    <div class="controls">
        <input class="form-control input" name="mips_operator_id" id="mips_operator_id" type="text" value="<?php echo $operatorId; ?>" required="required">
    </div>
</div>

<div class="form-group">
    <label class="control-label" for="mips_operator_password"><?php echo __('Mot de passe opérateur'); ?> *</label>
    <div class="controls">
        <input class="form-control input" name="mips_operator_password" id="mips_operator_password" type="text" value="<?php echo $operatorPassword; ?>" required="required">
    </div>
</div>

<div class="form-group">
    <label class="control-label" for="mips_username"><?php echo __('Nom d\'utilisateur'); ?> *</label>
    <div class="controls">
        <input class="form-control input" name="mips_username" id="mips_username" type="text" value="<?php echo $username; ?>" required="required">
    </div>
</div>

<div class="form-group">
    <label class="control-label" for="mips_password"><?php echo __('Mot de passe utilisateur'); ?> *</label>
    <div class="controls">
        <input class="form-control input" name="mips_password" id="mips_password" type="text" value="<?php echo $password; ?>" required="required">
    </div>
</div>

<div class="form-group">
    <label class="control-label" for="mips_salt"><?php echo __('Salt'); ?> *</label>
    <div class="controls">
        <input class="form-control input" name="mips_salt" id="mips_salt" type="text" value="<?php echo $salt; ?>" required="required">
    </div>
</div>

<div class="form-group">
    <label class="control-label" for="mips_cipher_key"><?php echo __('Clé de chiffrement'); ?> *</label>
    <div class="controls">
        <input class="form-control input" name="mips_cipher_key" id="mips_cipher_key" type="text" value="<?php echo $cipherKey; ?>" required="required">
    </div>
</div>
