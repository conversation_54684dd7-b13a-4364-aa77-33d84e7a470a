<?php

use Learnybox\Services\DI\ContainerBuilderService;

$container = ContainerBuilderService::getInstance();

$next_facture = 0;
$prefix = $invoiceSettings['prefixe'] ?? '';

if ($invoiceSettings and isset($invoiceSettings['num_facture'])) {
    //on met le numéro de facture à insérer
    $next_facture = $invoiceSettings['num_facture'];
}

// vérifie le cas où le num_facture est égale à 0 donc on récupère la derniere facture (DESC) et on incrémente
$realPrefix = \Learnybox\Helpers\TextVarHelper::replaceVars([
    'ANNEE' => date('Y'),
    'MOIS' => date('m'),
], $prefix);

$last_num_facture = $container->get(Factures::class)->getLastNumFacture($realPrefix) ?? 1;
if (!$next_facture && $last_num_facture) {
    $next_facture = $last_num_facture;
    ++$next_facture;
}

//on commence à la facture n°1
if (!$next_facture) {
    $next_facture = 1;
}

$prefixAndNumExist = $container->get(Factures::class)->getFactureByNumeroAndPrefixe($next_facture, $realPrefix);
?>

<div class="top-page-title">
    <h3><?php echo __('Génération automatique des factures'); ?></h3>
</div>

<form class="form" method="post" action="">

    <div class="form-group">
        <div class="switch-left">
            <div class="switch">
                <input type="checkbox" name="facture_automatique" id="facture_automatique" class="ios-toggle ios-toggle-round ios-toggle-switch" data-no-uniform="true" <?php echo ((isset($invoiceSettings['facture_automatique']) and $invoiceSettings['facture_automatique']) ? 'checked' : ''); ?>>
                <label for="facture_automatique"></label>
            </div>
        </div>
        <div class="switch-right">
            <div class="switch-infos">
                <label class="control-label" for="facture_automatique"><?php echo __('Générer les factures automatiquement'); ?></label>
                <span class="help-block"><?php echo __('Générer une facture automatiquement à chaque transaction'); ?></span>
            </div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label" for="num_facture"><?php echo __('Prochain numéro de facture'); ?> *</label>

        <span class="help-block"><?php echo __('La prochaine facture commencera à ce numéro, qui augmentera à chaque nouvelle facture.') . '<br>' . __('Indiquez "0" si vous souhaitez conserver le numéro actuel (qui est le n°') . $last_num_facture . ')'; ?></span>
        <div class="controls">
            <input class="form-control input-small" name="num_facture" id="num_facture" type="text" value="<?php echo (isset($invoiceSettings['num_facture']) ? $invoiceSettings['num_facture'] : '0'); ?>">
        </div>

        <?php if ($prefixAndNumExist) : ?>
          <div class="alert alert-danger m-t-12 m-b-0">
            <div class="alert-title"><?php echo __('Attention'); ?></div>
            <div class="alert-text">
                <?php echo __(
                    'Une facture existe déjà avec le préfixe "%s" et le numéro "%s" (%s).',
                    $realPrefix,
                    $invoiceSettings['num_facture'],
                    $realPrefix . $invoiceSettings['num_facture']
                ); ?>
                <br>
                <?php echo __('Veuillez renseigner un numéro de facture différent pour ce préfixe, sinon la prochaine facture sera automatiquement créée avec le numéro %s (%s).', $last_num_facture + 1, $realPrefix . ($last_num_facture + 1)); ?>
            </div>
          </div>
        <?php endif; ?>
    </div>

    <div class="form-group">
        <label class="control-label" for="prefixe"><?php echo __('Préfixe des factures'); ?> *</label>
        <span class="help-block"><?php echo __('Utilisez les codes %s ou %s pour afficher l\'année ou le mois en cours.', \Learnybox\Helpers\TextVarHelper::getI18nTag('ANNEE'), \Learnybox\Helpers\TextVarHelper::getI18nTag('MOIS')); ?></span>
        <div class="controls">
            <input class="form-control input-large" name="prefixe" id="prefixe" type="text" value="<?php echo ($prefix ?: 'FA'); ?>">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label" for="tva"><?php echo __('Taux de TVA par défaut'); ?></label>
        <div class="controls">
            <div class="input-group">
                <input class="form-control input-small" name="tva" id="tva" type="number" step="any" min="0" value="<?php echo (isset($invoiceSettings['tva']) ? $invoiceSettings['tva'] : ''); ?>">
                <span class="input-group-addon" id="basic-addon1">%</span>
            </div>
        </div>
    </div>

    <div class="form-group">
        <div class="switch-left">
            <div class="switch">
                <input type="checkbox" name="tva2015" id="tva2015" class="ios-toggle ios-toggle-round ios-toggle-switch" data-no-uniform="true" <?php echo ((isset($invoiceSettings['tva2015']) and $invoiceSettings['tva2015']) ? 'checked' : ''); ?>>
                <label for="tva2015"></label>
            </div>
        </div>
        <div class="switch-right">
            <div class="switch-infos">
                <label class="control-label" for="tva2015"><?php echo __('Activer la gestion de la TVA par pays.'); ?></label>
                <span class="help-block">
                    <?php echo __('Ce paramètre permet d\'appliquer la TVA du pays du consommateur.'); ?>
                    <?php echo __('Par exemple, si vous résidez en France et qu\'un espagnol achète une de vos formations, le montant TTC sera calculé en fonction du taux de TVA appliqué en Espagne, et non le taux de TVA en France.'); ?>
                    (<a href="<?php echo \Learnybox\Services\Shop\Formulaire\ShopFormulaireService::getEuropaBusinessVatCustomsHelpLink(); ?>" target="_blank"><i class="fa fa-external-link"></i> <?php echo __('En savoir plus'); ?></a>)
                    <br>
                    <?php echo __('Si vous vendez dans les DOM-TOM et que vous souhaitez appliquer les taux réduits légaux, il convient de cocher cette case.'); ?>
                </span>
            </div>
        </div>
    </div>

    <div class="form-actions">
        [[CSRF]]
        <input type="hidden" name="form_action" id="form_action" value="<?php echo $action; ?>"/>
        <input type="hidden" name="redirect" value="<?php echo Tools::makeLink('app', 'reglages_factures', 'generate'); ?>"/>
        <button type="submit" name="submit" class="btn btn-primary"><?php echo __('Valider'); ?></button>
    </div>

</form>
