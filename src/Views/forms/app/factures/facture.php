<?php
$reglages_facture = eden()->Reglages()->appGetParametreByName('facturation');
if ($reglages_facture) {
    $reglages_facture = json_decode($reglages_facture['value'], true);
}

$fatal_error = '';

//plus possible de modifier une facture
$id_facture = 0;
$action = 'insert_facture';

if (isset($id_facture) and $id_facture) {
    $id_facture = filter_var($id_facture, FILTER_VALIDATE_INT);
    if (!$id_facture) {
        $fatal_error = __('Erreur : impossible de récupérer cette facture');
    } else {
        $facture = eden()->Factures()->getFactureById($id_facture);
        if (!$facture) {
            $fatal_error = __("Erreur : cette facture n'existe pas");
        } else {
            if (!isset($_POST['prenom'])) {
                $facture_contenu = json_decode($facture['contenu_personnel'], true);

                $nom = $facture_contenu['nom'];
                $prenom = $facture_contenu['prenom'];
                $email = $facture_contenu['email'];
                $adresse = $facture_contenu['adresse'];
                $ville = $facture_contenu['ville'];
                $pays = $facture_contenu['pays'];
                $region = $facture_contenu['region'] ?? '';
                $telephone = $facture_contenu['telephone'];

                $societe = $tva_intracom = $code_postal = $facture_societe = '';
                if (isset($facture_contenu['societe'])) {
                    $societe = $facture_contenu['societe'];
                }
                if (isset($facture_contenu['facture_societe'])) {
                    $facture_societe = $facture_contenu['facture_societe'];
                }
                if (isset($facture_contenu['tva_intracom'])) {
                    $tva_intracom = $facture_contenu['tva_intracom'];
                }
                if (isset($facture_contenu['cp'])) {
                    $code_postal = $facture_contenu['cp'];
                }

                $devise = $facture['devise'];
                $mode_paiement = $facture['mode_paiement'];
                $id_transaction = $facture['id_transaction'];
                $num_facture = $facture['num_facture'];
                $prefixe = $facture['prefixe'];
                $date_facture = $facture['date_creation'];
            } else {
                $nom = $cleaned_post['nom'];
                $prenom = $cleaned_post['prenom'];
                $email = $cleaned_post['email'];
                $adresse = $cleaned_post['adresse'];
                $ville = $cleaned_post['ville'];
                $pays = $cleaned_post['pays'];
                $region = $cleaned_post['region'] ?? '';
                $telephone = $cleaned_post['telephone'];
                $societe = $cleaned_post['societe'];
                $tva_intracom = $cleaned_post['tva_intracom'];

                $devise = $cleaned_post['devise'];
                $mode_paiement = $cleaned_post['mode_paiement'];
                $id_transaction = $cleaned_post['id_transaction'];
                $prefixe = $cleaned_post['prefixe'];
                $date_facture = $cleaned_post['date_facture'];
            }
        }
    }
} else {
    if (isset($_POST['prenom'])) {
        $nom = $cleaned_post['nom'];
        $prenom = $cleaned_post['prenom'];
        $email = $cleaned_post['email'];
        $adresse = $cleaned_post['adresse'];
        $ville = $cleaned_post['ville'];
        $pays = $cleaned_post['pays'];
        $region = $cleaned_post['region'] ?? '';
        $telephone = $cleaned_post['telephone'];
        $societe = $cleaned_post['societe'];
        $tva_intracom = $cleaned_post['tva_intracom'];

        $devise = $cleaned_post['devise'];
        $mode_paiement = $cleaned_post['mode_paiement'];
        $id_transaction = $cleaned_post['id_transaction'];
        $num_facture = $cleaned_post['num_facture'];
        $prefixe = $cleaned_post['prefixe'];
        $date_facture = $cleaned_post['date_facture'];
    }
}

if ($fatal_error) {
    echo '
	    <div class="alert alert-danger">
			<button type="button" class="close" data-dismiss="alert">×</button>
			<h4 class="alert-heading">' . $fatal_error . '</h4>
			<a href="' . Tools::makeLink('app', 'factures') . '"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a>
		</div>';

    return;
}

if (!isset($id_facture) or !$id_facture) {
    $id_facture = '';
}
if (!isset($date_facture) or !$date_facture) {
    $date_facture = date('Y-m-d H:i:s');
}
if (!isset($devise) or !$devise) {
    $devise = eden()->Transactions()->getMainDevise();
}

if (!isset($num_facture) or !$num_facture) {
    $num_facture = 0;

    if ($reglages_facture and isset($reglages_facture['num_facture'])) {
        $num_facture = $reglages_facture['num_facture'];
    }

    // vérifie le cas où le num_facture est égale à 0 donc on récupère la derniere facture (DESC) et on incrémente
    if (!$num_facture) {
        $last_num_facture = eden()->Factures()->getLastNumFacture();
        if ($last_num_facture) {
            $num_facture = $last_num_facture;
            ++$num_facture;
        }
    }

    //on commence à la facture n°1
    if (!$num_facture) {
        $num_facture = 1;
    }
}

//insertion du prefixe contenu dans les réglages facture
if ((!isset($prefixe) or !$prefixe) and $reglages_facture and isset($reglages_facture['prefixe'])) {
    $prefixe = $reglages_facture['prefixe'];
}

$selectDevise = eden()->Transactions()->generateSelectDevise($devise);

$produits = [];
if (isset($cleaned_post['produits'])) {
    $produits = filter_var($cleaned_post['produits'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
}

if (!$produits) {
    $produits[] = [
        'quantite' => 1,
        'nom' => '',
        'montant_ht' => 0,
        'tva' => 20,
        'montant_ttc' => 0
    ];
}
?>
<div class="top-page-title">
    <h3><?php echo $id_facture ? __('Modification de facture') : __('Création de facture'); ?></h3>
</div>

<div class="panel">
    <div class="panel-body">
        <form class="form js-form-handler" id="form" method="post" action="">

            <div class="form-group">
                <label class="control-label" for="prefixe"><?php echo __('Préfixe'); ?></label>
                <div class="controls">
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-shopping-cart"></i></span>
                        <input name="prefixe" id="prefixe" type="text" class="form-control input-large"
                               value="<?php if (isset($prefixe)) {
                                    echo $prefixe;
                                      } ?>">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="num_facture"><?php echo __('Numéro de facture'); ?></label>
                <div class="controls">
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-shopping-cart"></i></span>
                        <input class="form-control input-large" name="num_facture" id="num_facture" type="text"
                               value="<?php echo $num_facture; ?>">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="date_facture"><?php echo __('Date de la facture'); ?></label>
                <div class="controls">
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input class="form-control input-large datepicker" name="date_facture" id="date_facture" type="text"
                               value="<?php echo $date_facture; ?>">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="id_transaction"><?php echo __('Numéro de transaction'); ?></label>
                <span
                        class="help-block"><?php echo __('Indiquez ici le numéro de transaction (Paypal, Paysite-Cash, etc.), numéro du chèque ou du virement, etc.'); ?></span>
                <div class="controls">
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-shopping-cart"></i></span>
                        <input class="form-control input-large" name="id_transaction" id="id_transaction" type="text"
                               value="<?php if (isset($id_transaction)) {
                                    echo $id_transaction;
                                      } ?>">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="mode_paiement"><?php echo __('Mode de règlement'); ?></label>
                <span class="help-block"><?php echo __('Paypal, Paysite-Cash, Chèque, Virement bancaire, etc.'); ?></span>
                <div class="controls">
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-credit-card"></i></span>
                        <input class="form-control input-large" name="mode_paiement" id="mode_paiement" type="text"
                               value="<?php if (isset($mode_paiement)) {
                                    echo $mode_paiement;
                                      } ?>">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="devise"><?php echo __('Devise'); ?> *</label>
                <div class="controls">
                    <select name="devise" data-rel="select2">
                        <?php echo $selectDevise; ?>
                    </select>
                </div>
            </div>


            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">

                <div class="panel panel-default active">
                    <div class="panel-heading" role="tab" id="headingProduit">
                        <a role="button" data-toggle="collapse" href="#collapseProduit" aria-expanded="true"
                           aria-controls="collapseProduit">
                            <strong><?php echo __('Configurez les produits'); ?></strong><br>
                            <span><?php echo __('Nom du produit, montant, numéro de facture, mode de paiement, etc.'); ?></span>
                        </a>
                    </div>

                    <div id="collapseProduit" class="panel-collapse collapse in" role="tabpanel"
                         aria-labelledby="headingProduit">
                        <div class="panel-body">

                            <table class="table table-striped table-hovered table-produits">
                                <thead>
                                <tr>
                                    <th><?php echo __('Quantité'); ?></th>
                                    <th><?php echo __('Produit'); ?></th>
                                    <th><?php echo __('Montant HT'); ?></th>
                                    <th><?php echo __('TVA'); ?> (%)</th>
                                    <th><?php echo __('Montant TTC'); ?></th>
                                </tr>
                                </thead>
                                <tbody>

                                <?php
                                $montant_ht = 0;
                                $montant_ttc = 0;
                                foreach ($produits as $i => $produit) {
                                    if (!is_numeric($produit['quantite'])) {
                                        $produit['quantite'] = 1;
                                    }
                                    if (!is_numeric($produit['montant_ht'])) {
                                        $produit['montant_ht'] = 0;
                                    }
                                    if (!is_numeric($produit['montant_ttc'])) {
                                        $produit['montant_ttc'] = 0;
                                    }
                                    $montant_ht += $produit['quantite'] * $produit['montant_ht'];
                                    $montant_ttc += $produit['quantite'] * $produit['montant_ttc'];
                                    echo '
                                <tr data-id="' . $i . '">
                                    <td><input name="produits[' . $i . '][quantite]" type="number" min="0" class="form-control input-small quantite" value="' . $produit['quantite'] . '"></td>
                                    <td><input name="produits[' . $i . '][nom]" type="text" class="form-control input" value="' . $produit['nom'] . '"></td>
                                    <td><input name="produits[' . $i . '][montant_ht]" type="number" min="0" step="any" class="form-control input-small montant_ht" value="' . $produit['montant_ht'] . '"></td>
                                    <td>
                                        <div class="input-group">
                                            <input name="produits[' . $i . '][tva]" type="number" min="0" step="any" class="form-control input-small tva" value="' . $produit['tva'] . '">
                                            <span class="input-group-addon">%</span>
                                        </div>
                                    </td>
                                    <td><input name="produits[' . $i . '][montant_ttc]" type="number" min="0" step="any" class="form-control input-small montant_ttc" value="' . $produit['montant_ttc'] . '"></td>
                                </tr>';
                                }
                                ?>

                                </tbody>
                                <tfoot>
                                <tr>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th style="text-align: right"><?php echo __('Montant HT'); ?></th>
                                    <th id="montant_ht"><?php echo $montant_ht; ?></th>
                                </tr>
                                <tr>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th style="text-align: right"><?php echo __('Montant TTC'); ?></th>
                                    <th id="montant_ttc"><?php echo $montant_ttc; ?></th>
                                </tr>
                                </tfoot>
                            </table>

                            <a class="btn btn-secondary" onclick="AddProduct();"><i
                                        class="fa fa-plus"></i> <?php echo __('Ajouter un produit'); ?></a>

                        </div>
                    </div>
                </div>


                <div class="panel panel-default">
                    <div class="panel-heading" role="tab" id="headingInfos">
                        <a role="button" data-toggle="collapse" href="#collapseInfos" aria-expanded="true"
                           aria-controls="collapseInfos">
                            <strong><?php echo __('Ajouter des informations complémentaires'); ?></strong><br>
                            <span><?php echo __('comme l\'adresse, téléphone, société, numéro de TVA intracommunautaire'); ?></span>
                        </a>
                    </div>

                    <div id="collapseInfos" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingInfos">
                        <div class="panel-body">

                            <div class="form-group">
                                <label class="control-label" for="nom"><?php echo __('Nom du client'); ?> *</label>
                                <div class="controls">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                        <input class="form-control input-xlarge" name="nom" id="nom" type="text"
                                               value="<?php echo (isset($nom)) ? $nom : ''; ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label" for="prenom"><?php echo __('Prénom du client'); ?> *</label>
                                <div class="controls">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                        <input class="form-control input-xlarge" name="prenom" id="prenom" type="text"
                                               value="<?php echo (isset($prenom)) ? $prenom : ''; ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label" for="email"><?php echo __('Adresse email'); ?></label>
                                <div class="controls">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="fa fa-at"></i></span>
                                        <input class="form-control input-xlarge" name="email" id="email" type="email"
                                               value="<?php echo (isset($email)) ? $email : ''; ?>">
                                    </div>
                                </div>
                            </div>

                            <?php echo $configAddressHtml; ?>

                            <div class="form-group">
                                <label class="control-label" for="telephone"><?php echo __('Téléphone'); ?></label>
                                <div class="controls">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="fa fa-phone"></i></span>
                                        <input class="form-control input-xlarge" name="telephone" id="telephone" type="text"
                                               value="<?php if (isset($telephone)) {
                                                    echo $telephone;
                                                      } ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label" for="societe"><?php echo __('Société'); ?></label>
                                <div class="controls">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="fa fa-building"></i></span>
                                        <input class="form-control input-xlarge" name="societe" id="societe" type="text"
                                               value="<?php if (isset($societe)) {
                                                    echo $societe;
                                                      } ?>">
                                    </div>
                                </div>
                                <div id="facture-societe" style="margin-top: 10px;<?php echo (!isset($societe)) ? 'display: none;' : ''; ?>">
                                    <div class="controls">
                                        <div class="input-group">
                                            <div class="checkbox">
                                                <label><input type="checkbox" name="facture_societe"<?php (isset($facture_societe) ? ' checked' : ''); ?>><?php echo __('Je souhaite que la facture soit émise au nom de la société'); ?>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label"
                                       for="tva_intracom"><?php echo __('Numéro de TVA intracom'); ?></label>
                                <div class="controls">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="fa fa-building"></i></span>
                                        <input class="form-control input-xlarge" name="tva_intracom" id="tva_intracom"
                                               type="text" value="<?php if (isset($tva_intracom)) {
                                                    echo $tva_intracom;
                                                                  } ?>">
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

            </div>

            <?php if (!$id_facture) { ?>
                <div class="form-group">
                    <label class="control-label" for="facture_email"><?php echo __('Envoi par email'); ?></label>
                    <span
                            class="help-block"><?php echo __('L\'email à envoyer est modifiable dans le menu "Réglages de facturation".'); ?></span>
                    <div class="checkbox">
                        <label for="facture_email">
                            <input type="checkbox" name="facture_email"
                                   id="facture_email" <?php if (isset($reglages_facture['facture_email']) and $reglages_facture['facture_email']) {
                                        echo 'checked';
                                                      } ?>> <?php echo __('Envoyer la facture par email au client '); ?>
                        </label>
                    </div>
                </div>
            <?php } ?>

            <div class="form-actions">
                [[CSRF]]
                <input type="hidden" name="form_action" id="form_action" value="<?php echo $action; ?>"/>
                <input type="hidden" name="id_facture" value="<?php echo $id_facture; ?>"/>
                <input type="hidden" name="redirect" value="<?php echo Tools::makeLink('app', 'factures'); ?>"/>
                <button type="submit" name="submit" class="btn btn-primary"><?php echo __('Valider'); ?></button>
                <a class="btn btn-default btn-lg"
                   href="<?php echo Tools::makeLink('app', 'factures'); ?>"><?php echo __('Annuler'); ?></a>
            </div>

        </form>
    </div>
</div>
