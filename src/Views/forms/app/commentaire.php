<?php

use Learnybox\Enums\User\UserTabEnum;
use Learnybox\Helpers\RouterHelper;

$container = \Learnybox\Services\DI\ContainerBuilderService::getInstance();

foreach ($cleaned_post as $key => $value) {
    if (!is_array($value)) {
        ${$key} = $value;
    }
}

$fatal_error = '';
if (isset($param) and $param) {
    $id_commentaire = filter_var($param, FILTER_VALIDATE_INT);
    if (!$id_commentaire) {
        $fatal_error = __('Erreur : impossible de récupérer le commentaire');
    } else {
        $commentaire = eden()->Commentaires()->getCommentaireById($id_commentaire);
        if (!$commentaire) {
            $fatal_error = __("Erreur : ce commentaire n'existe pas");
        } else {
            if (!isset($_POST['prenom'])) {
                $prenom = $commentaire['prenom'];
                $email = $commentaire['email'];
                $site = $commentaire['site'];
                $message = $commentaire['commentaire'];
                $etat = $commentaire['etat'];
                $nom = $commentaire['nom'];
                $adresse = $commentaire['adresse'];
                $telephone = $commentaire['telephone'];
            } else {
                $prenom = $cleaned_post['prenom'];
                $email = $cleaned_post['email'];
                $site = $cleaned_post['site'];
                $message = $_POST['message'];
                $etat = $cleaned_post['etat'];
                $nom = $cleaned_post['nom'];
                $adresse = $cleaned_post['adresse'];
                $telephone = $cleaned_post['telephone'];
            }
            $user_id = $commentaire['user_id'];
            $id_article = $commentaire['id_article'];
        }
    }
} else {
    $fatal_error = __('Erreur : impossible de récupérer le commentaire');
}

if ($fatal_error) {
    echo '
	    <div class="alert alert-danger">
			<h4 class="alert-heading">' . $fatal_error . '</h4>
			<a href="' . Tools::makeLink('app', 'commentaires') . '"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a>
		</div>';

    return;
}

$message = stripslashes($message);

$etat_select = eden()->Commentaires()->generateSelectEtat($etat);
$etat_select_approuve = eden()->Commentaires()->generateSelectEtat('approuve');

$article = eden()->Articles()->getArticleById($id_article);

//user config
$user = [];
$user_config = [];
if ($user_id) {
    $user = eden()->Utilisateurs()->getUserById($user_id);

    if ($user) {
        $user_infos = eden()->Utilisateur()->getUserInfosById($user_id);
        if ($user_infos) {
            foreach ($user_infos as $user_info) {
                $user_config[$user_info['name']] = $user_info['value'];
            }
        }
    }
}

$prenom = $commentaire['prenom'];
if ($user) {
    $prenom = $user['fname'] . ' ' . $user['lname'];
}

echo '
<div class="container-user">
    <div class="row">
        <div class="col-md-3">';

//widget user
if ($user) {
    $user_edit_link = RouterHelper::generate('app_user', ['id' => $user_id, 'tab' => UserTabEnum::HOME_TAB->value, 'action' => UserTabEnum::USER_EDIT_TAB->value]);
    echo eden()->Widgets()->displayWidgetUser($user, $user_config, $user_edit_link);
}

//widget article
if ($article) {
    echo eden()->Widgets()->displayWidgetArticle($article);
}

echo '
        </div>
        <div class="col-md-9">';
?>

<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">
            <i class="fa fa-comment"></i> <?php echo __('Répondre au commentaire'); ?>
        </h3>
    </div>
    <div class="panel-body">
        <form class="form" method="post" action="">

            <div class="form-group" id="group-reponse-message">
                <label class="control-label" for="message"><?php echo __('Votre message'); ?> *</label>
                <div class="controls" id="control-reponse-message">
                    <textarea class="ckeditor" id="ckeditorsmall2" name="message"></textarea>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="selectEtat"><?php echo __('Modifier l\'état du commentaire'); ?></label>
                <div class="controls">
                    <select id="selectEtat" name="etat" data-rel="select2" style="width:200px;">
                        <?php echo $etat_select_approuve; ?>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="notification_email"><?php echo __('Notification'); ?></label>
                <div class="controls">
                    <div class="checkbox">
                        <label for="notification_email">
                            <input type="checkbox" id="notification_email" name="notification_email"> <?php echo __('Envoyer votre réponse par email à cet utilisateur'); ?>
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-actions" style="position: relative;">
                [[CSRF]]
                <input type="hidden" name="form_action" id="act" value="commentaire_repondre"/>
                <input type="hidden" name="id_article" value="<?php echo $commentaire['id_article']; ?>">
                <input type="hidden" name="orig_comment" value="<?php echo $id_commentaire; ?>"/>
                <button type="submit" id="submit-reponse" class="btn btn-primary btn-lg"><i class="fa fa-comment"></i> <?php echo __('Répondre'); ?></button>
            </div>

        </form>
    </div>
</div>

<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">
            <i class="fa fa-edit"></i> <?php echo __('Modifier le commentaire'); ?>
        </h3>
    </div>
    <div class="panel-body">
        <form class="form" method="post" action="">

            <?php if (!$user_id or !$user) {
                ?>
                <div class="form-group">
                    <label class="control-label" for="titre"><?php echo __('Prénom'); ?> *</label>
                    <div class="controls">
                        <input class="form-control input-xlarge focused" name="prenom" id="prenom" type="text" value="<?php if (isset($prenom)) {
                            echo $prenom;
                                                                                                                      } ?>" required>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label" for="nom"><?php echo __('Nom'); ?></label>
                    <div class="controls">
                        <input class="form-control input-xlarge" name="nom" id="nom" type="text" value="<?php if (isset($nom)) {
                            echo $nom;
                                                                                                        } ?>">
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label" for="email"><?php echo __('Email'); ?> *</label>
                    <div class="controls">
                        <input class="form-control input-xlarge" name="email" id="email" type="email" value="<?php if (isset($email)) {
                            echo $email;
                                                                                                             } ?>" required>
                    </div>
                </div>
                <?php
            } ?>

            <?php if (isset($adresse) and $adresse) {
                ?>
                <div class="form-group">
                    <label class="control-label" for="adresse"><?php echo __('Adresse'); ?></label>
                    <div class="controls">
                        <input class="form-control input-xlarge" name="adresse" id="adresse" type="text" value="<?php if (isset($adresse)) {
                            echo $adresse;
                                                                                                                } ?>">
                    </div>
                </div>
                <?php
            } ?>

            <?php if (isset($telephone) and $telephone) {
                ?>
                <div class="form-group">
                    <label class="control-label" for="telephone"><?php echo __('Téléphone'); ?></label>
                    <div class="controls">
                        <input class="form-control input-xlarge" name="telephone" id="telephone" type="text" value="<?php if (isset($telephone)) {
                            echo $telephone;
                                                                                                                    } ?>">
                    </div>
                </div>
                <?php
            } ?>

            <?php if (isset($site) and $site) {
                ?>
                <div class="form-group">
                    <label class="control-label" for="site"><?php echo __('Site'); ?></label>
                    <div class="controls">
                        <input class="form-control input-xlarge" name="site" id="site" type="text" value="<?php if (isset($site)) {
                            echo $site;
                                                                                                          } ?>" placeholder="http://">
                    </div>
                </div>
                <?php
            } ?>

            <div class="form-group">
                <label class="control-label" for="selectEtat2"><?php echo __('Etat'); ?></label>
                <div class="controls">
                    <select id="selectEtat2" name="etat" data-rel="select2" style="width:200px;">
                        <?php echo $etat_select; ?>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="message"><?php echo __('Commentaire'); ?> *</label>
                <div class="controls">
                    <textarea class="ckeditor" id="ckeditorsmall" name="message"><?php if (isset($message)) {
                            echo str_replace("\n", '<br>', $message);
                                                                                 } ?></textarea>
                </div>
            </div>

            <div class="form-actions">
                [[CSRF]]
                <input type="hidden" name="form_action" id="form_action" value="<?php echo $action; ?>"/>
                <input type="hidden" name="redirect" value="<?php echo Tools::makeLink('app', 'commentaires'); ?>"/>
                <?php if ($id_commentaire) {
                    echo '<input type="hidden" name="id_commentaire" value="' . $id_commentaire . '" />';
                } ?>
                <button type="submit" name="submit" class="btn btn-primary btn-lg"><?php echo __('Valider'); ?></button>
                <a class="btn btn-default btn-lg" href="<?php echo Tools::makeLink('app', 'commentaires'); ?>"><?php echo __('Annuler'); ?></a>
            </div>

        </form>

    </div>
</div>

</div>
</div>
