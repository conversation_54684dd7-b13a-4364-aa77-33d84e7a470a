<?php

use Learnybox\Helpers\RouterHelper;
use Learnybox\Renderers\Menus\App\Reglages\CommonSettingsMenuRenderer;
use Learnybox\Services\DI\ContainerBuilderService;

$container = ContainerBuilderService::getInstance();

$idformation = $formation['idformation'];

$formation = eden()->Formation_Formation()->getFormationById($idformation);
if (!$formation) {
    echo '
        <div class="alert alert-danger">
            ' . __('Erreur : cette formation n\'existe pas.') . '<br>
            <a class="btn btn-danger btn-small" href="' . $redirect . '"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a>
        </div>';

    return;
}

$groupes = eden()->Formation_Groupes()->getGroupesLightByFormation($idformation);

$form_file = FORMS_PATH . '/app/autorepondeurs/' . $type_ar . '.php';
if (!file_exists($form_file)) {
    echo '
        <div class="alert alert-danger">
            ' . __('Cet autorépondeur n\'existe pas.') . '<br>
            <a class="btn btn-danger btn-small" href="' . $redirect . '"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a>
        </div>';

    return;
}

$ar_infos = eden()->Autorepondeurs_Base()->getAutorepondeurInfos($type_ar);

$type = 'formation';
$form_action = 'fautorepondeur';
$ar_datas = eden()->Autorepondeurs_Base()->getAutorepondeurByFormation($idformation, $type_ar);
$redirect_link = RouterHelper::generate('app_formation_autorepondeur', ['formationId' => $idformation, 'slug' => $type_ar]);
$delete_action = 'fautorepondeur_delete';
$delete_param = '<input type="hidden" name="idformation" value="' . $idformation . '">';

echo Eden_Template::i()->set('type_ar', $type_ar)
    ->set('type', $type)
    ->set('idformation', $idformation)
    ->set('id_object', $idformation)
    ->set('formation', $formation)
    ->set('groupes', $groupes)
    ->set('ar_infos', $ar_infos)
    ->set('form_action', $form_action)
    ->set('ar_datas', $ar_datas)
    ->set('redirect_link', $redirect_link)
    ->set('cancel_link', $redirect)
    ->set('delete_action', $delete_action)
    ->set('delete_param', $delete_param)
    ->set('cleaned_post', (isset($cleaned_post) ? $cleaned_post : []))
    ->parsePHP($form_file);
