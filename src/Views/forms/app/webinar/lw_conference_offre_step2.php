<?php

use Learnybox\Helpers\RouterHelper;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Services\PaymentMethod\Paypalv2PaymentMethodService;
use Learnybox\Enums\Transaction\TransactionTypeEnum;

$fatal_error = '';
$checkout = [];
$design = [];
$offreConferenceId = null;

$container = ContainerBuilderService::getInstance();

if (isset($id_offre) and $id_offre) {
    $id_offre = filter_var($id_offre, FILTER_VALIDATE_INT);
    if (!$id_offre) {
        $fatal_error = __('Erreur : impossible de récupérer cette offre');
    } else {
        $offre = eden()->Webinar_Offres()->getOffre($id_offre, $id_conference);
        if (!$offre) {
            $fatal_error = __("Erreur : cette offre n'existe pas");
        } else {
            $btn_type = $offre['btn_type'];

            $type = '';
            $url = '';
            $bouton = [];
            $design = [];
            $checkout = [];
            if ($offre['bouton']) {
                $bouton = json_decode($offre['bouton'], true);
            }

            if (isset($bouton['checkout']['id_conference']) && $bouton['checkout']['id_conference']) {
                $offreConferenceId = $bouton['checkout']['id_conference'];
            }

            if (!isset($_POST['nom'])) {
                if ($bouton) {
                    $design = $bouton['design'];
                    if (isset($bouton['type'])) {
                        $type = $bouton['type'];
                    }
                    if (isset($bouton['checkout'])) {
                        $checkout = $bouton['checkout'];
                    }

                    if (isset($bouton['url'])) {
                        $url = $bouton['url'];
                    }
                    if (isset($design['align'])) {
                        $align = $design['align'];
                    }
                    if (isset($design['size'])) {
                        $size = $design['size'];
                    }
                    if (isset($design['icone'])) {
                        $icone = $design['icone'];
                    }
                }
            } else {
                if (isset($cleaned_post['btn_type'])) {
                    $btn_type = $cleaned_post['btn_type'];
                }
                if (isset($cleaned_post['type'])) {
                    $type = $cleaned_post['type'];
                }
                if (isset($cleaned_post['product_name'])) {
                    $checkout['product_name'] = $cleaned_post['product_name'];
                }

                if (isset($cleaned_post['paypalDevise'])) {
                    $checkout['devise'] = $cleaned_post['paypalDevise'];
                } elseif (isset($cleaned_post['paypalv2Devise'])) {
                    $checkout['devise'] = $cleaned_post['paypalv2Devise'];
                } elseif (isset($cleaned_post['learnypayDevise'])) {
                    $checkout['devise'] = $cleaned_post['learnypayDevise'];
                } elseif (isset($cleaned_post['pscDevise'])) {
                    $checkout['devise'] = $cleaned_post['pscDevise'];
                } elseif (isset($cleaned_post['payboxDevise'])) {
                    $checkout['devise'] = filter_var($cleaned_post['payboxDevise'], FILTER_SANITIZE_SPECIAL_CHARS);
                } elseif (isset($cleaned_post['stripeDevise'])) {
                    $checkout['devise'] = filter_var($cleaned_post['stripeDevise'], FILTER_SANITIZE_SPECIAL_CHARS);
                } elseif (isset($cleaned_post['gocardlessDevise'])) {
                    $checkout['devise'] = filter_var($cleaned_post['gocardlessDevise'], FILTER_SANITIZE_SPECIAL_CHARS);
                } elseif (isset($cleaned_post['braintreeDevise'])) {
                    $checkout['devise'] = filter_var($cleaned_post['braintreeDevise'], FILTER_SANITIZE_SPECIAL_CHARS);
                } elseif (isset($cleaned_post['checkoutcomDevise'])) {
                    $checkout['devise'] = filter_var($cleaned_post['checkoutcomDevise'], FILTER_SANITIZE_SPECIAL_CHARS);
                } elseif (isset($cleaned_post['mipsDevise'])) {
                    $checkout['devise'] = filter_var($cleaned_post['mipsDevise'], FILTER_SANITIZE_SPECIAL_CHARS);
                }

                $checkout['amount'] = $cleaned_post['amount'];
                if (isset($cleaned_post['payment'])) {
                    $checkout['payment'] = $cleaned_post['payment'];
                }
                if (isset($cleaned_post['nb_payments'])) {
                    $checkout['nb_payments'] = $cleaned_post['nb_payments'];
                }
                if (isset($cleaned_post['subscription_time'])) {
                    $checkout['subscription_time'] = $cleaned_post['subscription_time'];
                }
                if (isset($cleaned_post['trial_period'])) {
                    $checkout['trial_period'] = $cleaned_post['trial_period'];
                }
                if (isset($cleaned_post['trial_amount'])) {
                    $checkout['trial_amount'] = $cleaned_post['trial_amount'];
                }
                if (isset($cleaned_post['trial_time'])) {
                    $checkout['trial_time'] = $cleaned_post['trial_time'];
                }
                if (isset($cleaned_post['trial_timetype'])) {
                    $checkout['trial_timetype'] = $cleaned_post['trial_timetype'];
                }
                if (isset($cleaned_post['redirection'])) {
                    $checkout['payment_return'] = $cleaned_post['redirection'];
                }

                if (isset($cleaned_post['redirection_url'])) {
                    $redirection_url = filter_var($cleaned_post['redirection_url'], FILTER_VALIDATE_URL);
                    if ($redirection_url) {
                        $checkout['payment_return'] = $redirection_url;
                    }
                }

                $align = $cleaned_post['align'];
                $size = $cleaned_post['size'];
                $icone = $cleaned_post['icone'];
                $design['texte'] = $cleaned_post['texte'];
                $design['image'] = $cleaned_post['image'];
                $design['largeur'] = $cleaned_post['largeur'];
                $design['hauteur'] = $cleaned_post['hauteur'];
                $design['color'] = $cleaned_post['color'];
                $design['color_text'] = $cleaned_post['color_text'];
            }
        }
    }
} else {
    $fatal_error = __('Erreur : aucune offre sélectionnée');
}

if ($fatal_error) {
    echo '
	    <div class="alert alert-danger">
			<button type="button" class="close" data-dismiss="alert">×</button>
			<h4 class="alert-heading">' . $fatal_error . '</h4>
			<a href="' . RouterHelper::generate('app_conference_offres', [
            'conferenceId' => $id_conference
        ]) . '"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a>
		</div>';

    return;
}

if (!isset($align) or !$align) {
    $align = 'center';
}
if (!isset($size) or !$size) {
    $size = 'btn';
}
if (!isset($icone)) {
    $icone = '';
}

$ratio = 0;
if (isset($design['largeur']) and $design['largeur'] and isset($design['hauteur']) and $design['hauteur']) {
    $ratio = round($design['largeur'] / $design['hauteur'], 2);
    $ratio = number_format($ratio, 2, '.', '');
}

$id_formation = 0;
if (isset($checkout['id_formation'])) {
    $id_formation = $checkout['id_formation'];
} elseif (isset($cleaned_post['id_formation'])) {
    $id_formation = $cleaned_post['id_formation'];
} elseif (isset($_SESSION['idformation'])) {
    $id_formation = $_SESSION['idformation'];
}

$idgroupe = 0;
if (isset($checkout['idgroupe'])) {
    $idgroupe = $checkout['idgroupe'];
} elseif (isset($cleaned_post['idgroupe'])) {
    $idgroupe = $cleaned_post['idgroupe'];
}

if (isset($checkout['payment_return'])) {
    $selectRedirectionPage = eden()->Pages()->generateSelectAllPages($checkout['payment_return'], '', false);
} else {
    $selectRedirectionPage = eden()->Pages()->generateSelectAllPages('', '', false);
}

$learnypay_account = '';
if (isset($checkout['learnypay_account'])) {
    $learnypay_account = $checkout['learnypay_account'];
} elseif (isset($cleaned_post['learnypay_account'])) {
    $learnypay_account = $cleaned_post['learnypay_account'];
}

$paypal_account = '';
if (isset($checkout['paypal_account'])) {
    $paypal_account = $checkout['paypal_account'];
} elseif (isset($cleaned_post['paypal_account'])) {
    $paypal_account = $cleaned_post['paypal_account'];
}

if (!$paypal_account) {
    $get_paypal_account = eden()->Reglages()->appGetParametreByName('paypal_account');
    if ($get_paypal_account) {
        $paypal_account = $get_paypal_account['value'];
    }
}

$paypalClientId = '';
if (isset($checkout['paypal_client_id'])) {
    $paypalClientId = $checkout['paypal_client_id'];
} elseif (isset($cleaned_post['paypal_client_id'])) {
    $paypalClientId = $cleaned_post['paypal_client_id'];
}
if (empty($paypalClientId)) {
    $paypalClientIdParam = $container->get(\Reglages::class)->appGetParametreByName('paypal_client_id');
    if ($paypalClientIdParam) {
        $paypalClientId = $paypalClientIdParam['value'];
    }
}

$paypalClientSecret = '';
if (isset($checkout['paypal_client_secret'])) {
    $paypalClientSecret = $checkout['paypal_client_secret'];
} elseif (isset($cleaned_post['paypal_client_secret'])) {
    $paypalClientSecret = $cleaned_post['paypal_client_secret'];
}
if (empty($paypalClientSecret)) {
    $paypalClientSecretParam = $container->get(\Reglages::class)->appGetParametreByName('paypal_client_secret');
    if ($paypalClientSecretParam) {
        $paypalClientSecret = $paypalClientSecretParam['value'];
    }
}

$psc_site = '';
if (isset($checkout['psc_site'])) {
    $psc_site = $checkout['psc_site'];
} elseif (isset($cleaned_post['psc_site'])) {
    $psc_site = $cleaned_post['psc_site'];
}

if (!$psc_site) {
    $get_psc_site = eden()->Reglages()->appGetParametreByName('psc_site');
    if ($get_psc_site) {
        $psc_site = $get_psc_site['value'];
    }
}

$paybox_config = (isset($checkout['paybox_config']) ? $checkout['paybox_config'] : []);
if (!$paybox_config) {
    $paybox_config = eden()->Reglages()->appGetParametreByName('paybox');
    if ($paybox_config and $paybox_config['value']) {
        $paybox_config = json_decode($paybox_config['value'], true);
    }
}

$sap_config = (isset($checkout['sap_config']) ? $checkout['sap_config'] : []);
if (!$sap_config) {
    $sap_config = eden()->Reglages()->appGetParametreByName('sap');
    if ($sap_config and $sap_config['value']) {
        $sap_config = json_decode($sap_config['value'], true);
    }
}

$stripeAccountId = ($checkout['stripe_account_id'] ?? 0);

$clickbank_account = (isset($checkout['clickbank_account']) ? $checkout['clickbank_account'] : '');
if (!$clickbank_account) {
    $get_clickbank_account = eden()->Reglages()->appGetParametreByName('clickbank_account');
    if ($get_clickbank_account) {
        $clickbank_account = $get_clickbank_account['value'];
    }
}

$clickbank_secret_key = (isset($checkout['clickbank_secret_key']) ? $checkout['clickbank_secret_key'] : '');
if (!$clickbank_secret_key) {
    $get_clickbank_secret_key = eden()->Reglages()->appGetParametreByName('clickbank_secret_key');
    if ($get_clickbank_secret_key) {
        $clickbank_secret_key = $get_clickbank_secret_key['value'];
    }
}

$payplugSecretKey = (isset($checkout['payplug_secret_key']) ? $checkout['payplug_secret_key'] : '');
$payplugPublicKey = (isset($checkout['payplug_public_key']) ? $checkout['payplug_public_key'] : '');
if (!$payplugSecretKey) {
    $getPayplugSecretKey = $container->get(\Reglages::class)->appGetParametreByName('payplug_secret_key');
    if ($getPayplugSecretKey) {
        $payplugSecretKey = $getPayplugSecretKey['value'];
    } else {
        $getPayplugSecretKey = $container->get(\Reglages::class)->appGetParametreByName('payplug_apikey');
        if ($getPayplugSecretKey) {
            $payplugSecretKey = $getPayplugSecretKey['value'];
        }
    }
}
if (!$payplugPublicKey) {
    $getPayplugPublicKey = $container->get(\Reglages::class)->appGetParametreByName('payplug_public_key');
    if ($getPayplugPublicKey) {
        $payplugPublicKey = $getPayplugPublicKey['value'];
    }
}

$gocardless_token = '';
$gocardless_webhook_token = '';
if (isset($checkout['gocardless_token'])) {
    $gocardless_token = $checkout['gocardless_token'];
}

if (!$gocardless_token) {
    $get_gocardless_token = eden()->Reglages()->appGetParametreByName('gocardless_token');
    if ($get_gocardless_token and $get_gocardless_token['value']) {
        $gocardless_token = $get_gocardless_token['value'];
    }
}

$get_gocardless_webhook_token = eden()->Reglages()->appGetParametreByName('gocardless_webhook_token');
if ($get_gocardless_webhook_token and $get_gocardless_webhook_token['value']) {
    $gocardless_webhook_token = $get_gocardless_webhook_token['value'];
}

$braintree_config = eden()->Reglages()->appGetParametreByName('braintree_config');
if ($braintree_config) {
    $braintree_config = json_decode($braintree_config['value'], true);
    $braintree_merchantId = (isset($braintree_config['merchantId']) ? $braintree_config['merchantId'] : '');
    $braintree_publicKey = (isset($braintree_config['publicKey']) ? $braintree_config['publicKey'] : '');
    $braintree_privateKey = (isset($braintree_config['privateKey']) ? $braintree_config['privateKey'] : '');
}

$braintree_display_paypal = false;
if (isset($checkout['braintree_display_paypal'])) {
    $braintree_display_paypal = true;
}

$checkoutcom_secret_key = (isset($checkout['checkoutcom_secret_key']) ? $checkout['checkoutcom_secret_key'] : '');
$checkoutcom_public_key = (isset($checkout['checkoutcom_public_key']) ? $checkout['checkoutcom_public_key'] : '');
$checkoutcom_processing_channel_id = (isset($checkout['checkoutcom_processing_channel_id']) ? $checkout['checkoutcom_processing_channel_id'] : '');

if (!$checkoutcom_secret_key) {
    $get_checkoutcom_secret_key = eden()->Reglages()->appGetParametreByName('checkoutcom_secret_key');
    if ($get_checkoutcom_secret_key) {
        $checkoutcom_secret_key = $get_checkoutcom_secret_key['value'];
    }
}
if (!$checkoutcom_public_key) {
    $get_checkoutcom_public_key = eden()->Reglages()->appGetParametreByName('checkoutcom_public_key');
    if ($get_checkoutcom_public_key) {
        $checkoutcom_public_key = $get_checkoutcom_public_key['value'];
    }
}
if (!$checkoutcom_processing_channel_id) {
    $get_checkoutcom_processing_channel_id = eden()->Reglages()->appGetParametreByName('checkoutcom_processing_channel_id');
    if ($get_checkoutcom_processing_channel_id) {
        $checkoutcom_processing_channel_id = $get_checkoutcom_processing_channel_id['value'];
    }
}

$centralPayVars = [
    'centralpay_api_id',
    'centralpay_api_password',
    'centralpay_merchant_public_key',
    'centralpay_merchant_country',
    'centralpay_point_of_sale_id',
];
foreach ($centralPayVars as $centralPayVar) {
    ${$centralPayVar} = (isset($checkout[$centralPayVar]) ? $checkout[$centralPayVar] : '');
    if (!${$centralPayVar}) {
        $getCentralPayVar = $container->get(\Reglages::class)->appGetParametreByName($centralPayVar);
        if ($getCentralPayVar) {
            ${$centralPayVar} = $getCentralPayVar['value'];
        }
    }
}

$mipsVars = [
    'mips_merchant_id',
    'mips_entity_id',
    'mips_operator_id',
    'mips_operator_password',
    'mips_username',
    'mips_password',
    'mips_salt',
    'mips_cipher_key',
];
foreach ($mipsVars as $mipsVar) {
    ${$mipsVar} = (isset($checkout[$mipsVar]) ? $checkout[$mipsVar] : '');
    if (!${$mipsVar}) {
        $getMipsVar = eden()->Reglages()->appGetParametreByName($mipsVar);
        if ($getMipsVar) {
            ${$mipsVar} = $getMipsVar['value'];
        }
    }
}

$selectIcone = Tools::generateSelectIcones($icone);
$selectFormation = eden()->Formation_Formation()->generateSelectFormation($id_formation);
$selectGroupe = eden()->Formation_Groupes()->generate_select_groupes($id_formation, $idgroupe);
$selectConference = eden()->Webinar_Conferences()->generateSelectConferences($offreConferenceId);
?>
<div class="top-page-title">
    <h3><?php echo __('Edition du bouton de paiement'); ?></h3>
</div>

<div class="panel panel-default">
    <div class="panel-body">
        <form id="shop_paiement_form" class="form" method="post" action="">

            <div class="form-group">
                <label class="control-label"><?php echo __('Configuration du bouton'); ?></label>
                <div class="controls">
                    <label class="radio-inline" style="margin-right:10px" for="btn_type1">
                        <input type="radio" name="btn_type" id="btn_type1"
                               value="url" <?php if (isset($btn_type) and 'url' == $btn_type) {
                                    echo 'checked';
                                           } ?> /> <?php echo __('Rediriger vers une adresse'); ?>
                    </label>
                    <br>
                    <label class="radio-inline" style="margin-right:10px" for="btn_type2">
                        <input type="radio" name="btn_type" id="btn_type2"
                               value="btn" <?php if (isset($btn_type) and 'btn' == $btn_type) {
                                    echo 'checked';
                                           } ?> /> <?php echo __('Bouton de paiement') . ' <small>(' . __('que vous pouvez configurer ci-dessous') . ')</small>'; ?>
                    </label>
                </div>
            </div>


            <div id="UrlConfiguration" style="display:<?php if (isset($btn_type) and 'url' == $btn_type) {
                echo 'block';
                                                      } else {
                                                          echo 'none';
                                                      } ?>">
                <div class="form-group">
                    <label class="control-label"><?php echo __('Adresse de la page'); ?></label>
                    <div class="controls">
                        <input class="form-control input-xxlarge" type="url" name="url" id="url" value="<?php if (isset($url)) {
                            echo $url;
                                                                                                        } ?>" placeholder="http://">
                    </div>
                </div>
            </div>


            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">

                <div id="PaiementConfiguration" style="display:<?php if (isset($btn_type) and 'btn' == $btn_type) {
                    echo 'block';
                                                               } else {
                                                                   echo 'none';
                                                               } ?>">

                    <div class="panel panel-default active">
                        <div class="panel-heading" role="tab">
                            <a role="button" data-toggle="collapse" href="#collapsePaiement"
                               aria-expanded="true">
                                <strong><?php echo __('Configuration du moyen de paiement'); ?></strong><br>
                                <span><?php echo __('Choisissez un moyen de paiement comme Paypal, Stripe, etc.'); ?></span>
                            </a>
                        </div>

                        <div id="collapsePaiement" class="panel-collapse collapse in" role="tabpanel">
                            <div class="panel-body">

                                <div class="form-group">
                                    <label class="control-label"><?php echo __('Moyen de paiement'); ?></label>
                                    <div class="payments">
                                        <?php
                                        $i = 0;
                                        foreach ($payments as $payment) {
                                            if (0 == $i % 6) {
                                                echo '<div class="row">';
                                            }
                                            ++$i;

                                            echo '
                                        <div class="col-md-2">
                                            <div class="payment payment-' . $payment['value'] . ((isset($type) and $type == $payment['value']) ? ' active' : '') . '" onclick="ChangePayment(\'' . $payment['value'] . '\');">
                                                <div class="payment-content">
                                                    <img class="pic" src="' . \Learnybox\Helpers\Assets::getImageUrl('integrations/' . $payment['value'] . '.png') . '" alt="' . $payment['label'] . '">
                                                </div>
                                            </div>
                                        </div>';

                                            if (0 == $i % 6) {
                                                echo '</div>';
                                            }
                                        }
                                        if (0 != $i % 6) {
                                            echo '</div>';
                                        }
                                        ?>
                                        <input type="hidden" name="type" id="payment"
                                               value="<?php echo(isset($type) ? $type : ''); ?>">
                                    </div>
                                </div>

                                <?php if (LEARNYPAY_ENABLE === true) : ?>
                                    <div id="learnypayConfiguration" class="configuration"
                                         style="display:<?php if (isset($type) and 'learnypay' == $type) {
                                                echo 'block';
                                                        } else {
                                                            echo 'none';
                                                        } ?>">
                                        <div class="form-group">
                                            <label class="control-label"
                                                   for="paypal_account"><?php echo __('Portefeuille LearnyPay'); ?></label>
                                            <div class="controls">
                                                <?php
                                                $selectAccount = eden()->Mangopay_Accounts()->generateSelectAccounts($learnypay_account);
                                                if (!$selectAccount) {
                                                    echo '<div class="alert alert-info">' . __('Aucun portefeuille LearnyPay trouvé.') . '<br><a class="btn btn-default btn-small" href="' . Tools::makeLink('app', '') . '">' . __('Créer un portefeuille LearnyPay') . '</a></div>';
                                                } else {
                                                    echo '<select name="learnypay_account">' . $selectAccount . '</select>';
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <div id="paypalConfiguration" class="configuration"
                                     style="display:<?php if (isset($type) and 'paypal' == $type) {
                                            echo 'block';
                                                    } else {
                                                        echo 'none';
                                                    } ?>">

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="paypal_account"><?php echo __('Adresse email de votre compte Paypal'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xxlarge" id="paypal_account" name="paypal_account"
                                                   type="email" value="<?php echo $paypal_account; ?>"/>
                                            <span class="help-block"><i
                                                        class="fa fa-warning"></i> <?php echo __('Avec Paypal, tout est automatisé sur') . ' ' . LB . ' ' . __('mais assurez-vous de bien avoir activé les notifications instantanées de paiement sur votre compte Paypal'); ?> (<a
                                                        href="<?php echo \Learnybox\Services\Help\HelpService::getHelpUrlWithQuestion('Paypal'); ?>"
                                                        target="_blank"><i
                                                            class="fa fa-external-link"></i> <?php echo __('En savoir plus'); ?></a>)</span>
                                        </div>
                                    </div>

                                </div>

                                <?php echo $container->get(Paypalv2PaymentMethodService::class)->getConfigPanelFormHtml(['paypal_client_id' => $paypalClientId, 'paypal_client_secret' => $paypalClientSecret, 'display' => isset($type) && strtolower(TransactionTypeEnum::PAYPAL_V2) == $type]); ?>

                                <div id="pscConfiguration" class="configuration"
                                     style="display:<?php if (isset($type) and 'psc' == $type) {
                                            echo 'block';
                                                    } else {
                                                        echo 'none';
                                                    } ?>">

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="psc_site"><?php echo __('Numéro du site sur Paysite-Cash'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-large" id="psc_site" name="psc_site" type="number"
                                                   min="0" value="<?php echo $psc_site; ?>"/>
                                            <span class="help-block"><a
                                                        href="<?php echo \Learnybox\Services\Help\HelpService::getHelpUrlWithQuestion('Paysite-Cash'); ?>"
                                                        target="_blank"><i
                                                            class="fa fa-external-link"></i> <?php echo __('Aide sur la configuration de Paysite-Cash'); ?></a></span>
                                        </div>
                                    </div>
                                </div>

                                <div id="payboxConfiguration" class="configuration"
                                     style="display:<?php echo (isset($type) and 'paybox' == $type) ? 'block' : 'none'; ?>">

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="pbx_secretKey"><?php echo __('Clé secrète de votre compte PayBox'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xxlarge" id="pbx_secretKey" name="pbx_secretKey"
                                                   type="text"
                                                   value="<?php echo isset($paybox_config['pbx_secretKey']) ? $paybox_config['pbx_secretKey'] : ''; ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label" for="pbx_site"><?php echo __('Site'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xxlarge" id="pbx_site" name="pbx_site" type="text"
                                                   value="<?php echo isset($paybox_config['pbx_site']) ? $paybox_config['pbx_site'] : ''; ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label" for="pbx_rang"><?php echo __('Rang'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xxlarge" id="pbx_rang" name="pbx_rang" type="text"
                                                   value="<?php echo isset($paybox_config['pbx_rang']) ? $paybox_config['pbx_rang'] : ''; ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="pbx_identifiant"><?php echo __('Identifiant'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xxlarge" id="pbx_identifiant"
                                                   name="pbx_identifiant" type="text"
                                                   value="<?php echo isset($paybox_config['pbx_identifiant']) ? $paybox_config['pbx_identifiant'] : ''; ?>"/>
                                            <span class="help-block"><a
                                                        href="<?php echo \Learnybox\Services\Help\HelpService::getHelpUrlWithQuestion('Paybox'); ?>"
                                                        target="_blank"><i
                                                            class="fa fa-external-link"></i> <?php echo __('Aide sur la configuration de PayBox'); ?></a></span>
                                        </div>
                                    </div>

                                </div>


                                <div id="stripeConfiguration" class="configuration"
                                     style="display:<?php echo (isset($type) and 'stripe' == $type) ? 'block' : 'none'; ?>">

                                    <?php
                                    $selectStripeAccount = \Learnybox\Services\Stripe\StripeAccountsService::generateSelectAccounts($checkout['stripe_account_id'] ?? 0);
                                    if (!$selectStripeAccount) {
                                        echo '<div class="alert alert-info">' . __('Avant de pouvoir utiliser Stripe comme moyen de paiement, vous devez le configurer en vous rendant sur cette page :') . '<br><a class="btn btn-default" href="' . Tools::makeLink('app', 'integration', 'stripe') . '">' . __('Configurer Stripe') . '</a></div>';
                                    } else {
                                        ?>
                                        <div class="form-group">
                                            <label class="control-label" for="stripe_account_id"><?php echo __('Compte Stripe'); ?></label>
                                            <div class="controls">
                                                <select class="form-control" id="stripe_account_id" name="stripe_account_id" data-rel="select2">
                                                    <?php echo $selectStripeAccount; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <?php
                                    } ?>

                                </div>


                                <div id="clickbankConfiguration" class="configuration"
                                     style="display:<?php echo (isset($type) and 'clickbank' == $type) ? 'block' : 'none'; ?>">

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="clickbank_account"><?php echo __('Votre nom d\'utilisateur ClickBank'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xlarge" id="clickbank_account"
                                                   name="clickbank_account" type="text"
                                                   value="<?php echo $clickbank_account; ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="clickbank_secret_key"><?php echo __('Clé secrète de votre compte ClickBank'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xlarge" id="clickbank_secret_key"
                                                   name="clickbank_secret_key" type="text"
                                                   value="<?php echo $clickbank_secret_key; ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="clickbank_id_product"><?php echo __('Numéro de votre produit'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xlarge" id="clickbank_id_product"
                                                   name="clickbank_id_product" type="text"
                                                   value="<?php echo isset($checkout['clickbank_id_product']) ? $checkout['clickbank_id_product'] : ''; ?>"/>
                                            <span class="help-block"><a
                                                        href="<?php echo \Learnybox\Services\Help\HelpService::getHelpUrlWithQuestion('ClickBank'); ?>"
                                                        target="_blank"><i
                                                            class="fa fa-external-link"></i> <?php echo __('Aide sur la configuration de ClickBank'); ?></a></span>
                                        </div>
                                    </div>

                                </div>


                                <div id="payplugConfiguration" class="configuration"
                                     style="display:<?php echo (isset($type) and 'payplug' == $type) ? 'block' : 'none'; ?>">
                                    <div class="form-group">
                                        <label class="control-label"
                                               for="payplug_secret_key"><?php echo __('Votre clé secrète PayPlug'); ?> *</label>
                                        <div class="controls">
                                            <input class="form-control input-xxlarge" id="payplug_secret_key"
                                                   name="payplug_secret_key" type="text"
                                                   value="<?php echo $payplugSecretKey; ?>" placeholder="sk_live_"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="payplug_public_key"><?php echo __('Votre clé publique PayPlug'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xxlarge" id="payplug_public_key"
                                                   name="payplug_public_key" type="text"
                                                   value="<?php echo $payplugPublicKey; ?>" placeholder="pk_live_"/>
                                            <span class="help-block">
                                        <a href="<?php echo \Learnybox\Services\Help\HelpService::getHelpUrlWithQuestion('payplug'); ?>"
                                           target="_blank">
                                            <i class="fa fa-external-link"></i> <?php echo __('Aide sur la configuration de PayPlug'); ?>
                                        </a>
                                    </span>
                                        </div>
                                    </div>
                                </div>


                                <div id="sapConfiguration" class="configuration"
                                     style="display:<?php echo (isset($type) and 'sap' == $type) ? 'block' : 'none'; ?>">

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="sap_key"><?php echo __('Clé secrète de votre compte SecureAndPay'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xxlarge" id="sap_key" name="sap_key" type="text"
                                                   value="<?php echo isset($sap_config['key']) ? $sap_config['key'] : ''; ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="sap_id_site"><?php echo __('Numéro de site'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-small" id="sap_id_site" name="sap_id_site"
                                                   type="number"
                                                   value="<?php echo isset($sap_config['id_site']) ? $sap_config['id_site'] : ''; ?>"/>
                                        </div>
                                    </div>
                                    <span class="help-block"><a
                                                href="<?php echo \Learnybox\Services\Help\HelpService::getHelpUrlWithQuestion('SecureAndPay'); ?>"
                                                target="_blank"><i
                                                    class="fa fa-external-link"></i> <?php echo __('Aide sur la configuration de SecureAndPay'); ?></a></span>

                                </div>


                                <div id="gocardlessConfiguration" class="configuration"
                                     style="display:<?php if (isset($type) and 'gocardless' == $type) {
                                            echo 'block';
                                                    } else {
                                                        echo 'none';
                                                    } ?>">

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="gocardless_token"><?php echo __('Token de votre compte GoCardLess'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xlarge" id="gocardless_token"
                                                   name="gocardless_token" type="text"
                                                   value="<?php echo $gocardless_token; ?>"/>
                                            <span class="help-block"><i
                                                        class="fa fa-warning"></i> <?php echo __('Avec GoCardLess, tout est automatisé sur') . ' ' . LB . ' ' . __('mais assurez-vous de bien avoir créé un "webhook" sur votre compte GoCardLess'); ?> (<a
                                                        href="<?php echo \Learnybox\Services\Help\HelpService::getHelpUrlWithQuestion('GoCardLess'); ?>"
                                                        target="_blank"><i
                                                            class="fa fa-external-link"></i> <?php echo __('En savoir plus'); ?>s</a>)</span>

                                            <?php
                                            if (!$gocardless_webhook_token) {
                                                echo '<div class="alert alert-danger">' . __('Attention : nous n\'avons trouvé aucun "webook" configuré actuellement. Assurez-vous de bien avoir créé un "webhook" sur votre compte GoCardLess') . ' (<a href="' . \Learnybox\Services\Help\HelpService::getHelpUrlWithQuestion('GoCardLess') . '" target="_blank"><i class="fa fa-external-link"></i> ' . __('En savoir plus') . '</a>)</div>';
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>


                                <div id="braintreeConfiguration" class="configuration"
                                     style="display:<?php if (isset($type) and 'braintree' == $type) {
                                            echo 'block';
                                                    } else {
                                                        echo 'none';
                                                    } ?>">

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="braintree_merchantId"><?php echo __('Identifiant commerçant Braintree'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xlarge" id="braintree_merchantId"
                                                   name="braintree_merchantId" type="text"
                                                   value="<?php echo isset($braintree_merchantId) ? $braintree_merchantId : ''; ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="braintree_publicKey"><?php echo __('Clé publique de votre compte Braintree'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xlarge" id="braintree_publicKey"
                                                   name="braintree_publicKey" type="text"
                                                   value="<?php echo isset($braintree_publicKey) ? $braintree_publicKey : ''; ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="braintree_privateKey"><?php echo __('Clé secrète de votre compte Braintree'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xlarge" id="braintree_privateKey"
                                                   name="braintree_privateKey" type="text"
                                                   value="<?php echo isset($braintree_privateKey) ? $braintree_privateKey : ''; ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="braintree_display_paypal"><?php echo __('Afficher Paypal sur la page de commande'); ?></label>
                                        <div class="checkbox">
                                            <label for="braintree_display_paypal">
                                                <input type="checkbox" id="braintree_display_paypal"
                                                       name="braintree_display_paypal" <?php if (isset($braintree_display_paypal) and $braintree_display_paypal) {
                                                            echo 'checked';
                                                                                       } ?>> <?php echo __('Permet de laisser la possibilité à vos clients de régler par Paypal en 1 clic. Notez que cette option n\'est disponible que si votre compte Braintree est relié à un compte Paypal.'); ?>
                                            </label>
                                        </div>
                                    </div>

                                    <span class="help-block"><a
                                                href="<?php echo \Learnybox\Services\Help\HelpService::getHelpUrlWithQuestion('Braintree'); ?>"
                                                target="_blank"><i
                                                    class="fa fa-external-link"></i> <?php echo __('Aide sur la configuration de Braintree'); ?></a></span>

                                </div>

                                <div id="checkoutcomConfiguration" class="configuration"
                                     style="display:<?php if (isset($type) and 'checkoutcom' == $type) {
                                            echo 'block';
                                                    } else {
                                                        echo 'none';
                                                    } ?>">

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="checkoutcom_secret_key"><?php echo __('Clé secrète de votre compte Checkout.com'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xlarge" id="checkoutcom_secret_key"
                                                   name="checkoutcom_secret_key" type="text"
                                                   value="<?php echo isset($checkoutcom_secret_key) ? $checkoutcom_secret_key : ''; ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label"
                                            for="checkoutcom_public_key"><?php echo __('Clé publique de votre compte Checkout.com'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xlarge" id="checkoutcom_public_key"
                                                name="checkoutcom_public_key" type="text"
                                                value="<?php echo isset($checkoutcom_public_key) ? $checkoutcom_public_key : ''; ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label"
                                            for="checkoutcom_processing_channel_id"><?php echo __('Processing channel id de votre compte Checkout.com'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xlarge" id="checkoutcom_processing_channel_id"
                                                name="checkoutcom_processing_channel_id" type="text"
                                                value="<?php echo isset($checkoutcom_processing_channel_id) ? $checkoutcom_processing_channel_id : ''; ?>"/>
                                        </div>
                                    </div>

                                    <span class="help-block"><a
                                                href="<?php echo \Learnybox\Services\Help\HelpService::getHelpUrlWithQuestion('Checkout'); ?>"
                                                target="_blank"><i
                                                    class="fa fa-external-link"></i> <?php echo __('Aide sur la configuration de Checkout.com'); ?></a></span>

                                </div>

                                <div id="centralpayConfiguration" class="configuration"
                                     style="display:<?php echo (isset($type) and 'centralpay' == $type) ? 'block' : 'none'; ?>">
                                    <?php if (!$centralpay_api_id) : ?>
                                        <div class="alert alert-info"><?php echo __('Avant de pouvoir utiliser CentralPay comme moyen de paiement, vous devez le configurer en vous rendant sur cette page :'); ?>
                                            <br><a class="btn btn-default"
                                                   href="<?php echo Tools::makeLink('app', 'integration', 'centralpay'); ?>"><?php echo __('Configurer CentralPay'); ?></a>
                                        </div>
                                    <?php else : ?>
                                        <div class="form-group">
                                            <label class="control-label"
                                                   for="centralpay_api_id"><?php echo __('Identifiant API'); ?> *</label>
                                            <div class="controls">
                                                <input class="form-control input" name="centralpay_api_id"
                                                       id="centralpay_api_id" type="text"
                                                       value="<?php echo $centralpay_api_id; ?>" required="required">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label"
                                                   for="centralpay_api_password"><?php echo __('Mot de passe API'); ?> *</label>
                                            <div class="controls">
                                                <input class="form-control input" name="centralpay_api_password"
                                                       id="centralpay_api_password" type="text"
                                                       value="<?php echo $centralpay_api_password; ?>" required="required">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label"
                                                   for="centralpay_merchant_public_key"><?php echo __('Clé publique commerçant'); ?>
                                                *</label>
                                            <div class="controls">
                                                <input class="form-control input" name="centralpay_merchant_public_key"
                                                       id="centralpay_merchant_public_key" type="text"
                                                       value="<?php echo $centralpay_merchant_public_key; ?>"
                                                       required="required">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label"
                                                   for="centralpay_merchant_country"><?php echo __('Code ISO pays du commerçant'); ?>
                                                *</label>
                                            <div class="controls">
                                                <input class="form-control input-small" name="centralpay_merchant_country"
                                                       id="centralpay_merchant_country" type="text"
                                                       value="<?php echo $centralpay_merchant_country; ?>" required="required">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label"
                                                   for="centralpay_point_of_sale_id"><?php echo __('Point de vente'); ?>
                                                *</label>
                                            <div class="controls">
                                                <input class="form-control input" name="centralpay_point_of_sale_id"
                                                       id="centralpay_point_of_sale_id" type="text"
                                                       value="<?php echo $centralpay_point_of_sale_id; ?>" required="required">
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div id="mipsConfiguration" class="configuration"
                                     style="display:<?php echo (isset($type) and 'mips' == $type) ? 'block' : 'none'; ?>">

                                    <?php
                                    if (!$mips_merchant_id) {
                                        echo '<div class="alert alert-info">' . __('Avant de pouvoir utiliser MIPS comme moyen de paiement, vous devez le configurer en vous rendant sur cette page :') . '<br><a class="btn btn-default" href="' . Tools::makeLink('app', 'integration', 'mips') . '">' . __('Configurer MIPS') . '</a></div>';
                                    } else {
                                        ?>
                                        <div class="form-group">
                                            <label class="control-label" for="mips_merchant_id"><?php echo __('Id marchand'); ?> *</label>
                                            <div class="controls">
                                                <input class="form-control input" name="mips_merchant_id" id="mips_merchant_id" type="text" value="<?php echo $mips_merchant_id; ?>" required="required">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label" for="mips_entity_id"><?php echo __('Id entité'); ?> *</label>
                                            <div class="controls">
                                                <input class="form-control input" name="mips_entity_id" id="mips_entity_id" type="text" value="<?php echo $mips_entity_id; ?>" required="required">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label" for="mips_operator_id"><?php echo __('Id opérateur'); ?> *</label>
                                            <div class="controls">
                                                <input class="form-control input" name="mips_operator_id" id="mips_operator_id" type="text" value="<?php echo $mips_operator_id; ?>" required="required">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label" for="mips_operator_password"><?php echo __('Mot de passe opérateur'); ?> *</label>
                                            <div class="controls">
                                                <input class="form-control input" name="mips_operator_password" id="mips_operator_password" type="text" value="<?php echo $mips_operator_password; ?>" required="required">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label" for="mips_username"><?php echo __('Nom d\'utilisateur'); ?> *</label>
                                            <div class="controls">
                                                <input class="form-control input" name="mips_username" id="mips_username" type="text" value="<?php echo $mips_username; ?>" required="required">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label" for="mips_password"><?php echo __('Mot de passe utilisateur'); ?> *</label>
                                            <div class="controls">
                                                <input class="form-control input" name="mips_password" id="mips_password" type="text" value="<?php echo $mips_password; ?>" required="required">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label" for="mips_salt"><?php echo __('Salt'); ?> *</label>
                                            <div class="controls">
                                                <input class="form-control input" name="mips_salt" id="mips_salt" type="text" value="<?php echo $mips_salt; ?>" required="required">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label class="control-label" for="mips_cipher_key"><?php echo __('Clé de chiffrement'); ?> *</label>
                                            <div class="controls">
                                                <input class="form-control input" name="mips_cipher_key" id="mips_cipher_key" type="text" value="<?php echo $mips_cipher_key; ?>" required="required">
                                            </div>
                                        </div>
                                    <?php } ?>
                                </div>

                                <div class="form-group" id="DeviseBlock" style="display:<?php if (isset($type) and $type) {
                                    echo 'block';
                                                                                        } else {
                                                                                            echo 'none';
                                                                                        } ?>">
                                    <label class="control-label"><?php echo __('Devise'); ?></label>
                                    <div class="controls">
                                        <?php
                                        echo $paymentMethodCurrencies !== '' ? $paymentMethodCurrencies->renderCurrencySelect($type, $mainCurrency) : '';
                                        ?>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>


                    <div class="panel panel-default">
                        <div class="panel-heading" role="tab">
                            <a role="button" data-toggle="collapse" href="#collapseProduit"
                               aria-expanded="true">
                                <strong><?php echo __('Configuration du produit'); ?></strong><br>
                                <span><?php echo __('Paiement en 1 fois ou plusieurs fois, facilités de paiement, période d\'essai'); ?></span>
                            </a>
                        </div>

                        <div id="collapseProduit" class="panel-collapse collapse" role="tabpanel">
                            <div class="panel-body">

                                <div class="form-group">
                                    <label class="control-label" for="product_name"><?php echo __('Nom du produit'); ?></label>
                                    <span
                                            class="help-block"><?php echo __('Vous pouvez indiquer ici le nom du produit qui apparaîtra sur la page de paiement. Si ce champ est vide, le nom de la formation apparaîtra, et si aucune formation n\'est sélectionnée, ce sera le nom de votre compte'); ?> <?php echo LB; ?> (<?php echo SITE_NAME; ?>).</span>
                                    <div class="controls">
                                        <input class="form-control input-xxlarge" id="product_name" name="product_name"
                                               type="text"
                                               value="<?php echo isset($checkout['product_name']) ? $checkout['product_name'] : ''; ?>"/>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="control-label" for="amount"><?php echo __('Montant'); ?></label>
                                    <span class="help-block"><?php echo __('Entrez le montant total pour un paiement en 1 fois, ou le montant de la récurrence pour un paiement en plusieurs fois.'); ?></span>
                                    <div class="input-group">
                                        <input class="form-control input-small" id="amount" name="amount" type="number" min="0"
                                               step="any"
                                               value="<?php echo isset($checkout['amount']) ? $checkout['amount'] : '0.00'; ?>"/>
                                        <span class="input-group-addon"><?php echo \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol($mainCurrency) ?></span>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="control-label"><?php echo __('Type de paiement'); ?></label>
                                    <div class="controls">
                                        <div class="checkbox">
                                            <label for="payment1">
                                                <input type="radio" name="payment" id="payment1"
                                                       onchange="$('#PaymentSubscription').hide();"
                                                       value="onetime" <?php echo (isset($checkout['payment']) and 'onetime' == $checkout['payment']) ? 'checked' : ''; ?> /> <?php echo __('Paiement en 1 fois'); ?>
                                            </label>
                                        </div>
                                        <div class="checkbox">
                                            <label for="payment2">
                                                <input type="radio" name="payment" id="payment2"
                                                       onchange="$('#PaymentSubscription').show();"
                                                       value="subscription" <?php echo (isset($checkout['payment']) and 'subscription' == $checkout['payment']) ? 'checked' : ''; ?> /> <?php echo __('Paiement en plusieurs fois'); ?>
                                            </label>
                                        </div>
                                    </div>
                                </div>


                                <div id="PaymentSubscription"
                                     style="display:<?php echo (isset($checkout['payment']) and 'subscription' == $checkout['payment']) ? 'block' : 'none'; ?>">

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="nb_payments"><?php echo __('Nombre de paiements'); ?></label>
                                        <span class="help-block"><?php echo __('Entrez "x" pour illimité.'); ?></span>
                                        <div class="controls">
                                            <input class="form-control input-small" id="nb_payments" name="nb_payments"
                                                   type="text"
                                                   value="<?php echo isset($checkout['nb_payments']) ? $checkout['nb_payments'] : '1'; ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label"
                                               for="subscription_time"><?php echo __('Tous les '); ?></label>
                                        <div class="form-inline">
                                            <div class="form-group">
                                                <input class="form-control input-small" id="subscription_time"
                                                       name="subscription_time" type="number" min="0"
                                                       value="<?php echo isset($checkout['subscription_time']) ? $checkout['subscription_time'] : '1'; ?>"/>
                                            </div>
                                            <div class="form-group">
                                                <select name="subscription_timetype" style="max-width: 100px">
                                                    <option value="jour" <?php echo (isset($checkout['subscription_timetype']) and 'jour' == $checkout['subscription_timetype']) ? 'selected' : ''; ?>><?php echo __('jours'); ?></option>
                                                    <option value="mois" <?php echo (isset($checkout['subscription_timetype']) and 'mois' == $checkout['subscription_timetype']) ? 'selected' : ''; ?>><?php echo __('mois'); ?></option>
                                                </select>
                                            </div>
                                        </div>
                                        <span class="text-danger paybox"
                                              style="display:<?php echo (isset($checkout['type']) and 'paybox' == $checkout['type']) ? 'block' : 'none'; ?>"><i
                                                    class="fa fa-warning"></i> <?php echo __('Avec PayBox, les prélèvements ne peuvent être espacés que par des mois, et non des jours.'); ?></span>
                                    </div>

                                </div>


                                <div class="form-group" id="TrialBlock" style="margin-bottom: 0px">
                                    <div class="form-group">
                                        <label class="control-label"><?php echo __('Période d\'essai'); ?></label>
                                        <div class="controls">
                                            <div class="checkbox">
                                                <div class="row">
                                                    <label for="trial_period">
                                                        <input type="checkbox" name="trial_period" id="trial_period"
                                                               onchange="$('#Trial').toggle();"
                                                               value="oui" <?php echo (isset($checkout['trial_period']) and $checkout['trial_period']) ? 'checked' : ''; ?> />
                                                        <?php echo __('Cochez cette case pour mettre en place une période d\'essai.') ?>
                                                    </label>
                                                </div>
                                                <div class="row p-l-28">
                                                    <small><em><?php echo __('Par exemple : 7 %s le premier mois, puis 47 %s les mois suivants.', \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol(), \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol()); ?></em></small>
                                                    <br><span class="text-danger">
                                                    <small>
                                                        <?php echo __('Attention : le montant défini dans la période d\'essai s\'ajoutera au montant total. Par exemple, si votre produit est à 100%s et que vous activez une période d\'essai de 10%s le premier mois, le client règlera 10%s maintenant, puis 100%s dans un mois.', \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol(), \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol(), \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol(), \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol()); ?>
                                                    </small>
                                                </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div id="Trial"
                                     style="display:<?php echo (isset($checkout['trial_period']) and $checkout['trial_period']) ? 'block' : 'none'; ?>">

                                    <div class="form-group">
                                        <label class="control-label" for="trial_amount"><?php echo __('Montant'); ?></label>
                                        <div class="input-group">
                                            <input class="form-control input-small" id="trial_amount" name="trial_amount"
                                                   type="number" min="0" step="any"
                                                   value="<?php echo isset($checkout['trial_amount']) ? $checkout['trial_amount'] : '0.00'; ?>"/>
                                            <span class="input-group-addon"><?php echo \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol($mainCurrency) ?></span>
                                        </div>
                                        <span class="text-danger paybox"
                                              style="display:<?php echo (isset($checkout['type']) and 'paybox' == $checkout['type']) ? 'block' : 'none'; ?>"><i
                                                    class="fa fa-warning"></i> <?php echo __('Avec PayBox, le montant de la période d\'essai est obligatoirement à 0.'); ?></span>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label" for="trial_time"><?php echo __('Durée'); ?></label>
                                        <div class="controls">
                                            <div class="form-inline">
                                                <div class="form-group">
                                                    <input class="form-control input-small" id="trial_time" name="trial_time"
                                                           type="number" min="0"
                                                           value="<?php echo isset($checkout['trial_time']) ? $checkout['trial_time'] : '1'; ?>"/>
                                                </div>
                                                <div class="form-group">
                                                    <select name="trial_timetype" style="max-width: 100px;">
                                                        <option value="jour" <?php echo (isset($checkout['trial_timetype']) and 'jour' == $checkout['trial_timetype']) ? 'selected' : ''; ?>><?php echo __('jours'); ?></option>
                                                        <option value="mois" <?php echo (isset($checkout['trial_timetype']) and 'mois' == $checkout['trial_timetype']) ? 'selected' : ''; ?>><?php echo __('mois'); ?></option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>


                                <div class="form-group">
                                    <label class="control-label"
                                           for="redirection"><?php echo __('Redirection après un paiement validé'); ?></label>
                                    <div class="controls">
                                        <select name="redirection" data-rel="select2" id="redirection">
                                            <?php echo $selectRedirectionPage; ?>
                                        </select>
                                        <br><br>
                                        <input class="form-control input-xxlarge" name="redirection_url" type="url"
                                               value="<?php echo (isset($checkout['payment_return']) and filter_var($checkout['payment_return'], FILTER_VALIDATE_URL)) ? $checkout['payment_return'] : ''; ?>"
                                               placeholder="http://">
                                        <span
                                                class="help-block"><small><?php echo __('Notez que si aucune page n\'est définie, votre client sera redirigé sur une page de confirmation d\'inscription') . '<br>' . __('l\'apparence de cette page est la même que celle de votre site ou blog configurés sur'); ?> <?php echo LB; ?>).</small></span>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>


                    <div class="panel panel-default">
                        <div class="panel-heading" role="tab">
                            <a role="button" data-toggle="collapse" href="#collapseFormation"
                               aria-expanded="true">
                                <strong><?php echo __('Accès à l\'achat de ce produit'); ?></strong><br>
                                <span><?php echo __('Ouvrez l\'accès à une de vos formations, ou inscrivez vos clients dans un webinaire live'); ?></span>
                            </a>
                        </div>

                        <div id="collapseFormation" class="panel-collapse collapse" role="tabpanel">
                            <div class="panel-body">

                                <div class="form-group" id="groupformation">
                                    <label class="control-label" for="id_formation"><?php echo __('Formation'); ?></label>
                                    <div class="controls">
                                        <select name="id_formation" id="id_formation" onchange="ShowGroupes(this.value);" data-rel="select2" class="select-w-300">
                                            <?php echo $selectFormation; ?>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group" id="groupes" style="display:<?php if ($id_formation) {
                                    echo 'block';
                                                                                    } else {
                                                                                        echo 'none';
                                                                                    } ?>">
                                    <label class="control-label" for="idgroupe"><?php echo __('Groupe'); ?></label>
                                    <div class="controls">
                                        <select name="idgroupe" id="idgroupe" data-rel="select2" class="select-w-300">
                                            <?php echo $selectGroupe; ?>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group" id="groupconference">
                                    <label class="control-label" for="id_conference"><?php echo __('Webinaire live'); ?></label>
                                    <div class="controls">
                                        <select name="id_conference" id="id_conference" data-rel="select2" class="select-w-300">
                                            <?php echo $selectConference; ?>
                                        </select>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                </div>


                <div class="panel panel-default">
                    <div class="panel-heading" role="tab">
                        <a role="button" data-toggle="collapse" href="#collapseBouton"
                           aria-expanded="true">
                            <strong><?php echo __('Configuration du bouton'); ?></strong><br>
                            <span><?php echo __('Choisissez une image ou configurez l\'apparence du bouton de paiement'); ?></span>
                        </a>
                    </div>

                    <div id="collapseBouton" class="panel-collapse collapse" role="tabpanel">
                        <div class="panel-body">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group" style="margin-top: 20px;">
                                        <label class="control-label"><?php echo __('Alignement du bouton'); ?></label>
                                        <div class="controls">
                                            <div class="checkbox">
                                                <label for="align1">
                                                    <input type="radio" name="align" id="align1"
                                                           value="left" <?php echo ($align and 'left' == $align) ? 'checked' : ''; ?> /> <?php echo __('Aligné à gauche'); ?>
                                                </label>
                                            </div>
                                            <div class="checkbox">
                                                <label for="align2">
                                                    <input type="radio" name="align" id="align2"
                                                           value="center" <?php echo ($align and 'center' == $align) ? 'checked' : ''; ?> /> <?php echo __('Aligné au centre'); ?>
                                                </label>
                                            </div>
                                            <div class="checkbox">
                                                <label for="align3">
                                                    <input type="radio" name="align" id="align3"
                                                           value="right" <?php echo ($align and 'right' == $align) ? 'checked' : ''; ?> /> <?php echo __('Aligné à droite'); ?>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <hr>
                                    <h3><?php echo __('Configurez votre bouton ci-dessous'); ?> :</h3>

                                    <div class="form-group">
                                        <label class="control-label" for="texte"><?php echo __('Texte du bouton'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-xxlarge" id="texte" name="texte" type="text"
                                                   value="<?php if (isset($design['texte'])) {
                                                        echo $design['texte'];
                                                          } else {
                                                              echo 'Valider';
                                                          } ?>"/>
                                        </div>
                                    </div>

                                    <div class="form-group hidden-phone">
                                        <label class="control-label"
                                               for="colorSelector1"><?php echo __('Couleur du bouton'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-small" type="text" name="color" maxlength="7"
                                                   id="colorSelector1" value="<?php if (isset($design['color'])) {
                                                        echo $design['color'];
                                                                              } ?>">
                                        </div>
                                    </div>

                                    <div class="form-group hidden-phone">
                                        <label class="control-label"
                                               for="colorSelector2"><?php echo __('Couleur du texte'); ?></label>
                                        <div class="controls">
                                            <input class="form-control input-small" type="text" name="color_text" maxlength="7"
                                                   id="colorSelector2" value="<?php if (isset($design['color_text'])) {
                                                        echo $design['color_text'];
                                                                              } ?>">
                                        </div>
                                    </div>

                                    <div class="form-group" style="margin-top: 20px;">
                                        <label class="control-label"><?php echo __('Taille du bouton'); ?></label>
                                        <div class="controls">
                                            <div class="checkbox">
                                                <label for="size1">
                                                    <input type="radio" name="size" id="size1"
                                                           value="btn-small" <?php echo ($size and 'btn-small' == $size) ? 'checked' : ''; ?> /> <?php echo __('Petit'); ?>
                                                </label>
                                            </div>
                                            <div class="checkbox">
                                                <label for="size2">
                                                    <input type="radio" name="size" id="size2"
                                                           value="btn" <?php echo ($size and 'btn' == $size) ? 'checked' : ''; ?> /> <?php echo __('Moyen'); ?>
                                                </label>
                                            </div>
                                            <div class="checkbox">
                                                <label for="size3">
                                                    <input type="radio" name="size" id="size3"
                                                           value="btn-lg" <?php echo ($size and 'btn-lg' == $size) ? 'checked' : ''; ?> /> <?php echo __('Grand'); ?>
                                                </label>
                                            </div>
                                            <div class="checkbox">
                                                <label for="size4">
                                                    <input type="radio" name="size" id="size4"
                                                           value="btn-xlarge" <?php echo ($size and 'btn-xlarge' == $size) ? 'checked' : ''; ?> /> <?php echo __('Très grand'); ?>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="control-label" for="icone"><?php echo __('Icône'); ?></label>
                                        <div class="controls">
                                            <select name="icone" id="selectIcone" data-rel="select2-icon" style="width:300px;">
                                                <?php echo $selectIcone; ?>
                                            </select>
                                        </div>
                                    </div>

                                    <hr>
                                    <h3><?php echo __('Ou choisissez une image ci-dessous...'); ?></h3>

                                    <div class="form-group">

                                        <?php
                                        if (isset($design['image']) and $design['image']) {
                                            echo '
                                        <div class="preview" id="preview-image">
                                            <img class="img-thumbnail" src="' . $design['image'] . '" width="100"  />
                                            <i class="fa fa-times" onclick="$(\'#image\').val(\'\'); $(\'#preview-image\').remove();"></i>
                                        </div>';
                                        }
                                        ?>

                                        <label class="control-label" for="image"><?php echo __('Image du bouton'); ?></label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input class="form-control input-xxlarge" id="bgimage" name="image" type="text"
                                                       value="<?php if (isset($design['image'])) {
                                                            echo $design['image'];
                                                              } ?>"/>
                                                <span class="input-group-btn"><button class="btn btn-default" type="button"
                                                                                      id="file"
                                                                                      onClick="return selectFileWithCKFinder('bgimage');"><?php echo __('Parcourir'); ?></button></span>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="form-group">
                                        <label class="control-label"
                                               for="largeur"><?php echo __('Largeur/Hauteur (facultatif, en pixels)'); ?></label>
                                        <div class="controls">
                                            <div class="form-inline">
                                                <div class="form-group">
                                                    <input class="form-control input-small" name="largeur" id="largeur"
                                                           type="number" min="0" value="<?php if (isset($design['largeur'])) {
                                                                echo $design['largeur'];
                                                                                        } ?>" <?php if ($ratio) {
                    echo 'onchange="CalcHeight(' . $ratio . ');"';
                                                                                        } ?>>
                                                </div>
                                                <div class="form-group">
                                                    <input class="form-control input-small" name="hauteur" id="hauteur"
                                                           type="number" min="0" value="<?php if (isset($design['hauteur'])) {
                                                                echo $design['hauteur'];
                                                                                        } ?>">
                                                </div>

                                                <?php
                                                if ($ratio) {
                                                    echo '
                                                <div class="btn-group" style="margin-left:20px">
                                                    <a onclick="CalcHeight(' . $ratio . ', ' . round($design['largeur'] * 0.25, 0) . ');" class="btn btn-default">-75%</a>
                                                    <a onclick="CalcHeight(' . $ratio . ', ' . round($design['largeur'] * 0.5, 0) . ');" class="btn btn-default">-50%</a>
                                                    <a onclick="CalcHeight(' . $ratio . ', ' . round($design['largeur'] * 0.75, 0) . ');" class="btn btn-default">-25%</a>
                                                    <a onclick="CalcHeight(' . $ratio . ', ' . $design['largeur'] . ');" class="btn btn-default" style="font-weight:bold">100%</a>
                                                    <a onclick="CalcHeight(' . $ratio . ', ' . round($design['largeur'] * 1.25, 0) . ');" class="btn btn-default">+25%</a>
                                                    <a onclick="CalcHeight(' . $ratio . ', ' . round($design['largeur'] * 1.5, 0) . ');" class="btn btn-default">+50%</a>
                                                    <a onclick="CalcHeight(' . $ratio . ', ' . round($design['largeur'] * 1.75, 0) . ');" class="btn btn-default">+75%</a>
                                                    <a onclick="CalcHeight(' . $ratio . ', ' . round($design['largeur'] * 2, 0) . ');" class="btn btn-default">+100%</a>
                                                </div>';
                                                }
                                                ?>

                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="col-md-6">
                                    <h3 class="panel-title"><?php echo __('Aperçu'); ?></h3>
                                    <hr>
                                    <div id="preview">
                                        <?php echo $preview; ?>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                [[CSRF]]
                <input type="hidden" name="form_action" id="form_action" value="<?php echo $action; ?>"/>
                <input type="hidden" name="id_offre" value="<?php echo $id_offre; ?>"/>
                <input type="hidden" name="id_current_conference" value="<?php echo $conference['id_conference']; ?>"/>
                <button type="submit" name="submit" class="btn btn-primary btn-lg"><i
                            class="fa fa-arrow-right"></i> <?php echo __('Étape suivante'); ?></button>
                <a class="btn btn-default btn-lg"
                   href="<?php echo RouterHelper::generate('app_conference_offres', [
                       'conferenceId' => $conference['id_conference']
                   ]); ?>"><?php echo __('Annuler'); ?></a>
            </div>

        </form>
    </div>
</div>
