<?php

// clean post values
use Learnybox\Helpers\RouterHelper;

if (isset($cleaned_post) and $cleaned_post) {
    foreach ($cleaned_post as $key => $value) {
        if (!is_array($value)) {
            ${$key} = $value;
        }
    }
}

$redirect = RouterHelper::generate('app_ctutoriel_step', [
    'step' => 'step3'
]);

$presentateurs = [];
if (isset($cleaned_post['presentateurs'])) {
    $presentateurs = $cleaned_post['presentateurs'];
} elseif (isset($_SESSION['ctutoriel']['presentateurs'])) {
    $presentateurs = $_SESSION['ctutoriel']['presentateurs'];
}

$presentateurs_select = eden()->Webinar_Presentateurs()->generate_presentateurs_select(false, $presentateurs);
?>
<div class="tutoriel-content">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <form class="form" method="post" action="" id="form-timeline">
                        <div class="form-group">
                            <label class="control-label"
                                   for="selectPresentateurs"><?php echo __('Présentateurs'); ?></label>
                            <span class="help-block"><?php echo __('Choisissez le ou les présentateurs du webinaire live : cela affichera leur photo et leur nom sur les pages de votre webinaire live.'); ?></span>
                            <div class="controls">
                                <select id="selectPresentateurs" name="presentateurs[]" multiple data-rel="select2"
                                        style="width:90%;"
                                        data-placeholder="<?php echo __('Sélectionnez un présentateur'); ?>">
                                    <?php echo $presentateurs_select; ?>
                                </select>
                                <br>
                                <a class="btn btn-secondary" onclick="$('#CreatePresentateur').modal('show');"
                                   style="margin-top:10px;"><i
                                            class="fa fa-plus"></i> <?php echo __('Ajouter un présentateur'); ?></a>
                            </div>
                        </div>

                        [[CSRF]]
                        <input type="hidden" name="form_action" id="form_action" value="<?php echo $action; ?>"/>
                        <input type="hidden" name="step" value="<?php echo $step; ?>"/>
                        <input type="hidden" name="redirect" value="<?php echo $redirect; ?>"/>

                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
