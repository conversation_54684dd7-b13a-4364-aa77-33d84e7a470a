<?php

use Learnybox\Helpers\RouterHelper;

// clean post values
if (isset($cleaned_post) and $cleaned_post) {
    foreach ($cleaned_post as $key => $value) {
        if (!is_array($value)) {
            ${$key} = $value;
        }
    }
}

$redirect = RouterHelper::generate('app_conference_edit2', [
    'conferenceId' => $_SESSION['id_conference'],
    'slug' => 'parametres'
]);
$link = 'https://' . $_SESSION['client_uniqid'] . '.' . LB_COM . '/conference/';
if (defined('CUSTOM_DOMAINE') and CUSTOM_DOMAINE) {
    $link = CUSTOM_DOMAINE . 'conference/';
}

if (!isset($timezone)) {
    $timezone = SERVER_TIMEZONE;
    $getDefaultTimezone = eden()->Reglages()->appGetParametreByName('default_timezone');
    if ($getDefaultTimezone) {
        $timezone = $getDefaultTimezone['value'];
    }
}
$selectTimeZones = Tools::generateSelectTimeZone($timezone);
?>
<form class="form" method="post" action="">

    <div class="row">
        <div class="box col-md-9">

            <div class="form-group">
                <label class="control-label" for="nom"><?php echo __('Nom'); ?> *</label>
                <div class="controls">
                    <input class="form-control input-xxlarge" name="nom" id="nom" type="text"
                           value="<?php if (isset($nom)) {
                                echo $nom;
                                  } ?>" required/>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="uniqid"><?php echo __('Lien du webinaire live'); ?></label>
                <span class="help-block"><?php echo __('Définissez l\'adresse d\'accès à votre webinaire live.'); ?></span>
                <div class="controls">
                    <div class="input-group">
                        <span class="input-group-addon w-25"><?php echo(mb_strlen($link) > 35 ? mb_substr($link, 0, 35) . '...' : $link); ?></span>
                        <input class="form-control input" name="random_id" id="nom_uniqid" type="text"
                               value="<?php if (isset($random_id)) {
                                    echo $random_id;
                                      } ?>">
                    </div>
                    <span class="help-block"><small><?php echo __('Caractères autorisés : chiffres, lettres et le symbole \'-\'.'); ?></small></span>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label"><?php echo __('Date'); ?> *</label>
                <div class="form-inline">
                    <div class="input-group">
                        <div class="form-group">
                            <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                            <input class="form-control input-large" id="lw_datepicker" name="date_timezone" type="text"
                                   value="<?php if (isset($date_timezone) and '0000-00-00 00:00:00' != $date_timezone) {
                                        echo $date_timezone;
                                          } ?>" required/>
                        </div>
                    </div>
                    <div class="form-group">
                        <select id="timezone" name="timezone" data-rel="select2">
                            <?php echo $selectTimeZones; ?>
                        </select>
                    </div>
                </div>
            </div>

        </div>

        <div class="box col-md-3">
            <div class="panel panel-default bootcards-summary">
                <div class="panel-heading">
                    <h3 class="panel-title"><i class="fa fa-question-circle"></i> <?php echo __('Aide'); ?></h3>
                </div>
                <div class="panel-body">
                    <p><strong><?php echo __('Nom'); ?> : </strong><?php echo __('Entrez le nom du webinaire live'); ?>
                    </p>
                    <p><strong><?php echo __('Date'); ?>
                            : </strong><?php echo __('Choisissez la date et l\'heure du début du webinaire live'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <div class="form-actions">
        [[CSRF]]
        <input type="hidden" name="form_action" id="form_action" value="<?php echo $action; ?>"/>
        <input type="hidden" name="redirect" value="<?php echo $redirect; ?>"/>
        <button type="submit" class="btn btn-primary btn-lg"><i
                    class="fa fa-arrow-right"></i> <?php echo __('Etape suivante'); ?></button>
        <a class="btn btn-default btn-lg"
           href="<?php echo RouterHelper::generate('app_conferences'); ?>"><?php echo __('Annuler'); ?></a>
    </div>

</form>
