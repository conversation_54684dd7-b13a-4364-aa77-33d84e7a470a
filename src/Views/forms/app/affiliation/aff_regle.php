<?php

use Learnybox\Helpers\RouterHelper;

$fatal_error = '';
if (isset($id_regle) and $id_regle) {
    $id_regle = filter_var($id_regle, FILTER_VALIDATE_INT);
    if (!$id_regle) {
        $fatal_error = __('Erreur : impossible de récupérer la règle');
    } else {
        $regle = eden()->Aff_Regles()->getRegleById($id_regle);
        if (!$regle) {
            $fatal_error = __("Erreur : cette règle n'existe pas");
        } else {
            $id_regle = $regle['id_regle'];
            if (!isset($cleaned_post)) {
                $commission_condition = $regle['condition_regle'];
                $value = $regle['value'];
                $commission_action = $regle['action'];
                $commission = $regle['commission'];
            } else {
                $commission_condition = $regle['condition_regle'];
                $value = $regle['value'];
                $commission_action = $regle['action'];
                $commission = $regle['commission'];
            }
        }
    }
} else {
    if (!isset($_POST['commission_condition'])) {
        $commission_condition = '';
        $value = '';
        $commission_action = '';
        $commission = '';
    } else {
        $commission_condition = $cleaned_post['commission_condition'];

        if (isset($cleaned_post['nb_commissions']) and $cleaned_post['nb_commissions']) {
            $value = $cleaned_post['nb_commissions'];
        } elseif (isset($cleaned_post['nom_produit']) and $cleaned_post['nom_produit']) {
            $value = $cleaned_post['nom_produit'];
        } elseif (isset($cleaned_post['montant_transaction']) and $cleaned_post['montant_transaction']) {
            $value = $cleaned_post['montant_transaction'];
        } elseif (isset($cleaned_post['montant_transaction2']) and $cleaned_post['montant_transaction2']) {
            $value = $cleaned_post['montant_transaction2'];
        }

        if (isset($cleaned_post['commission_action'])) {
            $commission_action = $cleaned_post['commission_action'];
        }

        if ('nouveau_taux' == $commission_action) {
            $commission = $cleaned_post['taux_commission'];
        } elseif ('nouveau_montant' == $commission_action) {
            $commission = $cleaned_post['montant_commission'];
        }
    }
}

if ($fatal_error) {
    echo '
	    <div class="alert alert-danger">
			<h4 class="alert-heading">' . $fatal_error . '</h4>
			<a href="' . RouterHelper::generate('app_affiliation_regles', [
            'campaignId' => $id_campaign
        ]) . '"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a>
		</div>';

    return;
}

//devise
$aff_devise = DEFAULT_CURRENCY;
$param = eden()->Reglages()->appGetParametreByName('affilie_devise');
if ($param and $param['value']) {
    $aff_devise = $param['value'];
}

if (!isset($id_regle) or isset($id_regle) and !$id_regle) {
    $id_regle = '';
}
if (!isset($id_campaign) or isset($id_campaign) and !$id_campaign) {
    $id_campaign = '';
}
if (!isset($commission_action)) {
    $commission_action = '';
}
if (!isset($value)) {
    $value = '';
}

$selectPage = eden()->TunnelsPages()->generateSelectPagesByTunnel($_SESSION['campaign']['id_tunnel'], $value);

echo '
<div class="top-page-title">
    <h3>' . ($id_regle ? __('Modification d\'une règle') : __('Création d\'une règle')) . '</h3>
</div>';
?>

<div class="panel panel-default">
    <div class="panel-body">
        <form class="form" method="post" action="">

            <div class="form-group" style="margin-bottom: 0">
                <label class="control-label" for="condition1"><?php echo __('Condition'); ?> *</label>
            </div>

            <table class="table table-striped table-hovered" id="table_conditions">
                <tr>
                    <td>
                        <!-- Nombre de commissions -->
                        <div class="checkbox" style="margin: 0">
                            <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px;"
                                   for="condition1">
                                <input type="radio" name="commission_condition" id="condition1"
                                       value="nombre_commission" <?php if (!$commission_condition or 'nombre_commission' == $commission_condition) {
                                            echo ' checked';
                                                                 } ?> onchange="ToggleReglesCondition();"/> <?php echo __('Si un affilié à plus de '); ?>
                            </label>
                            <div class="pull-left">
                                <div class="input-group">
                                    <input class="form-control input-small" name="nb_commissions" id="nb_commissions"
                                           type="number" min="0" step="1"
                                           value="<?php if ('nombre_commission' == $commission_condition) {
                                                echo $value;
                                                  } else {
                                                      echo '0';
                                                  } ?>" <?php if ($commission_condition and 'nombre_commission' != $commission_condition) {
                                 echo ' disabled';
                                                  } ?> required>
                                    <span class="input-group-addon"><?php echo __('commissions'); ?></span>
                                </div>
                            </div>
                            <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px; padding-left: 10px;"
                                   for="condition1"><?php echo __('dans cette campagne'); ?></label>
                            <div style="clear:both"></div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <!-- Page de vente -->
                        <div class="checkbox" style="margin: 0">
                            <label class="radio-inline" for="condition2">
                                <input type="radio" name="commission_condition" id="condition2"
                                       value="page_de_vente" <?php if ('page_de_vente' == $commission_condition) {
                                            echo ' checked';
                                                             } ?> onchange="ToggleReglesCondition();"/> <?php echo __('Si la vente vient de la page'); ?>
                                &nbsp;&nbsp;
                                <select name="id_page" id="select_page" data-rel="select2">
                                    <?php echo $selectPage; ?>
                                </select>
                            </label>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <!-- Produit -->
                        <div class="checkbox" style="margin: 0">
                            <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px;"
                                   for="condition3">
                                <input type="radio" name="commission_condition" id="condition3"
                                       value="nom_produit" <?php if ('nom_produit' == $commission_condition) {
                                            echo ' checked';
                                                           } ?>
                                       onchange="ToggleReglesCondition();"/> <?php echo __('Si le nom du produit de la transaction est'); ?>
                            </label>
                            <div class="pull-left">
                                <input class="form-control input-xlarge" name="nom_produit" id="nom_produit" type="text"
                                       value="<?php if ('nom_produit' == $commission_condition) {
                                            echo $value;
                                              } ?>" <?php if ('nom_produit' != $commission_condition) {
                             echo ' disabled';
                                              } ?> required>
                            </div>
                            <div style="clear:both"></div>
                            <span class="small text-danger" style="margin-left: 30px;"><i
                                    class="fa fa-warning"></i> <?php echo __('Ne fonctionne pas pour les transactions comportant plusieurs produits.') . ' ' . LB . ' ' . __('enregistre les commissions par transaction et non par produit.'); ?></span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <!-- Montant -->
                        <div class="checkbox" style="margin: 0">
                            <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px;"
                                   for="condition4">
                                <input type="radio" name="commission_condition" id="condition4"
                                       value="montant_sup" <?php if ($commission_condition == 'montant_sup') {
                                            echo ' checked';
                                                           } ?>
                                       onchange="ToggleReglesCondition();"/> <?php echo __('Si le montant de la transaction est supérieur à'); ?>
                            </label>
                            <div class="pull-left">
                                <div class="input-group">
                                    <input class="form-control input-small" name="montant_transaction" id="montant_transaction"
                                           type="number" min="0" step="any"
                                           value="<?php if ($commission_condition == 'montant_sup') {
                                                echo $value;
                                                  } else {
                                                      echo '0';
                                                  } ?>" <?php if ($commission_condition != 'montant_sup') {
                                 echo ' disabled';
                                                  } ?>>
                                    <span class="input-group-addon"><?php echo \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol() ?></span>
                                </div>
                            </div>
                            <div style="clear:both"></div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <!-- Montant -->
                        <div class="checkbox" style="margin: 0">
                            <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px;"
                                   for="condition5">
                                <input type="radio" name="commission_condition" id="condition5"
                                       value="montant_inf" <?php if ($commission_condition == 'montant_inf') {
                                            echo ' checked';
                                                           } ?>
                                       onchange="ToggleReglesCondition();"/> <?php echo __('Si le montant de la transaction est inférieur à'); ?>
                            </label>
                            <div class="pull-left">
                                <div class="input-group">
                                    <input class="form-control input-small" name="montant_transaction2"
                                           id="montant_transaction2" type="number" min="0" step="any"
                                           value="<?php if ($commission_condition == 'montant_inf') {
                                                echo $value;
                                                  } else {
                                                      echo '0';
                                                  } ?>" <?php if ($commission_condition != 'montant_inf') {
                                 echo ' disabled';
                                                  } ?>>
                                    <span class="input-group-addon"><?php echo \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol() ?></span>
                                </div>
                            </div>
                            <div style="clear:both"></div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <!-- Montant -->
                        <div class="checkbox" style="margin: 0">
                            <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px;"
                                   for="condition6">
                                <input type="radio" name="commission_condition" id="condition6"
                                       value="amount_campaign" <?php if ($commission_condition == 'amount_campaign') {
                                            echo ' checked';
                                                               } ?> onchange="ToggleReglesCondition();"/> <?php echo __('Si l\'affilié a généré plus de'); ?>
                            </label>
                            <div class="pull-left">
                                <div class="input-group">
                                    <input class="form-control input-small" name="amount_campaign" id="amount_campaign"
                                           type="number" min="0" value="<?php if ($commission_condition == 'amount_campaign') {
                                                echo $value;
                                                                        } else {
                                                                            echo '0';
                                                                        } ?>" <?php if ($commission_condition != 'amount_campaign') {
    echo ' disabled';
                                                                        } ?>>
                                    <span class="input-group-addon"><?php echo \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol() ?></span>
                                </div>
                            </div>
                            <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px; padding-left: 10px;"
                                   for="condition6"><?php echo __('de commissions, dans cette campagne'); ?></label>
                            <div style="clear:both"></div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <!-- Montant -->
                        <div class="checkbox" style="margin: 0">
                            <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px;"
                                   for="condition7">
                                <input type="radio" name="commission_condition" id="condition7"
                                       value="amount_total" <?php if ($commission_condition == 'amount_total') {
                                            echo ' checked';
                                                            } ?> onchange="ToggleReglesCondition();"/> <?php echo __('Si l\'affilié a généré plus de'); ?>
                            </label>
                            <div class="pull-left">
                                <div class="input-group">
                                    <input class="form-control input-small" name="amount_total" id="amount_total" type="number"
                                           min="0" value="<?php if ($commission_condition == 'amount_total') {
                                                echo $value;
                                                          } else {
                                                              echo '0';
                                                          } ?>" <?php if ($commission_condition != 'amount_total') {
                  echo ' disabled';
                                                          } ?>>
                                    <span class="input-group-addon"><?php echo \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol() ?></span>
                                </div>
                            </div>
                            <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px; padding-left: 10px;"
                                   for="condition7"><?php echo __('de commissions, toutes campagnes confondues'); ?></label>
                            <div style="clear:both"></div>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <div class="checkbox" style="margin: 0">
                            <label style="vertical-align: middle; margin-top: 10px; margin-right: 10px;" for="condition8">
                                <input type="radio" name="commission_condition" id="condition8"
                                       value="lead_exist" <?php if ($commission_condition == 'lead_exist') {
                                            echo ' checked';
                                                          } ?>
                                       onchange="ToggleReglesCondition();"/> <?php echo __('Si le contact était déjà présent dans votre base de contacts, avant de cliquer sur un lien d\'affilié.'); ?>
                            </label>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <div class="checkbox" style="margin: 0">
                            <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px;"
                                   for="condition9">
                                <input type="radio" name="commission_condition" id="condition9"
                                       value="client_exist" <?php if ($commission_condition == 'client_exist') {
                                            echo ' checked';
                                                            } ?>
                                       onchange="ToggleReglesCondition();"/> <?php echo __('Si le contact était déjà client avant sa commande, et avait enregistré plus de'); ?>
                            </label>
                            <div class="pull-left">
                                <div class="input-group">
                                    <input class="form-control input-small" name="amount_total" id="amount_total" type="number"
                                           min="0" value="<?php if ($commission_condition == 'client_exist') {
                                                echo $value;
                                                          } else {
                                                              echo '0';
                                                          } ?>" <?php if ($commission_condition != 'client_exist') {
                  echo ' disabled';
                                                          } ?>>
                                    <span class="input-group-addon"><?php echo \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol() ?></span>
                                </div>
                            </div>
                            <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px; padding-left: 10px;"
                                   for="condition9"><?php echo __('de transactions'); ?></label>
                            <div style="clear:both"></div>
                        </div>
                    </td>
                </tr>
            </table>

            <div class="form-group" style="margin-bottom: 0">
                <label class="control-label" for="action1"><?php echo __('Action'); ?> *</label>
            </div>
            <table class="table table-striped table-hovered" id="table_conditions">
                <tr>
                    <td>
                        <div class="controls">
                            <div class="checkbox" style="margin: 0">
                                <label class="radio-inline" style="margin-left: 0px;">
                                    <input type="radio" name="commission_action" id="action1"
                                           value="pas_de_commission" <?php if (!$commission_action or 'pas_de_commission' == $commission_action) {
                                                echo ' checked';
                                                                     } ?>
                                           onchange="ToggleReglesAction();"/> <?php echo __('Ne pas enregistrer de commission'); ?>
                                </label>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="controls">
                            <div class="checkbox" style="margin: 0">
                                <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px;"
                                       for="action2">
                                    <input type="radio" name="commission_action" id="action2"
                                           value="nouveau_taux" <?php if ('nouveau_taux' == $commission_action) {
                                                echo ' checked';
                                                                } ?>
                                           onchange="ToggleReglesAction();"/> <?php echo __('Enregistrer une commission avec le taux suivant :'); ?>
                                </label>
                                <div class="pull-left">
                                    <div class="input-group">
                                        <input class="form-control input-small" name="taux_commission" id="taux_commission"
                                               type="number" min="0" max="100" step="any"
                                               value="<?php if ('nouveau_taux' == $commission_action) {
                                                    echo $commission;
                                                      } ?>" <?php if ('nouveau_taux' != $commission_action) {
                                     echo ' disabled';
                                                      } ?> required>
                                        <span class="input-group-addon">%</span>
                                    </div>
                                </div>
                                <div style="clear:both"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="controls">
                            <div class="checkbox" style="margin: 0">
                                <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px;"
                                       for="action3">
                                    <input type="radio" name="commission_action" id="action3"
                                           value="nouveau_montant" <?php if ('nouveau_montant' == $commission_action) {
                                                echo ' checked';
                                                                   } ?>
                                           onchange="ToggleReglesAction();"/> <?php echo __('Enregistrer une commission avec le montant suivant :'); ?>
                                </label>
                                <div class="pull-left">
                                    <div class="input-group">
                                        <input class="form-control input-small" name="montant_commission"
                                               id="montant_commission" type="number" min="0" step="any"
                                               value="<?php if ('nouveau_montant' == $commission_action) {
                                                    echo $commission;
                                                      } ?>" <?php if ('nouveau_montant' != $commission_action) {
                                     echo ' disabled';
                                                      } ?> required>
                                        <span class="input-group-addon"><?php echo \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol($aff_devise); ?></span>
                                    </div>
                                </div>
                                <div style="clear:both"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="controls">
                            <div class="checkbox" style="margin: 0">
                                <label style="float: left; vertical-align: middle; margin-top: 10px; margin-right: 10px;"
                                       for="action4">
                                    <input type="radio" name="commission_action" id="action4"
                                           value="max_montant" <?php echo ('max_montant' == $commission_action ? 'checked' : ''); ?>
                                           onchange="ToggleReglesAction();"/> <?php echo __('Limiter le montant de la commission à :'); ?>
                                </label>
                                <div class="pull-left">
                                    <div class="input-group">
                                        <input class="form-control input-small" name="max_montant_commission"
                                               id="max_montant_commission" type="number" min="0" step="any"
                                               value="<?php if ('max_montant' == $commission_action) {
                                                    echo $commission;
                                                      } ?>" <?php if ('max_montant' != $commission_action) {
                                     echo ' disabled';
                                                      } ?> required>
                                        <span class="input-group-addon"><?php echo \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol($aff_devise); ?></span>
                                    </div>
                                </div>
                                <div style="clear:both"></div>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>

            <div class="form-actions">
                [[CSRF]]
                <input type="hidden" name="form_action" id="form_action"
                       value="<?php echo $id_regle ? 'aff_regle_edit' : 'aff_regle_add'; ?>"/>
                <input type="hidden" name="id_campaign" value="<?php echo $id_campaign; ?>"/>
                <input type="hidden" name="redirect" value="<?php echo RouterHelper::generate('app_affiliation_regles', [
                    'campaignId' => $id_campaign
                ]); ?>"/>
                <?php if ($id_regle) {
                    echo '<input type="hidden" name="id_regle" value="' . $id_regle . '" />';
                } ?>
                <button type="submit" name="submit" class="btn btn-primary"><?php echo __('Valider'); ?></button>
                <a class="btn btn-default btn-lg" href="<?php echo RouterHelper::generate('app_affiliation_regles', [
                    'campaignId' => $id_campaign
                ]); ?>"><?php echo __('Annuler'); ?></a>
            </div>
        </form>
    </div>
</div>
