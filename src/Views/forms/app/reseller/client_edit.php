<div class="top-page-title">
    <h3><?php echo __('Modification d\'un client'); ?></h3>
</div>

<form class="form" method="post" action="" id="form">

    <div class="form-group">
        <label class="control-label" for="nom_client"><?php echo __('Nom du client'); ?> *</label>
        <div class="controls">
            <input class="input-xxlarge form-control focused" name="nom_client" id="nom" type="text" value="<?php echo $client->getNomClient(); ?>" required>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label" for="id_license"><?php echo __('Licence'); ?></label>
        <div class="controls">
            <select name="id_license" data-rel="select2">
                <?php echo $selectLicence; ?>
            </select>
        </div>
    </div>

    <div class="form-actions">
        [[CSRF]]
        <input type="hidden" name="form_action" value="<?php echo $action; ?>" />
        <input type="hidden" name="id_client" value="<?php echo $client->getIdClient(); ?>" />
        <input type="hidden" name="redirect" value="<?php echo Tools::makeLink('app', 'reseller', 'clients'); ?>" />
        <button type="submit" name="submit" class="btn btn-primary"><?php echo __('Valider'); ?></button>
        <a class="btn btn-default btn-lg" href="<?php echo Tools::makeLink('app', 'reseller', 'clients'); ?>"><?php echo __('Annuler'); ?></a>
    </div>

</form>