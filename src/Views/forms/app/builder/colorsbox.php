<div class="builder-box-header">
    <div class="color<?php echo $selected_color ? '' : ' color-transparent'; ?>" style="background: <?php echo $selected_color ?>"></div>
    <div class="color-code">#<input type="text" maxlength="6" value="<?php echo str_replace('#', '', $selected_color) ?>" /></div>
    <div style="clear:both"></div>
</div>
<div class="builder-box-content">

    <form class="form" method="post" action="">

        <?php $has_color_selected = false; ?>

        <?php if (isset($swatches) and $swatches) : ?>
            <div class="builder-color-theme">
                <h5><?php echo __('Couleurs du thème') ?></h5>
                <div class="color color-circle color-transparent" data-color=""></div>

                <?php foreach ($swatches as $c) : ?>
                    <div class="color color-circle

                    <?php if ($selected_color and $selected_color == $c and !$has_color_selected) : ?>
                        color-selected
                        <?php $has_color_selected = true ?>
                    <?php endif; ?>

                    " style="background-color:<?php echo $c ?>" data-color="<?php echo $c ?>"></div>
                <?php endforeach; ?>

                <div style="clear:both"></div>
            </div>
        <?php endif; ?>

        <?php if (isset($last_colors) and $last_colors) : ?>
            <div class="builder-color-last">
                <h5><?php echo __('Couleurs récentes') ?></h5>
                <div class="colors">

                    <?php foreach ($last_colors as $o) : ?>
                        <div class="color color-circle

                        <?php  if ($selected_color and $selected_color == $o and !$has_color_selected) : ?>
                            color-selected
                            <?php $has_color_selected = true ?>
                        <?php endif; ?>

                       " style="background-color:<?php echo $o ?>" data-color="<?php echo $o ?>"></div>
                    <?php endforeach; ?>

                </div>
                <div style="clear:both"></div>
            </div>
        <?php endif; ?>

        <div class="builder-minicolors-inline">
            <h5><?php echo __('Palette') ?></h5>
            <div class="minicolors-inline-selected-color" <?php echo ($selected_color ? 'style="background:' . $selected_color . '"' : '') ?>></div>
            <input class="color-inline" type="text" name="select-color" maxlength="7" id="select-color" value="<?php echo $selected_color ?>">
        </div>

    </form>
</div>