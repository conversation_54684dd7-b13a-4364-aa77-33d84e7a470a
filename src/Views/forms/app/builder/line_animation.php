<?php
$fatal_error = '';

if (!isset($objet) or !isset($id_objet) or !isset($id_line)) {
    $fatal_error = __('Erreur : impossible de récupérer la section');
} elseif (!$objet or !$id_objet or !$id_line) {
    $fatal_error = __('Erreur : impossible de récupérer la section');
} else {
    $line = eden()->{'Learnybox\Services\Builder\BuilderLines\BuilderLines' . ucfirst($objet)}()->getLineById($id_objet, $id_line);
    if (!$line) {
        $fatal_error = __("Erreur : cette section n'existe pas");
    } else {
        if (!isset($_POST['nom'])) {
            if ($is_mobile && $line['contenu_mobile']) {
                $line = eden()->Builder_Tools()->getElementForMobile($line);
            }

            $design = $line['design'];
            if ($design) {
                $design = json_decode($design, true);
                extract($design);
            }
        } else {
            if (isset($cleaned_post['delay'])) {
                $delay = $cleaned_post['delay'];
            }
            if (isset($cleaned_post['delay_disappear'])) {
                $delay_disappear = $cleaned_post['delay_disappear'];
            }
        }
    }
}

if ($fatal_error) {
    echo '
	    <div class="alert alert-danger">
			<button type="button" class="close" data-dismiss="alert">×</button>
			<h4 class="alert-heading">' . $fatal_error . '</h4>
		</div>';

    return;
}
?>

<form class="form" method="post" action="" id="FormPageLine">
    
    <div class="form-group">
        <label class="control-label" for="delay"><?php echo __('Délai d\'apparition'); ?> <i class="fa fa-info-circle" data-rel="tooltip" data-placement="bottom" data-title="<?php echo __('Vous permet de faire apparaître cette section plusieurs secondes après l\'ouverture de la page. Si vous définissez 10, cette section apparaîtra au bout de 10 secondes après l\'ouverture de la page.'); ?>"></i></label>
        <div class="input-group">
            <input class="form-control input-small" name="delay" id="delay" type="number" value="<?php if (isset($delay)) {
                echo $delay;
                                                                                                 } else {
                                                                                                     echo '0';
                                                                                                 } ?>">
            <span class="input-group-addon"><?php echo __('secondes'); ?></span>
        </div>
        <span class="help-block" style="margin-bottom: 0"><small><?php echo __('Note pour les visiteurs'); ?> <i class="fa fa-info-circle" data-rel="tooltip" data-placement="bottom" data-title="<?php echo __('Notez que lorsqu\'un visiteur aura vu cette section apparaître, elle sera directement visible dès le prochain chargement de la page pour ce visiteur.'); ?>"></i></small></span>
        <span class="help-block"><small><?php echo __('Note pour les administrateurs'); ?> <i class="fa fa-info-circle" data-rel="tooltip" data-placement="bottom" data-title="<?php echo __('Notez aussi qu\'en tant qu\'administrateur, même si vous avez déjà vu cette section, elle n\'apparaîtra qu\'au bout du temps défini, ceci afin de vous permettre de vérifier que tout fonctionne bien.'); ?>"></i></small></span>
    </div>
        
    <div class="form-group">
        <label class="control-label" for="delay_disappear"><?php echo __('Délai de disparition'); ?> <i class="fa fa-info-circle" data-rel="tooltip" data-placement="bottom" data-title="<?php echo __('Vous permet de faire disparaître cette section au bout de plusieurs secondes après l\'ouverture de la page. Si vous définissez 10, cette section disparaîtra au bout de 10 secondes après l\'ouverture de la page.'); ?>"></i></label>
        <div class="input-group">
            <input class="form-control input-small" name="delay_disappear" id="delay_disappear" type="number" value="<?php if (isset($delay_disappear)) {
                echo $delay_disappear;
                                                                                                                     } else {
                                                                                                                         echo '0';
                                                                                                                     } ?>">
            <span class="input-group-addon"><?php echo __('secondes'); ?></span>
        </div>
    </div>
            
    [[CSRF]]
    <input type="hidden" name="form_action" value="builder_line_edit_animation" />
    <input type="hidden" name="objet" value="<?php echo $objet; ?>" />
    <input type="hidden" name="id_objet" value="<?php echo $id_objet; ?>" />
    <input type="hidden" name="id_line" value="<?php echo $id_line; ?>" />
    
</form>