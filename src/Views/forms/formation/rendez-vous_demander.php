<?php
$credits = eden()->Formation_Coachings()->getCreditsCoachings();

$min_credits = 3;
$default_duree_coaching = '30min';
if (isset($_SESSION['formation']['credits_live'])) {
    $min_credits = $_SESSION['formation']['credits_live'];
}
if (isset($_SESSION['formation']['duree_coaching']) and $_SESSION['formation']['duree_coaching']) {
    $default_duree_coaching = $_SESSION['formation']['duree_coaching'];
}

if (Learnybox\Services\RightsService::isUser() and $credits['solde'] < $min_credits) {
    echo '<div class="alert alert-danger">' . __('Vous n\'avez pas assez de crédits pour effectuer une demande de rendez-vous (') . n__('%d crédit requis', '%d crédits requis', $min_credits, $min_credits) . ')</div>';
    if (isset($_SESSION['formation']['credits_link']) and $_SESSION['formation']['credits_link']) {
        echo '<a href="' . $_SESSION['formation']['credits_link'] . '" target="_blank" class="btn btn-primary">' . __('Commander des crédits') . '</a>';
    }

    return;
}

$duree = 30;

if (isset($cleaned_post) and $cleaned_post) {
    $rdv_date = isset($cleaned_post['rdv_date']) ? $cleaned_post['rdv_date'] : '';
    $membre_message = isset($cleaned_post['membre_message']) ? $cleaned_post['membre_message'] : '';
    $skype = isset($cleaned_post['skype']) ? $cleaned_post['skype'] : '';
    $duree = isset($cleaned_post['duree']) ? $cleaned_post['duree'] : 30;
} else {
    //récupération du contact skype si il existe
    if (isset($configuration['skype']) and $configuration['skype']) {
        $skype = $configuration['skype'];
    }
}
?>
<div class="alert alert-info">
    <i class="fa fa-info-circle"></i> <?php echo __('Crédits nécessaires pour une demande de rendez-vous'); ?> :
    <strong><?php echo $min_credits; ?></strong><br>
    <em><?php echo __('Le nombre de crédits nécessaires peut varier d\'un coach à l\'autre.'); ?></em><br>
    <?php echo n__('Vous avez actuellement %d crédit.', 'Vous avez actuellement %d crédits.', $credits['solde'], $credits['solde']); ?>
    <?php if (isset($_SESSION['formation']['credits_link']) and $_SESSION['formation']['credits_link']) : ?>
    <br><small><a href="<?php echo $_SESSION['formation']['credits_link']; ?>" target="_blank"><?php echo __('Cliquez ici pour commander un ou plusieurs crédits supplémentaires.'); ?></a></small>
    <?php endif; ?>
</div>

<style type='text/css'>
    .fc-time-grid-event {
        left: 0% !important;
        margin-right: 0px !important;
    }
</style>
<form class="form" action="" method="post">

    <div class="box">
        <div class="head">
            <h4><?php echo __('Etape 1 : choisissez votre coach'); ?></h4>
        </div>
        <div class="box-content">
            <div class="control-group" style="margin-bottom:0px">
                <label class="control-label" for="idcoach"><?php echo __('Choisissez votre coach'); ?> *</label>
                <div class="controls">
                    <?php
                    if (!$select_coach) {
                        echo '<div class="alert alert-danger">' . __('Aucun coach disponible') . '</div>';
                    } else {
                        echo '
						    <select name="idcoach" data-rel="select2" style="width:100%!important;" onchange="DisplayCoach(this.options[this.selectedIndex].value);">' . $select_coach . '</select>
						    <div id="coach_description" style="margin-top:20px"></div>
						    <div id="calendrier" style="margin-top:20px"></div>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <div class="box">
        <div class="head">
            <h4><?php echo __('Etape 2 : Envoi de la demande de rendez-vous'); ?></h4>
        </div>
        <div class="box-content">

            <div class="form-group">
                <label class="control-label" for="rdv_date"><?php echo __('Date du rendez-vous'); ?> *</label>
                <div class="controls">
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        <input type="text" class="input-large form-control datepicker" id="rdv_date" name="rdv_date"
                               value="<?php echo (isset($rdv_date)) ? $rdv_date : ''; ?>" style="width: 100%!important;">
                    </div>
                </div>
            </div>

            <?php if ($default_duree_coaching == '30min') : ?>
                <div class="form-group">
                    <label class="control-label" for="duree"><?php echo __('Durée du coaching'); ?> *</label>
                    <div class="controls">
                        <select name="duree" id="duree" data-rel="select2">
                            <option value="30" <?php echo $duree == 30 ? 'selected' : ''; ?>><?php echo __('30 minutes'); ?></option>
                            <option value="60" <?php echo $duree == 60 ? 'selected' : ''; ?>><?php echo __('1 heure'); ?></option>
                            <option value="90" <?php echo $duree == 90 ? 'selected' : ''; ?>><?php echo __('1 heure et 30 minutes'); ?></option>
                            <option value="120" <?php echo $duree == 120 ? 'selected' : ''; ?>><?php echo __('2 heures'); ?></option>
                        </select>
                    </div>
                </div>
            <?php endif; ?>

            <div class="form-group">
                <label class="control-label" for="membre_message"><?php echo __('Message'); ?> *</label>
                <div class="controls">
                    <textarea class="form-control" name="membre_message" id="membre_message"
                      placeholder="<?php echo __('Message'); ?>" rows="3"
                      style="width:100% !important;"><?php echo (isset($membre_message)) ? $membre_message : ''; ?></textarea>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="skype"><?php echo __('Moyen de contact (téléphone, etc.)'); ?> *</label>
                <div class="controls">
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa-solid fa-phone"></i></span>
                        <input class="input-xlarge form-control focused" name="skype" id="skype" type="text"
                               placeholder="<?php echo __('Indiquer un moyen pour vous contacter'); ?>"
                               value="<?php echo (isset($skype)) ? $skype : ''; ?>" style="width: 100%!important;">
                    </div>
                </div>
            </div>

        </div>
    </div>

    <div class="form-group button-calendar">
        <div style="display: none">[[CSRF]]</div>
        <input type="hidden" name="form_action" id="act" value="demander_rendez_vous"/>
        <?php
        if (isset($redirect)) {
            echo '<input type="hidden" name="redirect" value="' . $redirect . '" />';
        }
        if (isset($idmodule)) {
            echo '<input type="hidden" name="idmodule" value="' . $idmodule . '" />';
        }
        if (isset($idpage)) {
            echo '<input type="hidden" name="idpage" value="' . $idpage . '" />';
        }
        ?>
        <?php if (!empty($btn_calendar)) : ?>
            <style type="text/css"><?php echo $btn_calendar['style']; ?></style>
            <?php echo $btn_calendar['output']; ?>
        <?php else : ?>
            <button class="btn btn-primary"><i class="fa fa-calendar"></i> <?php echo __('Envoyer la demande de rendez-vous'); ?></button>
        <?php endif; ?>
    </div>
</form>
