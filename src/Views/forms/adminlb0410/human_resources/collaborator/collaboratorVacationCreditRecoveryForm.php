<div class="box">
    <div class="box-header-nav black">
        <?php
        if (null !== $collaboratorVacationCreditRecovery->getId()) { ?>
            <h2><i class="fa fa-pencil"></i> <?php echo __('Editer les jours de récupération'); ?></h2>
            <?php
        } else { ?>
            <h2><i class="fa fa-plus"></i> <?php echo __('Ajouter des jours de récupération'); ?></h2>
            <?php
        } ?>
        <span class="break"></span>
        <div class="box-icon">
            <a href="#" class="btn-minimize"><i class="fa fa-chevron-up"></i></a>
            <a href="#" class="btn-close"><i class="fa fa-times"></i></a>
        </div>
    </div>
    <div class="box-content">
        <div class="row">
            <div class="col-md-8">
                <form class="form" method="post" action="" enctype="multipart/form-data">
                    <?php
                    echo Eden_Template::i()
                        ->set('label', __('Nombre de jours'))
                        ->set('name', 'credit')
                        ->set('required', true)
                        ->set('value', $collaboratorVacationCreditRecovery->getCredit())
                        ->parsePhp(FORMS_PATH . '/partials/input.php');
                    ?>

                    <?php
                    echo Eden_Template::i()
                        ->set('label', __('Raison'))
                        ->set('name', 'reason')
                        ->set('required', false)
                        ->set('value', $collaboratorVacationCreditRecovery->getReason())
                        ->parsePhp(FORMS_PATH . '/partials/textarea.php');
                    ?>

                    <div class="form-actions" style="padding-left:100px; margin:0">
                        [[CSRF]]
                        <?php
                        if (null !== $collaboratorVacationCreditRecovery->getId()) { ?>
                            <input type="hidden" name="form_action" id="form_action" value="collaborator_vacation_credit_recovery_edit"/>
                            <input type="hidden" name="id" value="<?php echo $collaboratorVacationCreditRecovery->getId(); ?>" />
                            <?php
                        } else { ?>
                            <input type="hidden" name="form_action" id="form_action" value="collaborator_vacation_credit_recovery_add"/>
                            <input type="hidden" name="collaborator_id" value="<?php echo $collaboratorVacationCreditRecovery->getCollaborator()->getId(); ?>" />
                            <?php
                        }
                        ?>
                        <input type="hidden" name="redirect" value="<?php echo isset($redirect) ? $redirect : ''; ?>"/>

                        <a class="btn" href="<?php echo isset($redirect) ? $redirect : ''; ?>"><?php echo __('Annuler'); ?></a>
                        <button type="submit" name="submit" class="btn btn-primary"><?php echo __('Valider'); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>