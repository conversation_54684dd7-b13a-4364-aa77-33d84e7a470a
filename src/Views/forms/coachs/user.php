<?php

use Learnybox\Enums\Coach\CoachServiceGroupEnum;

$container = \Learnybox\Services\DI\ContainerBuilderService::getInstance();

foreach ($cleaned_post as $key => $value) {
    if (!is_array($value)) {
        ${$key} = $value;
    }
}

if (isset($cleaned_post['description']) && isset($_POST['description'])) {
    $description = filter_input(INPUT_POST, 'description', FILTER_UNSAFE_RAW);
}

$fatal_error = '';

$user_id = $_SESSION['user_id'];
if (!$user_id) {
    $fatal_error = __("Erreur : impossible de récupérer l'utilisateur");
} else {
    $user = eden()->Utilisateurs()->getUserById($user_id);
    if (!$user) {
        $fatal_error = __("Erreur : cet utilisateur n'existe pas");
    } else {
        if (!isset($_POST['fname'])) {
            $fname = $user['fname'];
            $lname = $user['lname'];
            $email = $user['email'];
            $password = '';
            $restricted = $user['restricted'];
            $validated = $user['validated'];
            $contact_technique = $user['contact_technique'];
            $newsletter = $user['newsletter'];

            if (1 == $restricted) {
                $restricted = 'on';
            }
            if (1 == $validated) {
                $validated = 'on';
            }
            if (1 == $contact_technique) {
                $contact_technique = 'on';
            }
            if (1 == $newsletter) {
                $newsletter = 'on';
            }

            $description = eden()->UserConfig()->getUserConfigByName('description', $user_id);
            if ($description) {
                $description = $description['value'];
            }

            $get_appearin = eden()->UserConfig()->getUserConfigByName('appearin', $user_id);
            if ($get_appearin) {
                $appearin = $get_appearin['value'];
            }
        }
    }
}

if ('' != trim($fatal_error)) {
    echo '
	    <div class="alert alert-danger">
			<button type="button" class="close" data-dismiss="alert">×</button>
			<h4 class="alert-heading">' . $fatal_error . '</h4>
			<a href="' . Tools::makeLink('coachs', 'index') . '">&laquo;&nbsp;' . __('Retour') . '</a>
		</div>';

    return;
}

if (!isset($pays)) {
    $pays = 'France';
}
$selectPays = Tools::generateSelectPays($pays);

$avatar = $container->get(\Learnybox\Services\Users\UsersService::class)->getAvatar($user['email'], 128);

?>
<div class="top-page-title">
    <h3><?php echo __('Edition de votre profil') ?></h3>
</div>

<form class="form" method="post" action="" autocomplete="off">

    <div>
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active">
                <a href="#general" aria-controls="general" role="tab" data-toggle="tab"><?php echo __('Profil général') ?></a>
            </li>
            <li role="presentation" style="<?php echo !$canDoCoachings ? 'display:none' : null ?>">
                <a href="#coaching" aria-controls="coaching" role="tab" data-toggle="tab"><?php echo __('Coaching') ?></a>
            </li>
            <li role="presentation" style="<?php echo !$canDoDfy ? 'display:none' : null ?>">
                <a href="#done_for_you" aria-controls="done_for_you" role="tab" data-toggle="tab"><?php echo __('Done For You') ?></a>
            </li>
        </ul>
        <div class="tab-content">
            <!-- General Panel -->
            <div role="tabpanel" class="tab-pane active" id="general">
                <div class="row">
                    <div class="col-sm-12 p-t-20">

                        <div class="form-group">
                            <label class="control-label" for="fname"><?php echo __('Prénom'); ?> *</label>
                            <div class="controls">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                    <input class="input form-control focused" name="fname" id="fname" type="text" value="<?php if (isset($fname)) {
                                        echo $fname;
                                                                                                                         } ?>" required />
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label" for="lname"><?php echo __('Nom'); ?> *</label>
                            <div class="controls">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                    <input class="input form-control focused" name="lname" id="lname" type="text" value="<?php if (isset($lname)) {
                                        echo $lname;
                                                                                                                         } ?>" required />
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label" for="email"><?php echo __('Email'); ?> *</label>
                            <div class="controls">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-at"></i></span>
                                    <input class="input form-control focused" name="email" id="email" type="email" value="<?php if (isset($email)) {
                                        echo $email;
                                                                                                                          } ?>" required />
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label" for="password"><?php echo __('Mot de passe'); ?></label>
                            <div class="controls">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-lock"></i></span>
                                    <input class="input form-control focused" name="password" id="password" type="password" value="" autocomplete="new-password" />
                                </div>
                                <?php if ($user_id) {
                                    echo '<span class="help-inline">' . __('Laissez vide pour ne pas le modifier') . '</span>';
                                } ?>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label" for="password2"><?php echo __('Mot de passe'); ?><br><small><em>(<?php echo __('confirmation'); ?>)</em></small></label>
                            <div class="controls">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-lock"></i></span>
                                    <input class="input form-control focused" name="password2" id="password2" type="password" value="" />
                                </div>
                                <?php if ($user_id) {
                                    echo '<span class="help-inline">' . __('Laissez vide pour ne pas le modifier') . '</span>';
                                } ?>
                            </div>
                        </div>

                        <?php echo '<img class="img-thumbnail" src="' . $avatar . '" width="100" style="margin-bottom:5px" />' ?>
                        <div class="form-group">
                            <label class="control-label" for="avatar"><?php echo __('Avatar'); ?></label>
                            <div class="controls">
                                <div class="input-group">
                                    <input class="form-control input-xxlarge" id="avatar" name="avatar" type="text" value="<?php echo $avatar ?>" />
                                    <span class="input-group-btn float-left"><button class="btn btn-default" type="button" id="file" onClick="return selectFileWithCKFinder('avatar');"><?php echo __('Parcourir'); ?></button></span>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="validated" value="1" />
                        <input type="hidden" name="newsletter" value="1" />
                    </div>
                </div>
            </div>

            <!-- Coaching Panel -->
            <div role="tabpanel" class="tab-pane" id="coaching">

                <div class="row">
                    <div class="col-sm-12 p-t-20">

                        <div class="form-group">
                            <label class="control-label" for="shortDescription"><?php echo __('Description courte'); ?></label>
                            <div class="controls">
                                <input class="input form-control focused" name="shortDescription" id="shortDescription" type="text" maxlength="255" value="<?php if (isset($shortDescription)) {
                                    echo $shortDescription;
                                                                                                                                                           } ?>">
                                <p class="help-inline"><?php echo __('Les clients verront votre courte description juste à côté de votre calendrier.'); ?></p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label" for="ckeditorsmall"><?php echo __('Description'); ?></label>
                            <div class="controls">
                                <textarea name="description" id="ckeditorsmall" type="text"><?php if (isset($description)) {
                                    echo $description;
                                                                                            } ?></textarea>
                                <p class="help-inline"><?php echo __('Les clients verront votre description dans votre fiche personnelle.'); ?></p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label" for="timeBeforeBooking"><?php echo __('Délai (en minutes) minimum avant réservation'); ?></label>
                            <div class="controls">
                                <input class="input form-control focused" name="timeBeforeBooking" id="timeBeforeBooking" type="number" value="<?php if (isset($timeBeforeBooking)) {
                                    echo $timeBeforeBooking;
                                                                                                                                               } ?>">
                                <p class="help-inline"><?php echo __('Les clients pourront réserver au plus tôt X minutes après l\'heure actuelle.'); ?></p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label" for="cout"><?php echo __('Coût en crédits par 1/2 heure'); ?></label>
                            <div class="controls">
                                <input class="input form-control focused" name="cout" id="cout" type="number" value="<?php if (isset($cout)) {
                                    echo $cout;
                                                                                                                     } ?>">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label" for="appearin"><?php echo __('Salle de réunion appear.in'); ?> *</label>
                            <div class="controls">
                                <input required class="input form-control focused" name="appearin" id="appearin" type="url" value="<?php if (isset($appearin)) {
                                    echo $appearin;
                                                                                                                                   } ?>">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-6">
                                <div class="box">
                                    <div class="box-header-nav">
                                        <h2><?php echo __('Applications') ?></h2>
                                    </div>

                                    <div class="box-content">
                                        <?php
                                        foreach ($appScoreChoices as $appScoreName => $appScoreChoice) { ?>
                                            <div class="form-group row">
                                                <label class="col-sm-8 control-label" for="<?php echo $appScoreName; ?>"><?php echo $appScoreChoice[0];?></label>
                                                <div class="col-sm-4">
                                                    <div class="noter">
                                                        <div id="<?php echo $appScoreName; ?>" class="raty" data-score="<?php if (isset(${$appScoreName})) {
                                                            echo ${$appScoreName};
                                                                 } ?>"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="box">
                                    <div class="box-header-nav">
                                        <h2><?php echo __('Autres expertises') ?></h2>
                                    </div>

                                    <div class="box-content">
                                        <?php
                                        foreach ($skillScoreChoices as $skillScoreName => $title) { ?>
                                            <div class="form-group row">
                                                <label class="col-sm-8 control-label" for="<?php echo $skillScoreName; ?>"><?php echo $title;?></label>
                                                <div class="col-sm-4">
                                                    <div class="noter">
                                                        <div id="<?php echo $skillScoreName; ?>" class="raty" data-score="<?php if (isset(${$skillScoreName})) {
                                                            echo ${$skillScoreName};
                                                                 } ?>"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Done for you Panel -->
            <div role="tabpanel" class="tab-pane" id="done_for_you">

                <div class="row">
                    <div class="col-sm-12 p-t-20">

                        <div class="form-group">
                            <label class="control-label" for="available"><?php echo __('Disponibilité'); ?></label>
                            <div class="">
                                <label for="available">
                                    <input type="checkbox" name="available" id="available" <?php if ($available) {
                                        echo 'checked="checked"';
                                                                                           } ?>>
                                    <?php echo __('Je suis disponible pour l\'attribution de nouveaux services.'); ?>
                                </label>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="form-actions">
        [[CSRF]]
        <input type="hidden" name="form_action" id="form_action" value="profil" />
        <input type="hidden" name="user_role_id" id="user_role_id" value="<?php echo $user['user_role_id'] ?? null ?>" />
        <?php
        $redirect_url = Tools::makeLink('coachs', 'index');
        echo '<input type="hidden" name="redirect" value="' . $redirect_url . '" />';
        ?>
        <button type="submit" class="btn btn-primary"><?php echo __('Valider'); ?></button>
        <a class="btn btn-default btn-lg" href="<?php echo $redirect_url; ?>"><?php echo __('Annuler'); ?></a>
    </div>

</form>
