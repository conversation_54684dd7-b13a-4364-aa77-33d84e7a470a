<?php
$fatal_error = '';
if (isset($param) and $param) {
    $id_coaching = filter_var($param, FILTER_VALIDATE_INT);
    if (!$id_coaching) {
        $fatal_error = __('Erreur : impossible de récupérer le coaching');
    } else {
        $coaching = eden()->Coachs_Coachings()->coachGetRendezVousById($id_coaching);
        if (!$coaching) {
            $fatal_error = __("Erreur : ce coaching n'existe pas");
        } else {
            if (!isset($_POST['message'])) {
                $message = $coaching['coach_rapport'];
            } else {
                $message = $_POST['message'];
            }
        }
    }
} else {
    $fatal_error = __('Erreur : impossible de récupérer le coaching');
}

if (!isset($id_coaching)) {
    $id_coaching = '';
}

if ('' != trim($fatal_error)) {
    echo '<div class="alert alert-danger">
			<button type="button" class="close" data-dismiss="alert">×</button>
			<h4 class="alert-heading">' . $fatal_error . '</h4>
			<a href="' . Tools::makeLink('coachs', 'coachings') . '">&laquo;&nbsp;' . __('Retour') . '</a>
		</div>';
} else {
    $message = stripslashes($message); ?>

    <div class="box">
        <div class="box-header-nav">
            <h2><i class="fa fa-user"></i> <?php echo __('Terminer le coaching'); ?></h2>
        </div>
        <div class="box-content">
            <form class="form" method="post" action="">

                <div class="form-group">
                    <label class="control-label" for="message"><?php echo __('Rapport'); ?></label>
                    <div class="controls">
                        <textarea class="ckeditor" id="ckeditorsmall" name="message"><?php if (isset($message)) {
                            echo $message;
                                                                                     } ?></textarea>
                    </div>
                </div>

                <div class="">
                    [[CSRF]]
                    <input type="hidden" name="form_action" id="act" value="terminer_coaching" />
                    <input type="hidden" name="id_coaching" value="<?php echo $id_coaching; ?>" />
                    <a class="btn" href="<?php echo Tools::makeLink('coachs', 'coaching', $id_coaching); ?>"><?php echo __('Annuler'); ?></a>
                    <button type="submit" name="submit" class="btn btn-success"><?php echo __('Terminer le coaching'); ?></button>
                </div>

            </form>
        </div>
    </div>

    <?php
} ?>
