<h1>8.4. <?php echo __('Sondages'); ?></h1>

<div class="video-block-center" style="width: 853px; height: 480px; margin-bottom:20px;">
    <iframe width="853" height="480" src="https://www.youtube.com/embed/k-X43OdM_kE" frameborder="0" allowfullscreen="" kwframeid="1"></iframe>
</div>

<p><?php echo __('Cette page vous permet de préparer des sondages que vous pourrez publier lors de votre webinaire live.'); ?><?php echo __('La colonne de gauche affiche les différents sondages avec la question, le nombre de réponses et l’état du sondage.'); ?><?php echo __('Les actions situées à droite vous permettent de le modifier, le publier ou le supprimer.'); ?><?php echo __('Le bouton <i class="fa fa-search"></i> vous permet de consulter les résultats de votre sondage.'); ?></p>

<p><?php echo __('Pour créer un nouveau sondage, cliquez sur le bouton "Créer un sondage".'); ?><?php echo __('Le bouton "Importer un sondage prédéfini" vous permet d’importer des sondages prédéfinis, que vous pouvez créer dans le menu de gauche <strong>Sondages</strong>.'); ?><?php echo __('Ce sont des sondages qui n’appartiennent à aucun webinaire live, mais que vous pouvez importer dans n’importe quel webinaire live, en un clic, juste en cliquant sur le bouton "Importer".'); ?><?php echo __('Le bouton "Publier" vous permet de l’importer dans le webinaire live et de démarrer la publication instantanément.'); ?></p>

<hr>
<h4><?php echo __('Création d’un sondage'); ?></h4>

<p><?php echo __('La création d’un sondage est très simple : indiquez le titre de votre question, par exemple « Quelle est votre profession ? ».'); ?><?php echo __('Sélectionnez ensuite le type de réponse :'); ?> </p>
<ul>
    <li><strong><?php echo __('Réponse unique'); ?></strong> : <?php echo __('vos visiteurs ne pourront choisir qu’une seule réponse parmi celles proposées'); ?></li>
    <li><strong><?php echo __('Réponse multiple'); ?></strong> : <?php echo __('vos visiteurs pourront cocher plusieurs réponses parmi celles proposées'); ?></li>
    <li><strong><?php echo __('Champ libre'); ?></strong> : <?php echo __('affiche un champ de texte.'); ?></li>
</ul>

<p><?php echo __('Ajoutez ensuite les différentes réponses possibles, par exemple : « infopreneur », « conférencier », etc.'); ?></p>
<p><?php echo __('Si vous avez besoin d’ajouter plus de réponses, cliquez simplement ici sur « Ajouter une réponse ».'); ?></p>
