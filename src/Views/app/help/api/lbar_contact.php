<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Contacts'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Récupérer un contact'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_contact</td>
                                <td>int</td>
                                <td><?php echo __('ID du contact'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'learnymail/contact/get')
      ->set('email', '<EMAIL>');</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [datas] => Array
        (
            [id_contact] => 1234
            [prenom] => John
            [nom] => Watson
            [email] => <EMAIL>
            [adresse] => Rue du temple
            [code_postal] => 1234
            [ville] => LearnyCity
            [pays] => France
            [tel] => 0123456789
            [etat] => subscribed
            [date_inscription] => <?php echo date('Y-m-d H:i:s', strtotime('-2 weeks')) . "\n"; ?>
            [sequences] => Array
                (
                    [0] => Array
                        (
                            [id_sequence] => 103
                            [nom] => <?php echo __('Ma première séquence'); ?>
                            [etat] => subscribed
                            [step] => 0
                            [next_step] => 12
                            [date_inscription] => <?php echo date('Y-m-d H:i:s', strtotime('-2 weeks')) . "\n"; ?>
                            [date_desinscription] => 0000-00-00 00:00:00
                        )

                )

            [tags] => Array
                (
                    [0] => Array
                        (
                            [id_tag] => 17
                            [nom] => Tag 1
                            [date_inscription] => <?php echo date('Y-m-d H:i:s', strtotime('-5 days')) . "\n"; ?>
                            [date_suppression] => 0000-00-00 00:00:00
                        )

                )
        )

)</code></pre>
        </div>
    </div>
</div>


<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Inscription d\'un contact'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Inscrire un nouveau contact'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>prenom</td>
                                <td>string</td>
                                <td><?php echo __('Prénom du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>nom</td>
                                <td>string</td>
                                <td><?php echo __('Nom du contact'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">email</span></td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>id_sequence</td>
                                <td>int</td>
                                <td><?php echo __('ID d\'une séquence'); ?></td>
                            </tr>
                            <tr>
                                <td>tags</td>
                                <td>string</td>
                                <td><?php echo __('Liste des tags séparés par une virgule'); ?></td>
                            </tr>
                            <tr>
                                <td>adresse</td>
                                <td>string</td>
                                <td><?php echo __('Adresse du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>ville</td>
                                <td>string</td>
                                <td><?php echo __('Ville du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>pays</td>
                                <td>string</td>
                                <td><?php echo __('Pays du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>code_postal</td>
                                <td>string</td>
                                <td><?php echo __('Code postal du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>tel</td>
                                <td>string</td>
                                <td><?php echo __('Numéro de téléphone du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>rgpd</td>
                                <td>boolean</td>
                                <td><?php echo __('0 pour non qualifié RGPD / 1 pour qualifié RGPD'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">rgpd_date</span></td>
                                <td>date</td>
                                <td><?php echo __('Date au format YYYY-MM-DD de la qualification RGPD du contact (obligatoire si rgpd = 1)'); ?></td>
                            </tr>
                            <tr>
                                <td>rgpd_notice</td>
                                <td>string</td>
                                <td><?php echo __('Texte du consentement'); ?></td>
                            </tr>
                            <tr>
                                <td>rgpd_aff</td>
                                <td>boolean</td>
                                <td><?php echo __('0 pour non qualifié RGPD Partenaires / 1 pour qualifié RGPD Partenaires'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">rgpd_aff_date</span></td>
                                <td>date</td>
                                <td><?php echo __('Date au format YYYY-MM-DD de la qualification RGPD Partenaires du contact (obligatoire si rgpd_aff = 1)'); ?></td>
                            </tr>
                            <tr>
                                <td>rgpd_aff_notice</td>
                                <td>string</td>
                                <td><?php echo __('Texte du consentement'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'learnymail/contact/add')
    ->set('nom', 'Watson')
    ->set('prenom', 'John')
    ->set('email', '<EMAIL>')
    ->set('id_sequence', 1234)
    ->set('tags', '14,67')
    ->set('adresse', 'adresse')
    ->set('ville', 'ville')
    ->set('pays', 'France')
    ->set('code_postal', '12345')
    ->set('tel', '0123456789')
    ->set('rgpd', 1)
    ->set('rgpd_date', '2018-05-25')
    ->set('rgpd_aff', 0);
    </code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('La commission a été enregistrée avec succès'); ?>.
)</code></pre>
        </div>
    </div>
</div>



<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Ajout d\'une séquence'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Ajout d\'une séquence à un contact'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_contact</td>
                                <td>int</td>
                                <td><?php echo __('ID du contact'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">id_sequence</span></td>
                                <td>int</td>
                                <td><?php echo __('ID d\'une séquence'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'learnymail/contact/add_sequence')
    ->set('email', '<EMAIL>')
    ->set('id_sequence', 1234);
    </code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('La séquence a bien été ajoutée au contact'); ?>.
)</code></pre>
        </div>
    </div>
</div>

<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo _('Suppression d\'une séquence'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo _('Suppression d\'une séquence d\'un contact'); ?></h4>
                    <table>
                        <thead>
                        <tr>
                            <th><?php echo _('Paramètre'); ?></th>
                            <th><?php echo _('Type'); ?></th>
                            <th><?php echo _('Description'); ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>email</td>
                            <td>string</td>
                            <td><?php echo _('Adresse email du contact'); ?></td>
                        </tr>
                        <tr>
                            <td><?php echo _('ou'); ?></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>id_contact</td>
                            <td>int</td>
                            <td><?php echo _('ID du contact'); ?></td>
                        </tr>
                        <tr>
                            <td><span class="required">id_sequence</span></td>
                            <td>int</td>
                            <td><?php echo _('ID d\'une séquence'); ?></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'learnymail/contact/remove_sequence')
    ->set('email', '<EMAIL>')
    ->set('id_sequence', 1234);
    </code></pre>
            <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo _('La séquence a bien été retirée du contact.'); ?>

)</code></pre>
        </div>
    </div>
</div>

<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Ajout d\'un tag'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Ajout d\'un tag à un contact'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_contact</td>
                                <td>int</td>
                                <td><?php echo __('ID du contact'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">id_tag</span></td>
                                <td>int</td>
                                <td><?php echo __('ID d\'un tag'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'learnymail/contact/add_tag')
    ->set('email', '<EMAIL>')
    ->set('id_tag', 12);
    </code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('Le tag a bien été ajouté au contact'); ?>.
)</code></pre>
        </div>
    </div>
</div>

<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo _('Suppression d\'un tag'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo _('Suppression d\'un tag d\'un contact'); ?></h4>
                    <table>
                        <thead>
                        <tr>
                            <th><?php echo _('Paramètre'); ?></th>
                            <th><?php echo _('Type'); ?></th>
                            <th><?php echo _('Description'); ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>email</td>
                            <td>string</td>
                            <td><?php echo _('Adresse email du contact'); ?></td>
                        </tr>
                        <tr>
                            <td><?php echo _('ou'); ?></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>id_contact</td>
                            <td>int</td>
                            <td><?php echo _('ID du contact'); ?></td>
                        </tr>
                        <tr>
                            <td><span class="required">id_tag</span></td>
                            <td>int</td>
                            <td><?php echo _('ID d\'un tag'); ?></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'learnymail/contact/remove_tag')
    ->set('email', '<EMAIL>')
    ->set('id_tag', 12);
    </code></pre>
            <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo _('Le tag a bien été retiré du contact.'); ?>

)</code></pre>
        </div>
    </div>
</div>

<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Modification d\'un contact'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Mise à jour d\'un contact'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_contact</td>
                                <td>int</td>
                                <td>ID du contact</td>
                            </tr>
                            <tr>
                                <td>prenom</td>
                                <td>string</td>
                                <td><?php echo __('Nouveau prénom du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>nom</td>
                                <td>string</td>
                                <td><?php echo __('Nouveau nom du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>new_email</td>
                                <td>string</td>
                                <td><?php echo __('Nouvelle adresse email du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>rgpd</td>
                                <td>boolean</td>
                                <td><?php echo __('0 pour non qualifié RGPD / 1 pour qualifié RGPD'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">rgpd_date</span></td>
                                <td>date</td>
                                <td><?php echo __('Date au format YYYY-MM-DD de la qualification RGPD du contact (obligatoire si rgpd = 1)'); ?></td>
                            </tr>
                            <tr>
                                <td>rgpd_notice</td>
                                <td>string</td>
                                <td><?php echo __('Texte du consentement'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">rgpd_aff</span></td>
                                <td>boolean</td>
                                <td><?php echo __('0 pour non qualifié RGPD Partenaires / 1 pour qualifié RGPD Partenaires'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">rgpd_date</span></td>
                                <td>date</td>
                                <td><?php echo __('Date au format YYYY-MM-DD de la qualification RGPD Partenaires du contact (obligatoire si rgpd_aff = 1)'); ?></td>
                            </tr>
                            <tr>
                                <td>rgpd_aff_notice</td>
                                <td>string</td>
                                <td><?php echo __('Texte du consentement'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'learnymail/contact/edit')
    ->set('email', '<EMAIL>')
    ->set('new_email', '<EMAIL>')
    ->set('nom', 'Watson')
    ->set('prenom', 'Brad');
    </code></pre>
            <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('Le contact a été mis à jour avec succès'); ?>.
)</code></pre>
        </div>
    </div>
</div>


<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Suppression d\'un contact'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Suppression d\'un contact'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du contact'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_contact</td>
                                <td>int</td>
                                <td><?php echo __('ID du contact'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'learnymail/contact/delete')
    ->set('email', '<EMAIL>');</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('Le contact a bien été supprimé'); ?>.
)</code></pre>
        </div>
    </div>
</div>
