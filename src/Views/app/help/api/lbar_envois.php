<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Envois'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Récupérer la liste des envois'); ?></h4>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'learnymail/envois/get');</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [datas] => Array
        (
            [0] => Array
                (
                    [id_envoi] => 41
                    [sequences] => [23,24,92,103]
                    [exclude_sequences] => []
                    [tags] => []
                    [exclude_tags] => []
                    [etat] => waiting
                    [nb_envois] => 0
                    [nb_opens] => 0
                    [nb_bounces] => 0
                    [nb_complaints] => 0
                    [date] => <?php echo date('Y-m-d H:i:s', strtotime('+5 days')) . "\n"; ?>
                    [date_envoi] => 0000-00-00 00:00:00
                    [mail] => <?php echo __('Informations importantes concernant le programme !'); ?>
                    [destinataires] => <?php echo __('4 séquences'); ?>
                )

            [1] => Array
                (
                    [id_envoi] => 35
                    [sequences] => [23,24]
                    [exclude_sequences] => []
                    [tags] => []
                    [exclude_tags] => []
                    [etat] => processed
                    [nb_envois] => 564
                    [nb_opens] => 324
                    [nb_bounces] => 2
                    [nb_complaints] => 1
                    [date] => <?php echo date('Y-m-d H:i:s', strtotime('-1 week')) . "\n"; ?>
                    [date_envoi] => <?php echo date('Y-m-d H:i:s', strtotime('-1 week')) . "\n"; ?>
                    [mail] => <?php echo __('Bienvenue dans le programme!'); ?>
                    [destinataires] => <?php echo __('2 séquences'); ?>
                )
        )
)</code></pre>
        </div>
    </div>
</div>