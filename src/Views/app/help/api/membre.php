<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Membre'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Récupérer un membre'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_membre</td>
                                <td>int</td>
                                <td><?php echo __('ID du membre'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'formation/membre/get')
    ->set('email', '<EMAIL>');</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [id_membre] => 1234
    [prenom] => John
    [nom] => Watson
    [email] => <EMAIL>
    [formations] => Array
        (
            [0] => Array
                (
                    [id_formation] => 123
                    [nom] => <?php echo __('Ma formation'); ?> 1
                    [groupes] => 2,8
                    [datecreation] => <?php echo date('Y-m-d H:i:s', strtotime('-3 weeks')) . "\n"; ?>
                    [type] => program
                    [acces] => active
                )

        )

)</code></pre>
        </div>
    </div>
</div>



<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Progression d\'un membre'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Récupérer la progression d\'un membre'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email ou id_membre</td>
                                <td>string | int</td>
                                <td><?php echo __('Adresse email ou ID du membre'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">id_formation</span></td>
                                <td>int</td>
                                <td><?php echo __('ID de la formation'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'formation/membre/progression')
    ->set('email', '<EMAIL>')
    ->set('id_formation', 1234);</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [id_membre] => 1234
    [prenom] => John
    [nom] => Watson
    [email] => <EMAIL>
    [groupes] => 1,2,5
    [progression] => 34%
    [total_time] => 737s
    [details] => Array
        (
            [0] => Array
                (
                    [date] => <?php echo date('Y-m-d H:i:s', strtotime('-1 day')) . "\n"; ?>
                    [id_module] => 1234
                    [module] => <?php echo __('Module'); ?> 1
                    [id_page] => 643
                    [page] => <?php echo __('Page'); ?> 1
                    [time] => 265s
                )

            [1] => Array
                (
                    [date] => <?php echo date('Y-m-d H:i:s', strtotime('-1 day')) . "\n"; ?>
                    [id_module] => 1234
                    [module] => <?php echo __('Module'); ?> 1
                    [id_page] => 768
                    [page] => <?php echo __('Page'); ?> 2
                    [time] => 327s
                )

            [2] => Array
                (
                    [date] => <?php echo date('Y-m-d H:i:s', strtotime('-2 days')) . "\n"; ?>
                    [id_module] => 0
                    [module] =>
                    [id_page] => 145
                    [page] => <?php echo __('Introduction'); ?>
                    [time] => 145s
                )

        )

)</code></pre>
        </div>
    </div>
</div>


<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Inscription d\'un membre'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Inscrire un nouveau membre'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="required">prenom</span></td>
                                <td>string</td>
                                <td><?php echo __('Prénom du membre'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">nom</span></td>
                                <td>string</td>
                                <td><?php echo __('Nom du membre'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">email</span></td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du membre'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">id_formation</span></td>
                                <td>int</td>
                                <td><?php echo __('ID de la formation'); ?></td>
                            </tr>
                            <tr>
                                <td>groupes</td>
                                <td>string</td>
                                <td><?php echo __('ID des groupes séparés par une virgule'); ?></td>
                            </tr>
                            <tr>
                                <td>adresse</td>
                                <td>string</td>
                                <td><?php echo __('Adresse du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>ville</td>
                                <td>string</td>
                                <td><?php echo __('Ville du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>pays</td>
                                <td>string</td>
                                <td><?php echo __('Pays du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>code_postal</td>
                                <td>string</td>
                                <td><?php echo __('Code postal du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>tel</td>
                                <td>string</td>
                                <td><?php echo __('Numéro de téléphone du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>rgpd</td>
                                <td>boolean</td>
                                <td><?php echo __('0 pour non qualifié RGPD / 1 pour qualifié RGPD'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">rgpd_date</span></td>
                                <td>date</td>
                                <td><?php echo __('Date au format YYYY-MM-DD de la qualification RGPD du membre (obligatoire si rgpd = 1)'); ?></td>
                            </tr>
                            <tr>
                                <td>rgpd_notice</td>
                                <td>string</td>
                                <td><?php echo __('Texte du consentement'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">rgpd_aff</span></td>
                                <td>boolean</td>
                                <td><?php echo __('0 pour non qualifié RGPD Partenaires / 1 pour qualifié RGPD Partenaires'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">rgpd_date</span></td>
                                <td>date</td>
                                <td><?php echo __('Date au format YYYY-MM-DD de la qualification RGPD Partenaires du membre (obligatoire si rgpd_aff = 1)'); ?></td>
                            </tr>
                            <tr>
                                <td>rgpd_aff_notice</td>
                                <td>string</td>
                                <td><?php echo __('Texte du consentement'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'formation/membre/add')
    ->set('nom', 'Watson')
    ->set('prenom', 'John')
    ->set('email', '<EMAIL>')
    ->set('id_formation', 1234)
    ->set('groupes', 14,67)
    ->set('adresse', 'adresse')
    ->set('ville', 'ville')
    ->set('pays', 'France')
    ->set('code_postal', '12345')
    ->set('tel', '0123456789');
    </code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('Le membre a été enregistré avec succès'); ?>.
)</code></pre>
        </div>
    </div>
</div>



<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Formation'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Ajouter le membre à une formation'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_membre</td>
                                <td>int</td>
                                <td><?php echo __('ID du membre'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">id_formation</span></td>
                                <td>int</td>
                                <td><?php echo __('ID de la formation'); ?></td>
                            </tr>
                            <tr>
                                <td>groupes</td>
                                <td>string</td>
                                <td><?php echo __('ID des groupes séparés par une virgule'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'formation/membre/add_formation')
    ->set('email', '<EMAIL>')
    ->set('id_formation', 1234)
    ->set('groupes', '12,15');</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('La formation a bien été ajoutée au membre'); ?>.
)</code></pre>
        </div>
    </div>
</div>



<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Groupes'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Ajouter le membre dans un groupe / Retirer le membre d\'un groupe'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_membre</td>
                                <td>int</td>
                                <td><?php echo __('ID du membre'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">id_formation</span></td>
                                <td>int</td>
                                <td><?php echo __('ID de la formation'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">groupes</span></td>
                                <td>string</td>
                                <td><?php echo __('ID des groupes séparés par une virgule'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">//Ajouter dans un groupe
$lbApi->set('action', 'formation/membre/add_to_group')
    ->set('email', '<EMAIL>')
    ->set('id_formation', 1234)
    ->set('groupes', '12,14,16');</code></pre>
        <pre><code class="php hljs">
//Retirer d'un groupe
$lbApi->set('action', 'formation/membre/remove_from_group')
    ->set('email', '<EMAIL>')
    ->set('id_formation', 1234)
    ->set('groupes', '12,14');</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('Le membre a bien été ajouté dans le(s) groupe(s) spécifié(s)'); ?>.
)</code></pre>
        </div>
    </div>
</div>


<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Modules'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Donner à un membre l\'accès à un ou plusieurs modules / Retirer l\'accès à un ou plusieurs modules'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_membre</td>
                                <td>int</td>
                                <td><?php echo __('ID du membre'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">id_formation</span></td>
                                <td>int</td>
                                <td><?php echo __('ID de la formation'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">modules</span></td>
                                <td>string</td>
                                <td><?php echo __('ID des modules séparés par une virgule'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">//Donner l'accès à un module
$lbApi->set('action', 'formation/membre/add_to_modules')
    ->set('email', '<EMAIL>')
    ->set('id_formation', 1234)
    ->set('modules', '1234,2578');</code></pre>
        <pre><code class="php hljs">
//Retirer l'accès à un module
$lbApi->set('action', 'formation/membre/remove_from_modules')
    ->set('email', '<EMAIL>')
    ->set('id_formation', 1234)
    ->set('modules', '1234,2578');</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('Les modules ont bien été ajoutés au membre'); ?>.
)</code></pre>
        </div>
    </div>
</div>


<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Crédits'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Ajouter des crédits au membre'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_membre</td>
                                <td>int</td>
                                <td><?php echo __('ID du membre'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">nb_credits</span></td>
                                <td>int</td>
                                <td><?php echo __('Nombre de crédits à ajouter'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'formation/membre/add_credits')
    ->set('email', '<EMAIL>')
    ->set('nb_credits', 12);</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('Les crédits ont bien été ajoutés au membre'); ?>.
)</code></pre>
        </div>
    </div>
</div>



<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Etat du membre'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Activer un membre'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_membre</td>
                                <td>int</td>
                                <td><?php echo __('ID du membre'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'formation/membre/active')
    ->set('email', '<EMAIL>');</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('Le membre a bien été activé'); ?>.
)</code></pre>
        </div>
    </div>

    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Désactiver un membre'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_membre</td>
                                <td>int</td>
                                <td><?php echo __('ID du membre'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'formation/membre/desactive')
    ->set('email', '<EMAIL>');</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('Le membre a bien été désactivé'); ?>.
)</code></pre>
        </div>

    </div>
</div>



<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Mot de passe'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Réinitialiser le mot de passe d\'un membre'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_membre</td>
                                <td>int</td>
                                <td><?php echo __('ID du membre'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'formation/membre/reinit_password')
    ->set('email', '<EMAIL>');</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('Le mot de passe a bien été renvoyé au membre'); ?>.
)</code></pre>
        </div>
    </div>
</div>


<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Tester la connexion d\'un membre'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Permet de vérifier l\'adresse email et le mot de passe d\'un membre'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_membre</td>
                                <td>int</td>
                                <td><?php echo __('ID du membre'); ?></td>
                            </tr>
                            <tr>
                                <td><span class="required">password</span></td>
                                <td>string</td>
                                <td><?php echo __('Mot de passe du membre'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'formation/membre/connect')
    ->set('email', '<EMAIL>')
    ->set('password', '<?php echo uniqid(); ?>');</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [id_membre] => 1234
    [nom] => Watson
    [prenom] => John
    [email] => <EMAIL>
)</code></pre>
        </div>
    </div>
</div>


<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Modification d\'un membre'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Mise à jour d\'un membre'); ?></h4>
                    <table>
                        <thead>
                        <tr>
                            <th><?php echo __('Paramètre'); ?></th>
                            <th><?php echo __('Type'); ?></th>
                            <th><?php echo __('Description'); ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>email</td>
                            <td>string</td>
                            <td><?php echo __('Adresse email du membre'); ?></td>
                        </tr>
                        <tr>
                            <td>ou</td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>id_membre</td>
                            <td>int</td>
                            <td><?php echo __('ID du membre'); ?></td>
                        </tr>
                        <tr>
                            <td>prenom</td>
                            <td>string</td>
                            <td><?php echo __('Nouveau prénom du membre'); ?></td>
                        </tr>
                        <tr>
                            <td>nom</td>
                            <td>string</td>
                            <td><?php echo __('Nouveau nom du membre'); ?></td>
                        </tr>
                        <tr>
                            <td>new_email</td>
                            <td>string</td>
                            <td><?php echo __('Nouvelle adresse email du membre'); ?></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'formation/membre/edit')
    ->set('email', '<EMAIL>')
    ->set('new_email', '<EMAIL>')
    ->set('nom', 'Watson')
    ->set('prenom', 'Brad');
    </code></pre>
            <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('Le membre a été mis à jour avec succès'); ?>.
)</code></pre>
        </div>
    </div>
</div>


<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Suppression d\'un membre'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Suppression d\'un membre'); ?></h4>
                    <table>
                        <thead>
                            <tr>
                                <th><?php echo __('Paramètre'); ?></th>
                                <th><?php echo __('Type'); ?></th>
                                <th><?php echo __('Description'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>email</td>
                                <td>string</td>
                                <td><?php echo __('Adresse email du membre'); ?></td>
                            </tr>
                            <tr>
                                <td>ou</td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>id_membre</td>
                                <td>int</td>
                                <td><?php echo __('ID du membre'); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'formation/membre/delete')
    ->set('email', '<EMAIL>');</code></pre>
    <pre class="retour"><code class="php hljs">Array
(
    [status] => true
    [message] => <?php echo __('Le membre a bien été supprimé'); ?>.
)</code></pre>
        </div>
    </div>
</div>


<div class="method" id="intro">
    <div class="method-section clearfix" style="padding-top: 0px;">
        <div class="method-description">
            <h3><?php echo __('Connections d\'un membre'); ?></h3>
            <div class="method-content">
                <div class="info">
                    <h4><?php echo __('Connections d\'un membre'); ?></h4>
                    <table>
                        <thead>
                        <tr>
                            <th><?php echo __('Paramètre'); ?></th>
                            <th><?php echo __('Type'); ?></th>
                            <th><?php echo __('Description'); ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>email</td>
                            <td>string</td>
                            <td><?php echo __('Adresse email du membre'); ?></td>
                        </tr>
                        <tr>
                            <td>ou</td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>id_membre</td>
                            <td>int</td>
                            <td><?php echo __('ID du membre'); ?></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="method-example">
            <pre><code class="php hljs">$lbApi->set('action', 'formation/membre/connections')
    ->set('email', '<EMAIL>');</code></pre>
            <pre class="retour"><code class="php hljs">Array
(
    [id_membre] => 1234
    [prenom] => John
    [nom] => Watson
    [email] => <EMAIL>
    [connections] => Array
        (
            [0] => Array
                (
                    [ip] => 123.456.789.100
                    [date] => <?php echo date('Y-m-d H:i:s', strtotime('-1 weeks')) . "\n"; ?>
                )
            [1] => Array
                (
                    [ip] => 123.456.789.100
                    [date] => <?php echo date('Y-m-d H:i:s', strtotime('-10 days 4 hours 12 minutes')) . "\n"; ?>
                )

        )
)</code></pre>
        </div>
    </div>
</div>
