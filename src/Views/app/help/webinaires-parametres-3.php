<h1>15.10 <?php echo __('Paramètres'); ?> - <?php echo __('Inscriptions'); ?></h1>

<div class="video-block-center" style="width: 853px; height: 480px; margin-bottom:20px;">
    <iframe width="853" height="480" src="https://www.youtube.com/embed/3GXJgd4snVg" frameborder="0" allowfullscreen="" kwframeid="1"></iframe>
</div>

<p><?php echo __('Vous pouvez choisir ici un certain nombre de renseignements qui seront demandés à vos prospects sur la page d\'inscription du webinaire.'); ?> <?php echo __('Pour chacun d’entre eux, cochez la case « Obligatoire » si nécessaire.'); ?></p>
<p><?php echo __('Si vous le souhaitez, vous pouvez aussi ajouter vos propres questions en cliquant sur le bouton "Ajouter une question".'); ?> <?php echo __('Indiquez alors le titre de votre question, par exemple « Quelle est votre profession ? ».'); ?> <?php echo __('Sélectionnez ensuite le type de réponse :'); ?></p>
<ul>
    <li><?php echo __('réponse unique'); ?> : <?php echo __('vos visiteurs ne pourront choisir qu’une seule réponse parmi celles proposées'); ?></li>
    <li><?php echo __('réponse multiple'); ?> : <?php echo __('vos visiteurs pourront cocher plusieurs réponses parmi celles proposées'); ?></li>
    <li><?php echo __('et enfin « champ libre » affiche un champ de texte.'); ?></li>
</ul>

<p><?php echo __('Cochez la case « Obligatoire » si nécessaire et ajoutez ensuite les différentes réponses possibles, par exemple : « infopreneur », « conférencier », etc.'); ?><br><?php echo __('Notez qu\'il ne sera plus possible de modifier les renseignements dès que des personnes se seront inscrites à ce webinaire.'); ?></p>

<p><?php echo __('Vous trouverez ensuite une liste de thèmes disponibles pour votre page d’inscription.'); ?> <?php echo __('Vous pouvez utiliser la page d’inscription par défaut de LearnyBox en sélectionnant « Page par défaut ».'); ?><br><?php echo __('Si vous sélectionnez un thème, cela créera un tunnel de vente portant le nom de votre webinaire, et créera une page d’inscription dans ce tunnel.'); ?> <?php echo __('Vous pourrez ensuite complètement la personnaliser en changeant les images, les couleurs, le texte, etc.'); ?></p>

<p><?php echo __('Le paramètre « Tracking d\'inscription » vous permet d’insérer du code javascript sur votre page, comme des codes de tracking Facebook, etc.'); ?></p>
<p><?php echo __('Si vous souhaitez enregistrer les inscrits à votre webinaire dans un autorépondeur non pris en charge par LearnyBox, collez simplement le code HTML d’un formulaire dans le bloc « Autorépondeur ».'); ?> <?php echo __('Si votre autorépondeur est pris en charge par LearnyBox, utilisez plutôt le menu ci-contre.'); ?></p>