<?php
$memberStatement = __('les membres');
if ($groupName) {
    $memberStatement .= ' ' . __('du group %s', $groupName);
} elseif ($memberName) {
    $memberStatement = __('le membre %s', $memberName);
}
?>

<p><?php echo __('Bonjour %s,', $userName); ?></p>
<p>
    <?php echo __(
        'Le document de type %s concernant %s de la formation %s est disponible au téléchargement jusqu\'au %s en cliquant sur le lien suivant :',
        $documentName,
        $memberStatement,
        $formationName,
        $expirationDate
    ); ?>
    <a href="<?php echo $downloadUrl; ?>">
        <?php echo __('Télécharger'); ?>
    </a>
</p>
<p>
    <?php echo __('Ce document est aussi disponible dans votre bibliothèque de médias :'); ?>
    <a href="<?php echo $mediaUrl; ?>">
        <?php echo __('ici'); ?>
    </a>
</p>
<p><?php echo __('En vous souhaitant bonne réception.'); ?></p>
<p><?php echo __('L\'équipe LearnyBox.'); ?></p>
