<div class="form-group checkboxsell">

    <table class="table">
        <thead>
            <tr class="checkboxsell-header-notif" style="display: none">
                <th></th>
                <th>
                    <div>
                        <span><?php echo __('Répartition du volume'); ?></span>
                    </div>
                    <div>
                        <span class="header-subtitle"><?php echo "(" . __('Une estimation nous suffit') . ")"; ?></span>
                    </div>
                </th>
            </tr>
        </thead>
        <tbody>
        <?php
            $required = ($question->getMandatory()) ? 'required' : '';
            $questionId = $question->getId();
        ?>
        <?php
        foreach ($answers as $a) {
            $js = '';
            $dataOnchange = '0';
            if (!empty($dependAnswersIds) && in_array($a->getId(), $dependAnswersIds)) {
                $dataOnchange = '1';
            }

            $id = $a->getId();
            ?>
            <tr>
                <td>
                    <div class="checkbox checkbox-success <?php echo $required; ?>">
                        <input id="checkbox_question_type_<?php echo $id; ?>"
                               name="checkbox_question_type_<?php echo $questionId; ?>"
                               type="checkbox"
                               data-onchange="<?php echo $dataOnchange; ?>"
                               data-question-id="<?php echo $questionId; ?>"
                               value="<?php echo $id; ?>"
                               class="answer">
                        <label for="checkbox_question_type_<?php echo $id; ?>">
                        <?php echo $a->getLibelle(); ?>
                        </label>
                    </div>
                </td>
                <td class="input-range">
                    <div class="input-range-general input-range-<?php echo $id; ?>" style="display: none">
                        <select
                            class="form-control select-question-type answer"
                            id="select_question_type_<?php echo $questionId . '_' . $id; ?>"
                            name="select_question_type_<?php echo $questionId . '_' . $id; ?>"
                            data-question-id="<?php echo $questionId . '_' . $id; ?>"
                            data-dependonanswer="<?php echo $id; ?>">
                            <option id="" value="" data-onchange="0"></option>
                            <option id="1" value="<?php echo __('beaucoup'); ?>"><?php echo __('Beaucoup'); ?></option>
                            <option id="2" value="<?php echo __('moyen'); ?>"><?php echo __('Modéré'); ?></option>
                            <option id="3" value="<?php echo __('peu'); ?>"><?php echo __('Peu'); ?></option>
                        </select>
                    </div>
                </td>
            </tr>

            <?php
        }
        ?>
        </tbody>
    </table>

</div>

<script type="application/javascript">
(function ($) {
    $(document).ready(function() {
        $(':checkbox[name="checkbox_question_type_<?php echo $questionId; ?>"]').change(function () {
            var questionId = $(this).data('question-id');
            var checkboxValue = $(this).val();
            var nbSelected = $(':checkbox[name="checkbox_question_type_<?php echo $questionId; ?>"]:checked').length;
            if (!this.checked) {
                $('.input-range-' + checkboxValue).hide();
                $('.input-range-' + checkboxValue + ' input[type="number"]').val('');
                updatePercentage();
            }
            if(nbSelected < 2) {
                $('.input-range-general').hide();
                $('.checkboxsell-header-notif').hide();
                $('.onboarding-task-group-footer-btn').removeAttr('disabled');
                $('.onboarding-task-group-footer-btn').text('question suivante');
                $('input[type="number"]').val('');
            }
            else if (this.checked && nbSelected >= 2) {

                // disable submit button
                $('.onboarding-task-group-footer-btn').attr('disabled', 'disabled');

                $('.checkboxsell-header-notif').show();
                $(':checkbox[name="checkbox_question_type_<?php echo $questionId; ?>"]:checked').each(function () {
                    var checkboxValueChecked = $(this).val();
                    $('.input-range-' + checkboxValueChecked).show();
                    disableButtonNextQuestion();
                });
            }
        });

        $('input[type=number]').change(function () {
            updatePercentage();
        });

        function disableButtonNextQuestion() {
            $(".select-question-type:visible").change(function () {
                $(".select-question-type:visible").each(function () {
                    if ($(this).attr('value') == '') {
                        $('.onboarding-task-group-footer-btn').attr('disabled', 'disabled');
                        return false;
                    }
                    $('.onboarding-task-group-footer-btn').removeAttr('disabled');
                });
            });
        }

        function updatePercentage() {
            var percentage = 0;
            $('input[type=number]:visible').each(function () {
                percentage = percentage + Number($(this).val());
                //$('.pourcentage-division').text(percentage + ' %');
                var restPercentage = 100 - percentage;
                var textButton = 'encore ' + restPercentage + ' %';
                if (restPercentage < 0) {
                    textButton = 'Répartition supérieure à 100%';
                }

                $('.onboarding-task-group-footer-btn').text(textButton);

                if (percentage == 100) {
                    $('.pourcentage-division').removeClass('pourcentage-division-fail');
                    $('.pourcentage-division').addClass('pourcentage-division-success');
                    $('.onboarding-task-group-footer-btn').removeAttr('disabled');
                    $('.onboarding-task-group-footer-btn').text('question suivante');
                } else {
                    $('.pourcentage-division').removeClass('pourcentage-division-success');
                    $('.pourcentage-division').addClass('pourcentage-division-fail');
                    $('.onboarding-task-group-footer-btn').attr('disabled', 'disabled');
                }
            });
        }
    });
})(jQuery);
</script>

