<div class="form-group">
    <?php if ($isRelativeQuestion) { ?>
    <label for="select_question_type_<?php echo $question->getId(); ?>"><?php echo $question->getLibelle(); ?></label>
    <?php } ?>
    <?php
        // @TODO
        $required = ($question->getMandatory()) ? 'required' : '';
    ?>
    <select class="form-control select-question-type answer <?php echo $required; ?>" id="select_question_type_<?php echo $question->getId(); ?>" name="select_question_type_<?php echo $question->getId(); ?>" data-question-id="<?php echo $question->getId(); ?>" <?php echo $required; ?>>
        <option id="" value="" data-onchange="0"></option>
        <?php
        foreach ($answers as $a) {
            $js = '';
            $dataOnchange = '0';
            if (!empty($dependAnswersIds) && in_array($a->getId(), $dependAnswersIds)) {
                $js = 'onclick="$(\'#relative-questions\').show()"';
                $dataOnchange = '1';
            }

            $id = $a->getId();
            ?>
        <option id="<?php echo $id; ?>" value="<?php echo $id; ?>" data-onchange="<?php echo $dataOnchange; ?>"><?php echo $a->getLibelle(); ?></option>
            <?php
        }
        ?>
    </select>
</div>

<script type="application/javascript">
    (function ($) {
        $('#select_question_type_<?php echo $question->getId(); ?>').change(function() {
            var eltSelected = $('#select_question_type_<?php echo $question->getId(); ?> option:selected');
            var onchange = eltSelected.data('onchange');

            if (onchange == '1' && !$(this).parent().hasClass('not-selected')) {
                $('#relative-questions').show();
            }
        });
    })(jQuery);
</script>
