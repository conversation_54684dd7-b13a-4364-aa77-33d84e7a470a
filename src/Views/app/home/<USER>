<?php
if (!empty($plugins)) { ?>
    <div class="section plugins">
        <div class="section-title"><?php echo __('Plugins'); ?></div>
        <div class="row cards-list">
            <?php foreach ($plugins as $plugin) : ?>
                <div class="col-lg-4 col-md-6 col-sm-6 col-xs-12">
                    <?php
                    $notifications = 0;
                    $notificationsTooltip = '';
                    foreach (\Learnybox\Enums\MigrationObjectEnum::getObjectsData() as $appName => $object) {
                        if (isset($objectsToMigrate[$appName]) && $object['universeId'] == $plugin['universe_id']) {
                            $notificationsTooltip .= $objectsToMigrate[$appName]['libelle'];
                            $notifications += $objectsToMigrate[$appName]['count'] ?? 0;
                        }
                    }
                    echo Eden_Template::i()
                        ->set('title', $plugin['nom'])
                        ->set('icon', $plugin['icon-medium'])
                        ->set('description', (isset($plugin['description_short']) ? $plugin['description_short'] : ''))
                        ->set('link', \Tools::makeLink('app', $plugin['homepage']))
                        ->set('notifications', $notifications)
                        ->set('notificationsTooltip', $notificationsTooltip)
                        ->parsePHP(VIEWS_PATH . '/common/cards/large_card.php');
                    ?>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php
}
?>
