<?php

use Learnybox\Helpers\RouterHelper;
use Learnybox\Services\DI\ContainerBuilderService;

$aff_devise = DEFAULT_CURRENCY;
$param = eden()->Reglages()->appGetParametreByName('affilie_devise');
if ($param and $param['value']) {
    $aff_devise = $param['value'];
}

$container = ContainerBuilderService::getInstance();

/** @var int $id_campaign */
/** @var array $regles */
?>
<div class="top-page-title">
    <h3><?php echo __('Règles'); ?></h3> <?php echo $helpTooltip; ?>
    <ul class="subnav">
        <li>
            <a class="btn btn-secondary" href="<?php echo RouterHelper::generate('app_affiliation_regle_add', ['campaignId' => $id_campaign]); ?>">
                <i class="fa fa-plus"></i> <?php echo __('Ajouter une règle'); ?>
            </a>
        </li>
    </ul>
</div>

<?php if (!$regles) { ?>
    <div class="alert alert-info"><?php echo __('Aucune règle enregistrée'); ?></div>
<?php } else { ?>
    <table class="table table-striped table-condensed datatable">
        <thead>
            <tr>
                <th><?php echo __('Condition'); ?></th>
                <th><?php echo __('Action à exécuter'); ?></th>
                <th class="no-sort"></th>
            </tr>
        </thead>
        <tbody>
        <?php foreach ($regles as $regle) { ?>
            <tr>
                <td>
                <?php switch ($regle['condition_regle']) {
                    case 'nombre_commission':
                        echo n__('Si un affilié a plus de %d commission dans cette campagne', 'Si un affilié a plus de %d commissions dans cette campagne', $regle['value'], $regle['value']);
                        break;

                    case 'page_de_vente':
                        echo __('Si la vente vient de la page') . ' ';
                        $page = $container->get(TunnelsPages::class)->getPageById($regle['value']);
                        if ($page) {
                            echo '<strong>' . $page['nom'] . '</strong>';
                        } else {
                            echo '<span class="label label-danger">' . __('Page inconnue') . '</span>';
                        }
                        break;

                    case 'nom_produit':
                        echo __('Si le nom du produit de la transaction est') . ' <strong>' . $regle['value'] . '</strong>';
                        break;

                    case 'montant_sup':
                        echo __('Si le montant de la transaction est supérieur à') . ' <strong>' . $regle['value'] . \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol() . '</strong>';
                        break;

                    case 'montant_inf':
                        echo __('Si le montant de la transaction est inférieur à') . ' <strong>' . $regle['value'] . \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol() . '</strong>';
                        break;

                    case 'amount_campaign':
                        echo __('Si l\'affilié a généré plus de') . ' <strong>' . Tools::formatAmount($regle['value'], $aff_devise) . ' ' . __('de commission dans cette campagne') . '</strong>';
                        break;

                    case 'amount_total':
                        echo __('Si l\'affilié a généré plus de') . ' <strong>' . Tools::formatAmount($regle['value'], $aff_devise) . ' ' . __('de commission, toutes campagnes confondues') . '</strong>';
                        break;

                    case 'lead_exist':
                        echo __('Si le contact était déjà présent dans votre base de contacts, avant de cliquer sur un lien d\'affilié.');
                        break;

                    case 'client_exist':
                        echo __('Si le contact était déjà client avant sa commande, et avait enregistré plus de') . ' <strong>' . Tools::formatAmount($regle['value'], $aff_devise) . ' ' . __('de transactions') . '</strong>';
                        break;
                } ?>
                </td>
                <td>
                <?php switch ($regle['action']) {
                    case 'pas_de_commission':
                        echo __('La commission n\'est pas enregistrée');
                        break;
                    case 'nouveau_taux':
                        echo __('Le taux de commission passe à') . ' <strong>' . $regle['commission'] . '%</strong>';
                        break;
                    case 'nouveau_montant':
                        echo __('Le montant de la commission est de') . ' <strong>' . Tools::formatAmount($regle['commission'], $aff_devise) . '</strong>';
                        break;
                    case 'max_montant':
                        echo __('Limiter le montant de la commission à %s', '<strong>' . Tools::formatAmount($regle['commission'], $aff_devise) . '</strong>');
                        break;
                } ?>
                </td>
                <td>
                    <div class="btn-group action">
                        <a type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="dropdown-toggle ">
                            <i class="dropdown-icon fa-solid fa-ellipsis-vertical"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="<?php echo RouterHelper::generate('app_affiliation_regle_edit', ['campaignId' => $id_campaign, 'regleId' => $regle['id_regle']]); ?>">
                                    <i class="fa-regular fa-gear"></i> <?php echo __('Modifier'); ?>
                                </a>
                            </li>
                            <li class="divider"></li>
                            <li>
                                <a onclick="$('#suppr<?php echo $regle['id_regle']; ?>').submit();">
                                    <i class="fa fa-trash-o"></i> <?php echo __('Supprimer'); ?>
                                </a>
                            </li>
                            <form method="post" action="" id="suppr<?php echo $regle['id_regle']; ?>" style="display: none;">
                                [[CSRF]]
                                <input type="hidden" name="form_action" value="aff_regle_suppr">
                                <input type="hidden" name="id_regle" value="<?php echo $regle['id_regle']; ?>">
                            </form>
                        </ul>
                    </div>
                </td>
            </tr>
        <?php } ?>
        </tbody>
    </table>
<?php } ?>
