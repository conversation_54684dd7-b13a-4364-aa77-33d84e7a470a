<?php

/** @var string $type */
/** @var array $renseignementsPersonnalises */

foreach ($renseignementsPersonnalises as $random => $renseignementPersonnalise) { ?>
    <input type="hidden" name="rens_personnalises_<?php echo $type; ?>[<?php echo $random; ?>][question]" value="<?php echo $renseignementPersonnalise['question']; ?>" />
    <input type="hidden" name="rens_personnalises_<?php echo $type; ?>[<?php echo $random; ?>][type]" value="<?php echo $renseignementPersonnalise['type']; ?>" />

    <?php
    if (isset($renseignementPersonnalise['obligatoire']) and $renseignementPersonnalise['obligatoire'] == "on") { ?>
        <input type="hidden" name="rens_personnalises_<?php echo $type; ?>[<?php echo $random; ?>][obligatoire]" value="on" />
        <?php
    }

    if (isset($renseignementPersonnalise['reponses'])) {
        foreach ($renseignementPersonnalise['reponses'] as $_reponse) { ?>
            <input type="hidden" name="rens_personnalises_<?php echo $type; ?>[<?php echo $random; ?>][reponses][]" value="<?php echo $_reponse; ?>" />
            <?php
        }
    } ?>

    <div class="controls">
        <div class="checkbox">
            <label for="rens_personnalises_<?php echo $type; ?>_<?php echo $random; ?>">
                <input type="checkbox" name="rens_personnalises_<?php echo $type; ?>[<?php echo $random; ?>][id]" id="rens_personnalises_<?php echo $type; ?>_<?php echo $random; ?>" value="<?php echo $random; ?>" <?php if (isset($renseignementPersonnalise['id'])) {
                    echo 'checked';
                                                                } ?> />
                <?php echo $renseignementPersonnalise['question']; ?>
            </label>
        </div>
    </div>
    <?php
}
