<?php

use Learnybox\Helpers\RouterHelper;

?>

<div class="btn-group action">
    <a type="button" class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <i class="dropdown-icon fa-solid fa-ellipsis-vertical"></i>
    </a>
    <ul class="dropdown-menu dropdown-menu-right">
        <li><a class="edit-tag" data-id="<?php echo $tag->getId(); ?>" data-name="<?php echo $tag->getName(); ?>" data-color="<?php echo $tag->getColor(); ?>"
               href="<?php echo RouterHelper::generate('app_formation_forum_tag_edit', ['formationId' => $tag->getForum()->getIdformation(), 'action' => 'edit', 'tagId' => $tag->getId()]); ?>"><i
                    class="fa-regular fa-gear"></i> <?php echo __('Modifier'); ?></a></li>
        <li><a class="delete-tag" data-id="<?php echo $tag->getId(); ?>"><i class="fa fa-trash-o"></i> <?php echo __('Supprimer'); ?></a></li>
    </ul>
</div>
