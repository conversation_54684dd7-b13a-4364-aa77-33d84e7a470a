<?php

use Learnybox\Factories\BlankStatePage\BlankStatePageServiceFactory;

$container = \Learnybox\Services\DI\ContainerBuilderService::getInstance();

echo $stats;

echo '<div class="row row-eq-height modules m-t-20">';

//derniers articles
$articles = eden()->Articles()->getLast5Articles();
if (!$articles) {
    $factory = BlankStatePageServiceFactory::create('blog_articles');
    echo '<div class="blankstate-eq-height col-md-6">' . $factory->render() . '</div>';
} else {
    echo '
<div id="articles" class="box col-md-6">
    <div class="bootcards-list">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-file-text-o"></i> ' . __('Articles') . '
                    <a href="' . Tools::makeLink('app', 'articles') . '" class="pull-right">' . __('Voir tout') . '</a>
                </h3>
            </div>
            <div class="list-group">';

    $i = 0;
    foreach ($articles as $article) {
        ++$i;

        $etat = '';
        switch ($article['publication']) {
            case 'enattente':
                $etat = '<span class="label label-status label-small label-default">' . __('En attente') . '</span>';
                break;
            case 'encours':
                $etat = '<span class="label label-status label-small label-success">' . __('Publié') . '</span>';
                break;
            case 'termine':
                $etat = '<span class="label label-status label-small label-info">' . __('Désactivé') . '</span>';
                break;
            default:
                $etat = '<span class="label label-status label-small label-default">' . $article['publication'] . '</span>';
                break;
        }

        echo '
    		<a class="list-group-item ' . ($i === 5 || $i - 1 === array_key_last($articles) ? "border-b-r-16 border-b-l-16" : "") . '" 
    		   href="' . Tools::makeLink('app', 'article' . ($article['version'] == 2 ? '_v2' : ''), $article['id_article']) . '">
                        <h3 class="list-group-item-heading">
                            ' . $article['titre'] . '
                            <span class="pull-right">' . $etat . '</span>
                        </h3>';

        //categorie
        $categorie = eden()->Categories()->getCategorieById($article['id_categorie']);
        if ($categorie) {
            echo '<p class="list-group-item-text">' . $categorie['nom'] . '</p>';
        }

        if ($article['date_publication'] and '0000-00-00 00:00:00' != $article['date_publication']) {
            echo '<p class="list-group-item-text">
                    <i class="fa fa-clock-o"></i> ' . \Learnybox\Helpers\LocalizedDatetime::getDateTime(new DateTime($article['date_publication']), true) . '
                  </p>';
        } else {
            echo '<p class="list-group-item-text">
                    <i class="fa fa-clock-o"></i> ' . \Learnybox\Helpers\LocalizedDatetime::getDateTime(new DateTime($article['date']), true) . '
                  </p>';
        }

        echo '
            </a>';

        if ($i >= 5) {
            break;
        }
    }

    echo '
            </div>
        </div>
    </div>
</div>';
}


//derniers commentaires
$commentaires = eden()->Commentaires()->getAllCommentaires(5);
if (!$commentaires) {
    $factory = BlankStatePageServiceFactory::create('blog_dashboard_comments');
    echo '<div class="blankstate-eq-height col-md-6">' . $factory->render() . '</div>';
} else {
    echo '
<div id="commentaires" class="box col-md-6">
    <div class="bootcards-list">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-comments"></i> ' . __('Commentaires') . '
                    <a href="' . Tools::makeLink('app', 'commentaires') . '" class="pull-right">' . __('Voir tout') . '</a>
                </h3>
            </div>
            <div class="list-group">';

    $i = 0;
    foreach ($commentaires as $commentaire) {
        ++$i;

        //utilisateur
        $user = false;
        if (0 != $commentaire['user_id']) {
            $user = eden()->Utilisateurs()->getUserById($commentaire['user_id']);
        }

        $avatar = '';
        if ($user) {
            $avatar .= '<img class="img-circle pull-left" src="' . $container->get(\Learnybox\Services\Users\UsersService::class)->getAvatar($user['email'], 32) . '" border="0" width="32" height="32" alt="' . $user['fname'] . ' ' . $user['lname'] . '" style="margin-right: 15px;" />';
        } else {
            $avatar .= '<img class="img-circle pull-left" src="' . $container->get(\Learnybox\Services\Users\UsersService::class)->getGuestAvatar($commentaire['email'], 32, $commentaire['prenom']) . '" border="0" width="32" height="32" alt="' . $commentaire['prenom'] . '" style="margin-right: 15px;" />';
        }

        $nom = $commentaire['nom'] . ' ' . $commentaire['prenom'];
        if ($user) {
            $nom = $user['fname'] . ' ' . $user['lname'];
        }

        $etat = '';
        switch ($commentaire['etat']) {
            case 'enattente':
                $etat .= '<span class="label label-status label-small label-default">' . __('En attente') . '</span>';
                break;
            case 'approuve':
                $etat .= '<span class="label label-status label-small label-success">' . __('Approuvé') . '</span>';
                break;
            case 'desapprouve':
                $etat .= '<span class="label label-status label-small label-danger">' . __('Désapprouvé') . '</span>';
                break;
            case 'spam':
                $etat .= '<span class="label label-status label-small label-danger">' . __('Indésirable') . '</span>';
                break;
            default:
                $etat .= '<span class="label label-status label-small label-default">' . $commentaire['etat'] . '</span>';
                break;
        }

        echo '
    		<a class="list-group-item ' . ($i === 5 || $i - 1 === array_key_last($commentaires) ? "border-b-r-16 border-b-l-16" : "") . '"
    		   href="' . Tools::makeLink('app', 'commentaire', $commentaire['id_commentaire']) . '">
                ' . $avatar . '
                <h3 class="list-group-item-heading">
                    ' . $nom . '
                    <span class="pull-right">' . $etat . '</span>
                </h3>
                <p class="list-group-item-text" style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">' . stripslashes($commentaire['commentaire']) . '</p>
                <p class="list-group-item-text"><i class="fa fa-clock-o"></i> ' . \Learnybox\Helpers\LocalizedDatetime::getDateTime(new DateTime($commentaire['date']), true) . '</p>
            </a>';

        if ($i >= 5) {
            break;
        }
    }

    echo '
            </div>
        </div>
    </div>
</div>';
}

echo '</div>';
