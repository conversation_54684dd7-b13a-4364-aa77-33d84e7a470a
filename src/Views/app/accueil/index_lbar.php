<?php

use Learnybox\Enums\Mailer\MailerEnum;
use Learnybox\Helpers\RouterHelper;
use Learnybox\Services\Client\Limit\AbstractClientLimit;
use Learnybox\Services\Client\Limit\ContactsClientLimit;
use Learnybox\Services\Client\Limit\MailsClientLimit;
use Learnybox\Services\Mail\ClientMailerService;
use Learnybox\Services\Mail\Domain\MailDomainService;

$container = Learnybox\Services\DI\ContainerBuilderService::getInstance();
$contactsClientLimit = $container->get(ContactsClientLimit::class);
$mailsClientLimit = $container->get(MailsClientLimit::class);

//stats
$nb_users = $contactsClientLimit->getCount($_SESSION['id_client']);

$dateObjectUserToday = getDateTimeUser();

$dateUserToday1 = $dateObjectUserToday->format('Y-m-d') . ' 00:00:00';
$dateUserToday2 = $dateObjectUserToday->format('Y-m-d') . ' 23:59:59';

$dateToday1 = convertDateFromClientToServerTimezone($dateUserToday1);
$dateToday2 = convertDateFromClientToServerTimezone($dateUserToday2);

$nbUsersToday = eden()->Lbar_Users()->getCountUsersByDate($dateToday1, $dateToday2);

$dateObjectUserToday->modify('-1 day');
$dateUserYesterday1 = $dateObjectUserToday->format('Y-m-d') . ' 00:00:00';
$dateUserYesterday2 = $dateObjectUserToday->format('Y-m-d') . ' 23:59:59';

$dateYesterday1 = convertDateFromClientToServerTimezone($dateUserYesterday1);
$dateYesterday2 = convertDateFromClientToServerTimezone($dateUserYesterday2);
$nbUsersYesterday = eden()->Lbar_Users()->getCountUsersByDate($dateYesterday1, $dateYesterday2);

$maxLbarUsers = $contactsClientLimit->getLimit($_SESSION['id_client']);

//pourcentage
if ($contactsClientLimit->isUnlimited($_SESSION['id_client'])) {
    $pourcentage = 0;
} else {
    $pourcentage = round($nb_users * 100 / $maxLbarUsers, 0);
}
if ($pourcentage > 100) {
    $pourcentage = 100;
}
if ($pourcentage < 1) {
    $pourcentage = 0;
}

$class = 'progress-bar-info';
if ($pourcentage) {
    switch ($pourcentage) {
        case $pourcentage >= 90:
            $class = 'progress-bar-danger';
            break;
        case $pourcentage > 75:
            $class = 'progress-bar-warning';
            break;
    }
}

$indication = number_format($nb_users, 0, '', ' ') . ' / ' . number_format($maxLbarUsers, 0, '', ' ');

if ($nb_users < $maxLbarUsers) {
    //réinitialisation des relances
    eden()->Lbar_Reglages()->reinit_relances($_SESSION['id_client']);
}


//pourcentage
$emails_envoyes = $mailsClientLimit->getCount($_SESSION['id_client']);
$maxMails = $mailsClientLimit->getLimit($_SESSION['id_client']);
$pourcentage2 = 0;
if ($maxMails > AbstractClientLimit::NONE) {
    $pourcentage2 = round($emails_envoyes * 100 / $maxMails, 0);
}
if ($pourcentage2 > 100) {
    $pourcentage2 = 100;
}
if ($pourcentage2 < 1) {
    $pourcentage2 = 0;
}

$class2 = 'progress-bar-info';
if ($pourcentage2) {
    switch ($pourcentage2) {
        case $pourcentage2 >= 90:
            $class2 = 'progress-bar-danger';
            break;
        case $pourcentage2 > 75:
            $class2 = 'progress-bar-warning';
            break;
    }
}
$indication2 = $emails_envoyes . ' / ' . number_format($maxMails, 0, '', ' ');
?>
    <form method="post" id="search_last_contacts" action="<?php echo Tools::makeLink('app', 'autorepondeur', 'contacts'); ?>" style="display:none">
        [[CSRF]]
        <input type="hidden" name="lbar_search_contacts" value="0">
    </form>

    <form method="post" id="search_contacts_today" action="<?php echo Tools::makeLink('app', 'autorepondeur', 'contacts'); ?>" style="display:none">
        [[CSRF]]
        <input type="hidden" name="conditions[0][field]" value="date">
        <input type="hidden" name="conditions[0][param]" value="<?php echo $dateUserToday1; ?>">
        <input type="hidden" name="conditions[0][param2]" value="<?php echo $dateUserToday2; ?>">
        <input type="hidden" name="lbar_search_contacts" value="true">
    </form>

    <form method="post" id="search_contacts_yesterday" action="<?php echo Tools::makeLink('app', 'autorepondeur', 'contacts'); ?>" style="display:none">
        [[CSRF]]
        <input type="hidden" name="conditions[0][field]" value="date">
        <input type="hidden" name="conditions[0][param]" value="<?php echo $dateUserYesterday1; ?>">
        <input type="hidden" name="conditions[0][param2]" value="<?php echo $dateUserYesterday2; ?>">
        <input type="hidden" name="lbar_search_contacts" value="true">
    </form>

    <?php if (!ClientMailerService::isClientFromMailerType($_SESSION['id_client'], MailerEnum::MAILER_BREVO)) : ?>
        <div class="alert alert-danger">
            <div class="alert-title">
              <i class="fa-solid fa-triangle-exclamation"></i> <?php echo __('Action requise pour éviter le blocage de vos envois d’emails'); ?>
            </div>
            <div class="alert-txt">
                <?php echo __('Passez dès aujourd\'hui à LearnyMail V2 en accédant à vos paramètres d\'email. Cliquez sur le lien ci-dessous pour commencer la transition :'); ?>
            </div>
            <div class="alert-link">
                <a href="<?php echo RouterHelper::generate('app_reglages_emails', ['_fragment' => 'domainList']) ?>">
                    <?php echo __('Passer à LearnyMail V2'); ?>
                </a>
            </div>
            <div class="alert-txt">
                <?php echo __(
                    '%sAttention%s : À partir du %s14 octobre%s, si vous n\'avez pas effectué la transition vers LearnyMail V2, vos emails seront envoyés depuis l\'adresse email : %s%s%s.',
                    '<strong>',
                    '</strong>',
                    '<strong>',
                    '</strong>',
                    '<strong>',
                    MailDomainService::getDefaultFromEmail($_SESSION['id_client']),
                    '</strong>'
                ) ?>
            </div>
        </div>
    <?php endif; ?>

<?php
$cardsOutput = $panelStatsRenderer->renderCardStatProgress(
    __('Nombre d\'inscriptions'),
    __('Aujourd\'hui'),
    $nbUsersToday,
    [],
    ['href' => '#', 'onclick' => '$(\'#search_contacts_today\').submit();', 'title' => __('Nombre total de contacts inscrits aujourd\'hui')],
    'fa-regular fa-ballot-check'
);
$cardsOutput .= $panelStatsRenderer->renderCardStatProgress(
    __('Nombre d\'inscriptions'),
    __('Hier'),
    $nbUsersYesterday,
    [],
    ['href' => '#', 'onclick' => '$(\'#search_contacts_yesterday\').submit();', 'title' => __('Nombre total de contacts inscrits hier')],
    'fa-regular fa-ballot-check text-warning',
    'bg-warning-light'
);
if (!$contactsClientLimit->isUnlimited($_SESSION['id_client'])) {
    $cardsOutput .= $panelStatsRenderer->renderCardStatProgress(
        __('Contacts inscrits'),
        __('Total'),
        $indication,
        ['value' => $pourcentage, 'class' => $class],
        ['href' => RouterHelper::generate('app_autorepondeur_upgrade')],
        'fa-regular fa-address-book'
    );
}
if (!$mailsClientLimit->isUnlimited($_SESSION['id_client'])) {
    $cardsOutput .= $panelStatsRenderer->renderCardStatProgress(
        __('Emails envoyés'),
        __('Au cours de ce mois'),
        $indication2,
        ['value' => $pourcentage2, 'class' => $class2],
        ['href' => RouterHelper::generate('app_autorepondeur_upgrade')]
    );
}
echo $panelStatsRenderer->renderPanelStats($cardsOutput);

//envois
$envois = eden()->Lbar_Envois()->getLastEnvois(3);
echo '
<div class="top-page-title m-b-20">
    <h3>' . __('Derniers envois') . '</h3>
    <ul class="subnav">
        <li><a class="btn btn-secondary" href="' . Tools::makeLink('app', 'autorepondeur', 'envoi_tutoriel_v2', 'clear') . '"><i class="fa fa-paper-plane"></i> ' . __('Envoyer un email') . '</a></li>
    </ul>
</div>';

echo Eden_Template::i()->set('envois', $envois)
    ->parsePHP(VIEWS_PATH . '/app/lbar/envois.php');

// derniers inscrits
if ($lastSubscriptions) {
    echo $lastSubscriptions;
}
