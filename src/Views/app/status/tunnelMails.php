<div id="tunnel-status-page" class="container" data-object-type="<?php echo \Learnybox\Enums\ObjectTypeEnum::EMAIL; ?>">

    <?php
    echo Eden_Template::i()
        ->set('selected', 'mails')
        ->set('tunnel_id', $tunnel_id)
        ->parsePHP(VIEWS_PATH . '/app/status/tunnelHeader.php');
    ?>

    <div class="tunnel-status-content">

        <div id="status_mails_navigation" class="row">
            <?php
            $i = 0;
            foreach ($sequences as $index => $sequence) {
                $mailsCount = count($sequence['mails']);
                if ($mailsCount === 0) {
                    continue;
                }
                ?>
                <div>
                    <a href="#" class="status-mails-navigation-sequence-btn <?php if ($i === 0) {
                        echo 'active';
                                                                            } ?>" data-sequence-id="<?php echo $sequence['id_sequence']; ?>" data-rel="tooltip"
                       title="<?php echo $sequence['nom'] ?>">
                        <div class="status-mails-navigation-sequence">
                            <div class="status-mails-navigation-sequence-name">
                                <?php echo $sequence['nom']; ?>
                            </div>
                            <div class="status-mails-navigation-sequence-description">
                                <?php echo $mailsCount; ?><?php echo __('emails') ?>
                            </div>
                        </div>
                    </a>
                    <div class="status-mails-navigation-sequence-mail">
                        <?php
                        if (null !== $sequence['mails']) {
                            $i2 = 0;
                            foreach ($sequence['mails'] as $mail) {
                                $statusName = $mail['lines'][0]['status_name'];
                                $statusCss = 'bg-status';
                                $statusCss .= ($statusName != '') ? '-' . $statusName : '';

                                $statusWidth = floor(100 / $mailsCount);
                                ?>
                                <div class="status-mails-navigation-sequence-mail-btn-wrapper"
                                     style="width: <?php echo $statusWidth; ?>%;">
                                    <a href="#"
                                       class="status-mails-navigation-sequence-mail-btn <?php if ($i === 0 && $i2 === 0) {
                                            echo 'active';
                                                                                        } ?>" data-sequence-id="<?php echo $sequence['id_sequence']; ?>"
                                       data-mail-id="<?php echo $mail['id_mail']; ?>">
                                        <div class="status-mails-navigation-sequence-mail-bar <?php echo $statusCss; ?>"></div>
                                    </a>
                                </div>
                                <?php
                                $i2++;
                            }
                        } ?>
                    </div>
                </div>
                <?php
                $i++;
            }
            ?>
        </div>

        <div class="row" id="status_mails_row">
            <?php
            $i = 0;
            foreach ($sequences as $sequence) {
                $mailsCount = count($sequence['mails']);
                if ($mailsCount === 0) {
                    continue;
                }
                ?>
                <div class="status-sequence-mails-container"
                     id="status_sequence_mails_container_<?php echo $sequence['id_sequence']; ?>"
                     style="<?php if ($i > 0) {
                            echo 'display: none;';
                            } ?>">
                    <?php
                    if (null !== $sequence['mails']) {
                        $nbMail = 1;
                        foreach ($sequence['mails'] as $mail) { ?>
                            <div class="status-card-mail-wrapper">
                                <div class="status-card-mail" id="status_card_mail_<?php echo $mail['id_mail']; ?>"
                                     data-mail-id="<?php echo $mail['id_mail']; ?>"
                                     data-object-type="<?php echo \Learnybox\Enums\ObjectTypeEnum::EMAIL; ?>">
                                    <div class="status-card-mail-header">
                                        <div class="row">
                                            <div class="col-xs-2">
                                                <div class="status-card-mail-header-delay">
                                                    J<?php echo round($mail['delai'] / 86400, 0); ?>
                                                </div>
                                            </div>
                                            <div class="col-xs-7">
                                                <?php
                                                if (isset($mail['type']) and 'conference' == $mail['type']) {
                                                    $link = Tools::makeLink('app', 'conference', 'mail_edit/' . $mail['id_mail'], 'rid=' . $mail['conference_random_id']);
                                                } else {
                                                    $link = Tools::makeLink('app', 'autorepondeur', 'mail_compose/' . $mail['id_mail']);
                                                }
                                                ?>
                                                <div class="status-card-mail-header-subject"
                                                     data-mail-id="<?php echo $mail['id_mail']; ?>"
                                                     data-id="<?php echo $mail['id_mail']; ?>">
                                                    <a href="<?php echo $link; ?>"
                                                       target="_blank">Email <?php echo $nbMail; ?></a>
                                                </div>
                                            </div>
                                            <div class="status-card-mail-status col-xs-3">
                                                <?php
                                                echo Eden_Template::i()
                                                    ->set('statusName', $mail['lines'][0]['status_name'])
                                                    ->set('statusLib', 'STATUT')
                                                    ->set('objectId', $mail['id_mail'])
                                                    ->set('objectType', \Learnybox\Enums\ObjectTypeEnum::EMAIL)
                                                    ->set('allStatus', $all_status)
                                                    ->parsePHP(VIEWS_PATH . '/app/status/dropdown.php');
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                    <hr class="status-card-mail-header-hr"/>
                                    <div class="status-card-mail-content" data-id="<?php echo $mail['id_mail']; ?>">

                                        <?php
                                        if (null !== $mail['lines']) {
                                            ?>
                                            <div id="subjectmaileditable<?php echo $mail['id_mail']; ?>"
                                                 name="subjectmaileditable<?php echo $mail['id_mail']; ?>"
                                                 class="subject-mail-editable"
                                                 data-mail-id="<?php echo $mail['id_mail']; ?>"
                                                 data-id="<?php echo $mail['id_mail']; ?>">
                                                <p class="subjectmailp"
                                                   data-id="<?php echo $mail['id_mail']; ?>"><?php echo $mail['sujet']; ?></p>
                                                <input class="form-control input" type="text"
                                                       id="inputsubject<?php echo $mail['id_mail']; ?>"
                                                       name="inputsubject<?php echo $mail['id_mail']; ?>"
                                                       data-id="<?php echo $mail['id_mail']; ?>"
                                                       value="<?php echo $mail['sujet']; ?>" style="display:none"/>
                                            </div>
                                            <?php
                                            foreach ($mail['lines'] as $line) {
                                                if (null !== $line['elements']) {
                                                    foreach ($line['elements'] as $element) {
                                                        if ($element['objet'] === 'txt') {
                                                            ?>
                                                            <div id="maileditable<?php echo $element['ID']; ?>"
                                                                 class="status-card-mail-content-element mail-editable"
                                                                 data-id="<?php echo $element['ID']; ?>"
                                                                 data-mail-id="<?php echo $mail['id_mail']; ?>"
                                                                 contenteditable="true">
                                                                <?php echo stripslashes($element['contenutexte']); ?>
                                                            </div>
                                                            <?php
                                                        }
                                                    }
                                                }
                                            }
                                        } ?>
                                    </div>
                                </div>
                            </div>
                            <?php
                            $nbMail++;
                        } ?>
                        <div class="status-card-mail-wrapper"></div>
                        <div class="status-card-mail-wrapper"></div>
                    <?php } ?>
                </div>
                <?php
                $i++;
            }
            ?>
        </div>
    </div>
</div>

<script type="application/javascript">
    setEditableMail();
    setEditableSubjectMail();
</script>
