<?php

use Learnybox\Enums\DoneForYou\DoneForYouRequestRefusalReasonEnum;
use Learnybox\Enums\DoneForYou\DoneForYouStateEnum;
use Learnybox\Helpers\ApplicationHelper;

?>

<?php if ($service->getState() === DoneForYouStateEnum::OFFER_REFUSED) : ?>
    <?php if (ApplicationHelper::isCurrentApplicationId(APPLICATION_APP)) : ?>
        <div class="offer-refused-client alert alert-danger">
            <p>
                <?php echo __('Vous avez refusé cette proposition.'); ?>
                <?php echo __('Vous pouvez refaire une demande basée sur les mêmes informations en cliquant le lien ci-dessous ou refaire une nouvelle demande depuis le tableau demande.'); ?>
            </p>
            <p><a href="<?php echo \Tools::makeLink('app', 'done_for_you/request/new', '', 'fromServiceId=' . $service->getId()); ?>"><?php echo __('Faire une nouvelle demande') ?></a></p>
        </div>
    <?php endif; ?>

    <?php if (ApplicationHelper::isCurrentApplicationId(APPLICATION_ADMIN) || ApplicationHelper::isCurrentApplicationId(APPLICATION_COACHS)) : ?>
        <div class="offer-refused-admin">
            <div class="alert alert-danger">
                <p><?php echo __('La proposition a été refusée par le client.') ?></p>
            </div>
            <p><label><?php echo __('Motif du refus :') ?></label><?php echo ' ' . DoneForYouRequestRefusalReasonEnum::getI18nChoice($offer->getRefusalReason()) ?></p>
            <p><label><?php echo __('Commentaire :') ?></label></p>
            <p class="description"><?php echo nl2br($offer->getRefusalComment()); ?></p>
            <hr>
        </div>
    <?php endif; ?>
<?php elseif ($service->getState() === DoneForYouStateEnum::OFFER_EXPIRED) : ?>
    <?php if (ApplicationHelper::isCurrentApplicationId(APPLICATION_APP)) : ?>
        <div class="offer-refused-client alert alert-info">
            <p>
                <?php echo __('Délai de paiement dépassé, cette demande a expiré.'); ?>
                <?php echo __('Vous pouvez refaire une demande basée sur les mêmes informations en cliquant le lien ci-dessous ou refaire une nouvelle demande depuis le tableau demande.'); ?>
            </p>
            <p><a href="<?php echo \Tools::makeLink('app', 'done_for_you/request/new', '', 'fromServiceId=' . $service->getId()); ?>"><?php echo __('Faire une nouvelle demande') ?></a></p>
        </div>
    <?php endif; ?>

<?php elseif ((ApplicationHelper::isCurrentApplicationId(APPLICATION_ADMIN) || ApplicationHelper::isCurrentApplicationId(APPLICATION_COACHS)) && $service->getState() === DoneForYouStateEnum::OFFER_MADE) : ?>
    <div class="offer-sent alert alert-info">
        <p><?php echo __('La proposition a été enregistrée et envoyée au client.') ?></p>
    </div>
<?php endif; ?>

<div class="summary-panel">
    <p><label><?php echo __('Date de livraison :') ?> </label><span> <?php echo \Learnybox\Helpers\LocalizedDatetime::getDateWithMonthName($offer->getWorkEndAt()) ?></span></p>
    <p><label><?php echo __('Nombre de crédits :') ?> </label><span> <?php echo n__(' %s crédit ', ' %s crédits ', $offer->getCreditCost(), $offer->getCreditCost()) ?></span></p>
    <p><label><?php echo __('Détail de la proposition :') ?> </label></p>
    <p class="description"><?php echo nl2br($offer->getDescription()); ?></p>
    
    <?php if ($offer->getClientComment()) : ?>
    <div class="client-comment">
        <p><label><?php echo __('Commentaire du client :') ?> </label></p>
        <p class="description"><?php echo nl2br($offer->getClientComment()); ?></p>
    </div>
    <?php endif; ?>
</div>
