<?php

use Learnybox\Entity\Universe\Universe;
use Learnybox\Enums\DoneForYou\DoneForYouStateEnum;
use Learnybox\Helpers\ApplicationHelper;

?>

<?php if (ApplicationHelper::isCurrentApplicationId(APPLICATION_APP) && $service->getState() === DoneForYouStateEnum::REQUESTED) : ?>
    <div class="request-requested alert alert-info">
        <p><?php echo __('Votre demande est en cours d\'analyse.') . ' ' . __('Nos experts LearnyBox reviendront vers vous sous 48h à 72h avec une proposition de réalisation.') ?></p>
    </div>
<?php elseif ($service->getState() === DoneForYouStateEnum::REQUEST_REFUSED) : ?>
    <div class="request-refused">
        <div class="alert alert-danger">
            <p><?php echo __('Cette demande a été refusée.') ?><?php echo __('Retrouvez ci-dessous le motif du refus.') ?></p>

            <?php if (ApplicationHelper::isCurrentApplicationId(APPLICATION_APP)) : ?>
                <p><?php echo __('Vous pouvez refaire une demande basée sur les mêmes informations en cliquant sur le lien ci-dessous ou refaire une nouvelle demande depuis le tableau de bord.') ?></p>
                <p><a href="<?php echo \Tools::makeLink('app', 'done_for_you/request/new', '', 'fromServiceId=' . $service->getId()); ?>"><?php echo __('Faire une nouvelle demande') ?></a></p>
            <?php endif; ?>
        </div>
        <label><?php echo __('Motif du refus de la demande') ?></label>
        <span class="help-block"><?php echo nl2br($request->getRefusalReason()); ?></span>
        <hr>
    </div>
<?php elseif (ApplicationHelper::isCurrentApplicationId(APPLICATION_APP) && $service->getState() === DoneForYouStateEnum::REQUEST_CANCELED) : ?>
    <div class="request-canceled alert alert-danger">
        <p><?php echo __('Cette demande a été annulée.') ?><?php echo __('Vous pouvez refaire une demande basée sur les mêmes informations en cliquant sur le lien ci-dessous ou refaire une nouvelle demande depuis le tableau de bord.') ?></p>
        <p><a href="<?php echo \Tools::makeLink('app', 'done_for_you/request/new', '', 'fromServiceId=' . $service->getId()); ?>"><?php echo __('Faire une nouvelle demande') ?></a></p>
    </div>
<?php endif; ?>

<?php $universes = $request->getUniverses(); ?>
<?php if (!empty($universes)) : ?>
    <p>
        <label><?php echo __('Application %s concernée', LB) ?></label>
        <span class="help-block">
            <?php
            echo implode(', ', array_map(function ($universe) {
                return Universe::getI18nLibelles($universe);
            }, $universes));
            ?>
        </span>
    </p>
<?php endif; ?>

<?php if (!empty($request->getDescription())) : ?>
    <p>
        <label><?php echo __('Description de la demande') ?></label>
        <span class="help-block"><?php echo nl2br($request->getDescription()); ?></span>
    </p>
<?php endif; ?>

<?php if (!empty($request->getGoal())) : ?>
    <p>
        <label><?php echo __('Objectif de la demande') ?></label>
        <span class="help-block"><?php echo nl2br($request->getGoal()); ?></span>
    </p>
<?php endif; ?>

<?php if (!empty($request->getPracticalCase())) : ?>
    <p>
        <label><?php echo __('Cas pratique') ?></label>
        <span class="help-block"><?php echo nl2br($request->getPracticalCase()); ?></span>
    </p>
<?php endif; ?>

<table class="table table-striped">
    <tbody>
    <?php if (!empty($request->getHoster())) : ?>
        <tr>
            <th><?php echo __('Hébergeur') ?></th>
            <td><?php echo $request->getHoster() ?></td>
        </tr>
    <?php endif; ?>
    <?php if (!empty($request->getHosterUsername())) : ?>
        <tr>
            <th><?php echo __('Identifiant') ?></th>
            <td><?php echo $request->getHosterUsername() ?></td>
        </tr>
    <?php endif; ?>
    <?php if (!empty($request->getHosterPassword())) : ?>
        <tr>
            <th><?php echo __('Mot de passe') ?></th>
            <td><?php echo $request->getHosterPassword() ?></td>
        </tr>
    <?php endif; ?>
    <?php if (!empty($request->getDomainName())) : ?>
        <tr>
            <th><?php echo __('Domaine') ?></th>
            <td><?php echo $request->getDomainName() ?></td>
        </tr>
    <?php endif; ?>
    <?php if (!empty($request->getCloudflareUsername())) : ?>
        <tr>
            <th><?php echo __('Identifiant CloudFlare') ?></th>
            <td><?php echo $request->getCloudflareUsername() ?></td>
        </tr>
    <?php endif; ?>
    <?php if (!empty($request->getCloudflarePassword())) : ?>
        <tr>
            <th><?php echo __('Mot de passe CloudFlare') ?></th>
            <td><?php echo $request->getCloudflarePassword() ?></td>
        </tr>
    <?php endif; ?>
    </tbody>
</table>

<?php if (count($request->getAttachedFiles()) > 0) : ?>
    <p>
        <label><?php echo n__('Pièce jointe', 'Pièces jointes', count($request->getAttachedFiles())) ?></label>
        <br/>
        <?php foreach ($request->getAttachedFiles() as $file) : ?>
            <i class="fa fa-link"></i> <a href="<?php echo $file->getFilePath() ?>" target="_blank"><?php echo basename($file->getFilePath()) ?></a><br/>
        <?php endforeach; ?>
    </p>
<?php endif; ?>
