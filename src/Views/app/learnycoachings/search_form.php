<div class="row">
    <div class="col-md-12">
        <h3 class="m-t-0"><?php echo __('Je sélectionne mon objectif pour trouver le coach approprié'); ?> :</h3>
    </div>
</div>
<div class="row div-ask-coaching-bloc m-t-10">
    <div class="col-md-9 ask-coaching-select2">
        <select id="coaching-goal" name="coaching-goal" data-rel="select2" style="width: 100%">
            <option value=""><?php echo('Choisissez') ?></option>
            <?php foreach (\Learnybox\Services\CoachsService::getCoachingGoals() as $coachingGoalId => $coachingGoal) { ?>
                <option value="<?php echo $coachingGoalId; ?>" <?php echo isset($currentCoachingGoal) && $currentCoachingGoal == $coachingGoalId ? 'selected' : null ?>><?php echo $coachingGoal['libelle']; ?></option>
            <?php } ?>
        </select>
    </div>
    <div class="col-md-3">
        <a class="btn btn-find-coach btn-primary btn-block" href="#"><i class="fa fa-search"></i> <?php echo __('Trouver un coach'); ?></a>
    </div>
</div>
