<?php

use Learnybox\Entity\Evaluation\Renseignement\EvaluationRenseignement;

/** @var array $reponses */
/** @var array $question */

if (!$reponses) { ?>
    <div class="alert alert-info"><?php echo __('Aucune réponse pour l\'instant'); ?></div>
<?php } else { ?>
    <?php $nbReponses = count($reponses); ?>
    <div class="alert alert-info">
        <h4><?php echo (isset($question['nom_affiche'])) ? EvaluationRenseignement::getI18nLibelles($question['nom_affiche']) : $question['question']; ?></h4>
        <strong><?php echo n__('%d réponse', '%d réponses', $nbReponses, $nbReponses); ?></strong>
    </div>
    <?php echo $resultats; ?>
<?php } ?>
