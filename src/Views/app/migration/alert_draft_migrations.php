<?php

use Learnybox\Enums\MigrationObjectEnum;
use Learnybox\Helpers\RouterHelper;

?>

<div class=" col-md-12">
    <div class="alert alert-warning">
        <strong><?php echo __('Migrations en brouillon'); ?></strong><br/>
        <?php echo __('Des migrations sont encore en brouillon et leurs objets ne vous sont pas accessibles. Cliquez sur les liens suivants pour supprimer le brouillon et pouvoir migrer vous même ces objets :'); ?>
        <br/>
        <ul>
            <?php foreach ($migrations as $migration) :
                $data = $migration->getData() ? json_decode($migration->getData(), true) : null;
                $type = $data && isset($data['type']) ? MigrationObjectEnum::getI18nChoice($data['type'])['title'] : 'Inconnu';
                $user = $migration->getUser()->getClient() !== $migration->getClient() ? __('Inconnu') : $migration->getUser();
                ?>
                <li>
                    <?php echo __('Brouillon de type "%s", initié par %s', $type, $user); ?> -
                    <a href="<?php echo RouterHelper::generate('app_migration_delete', ['idMigration' => $migration->getId()]) ?>"><?php echo __('Supprimer'); ?></a>
                </li>
            <?php endforeach; ?>
        </ul>
    </div>
</div>
