<?php

echo '
<div class="top-page-title">
    <h3>' . __('Tags') . '</h3> ' . $mailConfigTagsHelpTooltip . '
    <ul class="subnav">
        <li><a class="btn btn-primary btn-light" href="' . Tools::makeLink('app', 'autorepondeur', 'tag_add') . '"><i class="fa fa-plus"></i> ' . __('Ajouter un tag') . '</a></li>
    </ul>
</div>';

if (!$tags) {
    echo eden()->Help()->getStartScreen('lbar_tags');
    return;
}

echo '
<table class="table table-striped table-condensed datatable" id="table_tags">
    <thead>
	    <tr>
		    <th class="hidden"></th>
		    <th>' . __('Nom') . '</th>
		    <th>' . __('Inscrits') . '</th>
            <th class="no-sort"></th>
        </tr>
    </thead>
    <tbody>';

$m = 0;
$nb_tags = count($tags);
foreach ($tags as $tag) {
    ++$m;
    $nb_inscrits = eden()->Lbar_UsersTags()->getCountUsersByTag($tag['id_tag']);

    echo '
	    <tr>
	        <td class="hidden">' . $m . '</td>
	        <td><strong>' . $tag['nom'] . '</strong></td>
	        <td>' . $nb_inscrits . '</td>
            <td>
                <form method="post" id="search_tags_' . $tag['id_tag'] . '" action="' . Tools::makeLink('app', 'autorepondeur', 'contacts') . '" style="display:none;">
                    [[CSRF]]
                    <input type="hidden" name="id_tag" value="' . $tag['id_tag'] . '">
                    <input type="hidden" name="lbar_search_contacts" value="true">
                </form>

        	    <div class="btn-group action">
                    <a type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="dropdown-toggle ">
                        <i class="dropdown-icon fa-solid fa-ellipsis-vertical"></i>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-right">
                        <li><a onclick="$(\'#search_tags_' . $tag['id_tag'] . '\').submit(); return false;"><i class="fa-regular fa-eye"></i> ' . __('Voir les contacts') . '</a></li>
                        <li><a href="' . Tools::makeLink('app', 'autorepondeur', 'tag_edit/' . $tag['id_tag']) . '"><i class="fa-regular fa-gear"></i> ' . __('Modifier') . '</a></li>

                        <li><a onclick="$(\'#duplicate_' . $tag['id_tag'] . '\').submit();"><i class="fa fa-copy"></i> ' . __('Dupliquer') . '</a></li>
                        <form method="post" action="" id="duplicate_' . $tag['id_tag'] . '" style="margin:0; padding:0;display: inline;">
                            [[CSRF]]
                            <input type="hidden" name="form_action" value="lbar_tag_duplicate">
                            <input type="hidden" name="id_tag" value="' . $tag['id_tag'] . '">
                        </form>

        			    <li class="divider"></li>
        			    <form method="post" action="" id="suppr' . $tag['id_tag'] . '" style="margin:0; padding:0;display: inline;">
            			    [[CSRF]]
        					<input type="hidden" name="form_action" value="lbar_tag_suppr" />
        					<input type="hidden" name="id_tag" value="' . $tag['id_tag'] . '" />
        				</form>
                        <li><a class="js-modal-handler" data-method="createModal" data-title="' . __('Confirmation') . '" data-content="' . __('Êtes-vous sûr de vouloir supprimer ce tag ?') . '" data-action="$(\'#suppr' . $tag['id_tag'] . '\').submit(); return false;" data-button="' . __('Supprimer ce tag') . '" data-type="error" data-blocker="' . __('Oui, je confirme la suppression.') . '"><i class="fa fa-trash-o"></i> ' . __('Supprimer') . '</a></li>
                    </ul>
                </div>
            </td>
        </tr>';
}

echo '
    </tbody>
</table>';
