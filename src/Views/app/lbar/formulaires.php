<?php

use Learnybox\Enums\MigrationObjectEnum;
use Learnybox\Helpers\RouterHelper;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Services\Migration\MigrationService;

$container = ContainerBuilderService::getInstance();
$migrationService = $container->get(MigrationService::class);
?>

<div class="top-page-title">
    <h3><span class="nb-items"><?php echo count($formulaires); ?></span><?php echo __('Formulaires'); ?></h3> <?php echo $mailFormulairesHelpTooltip; ?>
    <ul class="subnav">
        <li><a class="btn btn-primary btn-light" href="<?php echo RouterHelper::generate('app_autorepondeur_formulaire_tutoriel'); ?>"><i class="fa fa-plus"></i> <?php echo __('Créer un formulaire'); ?></a></li>
    </ul>
</div>

<?php if (!$formulaires) : ?>
    <div class="alert alert-info"><?php echo __('Aucun formulaire enregistré pour l\'instant.'); ?></div>
    <?php return;
endif; ?>

<?php $inscriptions = eden()->Lbar_Formulaires_Stats()->getCountSubscriptionsGroupByFormulaire();
if ($inscriptions) {
    $inscriptions = array_change_key($inscriptions, 'id_formulaire', 'nb_inscriptions');
}
?>

<table class="table table-striped table-condensed datatable" id="table_formulaires">
    <thead>
    <tr>
        <th><?php echo __('Nom'); ?></th>
        <th class="hidden"></th>
        <th><?php echo __('Séquence'); ?></th>
        <th><?php echo __('Ratio'); ?></th>
        <th class="no-sort"></th>
    </tr>
    </thead>
    <tbody>

    <?php foreach ($formulaires as $formulaire) :
        $sequences = [];
        if ($formulaire['sequences']) {
            $sequences = json_decode($formulaire['sequences'], true);
        }

        $formulaire['nb_inscriptions'] = 0;
        if (isset($inscriptions[$formulaire['id_formulaire']])) {
            $formulaire['nb_inscriptions'] = $inscriptions[$formulaire['id_formulaire']];
        }

        if (!$formulaire['nb_vues']) {
            $ratio = 0;
        } else {
            $ratio = round($formulaire['nb_inscriptions'] * 100 / $formulaire['nb_vues'], 2);
        }
        if ($ratio > 100) {
            $ratio = 100;
        }
        $ratio = number_format($ratio, 2, '.', '');

        $formulaireName = stripslashes($formulaire['nom']);
        ?>
        <tr data-id="<?php echo $formulaire['id_formulaire']; ?>">
            <td>
                <a href="<?php echo RouterHelper::generate('app_autorepondeur_formulaire_compose', ['id' => $formulaire['id_formulaire']]); ?>">
                    <strong>
                        <?php if (mb_strlen($formulaireName) > 80) : ?>
                            <span data-rel="tooltip" data-placement="top" title="<?php echo $formulaireName; ?>">
                                <?php echo mb_substr($formulaireName, 0, 80) . '...' ?>
                            </span>
                        <?php else : ?>
                            <?php echo $formulaireName; ?>
                        <?php endif; ?>
                    </strong>
                </a>
            </td>
            <td class="hidden"><?php echo $formulaire['random_id']; ?></td>
            <td>
                <?php
                if ($sequences) {
                    foreach ($sequences as $id_sequence) {
                        $sequence = eden()->Lbar_Sequences()->getSequenceById($id_sequence);
                        if ($sequence) {
                            echo $sequence['nom'] . '<br>';
                        }
                    }
                }
                ?>
            </td>
            <td>
                <a rel="tooltip" data-html="true" href="<?php echo RouterHelper::generate('app_autorepondeur_formulaire_stats', ['id' => $formulaire['id_formulaire']]); ?>"
                   data-title="<?php echo htmlspecialchars('<h1>' . __('Statistiques du formulaire') . '</h1>' . n__('%d vue', '%d vues', $formulaire['nb_vues'], $formulaire['nb_vues']) . '<br>' . n__('%d inscription', '%d inscriptions', $formulaire['nb_inscriptions'], $formulaire['nb_inscriptions'])); ?>"><span
                            class="label label-light label-default"><?php echo $ratio; ?>% <i class="fa fa-info-circle"></i></a>
            </td>
            <td>
                <div class="btn-group action">
                    <a type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="dropdown-toggle ">
                        <i class="dropdown-icon fa-solid fa-ellipsis-vertical"></i>
                    </a>
                    <?php if ($migrationService->isMigrateable(MigrationObjectEnum::MAILFORM, $formulaire['id_formulaire'], $formulaire['version'])) : ?>
                        <span class="Icon-notification PositionBtnToRight"></span>
                    <?php endif; ?>
                    <ul class="dropdown-menu dropdown-menu-right">
                        <?php if ($migrationService->isMigrateable(MigrationObjectEnum::MAILFORM, $formulaire['id_formulaire'], $formulaire['version'])) : ?>
                            <li>
                                <a href="<?php echo RouterHelper::generate('app_migration_tutoriel_step_1', ['slug' => MigrationObjectEnum::MAILFORM, 'objectId' => $formulaire['id_formulaire']]); ?>">
                                    <i class="fa fa-window-restore"><span class="Icon-notification PositionIconTopLeft"></span></i>
                                    <?php echo __('Migrer vers la nouvelle version'); ?>
                                </a>
                            </li>
                        <?php endif; ?>
                        <li>
                            <a href="<?php echo RouterHelper::generate('app_autorepondeur_formulaire_edit', ['id' => $formulaire['id_formulaire']]); ?>">
                                <i class="fa fa-gear"></i> <?php echo __('Modifier'); ?>
                            </a>
                        </li>
                        <?php if ($formulaire['version'] != 0) : ?>
                            <li>
                                <a onclick="$('#duplicate #id_formulaire').val('<?php echo $formulaire['id_formulaire']; ?>'); $('#duplicate').submit();">
                                    <i class="fa fa-copy"></i> <?php echo __('Dupliquer'); ?>
                                </a>
                            </li>
                        <?php endif; ?>
                        <li>
                            <a class="js-modal-handler" data-method="createModal" data-title="<?php echo __('Confirmation'); ?>"
                               data-content="<?php echo __('Êtes-vous sûr de vouloir réinitialiser ce formulaire ?'); ?><br><?php echo __('Cela réinitialisera le nombre de vues et d\'inscriptions enregistrées dans ce formulaire.'); ?>"
                               data-action="$('#reinit #id_formulaire').val('<?php echo $formulaire['id_formulaire']; ?>'); $('#reinit').submit(); return false;" data-button="<?php echo __('Réinitialiser ce formulaire'); ?>"
                               data-type="error" data-blocker="<?php echo __('Oui, je confirme la réinitialisation.'); ?>">
                                <i class="fa fa-refresh"></i> <?php echo __('Réinitialiser'); ?>
                            </a>
                        </li>
                        <?php if ($formulaire['version'] == 2) : ?>
                            <li>
                                <a onclick="$('#create_theme #id_formulaire').val('<?php echo $formulaire['id_formulaire']; ?>'); $('#create_theme').submit();">
                                    <i class="fa fa-picture-o"></i> <?php echo __('Créer un thème à partir de ce formulaire'); ?>
                                </a>
                            </li>
                        <?php endif; ?>
                        <li class="divider"></li>
                        <li>
                            <a class="js-modal-handler" data-method="createModal" data-title="<?php echo __('Confirmation'); ?>" data-content="<?php echo __('Êtes-vous sûr de vouloir supprimer ce formulaire ?'); ?>"
                               data-action="$('#suppr #id_formulaire').val('<?php echo $formulaire['id_formulaire']; ?>'); $('#suppr').submit(); return false;" data-button="<?php echo __('Supprimer ce formulaire'); ?>" data-type="error"
                               data-blocker="<?php echo __('Oui, je confirme la suppression.'); ?>">
                                <i class="fa fa-trash-o"></i> <?php echo __('Supprimer'); ?>
                            </a>
                        </li>
                    </ul>
                </div>
            </td>
        </tr>
    <?php endforeach; ?>
    </tbody>
</table>

<form method="post" action="" id="duplicate" style="display:none">
    [[CSRF]]
    <input type="hidden" name="form_action" value="lbar_formulaire_duplicate">
    <input type="hidden" name="id_formulaire" id="id_formulaire" value="">
</form>

<form method="post" action="" id="create_theme" style="display:none">
    [[CSRF]]
    <input type="hidden" name="form_action" value="lbar_formulaire_create_theme">
    <input type="hidden" name="id_formulaire" id="id_formulaire" value="">
</form>

<form method="post" action="" id="reinit" style="display:none">
    [[CSRF]]
    <input type="hidden" name="form_action" value="lbar_formulaire_reinit">
    <input type="hidden" name="id_formulaire" id="id_formulaire" value="">
</form>

<form method="post" action="" id="suppr" style="display:none">
    [[CSRF]]
    <input type="hidden" name="form_action" value="lbar_formulaire_suppr">
    <input type="hidden" name="id_formulaire" id="id_formulaire" value="">
</form>
