<?php

$split_pages = eden()->SplitTestsPages()->getPagesBySplitTest($splittest['id_split']);
if (!$split_pages) {
    echo '<div class="alert alert-danger">' . __('Aucune page trouvée dans cet A/B Test.') . '</div>';

    return;
}

$split_pages = array_change_key($split_pages, 'id_split_page');


echo '
<div class="splittest">
    <h3 class="title">
        <i class="fa fa-random"></i> ' . $splittest['nom'] . '
        <div class="btn-group">
            <a class="btn btn-transparent dropdown-toggle" data-toggle="dropdown" href="#">
                <i class="fa fa-gear"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
                <li><a href="' . Tools::makeLink('app', 'splittest_edit', $splittest['id_split']) . '"><i class="fa fa-edit"></i> ' . __('Modifier') . '</a></li>
                <li><a onclick="$(\'#desactive_splittest_' . $splittest['id_split'] . '\').submit();"><i class="fa fa-power-off"></i> ' . __('Désactiver') . '</a></li>
                <form method="post" action="" id="desactive_splittest_' . $splittest['id_split'] . '" style="display:none;">
                    [[CSRF]]
                    <input type="hidden" name="form_action" value="splittest_desactive">
                    <input type="hidden" name="id_split" value="' . $splittest['id_split'] . '">
                </form>
                <li class="divider"></li>
                <form method="post" action="" id="suppr_splittest_' . $splittest['id_split'] . '" style="display: none;">
                    [[CSRF]]
                    <input type="hidden" name="form_action" value="splittest_suppr">
                    <input type="hidden" name="id_split" value="' . $splittest['id_split'] . '">
                </form>
                <li><a class="js-modal-handler" data-method="createModal" data-title="' . __('Confirmation') . '" data-content="' . __('Êtes-vous sûr de vouloir supprimer cet A/B Test ?') . '" data-action="$(\'#suppr_splittest_' . $splittest['id_split'] . '\').submit(); return false;" data-button="' . __('Supprimer l\'A/B Test') . '" data-type="error" data-blocker="' . __('Oui, je confirme la suppression.') . '"><i class="fa fa-trash-o"></i> ' . __('Supprimer') . '</a></li>
            </ul>
        </div>
    </h3>';

if ($splittest['description']) {
    echo '
        <div class="alert alert-info" style="margin-bottom: 20px">
            <i class="fa fa-info-circle"></i> ' . nl2br($splittest['description']) . '
        </div>';
}

$i = 0;
$span = 'col-md-3';
$offset = '';

$nb_pages = count($split_pages);
if ($nb_pages == 2) {
    $offset = ' col-md-offset-3';
} elseif ($nb_pages == 3) {
    $span = 'col-md-4';
}

$has_winner = false;
$str_winner_page = '';
foreach ($split_pages as $id => $split_page) {
    if ($split_page['winner']) {
        $has_winner = $split_page['id_split_page'];
    }

    if ($split_page['id_tunnel']) {
        $page = eden()->TunnelsPages()->getPageById($split_page['id_page'], $split_page['id_tunnel']);
    } else {
        $page = eden()->Pages_Pages()->getPageById($split_page['id_page']);
    }
    if ($page) {
        $split_pages[$id]['page'] = $page;
    }

    //confidence
    if ('optin' == $splittest['type']) {
        $split_pages[$id]['nb_conversions'] = $split_page['nb_optins'];
    } elseif ('sale' == $splittest['type']) {
        $split_pages[$id]['nb_conversions'] = $split_page['nb_sales'];
    } elseif ('page' == $splittest['type']) {
        $split_pages[$id]['nb_conversions'] = $split_page['nb_views_target'];
    }
}

if (!$has_winner and $splittest['confidence']) {
    $winner = eden()->SplitTests()->calculateConfidence($split_pages);
    if ($winner) {
        $has_winner = $winner['id_split_page'];
        $split_pages[$has_winner]['winner'] = true;

        eden()->SplitTestsPages()->setWinnerPage($splittest['id_split'], $winner['id_split_page']);
        if (isset($split_pages[$has_winner]['page'])) {
            echo '
                <div class="alert alert-success">
                    ' . __('La page') . ' <strong>' . $split_pages[$has_winner]['page']['nom'] . '</strong> ' . __('a un taux de réussite de') . ' ' . number_format(abs(1 - $winner['confidence']) * 100, 0, ',', ' ') . '%.<br>' . __('Cette page a un meilleur taux de conversion que toutes les autres et peut être considérée comme étant la meilleure.') . '
                </div>';
        }
    }
} else {
    if (isset($split_pages[$has_winner]['page'])) {
        echo '
            <div class="alert alert-success">
                <i class="fa fa-trophy"></i> ' . __('La page') . ' <strong>' . $split_pages[$has_winner]['page']['nom'] . '</strong> ' . __('a été définie comme page gagnante.') . '
            </div>';
    }
}

if (!$has_winner) {
    //objectif
    if ($splittest['confidence']) {
        echo '
            <div class="alert alert-info">
                 ' . __('Objectif') . ' : ' . $splittest['confidence_nb_conversions'] . '% ' . __('de conversion') . '
            </div>';
    } else {
        echo '
            <div class="alert alert-info">
                 ' . __('Objectif : déterminer une page qui a plus de') . ' ' . $splittest['taux'] . '% ' . __('de conversion que les autres pages') . '
            </div>';
    }
}

echo '<div class="splittest-pages">';

foreach ($split_pages as $id => $split_page) {
    if (0 === $i % 4) {
        echo '<div class="row">';
    }

    $class = '';
    if (!$split_page['active']) {
        $class = ' error';
    }
    if ($split_page['winner']) {
        $class = ' success';
    }
    if ($has_winner and !$split_page['winner']) {
        $class = ' looser';
    }

    $page = [];
    if (isset($split_page['page'])) {
        $page = $split_page['page'];
    }

    $visites = $split_page['nb_views'];
    if ($splittest['date_publication'] and $splittest['date_publication'] != '0000-00-00 00:00:00') {
        $dateStart = $splittest['date_publication'];
        $dateEnd = '';
        if (!$splittest['active']) {
            $dateEnd = $splittest['date_fin'];
        }

        if ($split_page['id_tunnel']) {
            $visites = eden()->TunnelsVisites()->getNbVisitesByPage($split_page['id_tunnel'], $split_page['id_page'], $dateStart, $dateEnd);
        } else {
            $visites = eden()->Pages_Visites()->getNbVisitesByPage($split_page['id_page'], $dateStart, $dateEnd);
        }
    }

    if (!$visites) {
        $pourcentage = 0;
        $description = __('Aucune visite');
    } else {
        switch ($splittest['type']) {
            case 'optin':
                $pourcentage = round($split_page['nb_optins'] * 100 / $visites, 0);
                $description = n__('%d inscription', '%d inscriptions', $split_page['nb_optins'], $split_page['nb_optins']);
                break;

            case 'sale':
                $pourcentage = round($split_page['nb_sales'] * 100 / $visites, 0);
                $description = n__('%d vente', '%d ventes', $split_page['nb_sales'], $split_page['nb_sales']);
                $description .= ' (' . $split_page['total_sales'] . \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol() . ')';
                break;

            case 'page':
                $pourcentage = round($split_page['nb_views_target'] * 100 / $visites, 0);
                $description = n__('%d conversion', '%d conversions', $split_page['nb_views_target'], $split_page['nb_views_target']);
                break;
        }
    }

    if ($pourcentage > 100) {
        $pourcentage = 100;
    }

    if ($i == 0) {
        $pourcentageFirstPage = $pourcentage;
    }

    if (0 == $pourcentage) {
        $circleColor = '#de4752';
        $circleClass = 'redCircle';
    } else {
        switch ($pourcentage) {
            case $pourcentage < 10:
                $circleColor = '#de4752';
                $circleClass = 'redCircle';
                break;
            case $pourcentage < 20:
                $circleColor = '#fffb60';
                $circleClass = 'pinkCircle';
                break;
            case $pourcentage <= 100:
                $circleColor = '#91cf48';
                $circleClass = 'orangeCircle';
                break;
        }
    }

    if ($split_page['winner']) {
        $circleColor = '#91cf48';
        $circleClass = 'orangeCircle';
        $icon = 'fa fa-thumbs-up';
    }

    echo '
	    <div class="' . $span . ($i == 0 ? $offset : '') . '">
	        <div class="splittest-page' . $class . '">';

    ++$i;

    if (!$split_page['image']) {
        switch ($splittest['type']) {
            case 'optin':
                $split_page['image'] = \Learnybox\Helpers\Assets::getImageUrl('builder/thumbnails/optin.png');
                break;

            case 'sale':
                $split_page['image'] = \Learnybox\Helpers\Assets::getImageUrl('builder/thumbnails/vente.png');
                break;

            case 'page':
                $split_page['image'] = \Learnybox\Helpers\Assets::getImageUrl('builder/thumbnails/confirmation.png');
                break;
        }
    }

    echo '
        <div class="page-thumbnail">
            <img class="pic img-thumbnail" src="' . $split_page['image'] . '" alt="' . (isset($page['nom']) ? $page['nom'] : null) . '" />';

    if ($split_page['winner']) {
        echo '<div class="trophy"><i class="fa fa-trophy"></i></div>';
    }

    echo '</div>';

    echo '<div class="page-infos">';

    if (!$page) {
        echo '<span class="label label-danger"><i class="fa fa-warning"></i> ' . __('Cette page n\'existe pas !') . '</span>';
    } else {
        echo '<h4>' . $page['nom'] . '</h4>';
        if (!$split_page['active']) {
            echo '<span class="label label-danger"><i class="fa fa-warning"></i> ' . __('Page désactivée') . '</span>';
        } elseif (isset($page['type']) and $page['type']) {
            switch ($page['type']) {
                case 'optin':
                    echo '<span class="page-type">' . __('Page de capture') . '</span>';
                    break;
                case 'content':
                    echo '<span class="page-type">' . __('Page de contenu') . '</span>';
                    break;
                case 'sale':
                    echo '<span class="page-type">' . __('Page de vente') . '</span>';
                    break;
                case 'compose':
                    echo '<span class="page-type">' . __('Page') . '</span>';
                    break;
                default:
                    echo '<span class="page-type">' . __('Page de capture') . '</span>';
                    break;
            }
        } else {
            echo '<span class="page-type">' . __('Page') . '</span>';
        }
    }

    echo '</div>';

    echo '<div class="separator" style="background: ' . $circleColor . '"></div>';

    $str_difference = '';
    if ($i > 1) {
        $difference = $pourcentageFirstPage - $pourcentage;
        $differenceClass = 'label label-difference';
        if ($difference < 0) {
            $differenceClass .= ' label-danger';
        } elseif ($difference < 10) {
            $differenceClass .= ' label-warning';
        } else {
            $differenceClass .= ' label-success';
        }
        $str_difference = '<br><span class="' . $differenceClass . '">' . ($difference >= 0 ? '+' : '') . $difference . '%</span>';
    }

    echo '
    <div class="page-stats">
        <div class="circle-container">
            <div class="circle" data-max="100" data-color="' . $circleColor . '">
                <input type="text" value="' . $pourcentage . '" class="SplitCircle">
                <span class="circle-percentage" style="color: ' . $circleColor . '">%</span>
            </div>
        </div>
    	<div class="page-stats-right">
        	' . n__('%d visite', '%d visites', $visites, $visites) . '
        	<br>
        	' . $description . '
        	' . $str_difference . '
        </div>
    </div>';

    //actions
    echo '
        <div class="actions">
            <div class="btn-group">
                <a class="btn btn-secondary dropdown-toggle" data-toggle="dropdown" href="#">
                    <i class="fa fa-gear"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-right">';

    if ($page) {
        echo '<li><a href="' . Tools::getLink($page['id_domaine'], 'site', $page['permalink']) . '" target="_blank"><i class="fa fa-search"></i> ' . __('Voir la page en ligne') . '</a></li>';
    }

    if (!$split_page['active']) {
        echo '
			<form method="post" action="" id="active' . $split_page['id_split_page'] . '" style="display:none">
			    [[CSRF]]
				<input type="hidden" name="form_action" value="splittest_page_active" />
				<input type="hidden" name="id_split" value="' . $split_page['id_split'] . '" />
				<input type="hidden" name="id_split_page" value="' . $split_page['id_split_page'] . '" />
			</form>
			<li><a onclick="$(\'#active' . $split_page['id_split_page'] . '\').submit(); return false;"><i class="fa fa-power-off"></i> ' . __('Activer cette page') . '</a></li>';
    } else {
        echo '
			<form method="post" action="" id="desactive' . $split_page['id_split_page'] . '" style="display:none">
			    [[CSRF]]
				<input type="hidden" name="form_action" value="splittest_page_desactive" />
				<input type="hidden" name="id_split" value="' . $split_page['id_split'] . '" />
				<input type="hidden" name="id_split_page" value="' . $split_page['id_split_page'] . '" />
			</form>
			<li><a onclick="$(\'#desactive' . $split_page['id_split_page'] . '\').submit(); return false;"><i class="fa fa-power-off"></i> ' . __('Désactiver cette page de l\'A/B Test') . '</a></li>';
    }

    if ($split_page['winner']) {
        echo '
			<form method="post" action="" id="unwinner' . $split_page['id_split_page'] . '" style="display:none">
			    [[CSRF]]
				<input type="hidden" name="form_action" value="splittest_page_unwinner" />
				<input type="hidden" name="id_split" value="' . $split_page['id_split'] . '" />
				<input type="hidden" name="id_split_page" value="' . $split_page['id_split_page'] . '" />
			</form>
			<li><a onclick="$(\'#unwinner' . $split_page['id_split_page'] . '\').submit(); return false;"><i class="fa fa-flag-checkered"></i> ' . __('Ne plus définir comme gagnant') . '</a></li>';
    } else {
        echo '
			<form method="post" action="" id="winner' . $split_page['id_split_page'] . '" style="display:none">
			    [[CSRF]]
				<input type="hidden" name="form_action" value="splittest_page_winner" />
				<input type="hidden" name="id_split" value="' . $split_page['id_split'] . '" />
				<input type="hidden" name="id_split_page" value="' . $split_page['id_split_page'] . '" />
			</form>
			<li><a onclick="$(\'#winner' . $split_page['id_split_page'] . '\').submit(); return false;"><i class="fa fa-flag-checkered"></i> ' . __('Définir comme gagnant') . '</a></li>';
    }

    echo '
                <form method="post" action="" id="reinit' . $split_page['id_split_page'] . '" style="display:none">
                    [[CSRF]]
                    <input type="hidden" name="form_action" value="splittest_page_reinit" />
                    <input type="hidden" name="id_split" value="' . $split_page['id_split'] . '" />
                    <input type="hidden" name="id_split_page" value="' . $split_page['id_split_page'] . '" />
                </form>
                <li><a class="js-modal-handler" data-method="createModal" data-title="' . __('Confirmation') . '" data-content="' . __('Êtes-vous sûr de vouloir réinitialiser les statistiques de cette page ?') . '<br>' . __('Cela supprimera le nombre de conversions enregistrées pour cette page.') . '" data-action="$(\'#reinit' . $split_page['id_split_page'] . '\').submit(); return false;" data-button="' . __('Réinitialiser cette page') . '" data-type="error" data-blocker="' . __('Oui, je confirme la réinitialisation.') . '"><i class="fa fa-refresh"></i> ' . __('Réinitialiser cette page') . '</a></li>
                <li class="divider"></li>
                <form method="post" action="" id="suppr' . $split_page['id_split_page'] . '" style="display:none;">
                    [[CSRF]]
                    <input type="hidden" name="form_action" value="splittest_page_suppr" />
                    <input type="hidden" name="id_split" value="' . $split_page['id_split'] . '" />
                    <input type="hidden" name="id_split_page" value="' . $split_page['id_split_page'] . '" />
                </form>
                <li><a class="js-modal-handler" data-method="createModal" data-title="' . __('Confirmation') . '" data-content="' . __('Êtes-vous sûr de vouloir supprimer cette page du split-test ?') . '<br>' . __('Notez que cela ne supprimera pas cette page, mais la retirera simplement de ce split-test.') . '" data-action="$(\'#suppr' . $split_page['id_split_page'] . '\').submit(); return false;" data-button="' . __('Supprimer cette page') . '" data-type="error" data-blocker="' . __('Oui, je confirme la suppression.') . '"><i class="fa fa-trash-o"></i> ' . __('Supprimer cette page du split-test') . '</a></li>
            </ul>
        </div>
    </div>';

    echo '
        </div>
    </div>';

    if (0 === $i % 4) {
        echo '</div>';
    }
}

if (0 !== $i % 4) {
    echo '</div>';
}

echo '</div>';
echo '</div>';
