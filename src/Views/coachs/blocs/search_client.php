<form id="add-coaching-search-client" name="add-coaching-search-client" class="form" method="post" action="">
    <div class="row">
        <div class="col-xs-12 col-md-6 col-md-offset-3" id="input-container">
            [[CSRF]]
            <div class="col-xs-12">
                <input type="text" value="<?php echo $query ?? '' ?>" name="search" placeholder="<?php echo __('Rechercher un client (id, url du compte, adresse email, ...)') ?>" id="search-clients" class="form-control input-lg" autofocus="" required="">
                <br />
                <input type="checkbox" name="only-coach-clients" id="only-coach-clients" />
                <label for="only-coach-clients"><?php echo __('Mes clients uniquement'); ?></label>
            </div>
        </div>
    </div>

    <?php if (isset($query)) : ?>
        <input type="hidden" name="query" value="<?php echo $query ?>" />
    <?php endif; ?>
</form>


<div class="tab-content" id="clients-results-content" style="display: none">
    <div id="div-spinner-clients" class="row">
        <div class="col-xs-12 text-center">
            <div class="spinner-border" role="status">
                <i id="spinner-clients" class="fa fa-spinner fa-spin fa-3x"></i>
            </div>
        </div>
    </div>

    <div id="div-error-clients" class="col-md-6" style="display: none;">
        <div class="alert alert-danger" id="error-clients"></div>
    </div>
    <div id="div-clients-table">
    </div>
</div>