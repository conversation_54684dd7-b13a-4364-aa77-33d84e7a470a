<!DOCTYPE html>
<html lang="<?php echo \Learnybox\Helpers\I18nHelper::getLangShort(); ?>">
<head>

    <meta charset="utf-8">
    <title><?php echo $this->data['title']; ?></title>
    <meta name="description" content="<?php echo __('Administration'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Main CSS -->
    <?php \Learnybox\Helpers\Assets::renderCss(); ?>
    <link href='https://fonts.googleapis.com/css?family=Roboto:400,700,300' rel='stylesheet' type='text/css'>

    <!-- Fav and touch icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo \Learnybox\Helpers\Assets::getImageUrl('favicon/apple-touch-icon.png', 'assets/') ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo \Learnybox\Helpers\Assets::getImageUrl('favicon/favicon-32x32.png', 'assets/') ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo \Learnybox\Helpers\Assets::getImageUrl('favicon/favicon-16x16.png', 'assets/') ?>">
    <link rel="manifest" href="<?php echo \Learnybox\Helpers\Assets::getImageUrl('favicon/site.webmanifest', 'assets/') ?>">
    <link rel="mask-icon" href="<?php echo \Learnybox\Helpers\Assets::getImageUrl('favicon/safari-pinned-tab.svg" color="#5bbad5', 'assets/') ?>">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="theme-color" content="#ffffff">

    <!-- Head JS -->
    <?php \Learnybox\Helpers\Assets::renderJs(true); ?>
    <?php \Learnybox\Helpers\Assets::renderInlineJs(true); ?>
</head>
<body>
    <?php if (isset($this->data['CSRF_form'])) { ?>
        <?php echo $this->data['CSRF_form']; ?>
    <?php } ?>

    <div class="container-fluid">
        <div class="row d-flex">

            <!-- start: Main Menu -->
            <div class="col-md-2 sidebar-affiliation" id="sidebar-default">
                <div class="current-user">
                    <a class="name" href="<?php echo LB_HTTP; ?>/affiliation/index/">
                        <img src="<?php echo \Learnybox\Helpers\Assets::getImageUrl('site/logo/v6/small/LearnyBox-Logo-white.png') ?>" alt="Logo">
                    </a>
                </div>

                <div class="main-menu-span">
                    <div class="navbar-header">
                        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar-collapse-1" aria-expanded="false">
                            <span class="fa fa-bars"></span> <?php echo __('Menu'); ?>
                        </button>
                    </div>
                    <div class="navbar-collapse collapse sidebar-nav" id="navbar-collapse-1">
                        <ul class="nav nav-tabs nav-stacked main-menu-admin">
                            <li id="index">
                                <a href="<?php echo Tools::makeLink('affiliation', 'index'); ?>"><i class="fa fa-home"></i><span class="hidden-tablet"> <?php echo __('Tableau de bord'); ?></span></a>
                            </li>

                            <li class="header"><?php echo __('Affiliation'); ?></li>
                            <li><a href="<?php echo Tools::makeLink('affiliation', 'campaigns'); ?>"><i class="fa fa-money"></i> <?php echo __('Campagnes'); ?></a></li>
                            <li><a href="<?php echo Tools::makeLink('affiliation', 'links'); ?>"><i class="fa fa-globe"></i> <?php echo __('Liens'); ?></a></li>
                            <li><a href="<?php echo Tools::makeLink('affiliation', 'clics'); ?>"><i class="fa fa-hand-o-up"></i> <?php echo __('Clics'); ?></a></li>
                            <li><a href="<?php echo Tools::makeLink('affiliation', 'lb_filleuls'); ?>"><i class="fa fa-user"></i> <?php echo __('Filleuls'); ?></a></li>
                            <li><a href="<?php echo Tools::makeLink('affiliation', 'commissions'); ?>"><i class="fa fa-money"></i> <?php echo __('Commissions'); ?></a></li>
                            <li><a href="<?php echo Tools::makeLink('affiliation', 'invoices'); ?>"><i class="fa fa-file"></i> <?php echo __('Factures'); ?></a></li>

                            <li class="header"><?php echo __('Aide / Contact'); ?></li>
                            <li><a href="<?php echo LB_HTTP; ?>/support/" target="_blank" data-rel="tooltip" data-html="true" data-placement="right"><i class="fa fa-ticket"></i> <span class="hidden-tablet"><?php echo __('Support en ligne'); ?></span></a></li>
                            <li><a href="<?php echo LB_HTTP; ?>/contact/" target="_blank" data-rel="tooltip" data-html="true" data-placement="right"><i class="fa fa-envelope"></i> <span class="hidden-tablet"><?php echo __('Contactez-nous'); ?></span></a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <!-- end: Main Menu -->

            <noscript>
                <div class="alert alert-block col-md-10">
                    <h4 class="alert-heading"><?php echo __('Attention!'); ?></h4>
                    <p><?php echo __('Vous devez activer!'); ?> <a href="http://en.wikipedia.org/wiki/JavaScript" target="_blank">JavaScript</a> <?php echo __('dans votre navigateur pour voir ce site'); ?>.</p>
                </div>
            </noscript>


            <div class="col-md-10 main-content-affiliation" id="main-content">
                <div class="navbar" id="highlight-navbar">
                    <div class="navbar-inner">

                        <div class="nav-no-collapse header-nav">
                            <ul class="nav pull-right">
                                <li class="dropdown">
                                    <a class="dropdown-toggle btn" data-toggle="dropdown" href="#">
                                        <img src="<?php echo $this->data['avatar']; ?>" width="25">
                                        <?php echo $_SESSION['name']; ?> <i class="fa fa-caret-down"></i>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-right" style="width:225px">
                                        <li><span class="dropdown-menu-title"><?php echo __('Mon profil'); ?></span></li>
                                        <li><a href="<?php echo Tools::makeLink('affiliation', 'profil'); ?>"><i
                                                    class="fa fa-user"></i> <?php echo __('Mon profil'); ?></a></li>
                                        <li><a href="<?php echo Tools::makeLink('affiliation', 'logout'); ?>"><i
                                                    class="fa fa-power-off"></i> <?php echo __('Déconnexion'); ?></a></li>
                                    </ul>
                                </li>
                            </ul>
                        </div>

                        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse"
                                data-target=".top-nav.nav-collapse,.sidebar-nav.nav-collapse" aria-expanded="false"
                                aria-controls="navbar">
                            <span class="fa fa-bars"></span> <?php echo __('Menu'); ?>
                        </button>

                    </div>

                    <?php if ((isset($this->data['univers_menu']) and $this->data['univers_menu'])) { ?>
                        <div class="top-submenu">
                            <div class="submenu-container">
                                <?php echo $this->data['univers_menu']; ?>
                            </div>
                        </div>
                    <?php } ?>
                </div>

                <div class="container container-padding">
                    <div id="content">
                <!-- start: Content -->
