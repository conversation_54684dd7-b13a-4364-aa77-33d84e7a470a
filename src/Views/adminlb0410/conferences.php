<?php

$database = MySQL::getInstance();

$date_today = date('Y-m-d') . ' 00:00:00';
$date_10days = date('Y-m-d', strtotime('+10 days')) . ' 00:00:00';

$conferences = $database
    ->search('lw_conferences')
    ->addFilter("date >= '$date_today' AND date <= '$date_10days'")
    ->addSort('date', 'ASC')
    ->getRows();

if (!$conferences) {
    echo __('aucune webinaire live programmé');
    exit();
}

echo '
<table class="table table-striped">
    <thead>
        <tr>
            <th>' . __('Nom') . '</th>
            <th>' . __('Date') . '</th>
            <th>' . __('Client') . '</th>
            <th>' . __('Diffusion') . '</th>
            <th>' . __('Etat') . '</th>
            <th>' . __('Inscrits') . '</th>
        </tr>
    </thead>
    <tbody>';
foreach ($conferences as $conference) {
    $client = eden()->Clients()->getClientById($conference['id_client']);

    $nb_inscrits = eden()->Webinar_Inscrits()->getNbInscritsByConf($conference['id_conference'], '', $conference['id_client']);

    $rtmp = json_decode($conference['rtmp'], true);

    echo '
    <tr>
        <td>' . $conference['nom'] . '</td>
        <td>' . datetimefr($conference['date']) . '</td>
        <td><a href="https://' . $client['uniqid'] . '.' . LB_COM . '/app/index/" target="_blank">https://' . $client['uniqid'] . '.' . LB_COM . '/app/index/</a></td>
        <td>' . ($rtmp['type'] ?? '-') . '</td>
        <td>' . $conference['etat'] . '</td>
        <td>' . $nb_inscrits . '</td>
    </tr>';
}
echo '</tbody></table>';
