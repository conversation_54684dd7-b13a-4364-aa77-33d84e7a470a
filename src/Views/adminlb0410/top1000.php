<?php

$database = MySQL::getInstance();

$stats = $database->query('SELECT SUM(montant) as total, id_client FROM `lb_transactions` WHERE valid=1 GROUP BY id_client ORDER BY total DESC');

if (!$stats) {
    echo __('aucun client');
    exit();
}

echo '
<table class="table table-striped">
    <thead>
        <tr>
            <th>#</th>
            <th>' . __('Client') . '</th>
            <th>' . __('Total') . '</th>
            <th>' . __('Nom/Prénom') . '</th>
            <th>' . __('Email') . '</th>
            <th>' . __('Lien') . '</th>
        </tr>
    </thead>
    <tbody>';

foreach ($stats as $i => $stat) {
    if ($stat['total'] > 100000) {
        continue;
    }
    if ($stat['total'] < 1000) {
        break;
    }

    $j = $i;
    $j++;

    $client = eden()->Clients()->getClientById($stat['id_client']);

    $user = [];
    $usersRolesRepository = Learnybox\Services\DI\ContainerBuilderService::getInstance()->get('Learnybox\Repositories\UsersRolesRepository');
    $users = $usersRolesRepository->getUsersByRole(ROLE_ADMIN, $stat['id_client']);
    if ($users) {
        $user = array_shift($users);
    }

    echo '
    <tr>
        <td>' . $j . '</td>
        <td>' . $client['nom_client'] . '</td>
        <td>' . number_format($stat['total'], 0, '.', ' ') . \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol() . '</td>
        <td>' . ($user ? $user['fname'] . ' ' . $user['lname'] : '') . '</td>
        <td>' . ($user ? $user['email'] : '') . '</td>
        <td><a href="https://' . $client['uniqid'] . '.' . LB_COM . '/app/index/" target="_blank">' . __('Lien') . '</a></td>
    </tr>';
}
echo '</tbody></table>';
