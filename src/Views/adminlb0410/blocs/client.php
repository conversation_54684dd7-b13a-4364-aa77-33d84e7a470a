<div class="box">
    <div class="box-header-nav">
        <h2><i class="fa fa-user"></i> <?php echo __('Client'); ?></h2>
    </div>
    <div class="box-content">
        <h2 style="margin-top: 0;">
        <?php if (isset($keyIndicators) && !empty($keyIndicators)) { ?>
            <a href="<?php echo Tools::makeLink('coachs', 'clients', $client->getIdClient()); ?>">
                <?php echo html_entity_decode($client->getNomClient()); ?>
            </a>
        <?php } else { ?>
            <?php echo html_entity_decode($client->getNomClient()); ?>
        <?php } ?>
        </h2>
        <p>
            <strong><?php echo __('Abonnement'); ?> : </strong><?php echo $client->getAbonnement(); ?><br>
            <strong><?php echo __('Expire le'); ?> : </strong><?php echo datefr($client->getDateFinAbonnement()->format('Y-m-d')); ?>
        </p>
        <?php if ($client->getActive() === true) { ?>
            <span class="label label-success"><?php echo __('Actif'); ?></span>
        <?php } else { ?>
            <span class="label label-danger"><?php echo __('Désactivé'); ?></span>
        <?php } ?>
        <br>
        <a href="<?php echo $client->getAccountUrl(); ?>" target="_blank"><?php echo $client->getAccountUrl(); ?></a><br>
        <a href="<?php echo $client->getAccountUrlIndex(); ?>" target="_blank"><?php echo $client->getAccountUrlIndex(); ?></a>

        <?php if (isset($keyIndicators) && !empty($keyIndicators)) { ?>
        <hr>
        <h4><i class="fa fa-sticky-note"></i> <?php echo __('Détails'); ?></h4>
        <div class="row">
            <div class="col-md-6">
                <span style="font-weight: bold"><?php echo __('Utilisateurs'); ?> : </span><span><?php echo $keyIndicators['nb_users']; ?></span><br>
                <span style="font-weight: bold"><?php echo __('Formations'); ?> : </span><span><?php echo $keyIndicators['nb_formations']; ?></span><br>
                <span style="font-weight: bold"><?php echo __('Tunnels'); ?> : </span><span><?php echo $keyIndicators['nb_tunnels']; ?></span><br>
            </div>
            <div class="col-md-6">
                <span style="font-weight: bold"><?php echo __('Conférences'); ?> : </span><span><?php echo $keyIndicators['nb_conferences']; ?></span><br>
                <span style="font-weight: bold"><?php echo __('Transactions'); ?> : </span><span><?php echo $keyIndicators['nb_transactions']; ?></span><br>
            </div>
        </div>
        <?php } else { ?>
        <hr>
        <h4><i class="fa fa-user"></i>  <?php echo __('Principal administrateur'); ?></h4>
            <?php echo $userAdmin; ?> (<a href="mailto:<?php echo $userAdmin->getEmail(); ?>"><?php echo $userAdmin->getEmail(); ?></a>)
        <br>

            <?php if ($lastLog) { ?>
                <?php echo __('Dernière connexion le'); ?> <?php echo datetimefr($lastLog->getLogTime()->format('Y-m-d H:i:s')); ?><br>
            <?php } ?>
        <span class="label label-info"><a href="https://app.intercom.io/apps/wqvfjatm/users/show?email=<?php echo $userAdmin->getEmail(); ?>" target="_blank" style="color:white"><i class="fa fa-paper-plane"></i> <?php echo __('Voir sur Intercom'); ?></a></span>
        <?php } ?>
    </div>
</div>
