<ul class="nav nav-pills">
    <li role="presentation" <?php echo ($item === 'formations' ? 'class="active"' : ''); ?>><a href="<?php echo \Learnybox\Helpers\RouterHelper::generate('admin_clients_objects_type', ['type' => 'formations']); ?>">Formations</a></li>
    <li role="presentation" <?php echo ($item === 'conferences' ? 'class="active"' : ''); ?>><a href="<?php echo \Learnybox\Helpers\RouterHelper::generate('admin_clients_objects_type', ['type' => 'conferences']); ?>">Conférences</a></li>
    <li role="presentation" <?php echo ($item === 'autowebinaires' ? 'class="active"' : ''); ?>><a href="<?php echo \Learnybox\Helpers\RouterHelper::generate('admin_clients_objects_type', ['type' => 'autowebinaires']); ?>">AutoWebinaires</a></li>
    <li role="presentation" class="dropdown">
        <a class="dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
            Transactions <span class="caret"></span>
        </a>
        <ul class="dropdown-menu">
            <li <?php echo ($item === 'transactions' ? 'class="active"' : ''); ?>><a href="<?php echo \Learnybox\Helpers\RouterHelper::generate('admin_clients_objects_type', ['type' => 'transactions']); ?>">Transactions</a></li>
            <?php $typesChoices = \Learnybox\Entity\Transaction\Transaction::getTypesChoices(); ?>
            <?php foreach ($typesChoices as $type => $label) : ?>
                <li><a href="<?php echo \Learnybox\Helpers\RouterHelper::generate('admin_clients_objects_type', ['type' => 'transactions', 'payment_method' => $type]); ?>"><?php echo $label; ?></a></li>
            <?php endforeach; ?>
        </ul>
    </li>
    <li role="presentation" class="dropdown">
        <a class="dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
            Emails <span class="caret"></span>
        </a>
        <ul class="dropdown-menu">
            <li <?php echo ($item === 'emails_transactionnels' ? 'class="active"' : ''); ?>><a href="<?php echo \Learnybox\Helpers\RouterHelper::generate('admin_clients_objects_type', ['type' => 'emails_transactionnels']); ?>">Transactionnels</a></li>
            <li <?php echo ($item === 'emails_learnymail' ? 'class="active"' : ''); ?>><a href="<?php echo \Learnybox\Helpers\RouterHelper::generate('admin_clients_objects_type', ['type' => 'emails_learnymail']); ?>">LearnyMail</a></li>
            <li <?php echo ($item === 'emails_conferences' ? 'class="active"' : ''); ?>><a href="<?php echo \Learnybox\Helpers\RouterHelper::generate('admin_clients_objects_type', ['type' => 'emails_conferences']); ?>">Conférences</a></li>
        </ul>
    </li>
</ul>

<?php echo $content; ?>
