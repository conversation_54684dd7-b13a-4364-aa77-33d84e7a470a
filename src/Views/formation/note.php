<?php
$fatal_error = '';
if (isset($param) and $param) {
    $id_note = filter_var($param, FILTER_VALIDATE_INT);
    if (!$id_note) {
        $fatal_error = __('Erreur : impossible de récupérer la note');
    } else {
        $note = eden()->Formation_Notes()->getNoteById($id_note);
        if (!$note) {
            $fatal_error = __("Erreur : cette note n'existe pas");
        }
    }
} else {
    $fatal_error = __('Erreur : impossible de récupérer la note');
}
?>

    <div class="scroll con">
        <div class="section padding" id="coachings">
            
            <ul class="tabs">
                <li class="current">
                    <a href="<?php echo Tools::makeLink('formation', 'statistiques', '#formations'); ?>"><i class="fa fa-book"></i> <?php echo __('Mes formations'); ?></a>
                </li>
                <li>
                    <a href="<?php echo Tools::makeLink('formation', 'statistiques', '#progression'); ?>"><i class="fa fa-road"></i> <?php echo __('Ma progression'); ?></a>
                </li>
                <li>
                    <a href="<?php echo Tools::makeLink('formation', 'statistiques', '#evaluations'); ?>"><i class="fa fa-bar-chart-o"></i> <?php echo __('Mes évaluations'); ?></a>
                </li>   
                <li>
                    <a href="<?php echo Tools::makeLink('formation', 'statistiques', '#evaluations-details'); ?>"><i class="fa fa-bar-chart-o"></i> <?php echo __('Détails des évaluations'); ?></a>
                </li>
                <li>
                    <a href="<?php echo Tools::makeLink('formation', 'docs'); ?>"><i class="fa fa-info-circle"></i> <?php echo __('Aide'); ?></a>
                </li>
            </ul>
            
            <div class="tabs">
                
                <div id="note" class="tab current">
                    <?php
                    if ($fatal_error) {
                        echo '
						    <div class="alert alert-danger">
								<button type="button" class="close" data-dismiss="alert">×</button>
								<i class="fa fa-warning"></i> ' . $fatal_error . '<br>
								<a href="' . Tools::makeLink('formation', 'statistiques', '#notes') . '"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a>
							</div>';
                    } else {
                        echo eden()->Formation_Notes()->DisplayNote($id_note);
                        echo '<a href="' . Tools::makeLink('formation', 'statistiques', '#notes') . '"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a>';
                    }
                    ?>
                </div>

            </div>
        </div>

    </div>