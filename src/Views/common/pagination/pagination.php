<?php

use Learnybox\Helpers\RequestHelper;
use Learnybox\Helpers\RouterHelper;

/** @var array $paginationData */
?>

<?php if (min($paginationData['limites']) < $paginationData['totalCount'] || $paginationData['pageCount'] > 1) : ?>
    <div class="row table-footer">
        <div class="col-md-12 col-xs-12 pagi-bottom">
            <?php if (min($paginationData['limites']) < $paginationData['totalCount']) : ?>
            <div class="dataTables_length">
                <label>
                    <select class="form-control input-sm js-pager-limit-select" name="<?php echo $paginationData['limitParameterName']; ?>">
                        <?php foreach ($paginationData['limites'] as $limit) : ?>
                            <option
                                value="<?php echo $limit; ?>"
                                data-url="<?php echo RouterHelper::generate(RequestHelper::getRoute(), array_merge(RequestHelper::getRouteParams(), [$paginationData['pageParameterName'] => 1, $paginationData['limitParameterName'] => $limit])); ?>"
                                <?php if ($limit === $paginationData['numItemsPerPage']) {
                                    echo 'selected';
                                } ?>
                            >
                                <?php echo $limit; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php echo __('éléments'); ?>
                </label>
            </div>
            <?php endif; ?>
            <?php if ($paginationData['pageCount'] > 1) : ?>
                <div class="dataTables_paginate paging_simple_numbers">
                    <ul class="pagination">
                        <?php if (isset($paginationData['previous'])) : ?>
                            <li class="paginate_button previous">
                                <a href="<?php echo RouterHelper::generate(RequestHelper::getRoute(), array_merge(RequestHelper::getRouteParams(), [$paginationData['pageParameterName'] => $paginationData['previous']])); ?>" class="js-pager-link">
                                </a>
                            </li>
                        <?php else : ?>
                            <li class="paginate_button previous disabled">
                                <a href="#" class="js-pager-link">
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if ($paginationData['firstPageInRange'] > $paginationData['first']) : ?>
                            <li class="paginate_button">
                                <a href="<?php echo RouterHelper::generate(RequestHelper::getRoute(), array_merge(RequestHelper::getRouteParams(), [$paginationData['pageParameterName'] => $paginationData['first']])); ?>" class="js-pager-link">
                                    <?php echo $paginationData['first']; ?>
                                </a>
                            </li>

                            <?php if ($paginationData['firstPageInRange'] > ($paginationData['first'] + 1)) :
                                if ($paginationData['firstPageInRange'] > ($paginationData['first'] + 2)) : ?>
                                    <li class="paginate_button disabled">
                                        <a href="#" class="js-pager-link">&hellip;</a>
                                    </li>
                                <?php else : ?>
                                    <li class="paginate_button">
                                        <a href="<?php echo RouterHelper::generate(RequestHelper::getRoute(), array_merge(RequestHelper::getRouteParams(), [$paginationData['pageParameterName'] => ($paginationData['first'] + 1)])); ?>" class="js-pager-link">
                                            <?php echo ($paginationData['first'] + 1); ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>

                        <?php endif; ?>

                        <?php foreach ($paginationData['pagesInRange'] as $page) :
                            $page = intval($page);
                            ?>
                            <li class="paginate_button <?php if ($paginationData['current'] === $page) {
                                echo 'active';
                                                       } ?>">
                                <a href="<?php if ($paginationData['current'] !== $page) {
                                    echo RouterHelper::generate(RequestHelper::getRoute(), array_merge(RequestHelper::getRouteParams(), [$paginationData['pageParameterName'] => $page]));
                                         } else {
                                             echo '#';
                                         } ?>" class="js-pager-link">
                                    <?php echo $page; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>

                        <?php if ($paginationData['lastPageInRange'] < $paginationData['last']) : ?>
                            <?php if ($paginationData['lastPageInRange'] < ($paginationData['last'] - 1)) : ?>
                                <?php if ($paginationData['lastPageInRange'] < ($paginationData['last'] - 2)) : ?>
                                    <li class="paginate_button disabled">
                                        <a href="#" class="js-pager-link">&hellip;</a>
                                    </li>

                                <?php else : ?>
                                    <li class="paginate_button">
                                        <a href="<?php echo RouterHelper::generate(RequestHelper::getRoute(), array_merge(RequestHelper::getRouteParams(), [$paginationData['pageParameterName'] => ($paginationData['last'] - 1)])); ?>" class="js-pager-link">
                                            <?php echo ($paginationData['last'] - 1); ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>
                            <li class="paginate_button">
                                <a href="<?php echo RouterHelper::generate(RequestHelper::getRoute(), array_merge(RequestHelper::getRouteParams(), [$paginationData['pageParameterName'] => $paginationData['last']])); ?>" class="js-pager-link">
                                    <?php echo $paginationData['last']; ?>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php if (isset($paginationData['next'])) : ?>
                            <li class="paginate_button next">
                                <a href="<?php echo RouterHelper::generate(RequestHelper::getRoute(), array_merge(RequestHelper::getRouteParams(), [$paginationData['pageParameterName'] => $paginationData['next']])); ?>" class="js-pager-link">
                                </a>
                            </li>
                        <?php else : ?>
                            <li class="paginate_button next disabled">
                                <a href="#" class="js-pager-link">
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

