<?php

use Learnybox\Renderers\Panels\PanelStatsRenderer;
use Learnybox\Services\DI\ContainerBuilderService;

$container = ContainerBuilderService::getInstance();
?>
<div class="bootcards-summary bootcards-summary-stats">
    <?php foreach ($statsItemContainer->getItems() as $statsItem) {
        echo $container->get(PanelStatsRenderer::class)->renderCardStatNumber(
            __('Moyenne ' . $statsItem->getLabel()),
            null,
            $statsItem->getAverageRating() != 0 ? $statsItem->getAverageRating() . '/5' : '-',
            null,
            [],
            'fa-regular fa-star'
        );
    } ?>
</div>


