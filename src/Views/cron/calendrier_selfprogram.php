<?php

/* Script de gestion du calendrier */
set_time_limit(0);

$log = __('Script de gestion du calendrier SelfProgram') . '<br><strong>' . __('Date') . ' : </strong>' . datetimefr(date('Y-m-d H:i:s')) . '<br><br>';

$cronCalendrierSelfProgram = eden()->Formation_Calendrier()->cronCalendrierSelfProgram();

$error = $cronCalendrierSelfProgram['error'];
$log .= __('Fin du script à') . ' ' . datetimefr(date('Y-m-d H:i:s'));
$log .= $cronCalendrierSelfProgram['log'];

if ($error) {
    \Learnybox\Services\Logger\LoggerService::log(\Monolog\Logger::ERROR, '<div class="alert alert-danger">' . $error . '</div>' . $log);
}
