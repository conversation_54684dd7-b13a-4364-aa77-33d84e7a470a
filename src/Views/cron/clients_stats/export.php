<?php

$database = MySQL::getInstance();
$stats = $database->search('clients_stats')
    ->getRows();

if (!$stats) {
    echo __('Aucune stat') . "\n";
    exit();
}

$fp = fopen(CACHE_PATH . '/clients_stats.csv', 'w');

$keys = array_keys($stats[0]);
$columns = [];
$prefix = 'BD-';
foreach ($keys as $key) {
    $columns[] = $prefix . $key;
}
fputcsv($fp, $columns, ';', '"');

foreach ($stats as $stat) {
    $stat['Go'] = str_replace('.', ',', $stat['Go']);
    fputcsv($fp, $stat, ';', '"');
}
fclose($fp);
echo "OK\n";
exit();
