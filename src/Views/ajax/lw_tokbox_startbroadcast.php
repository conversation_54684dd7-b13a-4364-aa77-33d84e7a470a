<?php

use Learnybox\Helpers\RouterHelper;
use Learnybox\Services\Client\Limit\ConferencesClientLimit;

$container = Learnybox\Services\DI\ContainerBuilderService::getInstance();

eden()->Utilisateurs()->checkIfConnected();

$output = '';

if ($container->get(ConferencesClientLimit::class)->isLimitNone($_SESSION['id_client'])) {
    $_SESSION['error_client_limit'] = $container->get(ConferencesClientLimit::class)->getMessage($_SESSION['id_client']);
    echo '{"status":false, "redirect":' . json_encode(RouterHelper::generate('app_abonnements_lb')) . '}';
    exit();
}

$tokbox_id = filter_input(INPUT_POST, 'tokbox_id', FILTER_SANITIZE_SPECIAL_CHARS);
if (!$tokbox_id) {
    echo '{"status":false, "message":' . json_encode(_('Une erreur est survenue : impossible de récupérer le webinaire')) . '}';
    exit();
}

$conference = eden()->Webinar_Conferences()->getConferenceByTokboxId($tokbox_id);
if (!$conference) {
    echo '{"status":false, "message":' . json_encode(_('Une erreur est survenue : ce webinaire n\'existe pas')) . '}';
    exit();
}

$layout = 'bestfit';
if (isset($_POST['layout'])) {
    $layout = filter_input(INPUT_POST, 'layout', FILTER_SANITIZE_SPECIAL_CHARS);
}
if (!$layout) {
    $layout = 'bestfit';
}

$screenshare_active = filter_input(INPUT_POST, 'screenshare_active', FILTER_SANITIZE_SPECIAL_CHARS);
if ('false' == $screenshare_active) {
    $screenshare_active = false;
}

//start broadcast
$startBroadcast = eden()->Webinar_Opentok()->startBroadcast($conference, $screenshare_active, $layout);
if (!$startBroadcast['valid']) {
    echo '{"status":false, "message":' . json_encode($startBroadcast['message']) . '}';
    exit();
}

$cid = $conference['random_id'];
$etat = 'encours';
$update_conf = eden()->Webinar_Conferences()->update_conf_state($cid, 'encours');
if (!$update_conf['valid']) {
    echo '{"status":false, "message":' . json_encode($update_conf['message']) . '}';
    exit();
}

echo '{"status":true, "html":' . json_encode($output) . ', "etat":' . json_encode($etat) . ', "screenshare_active":' . json_encode($screenshare_active) . '}';
exit();
