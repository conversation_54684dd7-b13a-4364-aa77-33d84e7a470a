<?php

eden()->Utilisateurs()->checkIfConnected();

$id_formulaire = filter_input(INPUT_POST, 'id_formulaire', FILTER_VALIDATE_INT);
$insertafterline = filter_input(INPUT_POST, 'insertafterline', FILTER_VALIDATE_INT);

if (!$id_formulaire) {
    echo '{"status":false,"message":' . json_encode(__('Une erreur est survenue')) . '}';
    exit();
}

//ajout d'une ligne
$insert = eden()->Lbar_Formulaires_Lines()->insert_line(['id_formulaire' => $id_formulaire, 'insertafterline' => $insertafterline]);
if (false == $insert['valid']) {
    echo '{"status":false,"message":' . json_encode($insert['message']) . '}';
    exit();
}

$line = eden()->Lbar_Formulaires_Lines()->getLineById($id_formulaire, $insert['id_line']);
if (!$line) {
    echo '{"status":false,"message":' . json_encode(__('Une erreur est survenue')) . '}';
    exit();
}

$html = eden()->Lbar_Formulaires_Elements()->adminDisplayLineContainer($id_formulaire, $line);
if (!$html) {
    echo '{"status":false,"message":' . json_encode(__('Impossible d\'afficher la zone enregistrée')) . '}';
    exit();
}

echo '{"status":true, "html":' . json_encode($html['output']) . ', "id_line":' . json_encode($insert['id_line']) . ', "afterLine":' . json_encode($insertafterline) . '}';
