<?php

eden()->Utilisateurs()->checkIfConnected();

$file_infos = filter_input(INPUT_POST, 'file_infos', FILTER_SANITIZE_SPECIAL_CHARS);
$recorderToken = filter_input(INPUT_POST, 'recorderToken', FILTER_SANITIZE_SPECIAL_CHARS);
if (!$file_infos or !$recorderToken) {
    echo '{"status":false, "message":' . json_encode(__('Les éléments n\'ont pas été reçu.')) . '}';
    exit();
}

$datas_informations = base64_decode($recorderToken);
$datas_informations = json_decode($datas_informations, true);
if (!$datas_informations) {
    echo '{"status":false, "message":' . json_encode(__('Les éléments n\'ont pas été reçu')) . '}';
    exit();
}

$saveAudio = eden()->Formation_Audios()->saveAudioMp3Coaching($file_infos, $datas_informations);
if (false == $saveAudio['valid']) {
    echo '{"status":false, "message":' . json_encode($saveAudio['message']) . '}';
} else {
    echo '{"status":true, "url":"' . $saveAudio['url'] . '"}';
}
exit();
