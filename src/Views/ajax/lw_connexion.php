<?php

if (isset($_POST['data'])) {
    $_POST['data'] = urldecode($_POST['data']);
    parse_str($_POST['data'], $postarray);
    $cleaned_post = clean_post($postarray);
}

$valid_post = eden()->Utilisateurs()->validatepost_connexion($cleaned_post);
if (!$valid_post['valid']) {
    $array_errors = [];
    $array_errors[] = $valid_post['message'];
    echo '{"status":false, "errors":' . json_encode($array_errors) . '}';
    exit();
}

$insert = eden()->Utilisateurs()->insert_connexion($cleaned_post);
if (!$insert['valid']) {
    $array_errors = [];
    $array_errors[] = $insert['message'];
    echo '{"status":false, "errors":' . json_encode($array_errors) . '}';
    exit();
}

echo '{"status":true}';
exit();
