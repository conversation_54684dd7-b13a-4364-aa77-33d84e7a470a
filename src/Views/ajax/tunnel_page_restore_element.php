<?php

eden()->Utilisateurs()->checkIfConnected();

$id_element = filter_input(INPUT_POST, 'id_element', FILTER_VALIDATE_INT);
if (!$id_element) {
    echo '{"status":false,"message":' . json_encode(__('Une erreur est survenue')) . '}';
    exit();
}

$restore = eden()->TunnelsPagesElements()->restore_element($id_element);
if (!$restore['valid']) {
    echo '{"status":false,"message":' . json_encode($restore['message']) . '}';
    exit();
}

echo '{"status":true}';
exit();
