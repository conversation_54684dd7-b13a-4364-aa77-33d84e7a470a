<?php

$id_article = filter_input(INPUT_POST, 'id_article', FILTER_VALIDATE_INT);
$start = filter_input(INPUT_POST, 'start', FILTER_VALIDATE_INT);

if (!$id_article) {
    echo '{"status":true, "content":""}';
    exit();
}

//récupération des commentaires
$output = eden()->Commentaires()->DisplayComments($id_article, $start);
if (!$output) {
    if ($start == -1) {
        echo '{"status":true, "content":""}';
    } else {
        echo '{"status":true, "content":""}';
    }
} else {
    echo '{"status":true, "content":' . json_encode($output) . '}';
}
exit();
