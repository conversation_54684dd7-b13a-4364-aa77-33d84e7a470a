<?php

eden()->Utilisateurs()->checkIfConnected();

$id_element = filter_input(INPUT_POST, 'id_element', FILTER_VALIDATE_INT);
if (!$id_element) {
    echo '{"status":false,"message":' . json_encode(__('Une erreur est survenue')) . '}';
    exit();
}

$duplicate = eden()->TunnelsPagesElements()->duplicate_element($id_element);
if (!$duplicate['valid']) {
    echo '{"status":false,"message":' . json_encode($duplicate['message']) . '}';
    exit();
}

$element = eden()->TunnelsPagesElements()->getPageElementById($duplicate['id_element']);
if (!$element) {
    echo '{"status":false,"message":' . json_encode(__('Une erreur est survenue')) . '}';
    exit();
}

$html = eden()->TunnelsPagesElements()->admin_display_element($element);
if (!$html) {
    echo '{"status":false,"message":' . json_encode(__('Impossible d\'afficher l\'élément enregistré')) . '}';
    exit();
}

echo '{"status":true, "html":' . json_encode($html) . ', "type":' . json_encode($element['objet']) . '}';
