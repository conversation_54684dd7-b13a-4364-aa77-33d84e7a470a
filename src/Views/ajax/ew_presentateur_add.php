<?php

if (isset($_POST['data'])) {
    $_POST['data'] = urldecode($_POST['data']);
    parse_str($_POST['data'], $postarray);
    $cleaned_post = clean_post($postarray);
}

$selected_values = [];
if (isset($_POST['selected_values'])) {
    $selected_values = filter_input(INPUT_POST, 'selected_values', FILTER_VALIDATE_INT, FILTER_REQUIRE_ARRAY);
}
if (!$selected_values) {
    $selected_values = [];
}

$valid_post = eden()->EverWebinar_Presentateurs()->validatepost_presentateur($cleaned_post, 'presentateur_add');
if (!$valid_post['valid']) {
    echo '{"status":false, "message":' . json_encode($valid_post['message']) . '}';
    exit();
}

$insert = eden()->EverWebinar_Presentateurs()->insert_presentateur($cleaned_post);
if (!$insert['valid']) {
    echo '{"status":false, "message":' . json_encode($insert['message']) . '}';
    exit();
}

array_push($selected_values, $insert['id_presentateur']);
$presentateurs_select = eden()->EverWebinar_Presentateurs()->generate_presentateurs_select($cleaned_post['id_webinaire'], $selected_values);

echo '{"status":true, "presentateurs_select":' . json_encode($presentateurs_select) . '}';
exit();
