<?php

$fatal_error = '';

if (Learnybox\Services\RightsService::isUser()) {
    return;
}

$objet = filter_input(INPUT_POST, 'objet', FILTER_SANITIZE_SPECIAL_CHARS);
$id_objet = filter_input(INPUT_POST, 'id_objet', FILTER_VALIDATE_INT);
$id_element = filter_input(INPUT_POST, 'id_element', FILTER_VALIDATE_INT);
$action = filter_input(INPUT_POST, 'action', FILTER_SANITIZE_SPECIAL_CHARS);
$param = filter_input(INPUT_POST, 'param', FILTER_SANITIZE_SPECIAL_CHARS);
$target = filter_input(INPUT_POST, 'target', FILTER_SANITIZE_SPECIAL_CHARS);

if (!$id_element) {
    $fatal_error = __('Erreur : Element non trouvé');
}

if (!$objet) {
    $fatal_error = __('Erreur : Objet non trouvé');
}

$id_tunnel = 0;
$get_objet = eden()->{'Learnybox\Services\Builder\Builder' . ucfirst($objet)}()->getObjetById($id_objet);
if (!$get_objet) {
    $fatal_error = __('Erreur : Objet non trouvé');
} elseif (isset($get_objet['id_tunnel'])) {
    $id_tunnel = $get_objet['id_tunnel'];
}

if ($fatal_error) {
    echo json_encode(['status' => false, 'message' => $fatal_error]);
    exit();
}

$form = eden()->{'Learnybox\Services\Builder\BuilderActions\BuilderActions' . ucfirst($objet)}()->getActionForm($action, $param, $id_objet, $id_element, $id_tunnel, $target);
if (!$form['valid']) {
    echo json_encode(['status' => false, 'message' => $form['message']]);
    exit();
}

echo json_encode(['status' => true, 'html' => $form['html']]);
exit();
