<?php

if (Learnybox\Services\RightsService::isUser()) {
    return;
}

if (!isset($_SESSION['idformation']) or !$_SESSION['idformation']) {
    echo json_encode(['status' => false, 'message' => __('Aucune formation sélectionnée')]);
    exit();
}

$idformation = filter_input(INPUT_POST, 'idformation', FILTER_VALIDATE_INT);
$objet = filter_input(INPUT_POST, 'objet', FILTER_SANITIZE_SPECIAL_CHARS);

if (!$idformation) {
    echo json_encode(['status' => false, 'message' => __('Aucune formation sélectionnée')]);
    exit();
}

$create_header = eden()->Formation_Pages()->create_header($idformation);
if (!$create_header['valid']) {
    echo json_encode(['status' => false, 'message' => $create_header['message']]);
    exit();
}

echo json_encode(['status' => true]);
exit();
