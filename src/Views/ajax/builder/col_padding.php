<?php

if (Learnybox\Services\RightsService::isUser()) {
    return;
}

$objet = filter_input(INPUT_POST, 'objet', FILTER_SANITIZE_SPECIAL_CHARS);
$id_objet = filter_input(INPUT_POST, 'id_objet', FILTER_VALIDATE_INT);
$id_line = filter_input(INPUT_POST, 'id_line', FILTER_SANITIZE_SPECIAL_CHARS);
$id_col = filter_input(INPUT_POST, 'id_col', FILTER_VALIDATE_INT);
$padding = $_POST['padding'];
if (!$objet or !$id_objet or !$id_col or !$padding) {
    echo json_encode(['status' => false, 'message' => __('Les éléments n\'ont pas été reçu')]);
    exit();
}

if (isset($_POST['other_objet']) and $_POST['other_objet']) {
    $other_objet = filter_input(INPUT_POST, 'other_objet', FILTER_VALIDATE_INT);
    if ($other_objet) {
        $id_objet = $other_objet;
    }
}

$padding = json_decode($padding, true);

$id_line = str_replace('line', '', $id_line);

$cleaned_post = [];
$cleaned_post['id_objet'] = $id_objet;
$cleaned_post['id_line'] = $id_line;
$cleaned_post['id_col'] = $id_col;
$cleaned_post['padding_top'] = (int)$padding['top'];
$cleaned_post['padding_bottom'] = (int)$padding['bottom'];
$cleaned_post['padding_left'] = (int)$padding['left'];
$cleaned_post['padding_right'] = (int)$padding['right'];

$cleaned_post['is_mobile'] = filter_input(INPUT_POST, 'is_mobile', FILTER_VALIDATE_BOOLEAN);

$update = eden()->{'Learnybox\Services\Builder\BuilderCols\BuilderCols' . ucfirst($objet)}()->update_col_padding($cleaned_post);
if (!$update['valid']) {
    echo json_encode(['status' => false, 'message' => $update['message']]);
} else {
    echo json_encode(['status' => true]);
}
exit();
