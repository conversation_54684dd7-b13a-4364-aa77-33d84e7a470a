<?php

if (Learnybox\Services\RightsService::isUser()) {
    return;
}

$objet = filter_input(INPUT_POST, 'objet', FILTER_SANITIZE_SPECIAL_CHARS);
$id_objet = filter_input(INPUT_POST, 'id_objet', FILTER_VALIDATE_INT);
if (!$objet or !$id_objet) {
    echo json_encode(['status' => false, 'message' => __('Une erreur est survenue')]);
    exit();
}

//edition d'un objet
$html = Eden_Template::i()->set('action', 'builder_design')
    ->set('cleaned_post', [])
    ->set('objet', $objet)
    ->set('id_objet', $id_objet)
    ->parsePHP(FORMS_PATH . '/app/builder/design.php');
$html = eden()->Csrf()->replaceForm($html);

echo json_encode(['status' => true, 'html' => $html]);
exit();
