<?php

eden()->Utilisateurs()->checkIfConnected();

$id_formulaire = filter_input(INPUT_POST, 'id_formulaire', FILTER_VALIDATE_INT);
$id_element = filter_input(INPUT_POST, 'id_element', FILTER_VALIDATE_INT);
if (!$id_formulaire or !$id_element) {
    echo '{"status":false,"message":' . json_encode(__('Une erreur est survenue')) . '}';
    exit();
}

$form = eden()->Lbar_Formulaires_Elements()->getElementForm($id_formulaire, $id_element);
if (!$form['valid']) {
    echo '{"status":false,"message":' . json_encode($form['message']) . '}';
    exit();
}

echo '{"status":true, "html":' . json_encode($form['html']) . '}';
