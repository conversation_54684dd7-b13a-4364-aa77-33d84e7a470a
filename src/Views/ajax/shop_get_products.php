<?php

$id_formulaire = filter_input(INPUT_POST, 'id_formulaire', FILTER_VALIDATE_INT);
if (!$id_formulaire) {
    echo '{"status":false, "message":' . json_encode(__('Une erreur est survenue')) . '}';
    exit();
}

$produits = eden()->Shop_Produits()->getAllProduits();
if (!$produits) {
    echo json_encode(['status' => true, 'html' => '<div class="alert alert-info">' . __('Aucun produit trouvé') . '</div>']);
    exit();
}

$products = [];
foreach ($produits as $produit) {
    $products[$produit['id_formulaire']][] = $produit;
}

$output = '
<style type="text/css">
#ModalCreated .chosen-container { min-width: 500px; }
</style>

<form class="form" method="post" action="" id="Form">		
	
	<div class="form-group">
		<label class="control-label" for="id_produit">' . __('Produit') . ' *</label>
		<div class="controls">
			<select name="id_produit" data-rel="select2" style="width:500px">';

$formulaires = eden()->Shop_Formulaires()->getAllFormulaires();
foreach ($formulaires as $formulaire) {
    if (!isset($products[$formulaire['id_formulaire']])) {
        continue;
    }

    $output .= '<optgroup label="' . $formulaire['nom'] . '">';

    foreach ($products[$formulaire['id_formulaire']] as $product) {
        $output .= '<option value="' . $product['id_produit'] . '">' . $product['nom'] . ' (' . Tools::formatAmount($product['montant_ttc'], $product['devise']) . ')</option>';
    }
    $output .= '</optgroup>';
}

$output .= '
			</select>
		</div>
	</div>
	
	<div class="form-actions" style="position: relative; margin-left: 0px; background: transparent; padding: 0; box-shadow: none">
		' . eden()->Csrf()->generateForm() . '
		<input type="hidden" name="form_action" id="form_action" value="shop_produit_import">
		<input type="hidden" name="id_formulaire" value="' . $id_formulaire . '">
		<button type="submit" name="submit" class="btn btn-primary">' . __('Valider') . '</button>
	</div>	
    
</form>';

echo json_encode(['status' => true, 'html' => $output]);
exit();
