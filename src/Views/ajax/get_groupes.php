<?php

eden()->Utilisateurs()->checkIfConnected();

$idformation = filter_input(INPUT_POST, 'idformation', FILTER_VALIDATE_INT);
$idgroupe = filter_input(INPUT_POST, 'idgroupe', FILTER_VALIDATE_INT);
if (!$idformation) {
    echo '{"status":true,"html":""}';
    exit();
}

$selectGroupes = eden()->Formation_Groupes()->generate_select_groupes($idformation, $idgroupe, true);
if (!$selectGroupes) {
    echo '{"status":true,"html":""}';
    exit();
}

$html = '
    <div class="form-group" id="groupes">
		<label class="control-label" for="idgroupe">' . __('Ajouter groupe') . '</label>
		<div class="controls">
		    <select name="idgroupe" id="idgroupe" class="form-control select-w-300" data-rel="select2">
		        ' . $selectGroupes . '
            </select>
		</div>
	</div>';

echo '{"status":true, "html":' . json_encode($html) . '}';
exit();
