<?php

use Learnybox\Forms\Domaine\DomaineForm;
use Learnybox\Repositories\DomainsRepository;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Services\RightsService;

if (Learnybox\Services\RightsService::isUser()) {
    echo json_encode(['status' => false, 'message' => __('Vous ne pouvez pas éxecuter cette action')]);
    exit();
}

$idDomain = filter_var($_POST['idDomain'], FILTER_VALIDATE_INT);
$idClient = filter_var($_POST['idClient'], FILTER_VALIDATE_INT);

$container = ContainerBuilderService::getInstance();
$delete = $container->get(DomaineForm::class)->delete($idDomain, $idClient);


$message = __("Erreur lors de la suppression du domaine");
$status = false;
if ($delete['valid']) {
    $status = true;
    $message = "Domaine correctement supprimé";
}

echo json_encode(['status' => $status, 'message' => $message]);
exit();
