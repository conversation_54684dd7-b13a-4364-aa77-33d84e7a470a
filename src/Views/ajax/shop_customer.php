<?php

$id_formulaire = filter_input(INPUT_POST, 'id_form', FILTER_VALIDATE_INT);
$id_element = filter_input(INPUT_POST, 'id_element', FILTER_VALIDATE_INT);
if (!$id_formulaire or !$id_element) {
    echo '{"status":false,"message":' . json_encode(__('Une erreur est survenue')) . '}';
    exit();
}

//datas
$postarray = [];
if (isset($_POST['data'])) {
    parse_str($_POST['data'], $postarray);
}

if (isset($_POST['CSRFGuard_token'])) {
    $postarray['CSRFGuard_token'] = filter_input(INPUT_POST, 'CSRFGuard_token', FILTER_SANITIZE_SPECIAL_CHARS);
}

$cleaned_post = clean_post($postarray);
$cleaned_post['id_formulaire'] = $id_formulaire;

if (isset($_POST['url'])) {
    $cleaned_post['url'] = filter_input(INPUT_POST, 'url', FILTER_VALIDATE_URL);
}

//create customer
$valid = eden()->Shop_Customers()->validatepost_inscription_customer($cleaned_post);
if (!$valid['valid']) {
    echo '{"status":false, "message":' . json_encode($valid['message']) . '}';
    exit();
}

$subscribe = eden()->Shop_Customers()->insert_customer($cleaned_post);
if (!$subscribe['valid']) {
    echo '{"status":false, "message":' . json_encode($subscribe['message']) . '}';
    exit();
}

if (!isset($_SESSION['cart_' . $id_formulaire]['id_cart'])) {
    echo '{"status":false, "message":"' . __('Veuillez sélectionner au moins 1 produit.') . '"}';
    exit();
} else {
    $id_cart = $_SESSION['cart_' . $id_formulaire]['id_cart'];
    $cart_produits = eden()->Shop_CartProduits()->getProduitsByIdCart($id_cart);
    if (!$cart_produits) {
        echo '{"status":false, "message":"' . __('Veuillez sélectionner au moins 1 produit.') . '"}';
        exit();
    }
}

$html = eden()->Shop_Formulaires()->displayFormulaireStep2($id_formulaire, $id_element);
if (!$html or !isset($html['output'])) {
    $html = ['output' => '<div class="alert alert-danger">' . __('Une erreur est survenue.') . '</div>'];
}

echo '{"status":true, "html":' . json_encode($html['output']) . '}';
exit();
