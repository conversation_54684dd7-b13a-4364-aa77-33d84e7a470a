<?php

eden()->Utilisateurs()->checkIfConnected();

//datas
$postarray = [];
if (isset($_POST['data'])) {
    parse_str($_POST['data'], $postarray);
}

/*$postarray = array();
foreach (explode('&', $_POST['data']) as $chunk) {
    $param = explode("=", $chunk);
    if ($param and isset($param[1]))
        $postarray[urldecode($param[0])] = urldecode($param[1]);
}*/

if (isset($_POST['CSRFGuard_token'])) {
    $postarray['CSRFGuard_token'] = filter_input(INPUT_POST, 'CSRFGuard_token', FILTER_SANITIZE_SPECIAL_CHARS);
}

$cleaned_post = clean_post($postarray);
if (!isset($cleaned_post['form_action'])) {
    echo '{"status":false, "message":' . json_encode(__('Aucune action reçue')) . '}';
    exit();
}

if (!isset($cleaned_post['id_page']) or !$cleaned_post['id_page'] or !isset($cleaned_post['id_line']) or !$cleaned_post['id_line']) {
    echo '{"status":false,"message":' . json_encode(__('Une erreur est survenue')) . '}';
    exit();
}

//traitement
$update = eden()->TunnelsPagesLines()->update_line($cleaned_post);
if (!$update['valid']) {
    echo '{"status":false, "message":' . json_encode($update['message']) . '}';
} else {
    $_SESSION['success-message'] = __('Zone modifiée avec succès');

    $css = [];

    if ($cleaned_post['bgimage']) {
        $css[] = ['background-image', 'url(\'' . $cleaned_post['bgimage'] . '\')'];
        $css[] = ['background-attachment', $cleaned_post['bgimage_position']];
        $css[] = ['background-position', 'center center'];
        $css[] = ['background-repeat', 'no-repeat'];
        $css[] = ['background-color', 'transparent'];
        $css[] = ['-webkit-background-size', $cleaned_post['bgimage_size']];
        $css[] = ['-moz-background-size', $cleaned_post['bgimage_size']];
        $css[] = ['-o-background-size', $cleaned_post['bgimage_size']];
        $css[] = ['background-size', $cleaned_post['bgimage_size']];
        $css[] = ['width', '100%'];
        $css[] = ['height', '100%'];
    } elseif ($cleaned_post['bgcolor1'] and $cleaned_post['bgcolor2']) {
        $css[] = ['background', $cleaned_post['bgcolor1']];
        $css[] = ['background', '-moz-linear-gradient(top, ' . $cleaned_post['bgcolor1'] . ' 0%, ' . $cleaned_post['bgcolor2'] . ' 100%)'];
        $css[] = ['background', '-webkit-gradient(linear, left top, left bottom, color-stop(0%,' . $cleaned_post['bgcolor1'] . '), color-stop(100%,' . $cleaned_post['bgcolor2'] . '))'];
        $css[] = ['background', '-webkit-linear-gradient(top, ' . $cleaned_post['bgcolor1'] . ' 0%,' . $cleaned_post['bgcolor2'] . ' 100%)'];
        $css[] = ['background', '-o-linear-gradient(top, ' . $cleaned_post['bgcolor1'] . ' 0%,' . $cleaned_post['bgcolor2'] . ' 100%)'];
        $css[] = ['background', '-ms-linear-gradient(top, ' . $cleaned_post['bgcolor1'] . ' 0%,' . $cleaned_post['bgcolor2'] . ' 100%)'];
        $css[] = ['background', 'linear-gradient(to bottom, ' . $cleaned_post['bgcolor1'] . ' 0%,' . $cleaned_post['bgcolor2'] . ' 100%)'];
        $css[] = ['filter', 'progid:DXImageTransform.Microsoft.gradient( startColorstr=\'' . $cleaned_post['bgcolor1'] . '\', endColorstr=\'' . $cleaned_post['bgcolor2'] . '\',GradientType=0);'];
    } elseif ($cleaned_post['bgcolor1']) {
        $css[] = ['background-image', 'none'];
        $css[] = ['background', 'transparent'];
        $css[] = ['background-color', $cleaned_post['bgcolor1']];
    } else {
        $css[] = ['background-image', 'none'];
        $css[] = ['background', 'transparent'];
        $css[] = ['background-color', 'transparent'];
    }

    if ($cleaned_post['margin_top']) {
        $css[] = ['margin-top', $cleaned_post['margin_top'] . 'px'];
    }
    if ($cleaned_post['margin_bottom']) {
        $css[] = ['margin-bottom', $cleaned_post['margin_bottom'] . 'px'];
    }
    if ($cleaned_post['padding_top']) {
        $css[] = ['padding-top', $cleaned_post['padding_top'] . 'px'];
    }
    if ($cleaned_post['padding_bottom']) {
        $css[] = ['padding-bottom', $cleaned_post['padding_bottom'] . 'px'];
    }

    if ($cleaned_post['border_top_width'] and $cleaned_post['border_top_color']) {
        $css[] = ['border-top', $cleaned_post['border_top_width'] . 'px solid ' . $cleaned_post['border_top_color']];
    }
    if ($cleaned_post['border_bottom_width'] and $cleaned_post['border_bottom_color']) {
        $css[] = ['border-bottom', $cleaned_post['border_bottom_width'] . 'px solid ' . $cleaned_post['border_bottom_color']];
    }

    $bg_color_overlay = '';
    if (isset($cleaned_post['bg_color_overlay'])) {
        if (!$cleaned_post['bg_color_overlay']) {
            $bg_color_overlay = 'transparent';
        } else {
            $bg_color_overlay = hex2rgba($cleaned_post['bg_color_overlay'], $cleaned_post['bg_color_overlay_opacity']);
        }
    }

    echo '{"status":true, "message":' . json_encode(__('Container modifié avec succès')) . ', "css":' . json_encode($css) . ', "bg_color_overlay":' . json_encode($bg_color_overlay) . '}';
    exit();
}

echo '{"status":false, "message":' . json_encode(__('Aucune action trouvée')) . '}';
exit();
