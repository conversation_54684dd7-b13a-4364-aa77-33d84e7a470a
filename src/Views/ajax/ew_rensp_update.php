<?php

if (isset($_POST['data'])) {
    $_POST['data'] = urldecode($_POST['data']);
    parse_str($_POST['data'], $postarray);
    $cleaned_post = clean_post($postarray);
}

$valid_post = eden()->EverWebinar_Renseignements()->validatepost_renseignement_personnalise_add($cleaned_post);
if (!$valid_post['valid']) {
    $array_errors = [];
    foreach ($valid_post['message'] as $id => $_error) {
        $array_errors[] = $_error;
    }
    echo json_encode(['status' => false, 'errors' => $array_errors]);
    exit();
}

$update = eden()->EverWebinar_Renseignements()->update_renseignement_personnalise($cleaned_post);
if (!$update['valid']) {
    $array_errors = [];
    $array_errors[] = $update['message'];
    echo json_encode(['status' => false, 'errors' => $array_errors]);
    exit();
}

$question = filter_var($cleaned_post['question'], FILTER_SANITIZE_SPECIAL_CHARS);

echo json_encode(['status' => true, 'question' => $question]);
exit();
