<?php

$elements_infos = filter_input(INPUT_POST, 'elements_infos', FILTER_SANITIZE_SPECIAL_CHARS);
$elements_infos = base64_decode($elements_infos);

if (!$elements_infos) {
    echo '{"status":false, "message":""}';
    exit();
}

parse_str($elements_infos, $result);

if (!isset($result['p']) or !$result['p'] or !isset($result['z']) or !$result['z']) {
    echo '{"status":false, "message":""}';
    exit();
}

$id_page = filter_var($result['p'], FILTER_VALIDATE_INT);
$id_line = filter_var($result['z'], FILTER_VALIDATE_INT);
if (!$result['p'] or !$result['z']) {
    echo '{"status":false, "message":""}';
    exit();
}

$insert = eden()->Pages_ElementsDisplay()->insert_display_line($id_page, $id_line);
if (!$insert['valid']) {
    echo '{"status":false,"message":' . json_encode($insert['message']) . '}';
    exit();
}

echo '{"status":true}';
exit();
