<?php

eden()->Utilisateurs()->checkIfConnected();

//datas
$postarray = [];
if (isset($_POST['data'])) {
    parse_str($_POST['data'], $postarray);
}

if (isset($_POST['CSRFGuard_token'])) {
    $postarray['CSRFGuard_token'] = filter_input(INPUT_POST, 'CSRFGuard_token', FILTER_SANITIZE_SPECIAL_CHARS);
}

$cleaned_post = clean_post($postarray);
if (!isset($cleaned_post['form_action'])) {
    echo '{"status":false, "message":' . json_encode(__('Aucune action reçue')) . '}';
    exit();
}

$id_formulaire = filter_input(INPUT_POST, 'id_formulaire', FILTER_VALIDATE_INT);
if (!$id_formulaire) {
    echo '{"status":false,"message":' . json_encode(__('Une erreur est survenue')) . '}';
    exit();
}

//traitement
$valid_post = eden()->Lbar_Formulaires()->validatepost_formulaire_add($cleaned_post);
if (!$valid_post['valid']) {
    echo '{"status":false, "message":' . json_encode($valid_post['message']) . '}';
    exit();
} else {
    $formulaire = eden()->Lbar_Formulaires()->getFormulaireById($id_formulaire);

    $insert = eden()->Lbar_Formulaires()->update_formulaire($cleaned_post);
    if (!$insert['valid']) {
        echo '{"status":false, "message":' . json_encode($insert['message']) . '}';
        exit();
    } else {
        echo '{"status":true, "message":' . json_encode(__('Formulaire modifié avec succès')) . '}';
        exit();
    }
}
echo '{"status":false, "message":' . json_encode(__('Aucune action trouvée')) . '}';
exit();
