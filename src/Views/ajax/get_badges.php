<?php

use Learnybox\Renderers\Formation\FormationGamificationBadgeRenderer;
use Learnybox\Services\DI\ContainerBuilderService;

eden()->Utilisateurs()->checkIfConnected();

$idformation = filter_input(INPUT_POST, 'idformation', FILTER_VALIDATE_INT);
if (!$idformation) {
    echo '{"status":true,"html":""}';
    exit();
}

$selectBadges = ContainerBuilderService::getInstance()->get(FormationGamificationBadgeRenderer::class)->renderSelectBadgeOptionsByFormations([$idformation]);
if (!$selectBadges) {
    $html = '<div class="alert alert-info">' . __('Aucun badge trouvé dans cette formation') . '</div>';
} else {
    $html = '
	<label class="control-label" for="id_badge">' . __('Badge') . '</label>
	<div class="controls">
	    <select name="id_badge" id="id_badge">
	        ' . $selectBadges . '
        </select>
	</div>';
}

echo '{"status":true, "html":' . json_encode($html) . '}';
exit();
