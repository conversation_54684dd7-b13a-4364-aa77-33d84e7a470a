<?php

$id_page = filter_input(INPUT_POST, 'id_page', FILTER_VALIDATE_INT);
if (!$id_page) {
    echo json_encode(['status' => false, 'message' => __('Page non trouvée #1')]);
    exit();
}

$page = [];
if (isset($_POST['page']) and $_POST['page']) {
    $page = $_POST['page'];
}

if (!isset($page['id_page'])) {
    $page['id_page'] = $id_page;
}

$tunnel = [];
if (isset($_POST['tunnel']) and $_POST['tunnel']) {
    $tunnel = $_POST['tunnel'];
}

if (!$page) {
    echo json_encode(['status' => false, 'message' => __('Page non trouvée #2')]);
    exit();
}

$displayPage = Eden_Template::i()->set('page', $page)
    ->set('tunnel', $tunnel)
    ->parsePhp(VIEWS_PATH . '/app/tunnels/tunnel-v2-page.php');

echo json_encode(['status' => true, 'output' => $displayPage]);
exit();
