<?php

namespace Learnybox\Event;

/**
 * Class AppEvents
 * @package Learnybox\Event
 */
final class EntityEvents
{
    const COMMENT_CREATED = 'comment.created';

    const FORMATION_COMMENT_CREATED = 'formation_comment.created';

    const PAGE_COMMENT_CREATED = 'page_comment.created';

    const TRANSACTION_CREATED = 'transaction.created';

    const CONTACT_CREATED = 'contact.created';
    const CONTACT_ANSWERED = 'contact.answered';

    const SUPPORT_TICKET_CREATED = 'support_ticket.created';
    const SUPPORT_REPLY_CREATED = 'support_reply.created';

    const BOXIDEA_IDEA_CREATED = 'boxidea_idea.created';
    const BOXIDEA_COMMENT_CREATED = 'boxidea_comment.created';

    // Done for you
    const DONE_FOR_YOU_SERVICE_TRANSITION_SAVED = 'done_for_you.service.transition.saved';
    const DONE_FOR_YOU_SERVICE_COACH_ATTRIBUTED = 'done_for_you.service.coach.attributed';
    const DONE_FOR_YOU_SERVICE_COACH_UNATTRIBUTED = 'done_for_you.service.coach.unattributed';

    // Dispatch action
    public const DISPATCH_ACTION_FORMATION_SAVE_LAST_ACTIVITY = 'dispatch_action.formation.save_last_activity';
    public const DISPATCH_ACTION_CLIENT_MAILER_UPDATE_LEVEL = 'dispatch_action.client_mailer.update_level';

    // HR
    const HR_COLLABORATOR_CREATED = 'hr.collaborator.created';
    const HR_COLLABORATOR_UPDATED = 'hr.collaborator.updated';
    const HR_COLLABORATOR_DOCUMENT_CREATED = 'hr.collaborator_document.created';
    const HR_COLLABORATOR_DOCUMENT_UPDATED = 'hr.collaborator_document.updated';
    const HR_COLLABORATOR_DOCUMENT_INVOICE_CREATED = 'hr.collaborator_document_invoice.created';
    const HR_COLLABORATOR_DOCUMENT_INVOICE_UPDATED = 'hr.collaborator_document_invoice.updated';
    const HR_COLLABORATOR_VACATION_CREATED = 'hr.collaborator_vacation.created';
    const HR_COLLABORATOR_VACATION_UPDATED = 'hr.collaborator_vacation.updated';


    public static function getEvents()
    {
        $oClass = new \ReflectionClass(__CLASS__);
        return $oClass->getConstants();
    }
}
