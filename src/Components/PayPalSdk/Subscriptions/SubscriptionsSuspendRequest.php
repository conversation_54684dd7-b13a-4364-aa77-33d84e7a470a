<?php

namespace Learnybox\Components\PayPalSdk\Subscriptions;

use Learnybox\Components\PayPalSdk\PaypalHttpRequest;

class SubscriptionsSuspendRequest extends PaypalHttpRequest
{
    function __construct(string $subscriptionId)
    {
        parent::__construct("/v1/billing/subscriptions/{subscription_id}/suspend", "POST");

        $this->path = str_replace("{subscription_id}", urlencode($subscriptionId), $this->path);
    }
}
