<?php

namespace Learnybox\Components\ObjectList\Service;

use Learnybox\Components\ObjectList\Model\ObjectListGroup;
use Learnybox\Components\ObjectList\Model\ObjectListCollection;
use Learnybox\Components\ObjectList\Model\ObjectListItem;
use Learnybox\Entity\Mail\Categorie\MailCategorie;
use Learnybox\Entity\Mail\Sequence\MailSequence;

class ObjectListSequenceService extends AbstractObjectListService implements ObjectListServiceInterface
{
    public function get(?array $params = null): array
    {
        $sequences = $this->getEntityManager()->getRepository(MailSequence::class)->findBy(
            ['client' => $_SESSION['id_client']],
            ['categorie' => 'DESC']
        );

        $res = array_reduce($sequences, function (ObjectListCollection $acc, MailSequence $sequence) {
            $sequenceName = html_entity_decode($sequence->getNom());

            if ($sequence->getCategorie() && $this->getEntityManager()->getRepository(MailCategorie::class)->findOneBy(['idCategorie' => $sequence->getCategorie()->getIdCategorie()])) {
                $categoryId = $sequence->getCategorie()->getIdCategorie();
                $filterObject = $acc->get($categoryId);

                if (!$filterObject) {
                    $filterObject = new ObjectListGroup();
                    $filterObject->setId($categoryId);
                    $filterObject->setLabel(html_entity_decode($sequence->getCategorie()->getNom()));

                    $acc->set($categoryId, $filterObject);
                }

                $filterObjectItem = new ObjectListItem();
                $filterObjectItem->setId($sequence->getIdSequence());
                $filterObjectItem->setLabel($sequenceName);

                $filterObject->getItems()->add($filterObjectItem);
            } else {
                $filterObject = $acc->get(0);

                if (!$filterObject) {
                    $filterObject = new ObjectListGroup();
                    $filterObject->setId(0);
                    $filterObject->setLabel(__('Non catégorisées'));

                    $acc->set(0, $filterObject);
                }

                $filterObjectItem = new ObjectListItem();
                $filterObjectItem->setId($sequence->getIdSequence());
                $filterObjectItem->setLabel($sequenceName);

                $filterObject->getItems()->add($filterObjectItem);
            }

            return $acc;
        }, new ObjectListCollection());

        return $res->getValues();
    }
}
