<?php

namespace Learnybox\Components\ObjectList\Service;

use Learnybox\Components\ObjectList\Model\ObjectListItem;
use Learnybox\Entity\Mail\Tag\MailTag;
use Learnybox\Entity\User\Customfield\UserCustomfield;
use Learnybox\Entity\User\Customfield\UserCustomfieldReponse;

class ObjectListCustomFieldsService extends AbstractObjectListService implements ObjectListServiceInterface
{
    public function get(?array $params = null): array
    {
        if (!isset($params['id'])) {
            return [];
        }

        $customField = $this->getEntityManager()->getRepository(UserCustomfieldReponse::class)->findBy(
            ['client' => $_SESSION['id_client'], 'customField' => $params['id']],
            ['idReponse' => 'ASC']
        );

        return array_map(function ($customField) {
            $filterObjectItem = new ObjectListItem();
            $filterObjectItem->setId(html_entity_decode($customField->getReponse()));
            $filterObjectItem->setLabel(html_entity_decode($customField->getReponse()));

            return $filterObjectItem;
        }, $customField);
    }
}
