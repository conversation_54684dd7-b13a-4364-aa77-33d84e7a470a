<?php

namespace Learnybox\Components\ObjectList\Service;

use Learnybox\Components\ObjectList\Model\ObjectListItem;
use Learnybox\Entity\Mail\Envoi\MailEnvoi;
use Learnybox\Entity\Mail\Mail;
use Learnybox\Helpers\LocalizedDatetime;

class ObjectListMailEnvoiService extends AbstractObjectListService implements ObjectListServiceInterface
{
    public function get(?array $params = null): array
    {
        $mailEnvois = $this->getEntityManager()->getRepository(MailEnvoi::class)->findBy(
            ['client' => $_SESSION['id_client']],
            ['dateEnvoi' => 'DESC']
        );

        return array_map(function ($mailEnvoi) {
            $filterObjectItem = new ObjectListItem();

            try {
                $mail = $this->getEntityManager()
                    ->getRepository(Mail::class)
                    ->find($mailEnvoi->getMail());

                $mailLabel = match (true) {
                    ($mail->getSujet() !== null) => $mail->getSujet(),
                    ($mail->getTitre() !== null) => $mail->getTitre(),
                    default => __('Email inconnu'),
                };
            } catch (\Exception) {
                $mailLabel = __('Email inconnu');
            }

            if (!$mailEnvoi->getDateEnvoi() || $mailEnvoi->getDateEnvoi()->format('Y') <= 0) {
                if (!$mailEnvoi->getDate() || $mailEnvoi->getDate()->format('Y') <= 0) {
                    $mailDate = __('Date inconnu');
                } else {
                    $mailDate = LocalizedDatetime::getDateTime($mailEnvoi->getDate());
                }
            } else {
                $mailDate = LocalizedDatetime::getDateTime($mailEnvoi->getDateEnvoi());
            }

            $filterObjectItem->setId($mailEnvoi->getIdEnvoi());
            $filterObjectItem->setLabel($mailDate . ' - ' . html_entity_decode($mailLabel));

            return $filterObjectItem;
        }, $mailEnvois);
    }
}
