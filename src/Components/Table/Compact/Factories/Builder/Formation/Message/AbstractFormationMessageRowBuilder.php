<?php

namespace Learnybox\Components\Table\Compact\Factories\Builder\Formation\Message;

use DI\Container;
use Doctrine\ORM\EntityManager;
use Learnybox\Components\Table\Compact\Factories\RowItems\RowActionJsFactory;
use Learnybox\Components\Table\Compact\Factories\RowItems\RowActionLinkFactory;
use Learnybox\Components\Table\Compact\Factories\RowItems\RowActionModalFactory;
use Learnybox\Components\Table\Compact\Factories\RowItems\RowAvatarFactory;
use Learnybox\Components\Table\Compact\Factories\RowItems\RowCheckboxFactory;
use Learnybox\Components\Table\Compact\Factories\RowItems\RowContentFactory;
use Learnybox\Components\Table\Compact\Factories\RowItems\RowFooterFactory;
use Learnybox\Components\Table\Compact\Factories\RowItems\RowLinkFactory;
use Learnybox\Components\Table\Compact\Factories\RowItems\RowStatusFactory;
use Learnybox\Components\Table\Compact\Factories\RowItems\RowSubHeaderFactory;
use Learnybox\Components\Table\Compact\Factories\RowItems\RowTagFactory;
use Learnybox\Components\Table\Compact\Factories\RowItems\RowTitleFactory;
use Learnybox\Components\Table\Compact\Models\Row\Action\RowActionJs;
use Learnybox\Components\Table\Compact\Models\Row\Action\RowActionList;
use Learnybox\Components\Table\Compact\Models\Row\Action\RowActionModal;
use Learnybox\Components\Table\Compact\Models\Row\RowAvatar;
use Learnybox\Components\Table\Compact\Models\Row\RowCheckbox;
use Learnybox\Components\Table\Compact\Models\Row\RowContent;
use Learnybox\Components\Table\Compact\Models\Row\RowFooter;
use Learnybox\Components\Table\Compact\Models\Row\RowStatus;
use Learnybox\Components\Table\Compact\Models\Row\RowSubHeader;
use Learnybox\Components\Table\Compact\Models\Row\Tag\RowTagList;
use Learnybox\Components\Table\Compact\Models\Row\Title\RowTitles;
use Learnybox\Entity\Formation\Message\FormationMessage;
use Learnybox\Entity\User\User;
use Learnybox\Helpers\LocalizedDatetime;
use Learnybox\Helpers\RouterHelper;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Services\Formations\FormationEventService;
use Learnybox\Services\Users\UsersService;

class AbstractFormationMessageRowBuilder
{
    private Container $container;

    protected FormationMessage $formationMessage;

    public function __construct(FormationMessage $formationMessage)
    {
        $this->container = ContainerBuilderService::getInstance();
        $this->formationMessage = $formationMessage;
    }

    protected function setLastChild(): void
    {
        $lastChild = $this->container->get(EntityManager::class)->getRepository(FormationMessage::class)->getLastChildByParentId($this->formationMessage->getIdMessage());
        $this->formationMessage->setLastChild($lastChild);
    }

    protected function getCheckbox(): RowCheckbox
    {
        return RowCheckboxFactory::createOne(
            $this->formationMessage->getIdMessage(),
            'fmessages[]',
            $this->formationMessage->getIdMessage(),
            $this->formationMessage->getIdMessage()
        );
    }

    protected function getRowStatus(): bool
    {
        return !$this->formationMessage->getLuAdmin() && !$this->formationMessage->getAdmin();
    }

    protected function getAvatar(): RowAvatar
    {
        $user = $this->formationMessage->getAdmin() ? $this->formationMessage->getAdminUser() : $this->formationMessage->getUser();

        if ($user && $this->getUser($user->getUserId())) {
            $url = $this->container->get(UsersService::class)->getGuestAvatar(
                $user->getEmail(),
                40,
                $user->getFname(),
                $user->getLname()
            );
        } else {
            $url = $this->container->get(UsersService::class)->getGuestAvatar(null, 40);
        }

        return RowAvatarFactory::createOne($url, $user && $this->getUser($user->getUserId()) ? $user : '');
    }

    protected function getTitles(): RowTitles
    {
        $titles = new RowTitles();

        if ($this->formationMessage->getAdminUser() && $this->getUser($this->formationMessage->getAdminUser()->getUserId())) {
            $admin = $this->formationMessage->getAdminUser();
            $adminLink = RowLinkFactory::createOne(RouterHelper::generate('app_user', ['id' => $admin->getUserId()]));
        } else {
            $admin = __('Administrateur inconnu');
            $adminLink = null;
        }

        if ($this->formationMessage->getUser() && $this->getUser($this->formationMessage->getUser()->getUserId())) {
            $member = $this->formationMessage->getUser();
            $memberLink = RowLinkFactory::createOne(RouterHelper::generate('app_user', ['id' => $member->getUserId()]));
            ;
        } else {
            $member = __('Membre inconnu');
            $memberLink = null;
        }

        $expediteur = $this->formationMessage->getAdmin() ? $admin : $member;
        $expediteurLink = $this->formationMessage->getAdmin() ? $adminLink : $memberLink;
        $expediteurTitle = RowTitleFactory::createOne(
            $expediteur,
            $expediteurLink,
            !$this->formationMessage->getAdmin() && !$this->formationMessage->getAdminUser() ? null : 'fa-regular fa-arrow-right'
        );
        $titles->add($expediteurTitle);

        $destinataire = '';
        switch ($this->formationMessage->getDestinataires()) {
            case FormationMessage::RECIPIENT_MEMBERS:
                $destinataire = __('Tous les membres');
                break;
            case FormationMessage::RECIPIENT_GROUP:
                $groupe = $this->container->get(\Formation_Groupes::class)->getGroupeById($this->formationMessage->getIdGroupe());
                if ($groupe) {
                    $destinataire = __('Groupe') . ' (' . $groupe['nomgroupe'] . ')';
                } else {
                    $destinataire = __('Groupe inconnu');
                }
                break;
            case FormationMessage::RECIPIENT_EVENT:
                $event = $this->container->get(FormationEventService::class)->getRepository()->find($this->formationMessage->getIdGroupe());
                if ($event) {
                    $destinataire = __('Évènement') . ' (' . $event->getTitle() . ')';
                } else {
                    $destinataire = __('Évènement inconnu');
                }
                break;
            default:
                if (!$this->formationMessage->getAdmin()) {
                    if ($this->formationMessage->getAdminUser()) {
                        $adminTitle = RowTitleFactory::createOne($admin, $adminLink);
                        $titles->add($adminTitle);
                    }
                } else {
                    $destinataireTitle = RowTitleFactory::createOne($member, $memberLink);
                    $titles->add($destinataireTitle);
                }
        }

        if ($destinataire) {
            $destinataireTitle = RowTitleFactory::createOne($destinataire);
            $titles->add($destinataireTitle);
        }

        return $titles;
    }

    protected function getDate(): string
    {
        if ($this->formationMessage->getLastChild()) {
            return LocalizedDatetime::getDateTimeWithMonthName($this->formationMessage->getLastChild()->getDate(), true);
        }

        return LocalizedDatetime::getDateTimeWithMonthName($this->formationMessage->getDate(), true);
    }

    protected function getStatus(): ?RowStatus
    {
        if ($this->formationMessage->getPriorite() === FormationMessage::PRIORITY_HIGH) {
            return RowStatusFactory::createOne('label label-status label-danger', __('Prioritaire'));
        }

        return null;
    }

    protected function getDropdownActions(): RowActionList
    {
        $newDropdownActions = new RowActionList();
        $newDropdownActions->add($this->getReadAction());
        $newDropdownActions->add($this->getDeleteAction());

        return $newDropdownActions;
    }

    protected function getReadAction(): RowActionJs
    {
        if ($this->formationMessage->getLuAdmin()) {
            $js = '$(\'#set_nonlu #id_message\').val(' . $this->formationMessage->getIdMessage() . '); $(\'#set_nonlu\').submit();';
            return RowActionJsFactory::createOne(__('Définir en non lu'), $js, 'fa fa-star', true);
        }

        $js = '$(\'#set_lu #id_message\').val(' . $this->formationMessage->getIdMessage() . '); $(\'#set_lu\').submit();';
        return RowActionJsFactory::createOne(__('Définir en lu'), $js, 'fa fa-star-o', true);
    }

    protected function getDeleteAction(): RowActionModal
    {
        return RowActionModalFactory::createOne(
            'js-modal-handler',
            'createModal',
            __('Confirmation'),
            __('Êtes-vous sûr de vouloir supprimer ce message ?'),
            '$(\'#fmessage_suppr #idmessage\').val(' . $this->formationMessage->getIdMessage() . '); $(\'#fmessage_suppr\').submit(); return false;',
            __('Supprimer le message'),
            'error',
            __('Oui, je confirme la suppression.'),
            __('Supprimer'),
            'fa fa-trash-o'
        );
    }

    protected function getSubHeader(): RowSubHeader
    {
        $titles = new RowTitles();

        $typeTitle = RowTitleFactory::createOne('<strong>' . $this->formationMessage->getType() . '</strong>');
        $titles->add($typeTitle);

        return RowSubHeaderFactory::createOne(null, $titles);
    }

    protected function getContent(): RowContent
    {
        if ($this->formationMessage->getLastChild()) {
            $content = $this->formationMessage->getLastChild()->getMessage();
        } else {
            $content = $this->formationMessage->getMessage();
        }

        return RowContentFactory::createOne($content, 220);
    }

    protected function getFooter(): ?RowFooter
    {
        $tags = null;
        if ($this->formationMessage->getAttachments() !== null) {
            $attachments = json_decode($this->formationMessage->getAttachments());
            if ($attachments) {
                $tags = new RowTagList();
                foreach ($attachments as $attachment) {
                    $link = RowLinkFactory::createOne($attachment, '_blank');
                    $tags->add(RowTagFactory::createOne(basename($attachment), 'label label-user label-warning', $link, null, 'fa-solid fa-paperclip'));
                }
            }
        }

        $actions = null;
        if (!$this->formationMessage->getDestinataires()) {
            $actions = new RowActionList();
            $source = RouterHelper::generate('app_formation_message', [
                'formationId' => $this->formationMessage->getFormation()->getIdformation(),
                'messageId' => $this->formationMessage->getIdMessage()
            ]);
            $actions->add(RowActionLinkFactory::createOne(__('Répondre'), RowLinkFactory::createOne($source)));
        }

        if ($actions || $tags) {
            return RowFooterFactory::createOne($actions, $tags);
        }

        return null;
    }

    protected function getUser(int $userId): ?User
    {
        return $this->container->get(EntityManager::class)->getRepository(User::class)->findOneBy(['userId' => $userId]);
    }
}
