<?php

namespace Learnybox\Components\Table\Compact\Factories\Builder;

use Learnybox\Components\Table\Compact\Factories\RowFactory;
use Learnybox\Components\Table\Compact\Models\Rows;

class RowsBuilder
{
    public static function build(string $rowBuilder, array $objects, Rows $rows): Rows
    {
        foreach ($objects as $object) {
            $rowBuilder = new $rowBuilder($object);
            $newRow = RowFactory::createOneFromBuilder($rowBuilder);
            $rows->add($newRow);
        }

        return $rows;
    }
}
