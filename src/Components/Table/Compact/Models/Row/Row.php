<?php

namespace Learnybox\Components\Table\Compact\Models\Row;

use Learnybox\Components\Table\Compact\Models\Row\Action\RowActionList;
use Learnybox\Components\Table\Compact\Models\Row\Title\RowTitles;

class Row
{
    private ?RowCheckbox $checkbox = null;
    private bool $rowStatus = false;
    private RowAvatar $avatar;
    private RowTitles $titles;
    private ?string $date = null;
    private ?RowStatus $status = null;
    private ?RowActionList $dropdownActions = null;
    private ?RowActionList $inlineActions = null;
    private ?RowSubHeader $subHeader = null;
    private ?RowContent $content = null;
    private ?RowFooter $footer = null;

    public function getCheckbox(): ?RowCheckbox
    {
        return $this->checkbox;
    }

    public function setCheckbox(?RowCheckbox $checkbox): self
    {
        $this->checkbox = $checkbox;

        return $this;
    }

    public function isRowStatus(): bool
    {
        return $this->rowStatus;
    }

    public function setRowStatus(bool $rowStatus): self
    {
        $this->rowStatus = $rowStatus;

        return $this;
    }

    public function getAvatar(): RowAvatar
    {
        return $this->avatar;
    }

    public function setAvatar(RowAvatar $avatar): self
    {
        $this->avatar = $avatar;

        return $this;
    }

    public function getTitles(): RowTitles
    {
        return $this->titles;
    }

    public function setTitles(RowTitles $titles): self
    {
        $this->titles = $titles;

        return $this;
    }

    public function getDate(): ?string
    {
        return $this->date;
    }

    public function setDate(?string $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getStatus(): ?RowStatus
    {
        return $this->status;
    }

    public function setStatus(?RowStatus $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDropdownActions(): ?RowActionList
    {
        return $this->dropdownActions;
    }

    public function setDropdownActions(?RowActionList $dropdownActions): self
    {
        $this->dropdownActions = $dropdownActions;

        return $this;
    }

    public function getInlineActions(): ?RowActionList
    {
        return $this->inlineActions;
    }

    public function setInlineActions(?RowActionList $inlineActions): self
    {
        $this->inlineActions = $inlineActions;

        return $this;
    }

    public function getSubHeader(): ?RowSubHeader
    {
        return $this->subHeader;
    }

    public function setSubHeader(?RowSubHeader $subHeader): self
    {
        $this->subHeader = $subHeader;

        return $this;
    }

    public function getContent(): ?RowContent
    {
        return $this->content;
    }

    public function setContent(?RowContent $content): self
    {
        $this->content = $content;

        return $this;
    }

    public function getFooter(): ?RowFooter
    {
        return $this->footer;
    }

    public function setFooter(?RowFooter $footer): self
    {
        $this->footer = $footer;

        return $this;
    }
}
