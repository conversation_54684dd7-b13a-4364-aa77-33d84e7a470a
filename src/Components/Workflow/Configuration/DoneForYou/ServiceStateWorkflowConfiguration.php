<?php

namespace Learnybox\Components\Workflow\Configuration\DoneForYou;

use Learnybox\Components\Workflow\Configuration\AbstractWorkflowConfiguration;
use Learnybox\Entity\DoneForYou\DoneForYou;
use Learnybox\Entity\DoneForYou\DoneForYouService;
use Learnybox\Enums\DoneForYou\DoneForYouStateEnum;
use Learnybox\Enums\DoneForYou\DoneForYouTransitionEnum;
use Symfony\Component\Workflow\DefinitionBuilder;
use Symfony\Component\Workflow\Transition;

class ServiceStateWorkflowConfiguration extends AbstractWorkflowConfiguration
{
    public function buildDefinition(DefinitionBuilder $definitionBuilder): void
    {
        $definitionBuilder
            ->addPlaces([
                DoneForYouStateEnum::INIT,
                DoneForYouStateEnum::REQUESTED,
                DoneForYouStateEnum::REQUEST_REFUSED,
                DoneForYouStateEnum::REQUEST_CANCELED,
                DoneForYouStateEnum::OFFER_MADE,
                DoneForYouStateEnum::OFFER_REFUSED,
                DoneForYouStateEnum::OFFER_EXPIRED,
                DoneForYouStateEnum::WORK_IN_PROGRESS,
                DoneForYouStateEnum::FINISHED,
                DoneForYouStateEnum::REVIEWED,
                DoneForYouStateEnum::CLAIM_MADE,
                DoneForYouStateEnum::CLAIM_ACCEPTED,
                DoneForYouStateEnum::CLAIM_REFUSED,
                DoneForYouStateEnum::REWORK_IN_PROGRESS,
                DoneForYouStateEnum::REFUNDED,
                DoneForYouStateEnum::ENDED,
            ])
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::MAKE_REQUEST,
                DoneForYouStateEnum::INIT,
                DoneForYouStateEnum::REQUESTED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::REFUSE_REQUEST,
                DoneForYouStateEnum::REQUESTED,
                DoneForYouStateEnum::REQUEST_REFUSED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::CANCEL_REQUEST,
                DoneForYouStateEnum::REQUESTED,
                DoneForYouStateEnum::REQUEST_CANCELED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::MAKE_OFFER,
                DoneForYouStateEnum::REQUESTED,
                DoneForYouStateEnum::OFFER_MADE
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::ACCEPT_OFFER,
                DoneForYouStateEnum::OFFER_MADE,
                DoneForYouStateEnum::WORK_IN_PROGRESS
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::REFUSE_OFFER,
                DoneForYouStateEnum::OFFER_MADE,
                DoneForYouStateEnum::OFFER_REFUSED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::EXPIRE_OFFER,
                DoneForYouStateEnum::OFFER_MADE,
                DoneForYouStateEnum::OFFER_EXPIRED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::FINISH,
                DoneForYouStateEnum::WORK_IN_PROGRESS,
                DoneForYouStateEnum::FINISHED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::MAKE_REVIEW,
                DoneForYouStateEnum::FINISHED,
                DoneForYouStateEnum::REVIEWED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::MAKE_CLAIM,
                DoneForYouStateEnum::FINISHED,
                DoneForYouStateEnum::CLAIM_MADE
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::REFUSE_CLAIM,
                DoneForYouStateEnum::CLAIM_MADE,
                DoneForYouStateEnum::CLAIM_REFUSED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::REFUND,
                DoneForYouStateEnum::CLAIM_MADE,
                DoneForYouStateEnum::REFUNDED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::ACCEPT_CLAIM,
                DoneForYouStateEnum::CLAIM_MADE,
                DoneForYouStateEnum::CLAIM_ACCEPTED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::REFUSE_CLAIM,
                DoneForYouStateEnum::CLAIM_ACCEPTED,
                DoneForYouStateEnum::CLAIM_REFUSED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::REFUND,
                DoneForYouStateEnum::CLAIM_ACCEPTED,
                DoneForYouStateEnum::REFUNDED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::START_REWORK,
                DoneForYouStateEnum::CLAIM_ACCEPTED,
                DoneForYouStateEnum::REWORK_IN_PROGRESS
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::END_CLAIM,
                DoneForYouStateEnum::REWORK_IN_PROGRESS,
                DoneForYouStateEnum::ENDED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::END,
                DoneForYouStateEnum::FINISHED,
                DoneForYouStateEnum::ENDED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::MAKE_REVIEW,
                DoneForYouStateEnum::ENDED,
                DoneForYouStateEnum::REVIEWED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::MAKE_REVIEW,
                DoneForYouStateEnum::CLAIM_REFUSED,
                DoneForYouStateEnum::REVIEWED
            ))
            ->addTransition(new Transition(
                DoneForYouTransitionEnum::MAKE_REVIEW,
                DoneForYouStateEnum::REFUNDED,
                DoneForYouStateEnum::REVIEWED
            ))
        ;
    }

    public function getName(): string
    {
        return 'done_for_you_service_state';
    }

    public function getProperty(): string
    {
        return 'state';
    }

    public function getSubjectClassName(): string
    {
        return DoneForYouService::class;
    }
}
