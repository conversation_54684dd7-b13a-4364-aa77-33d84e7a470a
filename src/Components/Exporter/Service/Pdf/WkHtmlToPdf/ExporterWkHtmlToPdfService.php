<?php

namespace Learnybox\Components\Exporter\Service\Pdf\WkHtmlToPdf;

use Learnybox\Components\Exporter\Enum\ExporterExtensionTypeEnum;
use Learnybox\Components\Exporter\Exception\ExporterException;

class ExporterWkHtmlToPdfService
{
    public function create(array $content, string $pdfDirname, string $pdfName = '', string $title = ''): array
    {
        $inputFileName = str_replace('.' . ExporterExtensionTypeEnum::EXTENSION_TYPE_PDF, '', $pdfName);
        $inputFile = UPLOAD_PATH . DIRECTORY_SEPARATOR . $pdfDirname . DIRECTORY_SEPARATOR . $inputFileName . '_content_' . '.html';
        $outputFile = UPLOAD_PATH . DIRECTORY_SEPARATOR . $pdfDirname . DIRECTORY_SEPARATOR . $pdfName;

        if (!file_put_contents($inputFile, $content['output'])) {
            throw new ExporterException(__('Impossible d\'écrire dans le fichier suivant: %s', $inputFile));
        }

        $cmd = WKHTMLTOPDF_PATH . ' --title "' . $title . '" --page-size "A4" --dpi 300 --disable-smart-shrinking --margin-left 10 --margin-right 10 --margin-top 20' . ($content['header_file'] ? ' --header-spacing 5 --header-html ' . $content['header_file'] : '') . ($content['footer_file'] ? ' --footer-spacing 10 --footer-html ' . $content['footer_file'] : '') . ' --quiet ' . $inputFile . ' ' . $outputFile;
        @exec($cmd);

        return ['inputFile' => $inputFile, 'outputFile' => $outputFile];
    }

    public function deleteFile(array $file): void
    {
        //delete original file
        if (isset($file['inputFile']) && $file['inputFile']) {
            @unlink($file['inputFile']);
        }
        if (isset($file['content']) && $file['content']) {
            @unlink($file['content']['header_file']);
            @unlink($file['content']['footer_file']);
        }

        //delete local PDF file
        if (isset($file['outputFile']) && $file['outputFile']) {
            @unlink($file['outputFile']);
        }
    }
}
