<?php

namespace Learnybox\Entity\Conference\Presentation;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Traits\ClientEntity;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Conference\Presentation\ConferencePresentationRepository::class)]
#[ORM\Table(name: 'lw_presentations')]
class ConferencePresentation
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $idPresentation;

    #[ORM\Column(type: 'string', length: 50)]
    private $titre;

    #[ORM\Column(type: 'datetime')]
    private $date;

    public function getIdPresentation(): ?int
    {
        return $this->idPresentation;
    }

    public function setIdPresentation(int $idPresentation): self
    {
        $this->idPresentation = $idPresentation;

        return $this;
    }

    public function getTitre(): ?string
    {
        return $this->titre;
    }

    public function setTitre(string $titre): self
    {
        $this->titre = $titre;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
