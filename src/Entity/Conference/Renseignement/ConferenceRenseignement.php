<?php

namespace Learnybox\Entity\Conference\Renseignement;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Conference\Renseignement\ConferenceRenseignementRepository::class)]
#[ORM\Table(name: 'lw_renseignements')]
class ConferenceRenseignement
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $ID;

    #[ORM\Column(type: 'integer')]
    private $idRens;

    #[ORM\Column(type: 'string', length: 50, nullable: true)]
    private $typeRens;

    #[ORM\Column(type: 'string', length: 50)]
    private $nom;

    #[ORM\Column(type: 'string', length: 100)]
    private $nomAffiche;

    #[ORM\Column(type: 'text')]
    private $value;

    #[ORM\Column(type: 'string', length: 50)]
    private $type;

    #[ORM\Column(type: 'integer')]
    private $size;

    #[ORM\Column(type: 'boolean', nullable: true)]
    private $obligatoire;

    #[ORM\Column(type: 'datetime')]
    private $date = 'CURRENT_TIMESTAMP';

    public function getID(): ?int
    {
        return $this->ID;
    }

    public function setID(int $ID): self
    {
        $this->ID = $ID;

        return $this;
    }

    public function getIdRens(): int
    {
        return $this->idRens;
    }

    public function setIdRens(int $idRens): self
    {
        $this->idRens = $idRens;

        return $this;
    }

    public function getTypeRens(): ?string
    {
        return $this->typeRens;
    }

    public function setTypeRens(?string $typeRens): self
    {
        $this->typeRens = $typeRens;

        return $this;
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(string $nom): self
    {
        $this->nom = $nom;

        return $this;
    }

    public function getNomAffiche(): ?string
    {
        return $this->nomAffiche;
    }

    public function setNomAffiche(string $nomAffiche): self
    {
        $this->nomAffiche = $nomAffiche;

        return $this;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(string $value): self
    {
        $this->value = $value;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getSize(): ?int
    {
        return $this->size;
    }

    public function setSize(int $size): self
    {
        $this->size = $size;

        return $this;
    }

    public function getObligatoire(): ?bool
    {
        return $this->obligatoire;
    }

    public function setObligatoire(?bool $obligatoire): self
    {
        $this->obligatoire = $obligatoire;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    /**
     * @param string $index
     * @return string
     */
    public static function getI18nLibelles(string $index): string
    {
        $i18nLibelles = [
            'lw_renseignements.nom' => __('Nom'),
            'lw_renseignements.adresse' => __('Adresse'),
            'lw_renseignements.code Postal' => __('Code Postal'),
            'lw_renseignements.ville' => __('Ville'),
            'lw_renseignements.tel' => __('Téléphone'),
            'lw_renseignements.organisation' => __('Organisation'),
            'lw_renseignements.profession' => __('Profession'),
            'lw_renseignements.pays' => __('Pays'),
            'lw_renseignements.note_conference' => __('Note générale du webinaire'),
            'lw_renseignements.note_informations' => __('Qualité des informations données pendant le webinaire'),
            'lw_renseignements.avis' => __('Votre avis'),
            'lw_renseignements.raison' => __('Pourquoi n\'avez-vous pas assisté au webinaire ?'),
            'lw_renseignements.raison.value.1' => __('Je n\'ai pas reçu l\'email'),
            'lw_renseignements.raison.value.2' => __('Je n\'étais pas disponible à cette horaire'),
            'lw_renseignements.raison.value.3' => __('Je n\'ai pas réussi à me connecter'),
            'lw_renseignements.raison.value.4' => __('Le webinaire live ne m\'intéressait plus'),
            'lw_renseignements.commentaire' => __('Votre commentaire'),
            'lw_renseignements.region' => __('Région'),
        ];

        return (isset($i18nLibelles[$index])) ? $i18nLibelles[$index] : $index;
    }
}
