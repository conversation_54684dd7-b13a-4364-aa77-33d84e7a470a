<?php

namespace Learnybox\Entity\Mail\Blog;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Traits\ClientEntity;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Mail\Blog\MailBlogBroadcastEnvoiRepository::class)]
#[ORM\Table(name: 'lbar_blog_broadcast_envois')]
class MailBlogBroadcastEnvoi
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $idBlogEnvoi;

    #[ORM\Column(type: 'datetime')]
    private $dateCreation;

    public function getIdBlogEnvoi(): ?int
    {
        return $this->idBlogEnvoi;
    }

    public function setIdBlogEnvoi(int $idBlogEnvoi): self
    {
        $this->idBlogEnvoi = $idBlogEnvoi;

        return $this;
    }

    public function getDateCreation(): ?\DateTimeInterface
    {
        return $this->dateCreation;
    }

    public function setDateCreation(\DateTimeInterface $dateCreation): self
    {
        $this->dateCreation = $dateCreation;

        return $this;
    }
}
