<?php

namespace Learnybox\Entity\Mail\Line;

use <PERSON>trine\ORM\Mapping as ORM;
use Learnybox\Entity\Interfaces\PageLineInterface;
use Learnybox\Entity\Traits\ClientEntity;
use Learnybox\Entity\Traits\MailEntity;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Mail\Line\MailLineRepository::class)]
#[ORM\Table(name: 'lbar_mails_lines')]
class MailLine implements PageLineInterface
{
    use MailEntity;
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $idLine;

    #[ORM\Column(type: 'integer')]
    private $position;

    #[ORM\Column(type: 'text')]
    private $design;

    #[ORM\Column(type: 'text')]
    private $contenuMobile;

    #[ORM\Column(type: 'datetime')]
    private $dateCreation;

    #[ORM\Column(type: 'datetime')]
    private $dateModification;

    public function getIdLine(): ?int
    {
        return $this->idLine;
    }

    public function setIdLine(int $idLine): self
    {
        $this->idLine = $idLine;

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function getDesign(): ?string
    {
        return $this->design;
    }

    public function setDesign(string $design): self
    {
        $this->design = $design;

        return $this;
    }

    public function getContenuMobile(): ?string
    {
        return $this->contenuMobile;
    }

    public function setContenuMobile(string $contenuMobile): self
    {
        $this->contenuMobile = $contenuMobile;

        return $this;
    }

    public function getDateCreation(): ?\DateTimeInterface
    {
        return $this->dateCreation;
    }

    public function setDateCreation(\DateTimeInterface $dateCreation): self
    {
        $this->dateCreation = $dateCreation;

        return $this;
    }

    public function getDateModification(): ?\DateTimeInterface
    {
        return $this->dateModification;
    }

    public function setDateModification(\DateTimeInterface $dateModification): self
    {
        $this->dateModification = $dateModification;

        return $this;
    }
}
