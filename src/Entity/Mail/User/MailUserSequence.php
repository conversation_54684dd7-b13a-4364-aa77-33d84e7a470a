<?php

namespace Learnybox\Entity\Mail\User;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Mail\Sequence\MailSequence;
use Learnybox\Entity\Traits\ClientEntity;
use Learnybox\Entity\Traits\UserEntity;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Mail\User\MailUserSequenceRepository::class)]
#[ORM\Table(name: 'lbar_users_sequences')]
class MailUserSequence
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $idUserSequence;

    #[ORM\Column(type: 'string', length: 20)]
    private $etat;

    #[ORM\Column(type: 'integer')]
    private $step;

    #[ORM\Column(type: 'integer')]
    private $nextStep;

    #[ORM\Column(type: 'datetime')]
    private $dateInscription;

    #[ORM\Column(type: 'datetime')]
    private $dateDesinscription;

    #[ORM\Column(type: 'datetime')]
    private $dateStart;

    #[ORM\ManyToOne(targetEntity: \Learnybox\Entity\Mail\Sequence\MailSequence::class, inversedBy: 'usersSequences')]
    #[ORM\JoinColumn(nullable: false, name: 'id_sequence', referencedColumnName: 'id_sequence')]
    private $sequence;

    #[ORM\ManyToOne(targetEntity: \Learnybox\Entity\Mail\User\MailUser::class, inversedBy: 'usersSequences', fetch: 'EAGER')]
    #[ORM\JoinColumn(nullable: false, name: 'user_id', referencedColumnName: 'user_id')]
    private $user;

    public function getIdUserSequence(): ?int
    {
        return $this->idUserSequence;
    }

    public function setIdUserSequence(int $idUserSequence): self
    {
        $this->idUserSequence = $idUserSequence;

        return $this;
    }

    public function getEtat(): ?string
    {
        return $this->etat;
    }

    public function setEtat(string $etat): self
    {
        $this->etat = $etat;

        return $this;
    }

    public function getStep(): ?int
    {
        return $this->step;
    }

    public function setStep(int $step): self
    {
        $this->step = $step;

        return $this;
    }

    public function getNextStep(): ?int
    {
        return $this->nextStep;
    }

    public function setNextStep(int $nextStep): self
    {
        $this->nextStep = $nextStep;

        return $this;
    }

    public function getDateInscription(): ?\DateTimeInterface
    {
        return $this->dateInscription;
    }

    public function setDateInscription(\DateTimeInterface $dateInscription): self
    {
        $this->dateInscription = $dateInscription;

        return $this;
    }

    public function getDateDesinscription(): ?\DateTimeInterface
    {
        return $this->dateDesinscription;
    }

    public function setDateDesinscription(\DateTimeInterface $dateDesinscription): self
    {
        $this->dateDesinscription = $dateDesinscription;

        return $this;
    }

    public function getDateStart(): ?\DateTimeInterface
    {
        return $this->dateStart;
    }

    public function setDateStart(\DateTimeInterface $dateStart): self
    {
        $this->dateStart = $dateStart;

        return $this;
    }

    public function getSequence(): ?MailSequence
    {
        return $this->sequence;
    }

    public function setSequence(?MailSequence $sequence): self
    {
        $this->sequence = $sequence;

        return $this;
    }

    public function getUser(): ?MailUser
    {
        return $this->user;
    }

    public function setUser(?MailUser $user): self
    {
        $this->user = $user;

        return $this;
    }
}
