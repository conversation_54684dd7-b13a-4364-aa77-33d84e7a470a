<?php

namespace Learnybox\Entity\Shop\Renseignement;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Traits\ClientEntity;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Shop\Renseignement\ShopRenseignementReponseRepository::class)]
#[ORM\Table(name: 'ps_renseignements_reponses')]
class ShopRenseignementReponse
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $idReponse;

    #[ORM\Column(type: 'string', length: 250)]
    private $reponse;

    #[ORM\ManyToOne(targetEntity: ShopRenseignement::class, inversedBy: 'reponses')]
    #[ORM\JoinColumn(nullable: false, name: 'id_rens', referencedColumnName: 'id_rens')]
    private $shopRenseignement;

    public function getIdReponse(): ?int
    {
        return $this->idReponse;
    }

    public function setIdReponse(int $idReponse): self
    {
        $this->idReponse = $idReponse;

        return $this;
    }

    public function getReponse(): ?string
    {
        return $this->reponse;
    }

    public function setReponse(string $reponse): self
    {
        $this->reponse = $reponse;

        return $this;
    }

    public function getShopRenseignement(): ?ShopRenseignement
    {
        return $this->shopRenseignement;
    }

    public function setShopRenseignement(?ShopRenseignement $shopRenseignement): self
    {
        $this->shopRenseignement = $shopRenseignement;

        return $this;
    }
}
