<?php

namespace Learnybox\Entity\Segment;

use <PERSON>trine\ORM\Mapping as ORM;
use Learnybox\Entity\Traits\ClientEntity;
use Gedmo\Mapping\Annotation as Gedmo;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Segment\SegmentRepository::class)]
#[ORM\Table(name: 'lb_segments')]
class Segment
{
    use ClientEntity;

    public const I18N_DEFAULT_NAME_PREFIX = 'segment.default.name.';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private int $id;

    #[ORM\Column(type: 'string')]
    private string $name;

    #[ORM\Column(type: 'string')]
    private string $filterParams;

    #[ORM\Column(type: 'boolean')]
    private bool $predefined;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private \DateTime $createdAt;

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'update')]
    private \DateTime $updatedAt;

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getFilterParams(): string
    {
        return $this->filterParams;
    }

    public function setFilterParams(string $filterParams): self
    {
        $this->filterParams = $filterParams;

        return $this;
    }

    public function getPredefined(): bool
    {
        return $this->predefined;
    }

    public function setPredefined(bool $predefined): self
    {
        $this->predefined = $predefined;

        return $this;
    }

    public function getCreatedAt(): \DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): \DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    public static function getI18nLibelles(string $index = null): string
    {
        $i18nLibelles = [
            self::I18N_DEFAULT_NAME_PREFIX . '1' => __('Tous les utilisateurs'),
            self::I18N_DEFAULT_NAME_PREFIX . '2' => __('Tous les membres de formation'),
            self::I18N_DEFAULT_NAME_PREFIX . '3' => __('Tous les contacts LearnyMail'),
            self::I18N_DEFAULT_NAME_PREFIX . '4' => __('Tous les clients'),
        ];

        return $i18nLibelles[$index] ?? $index;
    }
}
