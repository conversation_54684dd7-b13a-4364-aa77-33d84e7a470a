<?php

namespace Learnybox\Entity\Page\Verification;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Traits\ClientEntity;
use Gedmo\Mapping\Annotation as Gedmo;
use Learnybox\Entity\Traits\UserEntity;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Page\Verification\PageVerificationRepository::class)]
#[ORM\Table(name: 'lb_pages_verifications')]
class PageVerification
{
    use ClientEntity;
    use UserEntity;

    const STATUS_TO_REVIEW = 'to_review';
    const STATUS_APPROVED = 'approved';
    const STATUS_DENIED = 'denied';
    const STATUS_FOREVER_DENIED = 'forever_denied';
    const CONFIG_PAGES_BLOCKED = 'pages_blocked';
    const CONFIG_PAGES_FOREVER_BLOCKED = 'pages_forever_blocked';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 50)]
    private $status;

    #[ORM\Column(type: 'text')]
    private $message = '';

    #[ORM\Column(type: 'datetime')]
    #[Gedmo\Timestampable(on: 'create')]
    private $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
