<?php

namespace Learnybox\Entity\Onboarding\Task;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Onboarding\Task\OnboardingTaskRepository::class)]
#[ORM\Table(name: 'lb_onboarding_tasks')]
class OnboardingTask
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'integer')]
    private $position;

    #[ORM\Column(type: 'string', length: 200)]
    private $title;

    #[ORM\Column(type: 'string', length: 200, nullable: true)]
    private $text;

    #[ORM\Column(type: 'string', length: 200, nullable: true)]
    private $video;

    #[ORM\Column(type: 'string', length: 100, nullable: true)]
    private $formName;

    #[ORM\ManyToOne(targetEntity: \Learnybox\Entity\Onboarding\Task\OnboardingTaskGroup::class, inversedBy: 'onboardingTasks')]
    #[ORM\JoinColumn(name: 'task_group_id', nullable: false)]
    private $onboardingTaskGroup;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this::getI18nLibelles($this->title);
    }

    public function setTitle(string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getText(): ?string
    {
        return $this->text;
    }

    public function setText(?string $text): self
    {
        $this->text = $text;

        return $this;
    }

    public function getVideo(): ?string
    {
        return $this->video;
    }

    public function setVideo(?string $video): self
    {
        $this->video = $video;

        return $this;
    }

    public function getFormName(): ?string
    {
        return $this->formName;
    }

    public function setFormName(?string $formName): self
    {
        $this->formName = $formName;

        return $this;
    }

    public function getOnboardingTaskGroup(): ?OnboardingTaskGroup
    {
        return $this->onboardingTaskGroup;
    }

    public function setOnboardingTaskGroup(?OnboardingTaskGroup $onboardingTaskGroup): self
    {
        $this->onboardingTaskGroup = $onboardingTaskGroup;

        return $this;
    }

    /**
     * @param string|null $index
     * @return string|null
     */
    public static function getI18nLibelles(string $index = null): ?string
    {
        $i18nLibelles = [
            'lb_onboarding_tasks.title.1' => __('Paramétrage des cases de consentement à la prospection commerciale et personnalisation des phrases'),
            'lb_onboarding_tasks.title.3' => __('Personnalisez le texte de désinscription présent dans vos emails'),
            'lb_onboarding_tasks.title.4' => __('Configurez votre adresse mail par défaut'),
            'lb_onboarding_tasks.title.6' => __('Configurez votre champ SPF pour que vos mails aient une meilleure délivrabilité.'),
            'lb_onboarding_tasks.title.7' => __('Paramétrez l’autorépondeur LearnyMail. Si vous en avez déjà un autre, connectez-le.'),
            'lb_onboarding_tasks.title.8' => __('Personnalisez le texte de désinscription présent dans vos emails'),
            'lb_onboarding_tasks.title.10' => __('Configurez le(s) moyen(s) de paiement que vous allez proposer'),
            'lb_onboarding_tasks.title.11' => __('Ajoutez le code tracking (code de suivi) de Google Analytics à utiliser pour toutes vos pages'),
            'lb_onboarding_tasks.title.12' => __('Ajoutez votre code tracking publicitaire Facebook Ads, Adwords ou autres'),
            'lb_onboarding_tasks.title.13' => __('Ajoutez votre nom de domaine'),
            'lb_onboarding_tasks.title.14' => __('Créer le champ CNAME dans la zone DNS de votre fournisseur'),
            'lb_onboarding_tasks.title.15' => __('Ajoutez les mentions légales sur votre page'),
            'lb_onboarding_tasks.title.16' => __('Ajoutez le texte de vos conditions générales'),
            'lb_onboarding_tasks.title.17' => __('Ajoutez le texte de votre politique de confidentialité'),
            'lb_onboarding_tasks.title.18' => __('Configurez les paramètres de facturation'),
            'lb_onboarding_tasks.title.21' => __('Personnalisez votre logo'),
            'lb_onboarding_tasks.title.22' => __('Personnalisez votre pied de page'),
            'lb_onboarding_tasks.title.23' => __('Sélectionnez votre thème'),
            'lb_onboarding_tasks.title.24' => __('Ajoutez vos réseaux sociaux par défaut'),
            'lb_onboarding_tasks.title.26' => __('Activez votre lien d\'affilié LearnyBox'),
            'lb_onboarding_tasks.title.27' => __('Ce que vous pouvez gagner'),
            'lb_onboarding_tasks.title.28' => __('Ajoutez un nouveau nom de domaine pour pouvoir choisir sur quel domaine travailler'),
            'lb_onboarding_tasks.title.29' => __('Ajoutez les mentions légales sur votre page'),
            'lb_onboarding_tasks.title.30' => __('Ajoutez le texte de vos conditions générales'),
            'lb_onboarding_tasks.title.31' => __('Ajoutez le texte de votre politique de confidentialité'),
            'lb_onboarding_tasks.title.32' => __('Configurez votre adresse mail par défaut'),
            'lb_onboarding_tasks.title.33' => __('Ajoutez les mentions légales sur votre page'),
            'lb_onboarding_tasks.title.34' => __('Ajoutez le texte de vos conditions générales'),
            'lb_onboarding_tasks.title.35' => __('Ajoutez le texte de votre politique de confidentialité'),
            'lb_onboarding_tasks.title.36' => __('Configurez votre adresse mail par défaut'),
        ];

        return (isset($i18nLibelles[$index])) ? $i18nLibelles[$index] : $index;
    }
}
