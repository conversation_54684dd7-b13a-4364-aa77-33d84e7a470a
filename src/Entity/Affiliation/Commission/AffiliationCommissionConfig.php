<?php

namespace Learnybox\Entity\Affiliation\Commission;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Traits\AffilieEntity;
use Learnybox\Entity\Traits\CampaignEntity;
use Learnybox\Entity\Traits\ClientEntity;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Affiliation\Commission\AffiliationCommissionConfigRepository::class)]
#[ORM\Table(name: 'aff_commissions_config')]
class AffiliationCommissionConfig
{
    use ClientEntity;
    use AffilieEntity;
    use CampaignEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $idConfig;

    #[ORM\Column(type: 'string', length: 20)]
    private $typeCommission;

    #[ORM\Column(type: 'float')]
    private $montantCommission;

    #[ORM\Column(type: 'string', length: 10)]
    private $type = 'normal';

    #[ORM\Column(type: 'string', length: 20)]
    private $typeMontant;

    #[ORM\Column(type: 'datetime')]
    private $date;

    public function getIdConfig(): ?int
    {
        return $this->idConfig;
    }

    public function setIdConfig(int $idConfig): self
    {
        $this->idConfig = $idConfig;

        return $this;
    }

    public function getTypeCommission(): ?string
    {
        return $this->typeCommission;
    }

    public function setTypeCommission(string $typeCommission): self
    {
        $this->typeCommission = $typeCommission;

        return $this;
    }

    public function getMontantCommission(): ?float
    {
        return $this->montantCommission;
    }

    public function setMontantCommission(float $montantCommission): self
    {
        $this->montantCommission = $montantCommission;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getTypeMontant(): ?string
    {
        return $this->typeMontant;
    }

    public function setTypeMontant(string $typeMontant): self
    {
        $this->typeMontant = $typeMontant;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
