<?php

namespace Learnybox\Entity\Affiliation\Commission;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Affiliation\Invoice\AffiliationInvoice;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Affiliation\Commission\AffiliationCommissionInvoiceRepository::class)]
#[ORM\Table(name: 'aff_commissions_invoices')]
class AffiliationCommissionInvoice
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\OneToOne(targetEntity: \Learnybox\Entity\Affiliation\Commission\AffiliationCommission::class, cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: true, name: 'aff_commission_id', referencedColumnName: 'id_commission')]
    private AffiliationCommission $commission;

    #[ORM\OneToOne(targetEntity: \Learnybox\Entity\Affiliation\Invoice\AffiliationInvoice::class, cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: true, name: 'aff_invoice_id', referencedColumnName: 'id')]
    private AffiliationInvoice $invoice;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getCommission(): AffiliationCommission
    {
        return $this->commission;
    }

    public function setCommission(AffiliationCommission $commission): self
    {
        $this->commission = $commission;

        return $this;
    }

    public function getInvoice(): AffiliationInvoice
    {
        return $this->invoice;
    }

    public function setInvoice(AffiliationInvoice $invoice): self
    {
        $this->invoice = $invoice;

        return $this;
    }
}
