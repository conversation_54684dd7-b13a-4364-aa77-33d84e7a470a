<?php

namespace Learnybox\Entity\Formation\Membre;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Traits\ClientEntity;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Formation\Membre\FormationMembreImportRepository::class)]
#[ORM\Table(name: 'ptf_membres_imports')]
class FormationMembreImport
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $idImport;

    #[ORM\Column(type: 'integer')]
    private $idImportation;

    #[ORM\Column(type: 'text')]
    private $datas;

    #[ORM\Column(type: 'text')]
    private $datasFormation;

    #[ORM\Column(type: 'boolean')]
    private $processed;

    #[ORM\Column(type: 'text')]
    private $error;

    #[ORM\Column(type: 'datetime')]
    private $date;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $dateProcessed;

    public function getIdImport(): ?int
    {
        return $this->idImport;
    }

    public function setIdImport(int $idImport): self
    {
        $this->idImport = $idImport;

        return $this;
    }

    public function getIdImportation(): ?int
    {
        return $this->idImportation;
    }

    public function setIdImportation(int $idImportation): self
    {
        $this->idImportation = $idImportation;

        return $this;
    }

    public function getDatas(): ?string
    {
        return $this->datas;
    }

    public function setDatas(string $datas): self
    {
        $this->datas = $datas;

        return $this;
    }

    public function getDatasFormation(): ?string
    {
        return $this->datasFormation;
    }

    public function setDatasFormation(string $datasFormation): self
    {
        $this->datasFormation = $datasFormation;

        return $this;
    }

    public function getProcessed(): ?bool
    {
        return $this->processed;
    }

    public function setProcessed(bool $processed): self
    {
        $this->processed = $processed;

        return $this;
    }

    public function getError(): ?string
    {
        return $this->error;
    }

    public function setError(string $error): self
    {
        $this->error = $error;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateProcessed(): ?\DateTimeInterface
    {
        return $this->dateProcessed;
    }

    public function setDateProcessed(?\DateTimeInterface $dateProcessed): self
    {
        $this->dateProcessed = $dateProcessed;

        return $this;
    }
}
