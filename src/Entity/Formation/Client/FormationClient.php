<?php

namespace Learnybox\Entity\Formation\Client;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Formation\Client\FormationClientRepository::class)]
#[ORM\Table(name: 'ptf_clients')]
class FormationClient
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $idclient;

    #[ORM\Column(type: 'string', length: 255)]
    private $nomclient;

    #[ORM\Column(type: 'integer')]
    private $maxusers = 50;

    #[ORM\Column(type: 'datetime')]
    private $datecreation;

    #[ORM\Column(type: 'datetime')]
    private $datemodification = 'CURRENT_TIMESTAMP';

    public function getIdclient(): ?int
    {
        return $this->idclient;
    }

    public function setIdclient(int $idclient): self
    {
        $this->idclient = $idclient;

        return $this;
    }

    public function getNomclient(): ?string
    {
        return $this->nomclient;
    }

    public function setNomclient(string $nomclient): self
    {
        $this->nomclient = $nomclient;

        return $this;
    }

    public function getMaxusers(): ?int
    {
        return $this->maxusers;
    }

    public function setMaxusers(int $maxusers): self
    {
        $this->maxusers = $maxusers;

        return $this;
    }

    public function getDatecreation(): ?\DateTimeInterface
    {
        return $this->datecreation;
    }

    public function setDatecreation(\DateTimeInterface $datecreation): self
    {
        $this->datecreation = $datecreation;

        return $this;
    }

    public function getDatemodification(): ?\DateTimeInterface
    {
        return $this->datemodification;
    }

    public function setDatemodification(\DateTimeInterface $datemodification): self
    {
        $this->datemodification = $datemodification;

        return $this;
    }
}
