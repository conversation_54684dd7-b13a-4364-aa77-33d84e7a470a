<?php

namespace Learnybox\Entity\Formation\Academy;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Traits\ClientEntity;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Formation\Academy\FormationAcademyRepository::class)]
#[ORM\Table(name: 'ptf_academy')]
class FormationAcademy
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $id;

    #[ORM\Column(type: 'string', length: 255)]
    private string $name;

    #[ORM\Column(type: 'text')]
    private string $description = '';

    #[ORM\Column(type: 'text')]
    private string $image = '';

    #[ORM\Column(type: 'text')]
    private string $logo = '';

    #[ORM\Column(type: 'string', length: 7)]
    private string $mainColor = '';

    #[ORM\Column(type: 'string', length: 50)]
    private string $font = '';

    #[ORM\Column(type: 'string', length: 255)]
    private string $menu = '';

    #[ORM\Column(type: 'text')]
    private string $topMenu = '';

    #[ORM\Column(type: 'boolean')]
    private bool $public = false;

    #[ORM\Column(type: 'boolean')]
    private bool $displayList = true;

    #[ORM\Column(type: 'boolean')]
    private bool $displayGrid = true;

    #[ORM\Column(type: 'boolean')]
    private bool $displayMembersCount = true;

    #[ORM\Column(type: 'string', length: 255)]
    private ?string $welcomeSubject = null;

    #[ORM\Column(type: 'text')]
    private ?string $welcomeEmail = null;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $date;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(string $image): self
    {
        $this->image = $image;

        return $this;
    }

    public function getLogo(): ?string
    {
        return $this->logo;
    }

    public function setLogo(string $logo): self
    {
        $this->logo = $logo;

        return $this;
    }

    public function getMainColor(): ?string
    {
        if ($this->mainColor) {
            return $this->mainColor;
        }

        return '#EE5340';
    }

    public function setMainColor(string $mainColor): self
    {
        $this->mainColor = $mainColor;

        return $this;
    }

    public function getFont(): ?string
    {
        return $this->font;
    }

    public function setFont(string $font): self
    {
        $this->font = $font;

        return $this;
    }

    public function getMenu(): array
    {
        return ($this->menu ? json_decode($this->menu, true) : []);
    }

    public function setMenu(array $menu): self
    {
        $this->menu = json_encode($menu);

        return $this;
    }

    public function getTopMenu(): array
    {
        return ($this->topMenu ? json_decode($this->topMenu, true) : []);
    }

    public function setTopMenu(array $topMenu): self
    {
        $this->topMenu = json_encode($topMenu);

        return $this;
    }

    public function isPublic(): bool
    {
        return $this->public;
    }

    public function setPublic(bool $public): self
    {
        $this->public = $public;

        return $this;
    }

    public function canDisplayList(): bool
    {
        return $this->displayList;
    }

    public function setDisplayList(bool $displayList): self
    {
        $this->displayList = $displayList;

        return $this;
    }

    public function canDisplayGrid(): bool
    {
        return $this->displayGrid;
    }

    public function setDisplayGrid(bool $displayGrid): self
    {
        $this->displayGrid = $displayGrid;

        return $this;
    }

    public function displayMembersCount(): bool
    {
        return $this->displayMembersCount;
    }

    public function setDisplayMembersCount(bool $displayMembersCount): self
    {
        $this->displayMembersCount = $displayMembersCount;

        return $this;
    }

    public function getWelcomeSubject(): ?string
    {
        return $this->welcomeSubject;
    }

    public function setWelcomeSubject(string $welcomeSubject): self
    {
        $this->welcomeSubject = $welcomeSubject;

        return $this;
    }

    public function getWelcomeEmail(): ?string
    {
        return $this->welcomeEmail;
    }

    public function setWelcomeEmail(string $welcomeEmail): self
    {
        $this->welcomeEmail = $welcomeEmail;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }
}
