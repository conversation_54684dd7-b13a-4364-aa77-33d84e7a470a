<?php

namespace Learnybox\Entity\Quiz\Page;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Quiz\Quiz;
use Learnybox\Entity\Traits\ClientEntity;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Quiz\Page\QuizPageRepository::class)]
#[ORM\Table(name: 'qz_pages')]
class QuizPage
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $idPage;

    #[ORM\Column(type: 'string', length: 250)]
    private $nom;

    #[ORM\Column(type: 'text')]
    private $description;

    #[ORM\Column(type: 'integer')]
    private $timer;

    #[ORM\Column(type: 'integer')]
    private $position;

    #[ORM\Column(type: 'boolean')]
    private $random = false;

    #[ORM\ManyToOne(targetEntity: \Learnybox\Entity\Quiz\Quiz::class)]
    #[ORM\JoinColumn(nullable: false, name: 'id_quiz', referencedColumnName: 'id_quiz')]
    private $quiz;

    public function getIdPage(): ?int
    {
        return $this->idPage;
    }

    public function setIdPage(int $idPage): self
    {
        $this->idPage = $idPage;

        return $this;
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(string $nom): self
    {
        $this->nom = $nom;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getTimer(): ?int
    {
        return $this->timer;
    }

    public function setTimer(int $timer): self
    {
        $this->timer = $timer;

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setPosition(int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function getRandom(): ?bool
    {
        return $this->random;
    }

    public function setRandom(?bool $random): self
    {
        $this->random = $random;

        return $this;
    }

    public function getQuiz(): ?Quiz
    {
        return $this->quiz;
    }

    public function setQuiz(?Quiz $quiz): self
    {
        $this->quiz = $quiz;

        return $this;
    }
}
