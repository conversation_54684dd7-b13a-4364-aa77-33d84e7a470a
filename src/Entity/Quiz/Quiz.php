<?php

namespace Learnybox\Entity\Quiz;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Object\Category\ObjectCategory;
use Learnybox\Entity\Traits\ClientEntity;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Quiz\QuizRepository::class)]
#[ORM\Table(name: 'qz_quiz')]
class Quiz
{
    use ClientEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $idQuiz;

    #[ORM\Column(type: 'string', length: 200, nullable: true)]
    private $nom;

    #[ORM\Column(type: 'string', length: 200)]
    private $adminTitle;

    #[ORM\Column(type: 'string', length: 10)]
    private $type = 'type1';

    #[ORM\Column(type: 'integer')]
    private $noteMin = 70;

    #[ORM\Column(type: 'integer')]
    private $nbTimes = 0;

    #[ORM\Column(type: 'string', length: 10)]
    private $theme = 'theme1';

    #[ORM\Column(type: 'string', length: 200, nullable: true)]
    private $image;

    #[ORM\Column(type: 'text', nullable: true)]
    private $description;

    #[ORM\Column(type: 'text', nullable: true)]
    private $finishMessage;

    #[ORM\Column(type: 'text', nullable: true)]
    private $redirection;

    #[ORM\Column(type: 'text', nullable: true)]
    private $autorepondeur;

    #[ORM\Column(type: 'boolean')]
    private $anonymous = 0;

    #[ORM\Column(type: 'string', length: 50)]
    private $users;

    #[ORM\Column(type: 'string', length: 15, nullable: true)]
    private $etat;

    #[ORM\Column(type: 'string', length: 250)]
    private $randomId;

    #[ORM\Column(type: 'datetime')]
    private $date = 'CURRENT_TIMESTAMP';

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $dateCreation;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $dateDemarrage;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $dateTermine;

    #[ORM\Column(type: 'boolean')]
    private $rgpd;

    #[ORM\Column(type: 'text')]
    private $rgpdNotice;

    #[ORM\Column(type: 'boolean')]
    private $rgpdAff;

    #[ORM\Column(type: 'text')]
    private $rgpdAffNotice;

    #[ORM\ManyToOne(targetEntity: \Learnybox\Entity\Object\Category\ObjectCategory::class, inversedBy: 'quiz')]
    #[ORM\JoinColumn(nullable: false, name: 'id_categorie', referencedColumnName: 'id')]
    private $category;

    #[ORM\Column(type: 'boolean')]
    private $rgpdCheckedDefault = false;

    #[ORM\Column(type: 'boolean')]
    private $rgpdAffCheckedDefault = false;

    #[ORM\Column(type: 'integer', name: 'id_domaine')]
    private $idDomaine;

    public function getIdQuiz(): ?int
    {
        return $this->idQuiz;
    }

    public function setIdQuiz(int $idQuiz): self
    {
        $this->idQuiz = $idQuiz;

        return $this;
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(?string $nom): self
    {
        $this->nom = $nom;

        return $this;
    }

    public function getAdminTitle(): ?string
    {
        return $this->adminTitle;
    }

    public function setAdminTitle(?string $adminTitle): self
    {
        $this->adminTitle = $adminTitle;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getNoteMin(): ?int
    {
        return $this->noteMin;
    }

    public function setNoteMin(int $noteMin): self
    {
        $this->noteMin = $noteMin;

        return $this;
    }

    public function getNbTimes(): ?int
    {
        return $this->nbTimes;
    }

    public function setNbTimes(int $nbTimes): self
    {
        $this->nbTimes = $nbTimes;

        return $this;
    }

    public function getTheme(): ?string
    {
        return $this->theme;
    }

    public function setTheme(string $theme): self
    {
        $this->theme = $theme;

        return $this;
    }

    public function getImage(): ?string
    {
        return $this->image;
    }

    public function setImage(?string $image): self
    {
        $this->image = $image;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getFinishMessage(): ?string
    {
        return $this->finishMessage;
    }

    public function setFinishMessage(?string $finishMessage): self
    {
        $this->finishMessage = $finishMessage;

        return $this;
    }

    public function getRedirection(): ?string
    {
        return $this->redirection;
    }

    public function setRedirection(?string $redirection): self
    {
        $this->redirection = $redirection;

        return $this;
    }

    public function getAutorepondeur(): ?string
    {
        return $this->autorepondeur;
    }

    public function setAutorepondeur(?string $autorepondeur): self
    {
        $this->autorepondeur = $autorepondeur;

        return $this;
    }

    public function getAnonymous(): ?bool
    {
        return $this->anonymous;
    }

    public function setAnonymous(bool $anonymous): self
    {
        $this->anonymous = $anonymous;

        return $this;
    }

    public function getUsers(): ?string
    {
        return $this->users;
    }

    public function setUsers(string $users): self
    {
        $this->users = $users;

        return $this;
    }

    public function getEtat(): ?string
    {
        return $this->etat;
    }

    public function setEtat(?string $etat): self
    {
        $this->etat = $etat;

        return $this;
    }

    public function getRandomId(): ?string
    {
        return $this->randomId;
    }

    public function setRandomId(string $randomId): self
    {
        $this->randomId = $randomId;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getDateCreation(): ?\DateTimeInterface
    {
        return $this->dateCreation;
    }

    public function setDateCreation(?\DateTimeInterface $dateCreation): self
    {
        $this->dateCreation = $dateCreation;

        return $this;
    }

    public function getDateDemarrage(): ?\DateTimeInterface
    {
        return $this->dateDemarrage;
    }

    public function setDateDemarrage(?\DateTimeInterface $dateDemarrage): self
    {
        $this->dateDemarrage = $dateDemarrage;

        return $this;
    }

    public function getDateTermine(): ?\DateTimeInterface
    {
        return $this->dateTermine;
    }

    public function setDateTermine(?\DateTimeInterface $dateTermine): self
    {
        $this->dateTermine = $dateTermine;

        return $this;
    }

    public function getRgpd(): ?bool
    {
        return $this->rgpd;
    }

    public function setRgpd(bool $rgpd): self
    {
        $this->rgpd = $rgpd;

        return $this;
    }

    public function getRgpdNotice(): ?string
    {
        return $this->rgpdNotice;
    }

    public function setRgpdNotice(string $rgpdNotice): self
    {
        $this->rgpdNotice = $rgpdNotice;

        return $this;
    }

    public function getRgpdAff(): ?bool
    {
        return $this->rgpdAff;
    }

    public function setRgpdAff(bool $rgpdAff): self
    {
        $this->rgpdAff = $rgpdAff;

        return $this;
    }

    public function getRgpdAffNotice(): ?string
    {
        return $this->rgpdAffNotice;
    }

    public function setRgpdAffNotice(string $rgpdAffNotice): self
    {
        $this->rgpdAffNotice = $rgpdAffNotice;

        return $this;
    }

    public function getCategory(): ?ObjectCategory
    {
        return $this->category;
    }

    public function setCategory(?ObjectCategory $category): self
    {
        $this->category = $category;

        return $this;
    }

    public function getRgpdCheckedDefault(): ?bool
    {
        return $this->rgpdCheckedDefault;
    }

    public function setRgpdCheckedDefault(bool $rgpdCheckedDefault): self
    {
        $this->rgpdCheckedDefault = $rgpdCheckedDefault;

        return $this;
    }

    public function getRgpdAffCheckedDefault(): ?bool
    {
        return $this->rgpdAffCheckedDefault;
    }

    public function setRgpdAffCheckedDefault(bool $rgpdAffCheckedDefault): self
    {
        $this->rgpdAffCheckedDefault = $rgpdAffCheckedDefault;

        return $this;
    }

    public function getIdDomaine(): ?int
    {
        return $this->idDomaine;
    }

    public function setIdDomaine(?int $idDomaine): self
    {
        $this->idDomaine = $idDomaine;
        return $this;
    }
}
