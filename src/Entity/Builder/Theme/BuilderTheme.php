<?php

namespace Learnybox\Entity\Builder\Theme;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Builder\Master\BuilderMasterTheme;
use Learnybox\Entity\Builder\Specific\BuilderSpecificTheme;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Builder\Theme\BuilderThemeRepository::class)]
#[ORM\Table(name: 'builder_themes')]
class BuilderTheme
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $idTheme;

    #[ORM\Column(type: 'text')]
    private $design;

    #[ORM\Column(type: 'text')]
    private $images;

    #[ORM\ManyToOne(targetEntity: \Learnybox\Entity\Builder\Master\BuilderMasterTheme::class)]
    #[ORM\JoinColumn(nullable: false, name: 'id_master_theme', referencedColumnName: 'id_master_theme')]
    private $masterTheme;

    #[ORM\ManyToOne(targetEntity: \Learnybox\Entity\Builder\Specific\BuilderSpecificTheme::class)]
    #[ORM\JoinColumn(nullable: false, name: 'id_specific_theme', referencedColumnName: 'id_specific_theme')]
    private $specificTheme;

    public function getIdTheme(): ?int
    {
        return $this->idTheme;
    }

    public function setIdTheme(int $idTheme): self
    {
        $this->idTheme = $idTheme;

        return $this;
    }

    public function getDesign(): ?string
    {
        return $this->design;
    }

    public function setDesign(string $design): self
    {
        $this->design = $design;

        return $this;
    }

    public function getImages(): ?string
    {
        return $this->images;
    }

    public function setImages(string $images): self
    {
        $this->images = $images;

        return $this;
    }

    public function getMasterTheme(): ?BuilderMasterTheme
    {
        return $this->masterTheme;
    }

    public function setMasterTheme(?BuilderMasterTheme $masterTheme): self
    {
        $this->masterTheme = $masterTheme;

        return $this;
    }

    public function getSpecificTheme(): ?BuilderSpecificTheme
    {
        return $this->specificTheme;
    }

    public function setSpecificTheme(?BuilderSpecificTheme $specificTheme): self
    {
        $this->specificTheme = $specificTheme;

        return $this;
    }
}
