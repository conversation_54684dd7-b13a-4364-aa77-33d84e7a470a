<?php

namespace Learnybox\Entity\Sondage\Participant;

use Doctrine\ORM\Mapping as ORM;
use Learnybox\Entity\Sondage\Question\SondageQuestion;
use Learnybox\Entity\Sondage\Sondage;

#[ORM\Entity(repositoryClass: \Learnybox\Repository\Sondage\Participant\SondageParticipantReponseRepository::class)]
#[ORM\Table(name: 'sdg_participants_reponses')]
class SondageParticipantReponse
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private $ID;

    #[ORM\Column(type: 'text', nullable: true)]
    private $reponse;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private $date;

    #[ORM\ManyToOne(targetEntity: \Learnybox\Entity\Sondage\Participant\SondageParticipant::class)]
    #[ORM\JoinColumn(nullable: false, name: 'id_participant', referencedColumnName: 'id_participant')]
    private $participant;

    #[ORM\ManyToOne(targetEntity: \Learnybox\Entity\Sondage\Sondage::class)]
    #[ORM\JoinColumn(nullable: false, name: 'id_sondage', referencedColumnName: 'id_sondage')]
    private $sondage;

    #[ORM\ManyToOne(targetEntity: \Learnybox\Entity\Sondage\Question\SondageQuestion::class)]
    #[ORM\JoinColumn(nullable: false, name: 'id_question', referencedColumnName: 'id_question')]
    private $question;

    public function getID(): ?int
    {
        return $this->ID;
    }

    public function setID(int $ID): self
    {
        $this->ID = $ID;

        return $this;
    }

    public function getReponse(): ?string
    {
        return $this->reponse;
    }

    public function setReponse(?string $reponse): self
    {
        $this->reponse = $reponse;

        return $this;
    }

    public function getDate(): ?\DateTimeInterface
    {
        return $this->date;
    }

    public function setDate(?\DateTimeInterface $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getParticipant(): ?SondageParticipant
    {
        return $this->participant;
    }

    public function setParticipant(?SondageParticipant $participant): self
    {
        $this->participant = $participant;

        return $this;
    }

    public function getSondage(): ?Sondage
    {
        return $this->sondage;
    }

    public function setSondage(?Sondage $sondage): self
    {
        $this->sondage = $sondage;

        return $this;
    }

    public function getQuestion(): ?SondageQuestion
    {
        return $this->question;
    }

    public function setQuestion(?SondageQuestion $question): self
    {
        $this->question = $question;

        return $this;
    }
}
