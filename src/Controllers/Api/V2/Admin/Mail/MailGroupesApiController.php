<?php

namespace Learnybox\Controllers\Api\V2\Admin\Mail;

use Learnybox\Controllers\Api\V2\Admin\AbstractApiController;
use Learnybox\Entity\Mail\Groupe\MailGroupe;
use OpenApi\Annotations as OA;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class MailGroupesApiController
 * @package Learnybox\Controllers\Api\Mail
 */
class MailGroupesApiController extends AbstractApiController
{
    public function __construct()
    {
        parent::__construct();
        $this->entityClass = MailGroupe::class;
    }

    /**
    * @OA\Get(
    *   path="/api/v2/mail/groupes/",
    *   summary="Liste des groupes",
    *   tags={"Mail"},
    *   operationId="api_v2_mail_groupes_list",
    *   @OA\Response(
    *     response=200,
    *     description="",
    *     @OA\JsonContent(
    *       ref="#/components/schemas/ApiResponse",
    *       example={
    "status": true,
    "data": {
        {
        "id_groupe": 1,
        "nom": "abc",
        "description": "",
        "date": "2017-11-27T13:07:00+0100",
        "date_modification": "2017-11-28T13:07:00+0100",
        "client": {
       "nom_client": "Learny Box"
        }
        },
        {
        "id_groupe": 2,
        "nom": "clic",
        "description": "",
        "date": "2017-12-08T10:53:28+0100",
        "date_modification": "2017-12-09T10:53:28+0100",
        "client": {
       "nom_client": "Learny Box"
        }
        }
    },
    "message": "",
    "offset": 0,
    "limit": 100,
    "total": 2,
    "_links": {
        "self": {
       "href": "https://api.learnybox.com/api/v2/mail/groupes/?limit=100&offset=0",
       "rel": "self"
        }
    }
    }
    *     )
    *   )
    * )
    *
    *
    */
    #[Route(path: '/api/v2/mail/groupes/', name: 'api_v2_mail_groupes_list', methods: ['GET'])]
    public function listV2()
    {
        $this->querySorts = ['nom' => 'ASC'];
        return parent::listV2();
    }

    /**
    * @OA\Get(
    *   path="/api/v2/mail/groupes/{id_groupe}/",
    *   summary="Données d'un groupe",
    *   tags={"Mail"},
    *   operationId="api_v2_mail_groupes_show",
    *   @OA\Parameter(
    *     name="id_groupe",
    *     description="Identifiant du groupe",
    *     in="path",
    *     required=true,
    *     @OA\Schema(type="integer")
    *   ),
    *   @OA\Response(
    *     response=200,
    *     description="",
    *     @OA\JsonContent(
    *       ref="#/components/schemas/ApiResponse",
    *       example={
    "status": true,
    "data": {
        "id_groupe": 1,
        "nom": "abc",
        "description": "",
        "date": "2017-11-27T13:07:00+0100",
        "date_modification": "2017-11-28T13:07:00+0100",
        "client": {
       "nom_client": "Learny Box"
        }
    },
    "message": ""
    }
    *     )
    *   )
    * )
    *
    *
    */
    #[Route(path: '/api/v2/mail/groupes/{id_groupe}/', name: 'api_v2_mail_groupes_show', methods: ['GET'])]
    public function show()
    {
        return parent::showV2('id_groupe');
    }
}
