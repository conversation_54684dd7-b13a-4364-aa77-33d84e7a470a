<?php

namespace Learnybox\Controllers\Api\V2\Admin\Activity;

use Learnybox\Controllers\Api\V2\Admin\AbstractApiController;
use Learnybox\Entity\Activity\Activity;
use Learnybox\Services\Activity\UserActivityService;
use OpenApi\Annotations as OA;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ActivitiesApiController
 * @package Learnybox\Controllers\Api
 */
class ActivitiesApiController extends AbstractApiController
{
    public function __construct()
    {
        parent::__construct();
        $this->entityClass = Activity::class;
    }

    /**
    * @OA\Get(
    *   path="/api/v2/activities/",
    *   summary="Liste des activités",
    *   tags={"Activities"},
    *   operationId="api_v2_activities_list",
    *   @OA\Response(
    *     response=200,
    *     description="",
    *     @OA\JsonContent(
    *       ref="#/components/schemas/ApiResponse",
    *       example={
               "status": true,
               "data": {
                   {
                       "id": 1,
                       "viewed": false,
                       "transaction": {},
                       "type": "transaction"
                   },
                   {
                       "id": 2,
                       "viewed": false,
                       "comment": {},
                       "type": "comment"
                   },
                   {
                       "id": 3,
                       "viewed": false,
                       "page_comment": {},
                       "type": "page_comment"
                   },
                   {
                       "id": 4,
                       "viewed": false,
                       "formation_comment": {},
                       "type": "formation_comment"
                   }
               },
               "message": "",
               "offset": 0,
               "limit": 100,
               "total": 2,
               "_links": {
                   "self": {
                       "href": "https://api.learnybox.com/api/v2/activities/?limit=100&offset=0",
                       "rel": "self"
                   }
               }
           }
    *     )
    *   )
    * )
    *
    */
    #[Route(path: '/api/v2/activities/', name: 'api_v2_activities_list', methods: ['GET'])]
    public function listV2()
    {
        if (isset($this->currentRequestParams['types'])) {
            $this->customQueryBuilder = $this->getRepository()->createQueryBuilderByTypes($this->currentRequestParams['types']);
            $this->customQueryBuilder->orderBy('a.createdAt', 'DESC');
            $this->customQueryBuilder->andWhere('a.client = :client');
            $this->customQueryBuilder->setParameter('client', $_SESSION['id_client']);
        } else {
            $this->querySorts = ['createdAt' => 'DESC'];
            $this->queryFilters['client'] = $_SESSION['id_client'];
        }

        return parent::listV2();
    }

    /**
    * @OA\Get(
    *   path="/api/v2/activities/{id}/",
    *   summary="Données d'une activité",
    *   tags={"Activities"},
    *   operationId="api_v2_activities_show",
    *   @OA\Response(
    *     response=200,
    *     description="",
    *     @OA\JsonContent(
    *       ref="#/components/schemas/ApiResponse",
    *       example={
               "status": true,
               "data": {
                   "id": 1,
                   "viewed": false,
                   "transaction": {},
                   "type": "transaction"
               }
           }
    *     )
    *   )
    * )
    *
    */
    #[Route(path: '/api/v2/activities/{id}/', name: 'api_v2_activities_show', methods: ['GET'])]
    public function show()
    {
        return parent::showV2('id');
    }

    #[Route(path: '/api/v2/activities/user/{user_id}/', name: 'api_v2_activities_list_by_user', methods: ['GET'])]
    public function listByUser()
    {
        $limit = (isset($this->currentRequestParams['limit'])) ? $this->currentRequestParams['limit'] : 10;
        $offset = (isset($this->currentRequestParams['offset'])) ? $this->currentRequestParams['offset'] : 0;
        return $this->get(UserActivityService::class)->getByUser($_SESSION['id_client'], $this->currentRequestParams['user_id'], $offset, $limit);
    }
}
