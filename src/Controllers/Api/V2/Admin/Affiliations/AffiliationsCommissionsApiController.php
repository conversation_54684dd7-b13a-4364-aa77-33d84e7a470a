<?php

namespace Learnybox\Controllers\Api\V2\Admin\Affiliations;

use Learnybox\Controllers\Api\V2\Admin\AbstractApiController;
use Learnybox\Entity\Affiliation\Commission\AffiliationCommission;
use OpenApi\Annotations as OA;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class AffiliationsCommissionsApiController
 * @package Learnybox\Controllers\Api\Affiliations
 */
class AffiliationsCommissionsApiController extends AbstractApiController
{
    public function __construct()
    {
        parent::__construct();
        $this->serviceClassName = 'Api_Affiliation_Commissions';
        $this->resultsDataKey = 'commissions';
        $this->entityClass = AffiliationCommission::class;
    }

    /**
    * @OA\Get(
    *   path="/api/v2/commissions/",
    *   summary="Liste des commissions",
    *   tags={"Affiliations"},
    *   operationId="api_v2_commissions_list",
    *   @OA\Response(
    *     response=200,
    *     description="",
    *     @OA\JsonContent(
    *       ref="#/components/schemas/ApiResponse",
    *       example={
    "status": true,
    "data": {
        {
        "id_commission": 1,
        "type": "normal",
        "produit": "test produit",
        "montant": 89.21,
        "commission": 20.32,
        "etat": "valid",
        "date": "2019-02-20T16:37:40+0100"
        }
    },
    "message": "",
    "offset": 0,
    "limit": 100,
    "total": 1,
    "_links": {
        "self": {
       "href": "https://api.learnybox.com/api/v2/commissions/?limit=100&offset=0",
       "rel": "self"
        }
    }
    }
    *     )
    *   )
    * )
    *
    *
    */
    #[Route(path: '/api/v2/commissions/', name: 'api_v2_commissions_list', methods: ['GET'])]
    public function listV2()
    {
        return parent::listV2();
    }

    /**
    * @OA\Get(
    *   path="/api/v2/commissions/{id_comission}/",
    *   summary="Données d'une commission",
    *   tags={"Affiliations"},
    *   operationId="api_v2_commissions_show",
    *   @OA\Parameter(
    *     name="id_comission",
    *     description="ID de la commission",
    *     in="path",
    *     required=true,
    *     @OA\Schema(type="integer")
    *   ),
    *   @OA\Response(
    *     response=200,
    *     description="",
    *     @OA\JsonContent(
    *       ref="#/components/schemas/ApiResponse",
    *       example={
    "status": true,
    "data":
        {
        "id_commission": 1,
        "type": "normal",
        "produit": "test produit",
        "montant": 89.21,
        "commission": 20.32,
        "etat": "valid",
        "date": "2019-02-20T16:37:40+0100"
        },
    "message": ""
    }
    *     )
    *   )
    * )
    *
    *
    */
    #[Route(path: '/api/v2/commissions/{id_comission}/', name: 'api_v2_commissions_show', methods: ['GET'])]
    public function show()
    {
        return parent::showV2('id_comission');
    }

    /**
     * @OA\Post(
     *   path="/api/v2/commissions/",
     *   summary="Ajouter une commission",
     *   tags={"Affiliations"},
     *   operationId="api_v2_commissions_create",
     *   @OA\RequestBody(
     *     required=true,
     *     description="Champs id_campaign, id_affilie, montant et montant_commission obligatoires *",
     *     @OA\MediaType(
     *       mediaType="application/x-www-form-urlencoded;charset=UTF-8",
     *       @OA\Schema(
     *         @OA\Property(property="id_campaign", type="integer", description="Identifiant de la campagne"),
     *         @OA\Property(property="id_affilie", type="string", description="Identifiant de l'affilié"),
     *         @OA\Property(property="montant", type="string", description="Montant de la vente"),
     *         @OA\Property(property="montant_commission", type="string", description="Montant de la commission"),
     *         @OA\Property(property="id_transaction", type="string", description="Identifiant de la transaction"),
     *         @OA\Property(property="produit", type="string", description="Nom du produit"),
     *         @OA\Property(property="ip", type="string", description="Adresse IP du client"),
     *         @OA\Property(property="date", type="string", description="Date de la commission (au format YYYY-MM-DD HH:MM:SS)")
     *       )
     *     ),
     *   ),
     *   @OA\Response(
     *     response=200,
     *     description="",
     *     @OA\JsonContent(
     *       ref="#/components/schemas/ApiResponse"
     *     )
     *   ),
     *   x={
     *     "code-samples":{
     *       {
     *         "lang":"Form data",
     *         "source":"id_campaign:1&id_affilie:1&montant:99.99&montant_commission:30&id_transaction:TEST0001&produit:test+produit&date:2019-02-20+16:37:40"
     *       }
     *     }
     *   }
     * )
     *
     */
    #[Route(path: '/api/v2/commissions/', name: 'api_v2_commissions_create', methods: ['POST'])]
    public function create()
    {
        $this->serviceClassName = 'Api_Affiliation_Commission';
        return parent::create();

        // @TODO affilie repository no found row with id
        // version with doctrine entities which does not work yet
        /*
        $commission = new AffiliationCommission();

        try {
            $campaign = $this->get(EntityManager::class)->getRepository(AffiliationCampaign::class)->find($this->getParam('id_campaign', FILTER_VALIDATE_INT));

            $idAffilie = $this->getParam('id_affilie', FILTER_VALIDATE_INT);

            $affilie = $this->get(EntityManager::class)->getRepository(AffiliationAffilie::class)->find($idAffilie);
            $affilie2 = $this->get(EntityManager::class)->getRepository(AffiliationAffilie::class)->findOneBy(['idAffilie' => 12]);
            $transaction = $this->get(EntityManager::class)->getRepository(Transaction::class)->findOneBy(['idTrans' => $this->getParam('id_transaction')]);
        } catch (ORMException $e) {
            LoggerService::log(Logger::ERROR, $e->getMessage());
            throw new ApiException('', ApiException::BAD_PARAMETER);
        }

        $commission->setAffilie($affilie);
        $commission->setCampaign($campaign);
        $commission->setMontant($this->getParam('montant', FILTER_VALIDATE_FLOAT));
        $commission->setCommission($this->getParam('montant_commission', FILTER_VALIDATE_FLOAT));
        $commission->setTransaction($transaction);
        $commission->setProduit($this->getParam('produit'));
        $commission->setDate($this->getParam('date'));

        return parent::createV2($commission);
        */
    }
}
