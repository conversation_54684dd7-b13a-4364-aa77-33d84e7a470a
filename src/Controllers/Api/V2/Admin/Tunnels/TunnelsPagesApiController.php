<?php

namespace Learnybox\Controllers\Api\V2\Admin\Tunnels;

use Learnybox\Controllers\Api\V2\Admin\AbstractApiController;
use Learnybox\Entity\Tunnel\Page\TunnelPage;
use OpenApi\Annotations as OA;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class TunnelsPagesApiController
 * @package Learnybox\Controllers\Api\Tunnels
 */
class TunnelsPagesApiController extends AbstractApiController
{
    public function __construct()
    {
        parent::__construct();
        $this->entityClass = TunnelPage::class;
    }

    /**
    * @OA\Get(
    *   path="/api/v2/tunnels/{id_tunnel}/pages/",
    *   summary="Liste des pages d'un tunnel",
    *   tags={"Tunnels"},
    *   operationId="api_v2_tunnels_pages_list",
    *   @OA\Parameter(
    *     name="id_tunnel",
    *     description="Identifiant d'un tunnel",
    *     in="path",
    *     required=true,
    *     @OA\Schema(type="integer")
    *   ),
    *   @OA\Response(
    *     response=200,
    *     description="",
    *     @OA\JsonContent(
    *       ref="#/components/schemas/ApiResponse",
    *       example={
    "status": true,
    "data": {
        {
        "id_page": 1,
        "conf_type": "",
        "version": 1,
        "nom": "test capture 1",
        "permalink": "test-capture-1",
        "type": "optin",
        "seo_description": "",
        "seo_tags": "",
        "seo_image": "",
        "additionnal_css": "",
        "additionnal_js": "",
        "additionnal_js_head": "",
        "permission": 0,
        "display_header": false,
        "display_footer": false,
        "position": 1,
        "publication": "encours",
        "design": "{'bgcolor1':'','bgcolor2':'','gradient_angle':'0','colors':['#4a90e2','#6d76b5','#575e66'],'color_gray':'#a5a5a5','fonts':['Lato'],'font_sizes':{'h1':{'font_size':'72','font_color':'#575e66','font_family':'default'},'h2':{'font_size':'36','font_color':'#575e66','font_family':'default'},'h3':{'font_size':'28','font_color':'#575e66','font_family':'default'},'h4':{'font_size':'22','font_color':'#575e66','font_family':'default'},'h5':{'font_size':'16','font_color':'#575e66','font_family':'default'},'h6':{'font_size':'14','font_color':'#575e66','font_family':'default'},'p':{'font_size':'14','font_color':'#575e66','font_family':'default'},'small':{'font_size':'12','font_color':'#575e66','font_family':'default'}},'line_themes':{'1':{'bgcolor1':'#fafafa','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'2':{'bgcolor1':'#f2f2f2','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'3':{'bgcolor1':'#989eb3','bgcolor2':'#8f9fb3','angle':'180','opacity':'','opacity2':''},'4':{'bgcolor1':'#2b2d33','bgcolor2':'#292e33','angle':'180','opacity':'0.75','opacity2':'0.75'}},'btn':{'bg_color1':'#4a90e2','bg_color2':'','color_text':'#ffffff','bg_color1_white':'#ffffff','color_text_white':'#4a90e2','border_radius':'0'},'temoignages':{'color_icone':'#4a90e2','color_hr':'#4a90e2','color_txt':'#575e66','color_user':'#575e66','color_user_title':'#575e66'}}",
        "animations": "",
        "fonts": "",
        "config": "",
        "theme": "capture-grande-image",
        "editor_level": "expert",
        "vues": 0,
        "thumbnail": "",
        "facebook_pixel_event": "0",
        "date_creation": "2019-04-25T11:33:57+0200"
        }
    },
    "message": "",
    "offset": 0,
    "limit": 100,
    "total": 1,
    "_links": {
        "self": {
       "href": "https://api.learnybox.com/api/v2/tunnels/2100/pages/?limit=100&offset=0",
       "rel": "self"
        }
    }
    }
    *     )
    *   )
    * )
    *
    */
    #[Route(path: '/api/v2/tunnels/{id_tunnel}/pages/', name: 'api_v2_tunnels_pages_list', methods: ['GET'])]
    public function listV2()
    {
        $this->querySorts = ['ligne' => 'ASC', 'colonne' => 'ASC', 'position' => 'ASC'];
        $this->queryFilters['tunnel'] = $this->getParam('id_tunnel', FILTER_VALIDATE_INT);
        return parent::listV2();
    }

    /**
    * @OA\Get(
    *   path="/api/v2/tunnels/{id_tunnel}/pages/{id_page}/",
    *   summary="Données d'une page d'un tunnel",
    *   tags={"Tunnels"},
    *   operationId="api_v2_tunnels_pages_show",
    *   @OA\Parameter(
    *     name="id_tunnel",
    *     description="Identifiant d'un tunnel",
    *     in="path",
    *     required=true,
    *     @OA\Schema(type="integer")
    *   ),
    *   @OA\Parameter(
    *     name="id_page",
    *     description="Identifiant d'une page",
    *     in="path",
    *     required=true,
    *     @OA\Schema(type="integer")
    *   ),
    *   @OA\Response(
    *     response=200,
    *     description="",
    *     @OA\JsonContent(
    *       ref="#/components/schemas/ApiResponse",
    *       example={
    "status": true,
    "data": {
        "id_page": 1,
        "conf_type": "",
        "version": 1,
        "nom": "test capture 1",
        "permalink": "test-capture-1",
        "type": "optin",
        "seo_description": "",
        "seo_tags": "",
        "seo_image": "",
        "additionnal_css": "",
        "additionnal_js": "",
        "additionnal_js_head": "",
        "permission": 0,
        "display_header": false,
        "display_footer": false,
        "position": 1,
        "publication": "encours",
        "design": "{'bgcolor1':'','bgcolor2':'','gradient_angle':'0','colors':['#4a90e2','#6d76b5','#575e66'],'color_gray':'#a5a5a5','fonts':['Lato'],'font_sizes':{'h1':{'font_size':'72','font_color':'#575e66','font_family':'default'},'h2':{'font_size':'36','font_color':'#575e66','font_family':'default'},'h3':{'font_size':'28','font_color':'#575e66','font_family':'default'},'h4':{'font_size':'22','font_color':'#575e66','font_family':'default'},'h5':{'font_size':'16','font_color':'#575e66','font_family':'default'},'h6':{'font_size':'14','font_color':'#575e66','font_family':'default'},'p':{'font_size':'14','font_color':'#575e66','font_family':'default'},'small':{'font_size':'12','font_color':'#575e66','font_family':'default'}},'line_themes':{'1':{'bgcolor1':'#fafafa','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'2':{'bgcolor1':'#f2f2f2','bgcolor2':'','angle':'0','opacity':'','opacity2':''},'3':{'bgcolor1':'#989eb3','bgcolor2':'#8f9fb3','angle':'180','opacity':'','opacity2':''},'4':{'bgcolor1':'#2b2d33','bgcolor2':'#292e33','angle':'180','opacity':'0.75','opacity2':'0.75'}},'btn':{'bg_color1':'#4a90e2','bg_color2':'','color_text':'#ffffff','bg_color1_white':'#ffffff','color_text_white':'#4a90e2','border_radius':'0'},'temoignages':{'color_icone':'#4a90e2','color_hr':'#4a90e2','color_txt':'#575e66','color_user':'#575e66','color_user_title':'#575e66'}}",
        "animations": "",
        "fonts": "",
        "config": "",
        "theme": "capture-grande-image",
        "editor_level": "expert",
        "vues": 0,
        "thumbnail": "",
        "facebook_pixel_event": "0",
        "date_creation": "2019-04-25T11:33:57+0200"
    },
    "message": ""
    }
    *     )
    *   )
    * )
    *
    */
    #[Route(path: '/api/v2/tunnels/{id_tunnel}/pages/{id_page}/', name: 'api_v2_tunnels_pages_show', methods: ['GET'])]
    public function show()
    {
        return parent::showV2('id_page');
    }

    /**
     * @OA\Post(
     *   path="/api/v2/tunnels/{id_tunnel}/pages/",
     *   summary="Ajouter une page à un tunnel",
     *   tags={"Tunnels"},
     *   operationId="api_v2_tunnels_pages_create",
     *   @OA\Parameter(
     *     name="id_tunnel",
     *     description="Identifiant d'un tunnel",
     *     in="path",
     *     required=true,
     *     @OA\Schema(type="integer")
     *   ),
     *   @OA\RequestBody(
     *     required=true,
     *     description="",
     *     @OA\MediaType(
     *       mediaType="application/x-www-form-urlencoded;charset=UTF-8",
     *       @OA\Schema(
     *         @OA\Property(property="id_tunnel", type="integer", description="Identifiant d'un tunnel"),
     *         @OA\Property(property="nom", type="string", description=""),
     *         @OA\Property(property="type", type="string", description=""),
     *         @OA\Property(property="position", type="integer", description=""),
     *         @OA\Property(property="permalink", type="string", description=""),
     *         @OA\Property(property="theme", type="string", description=""),
     *         @OA\Property(property="editor_level", type="string", description=""),
     *         @OA\Property(property="id_conference", type="integer", description=""),
     *         @OA\Property(property="conf_type", type="string", description=""),
     *         @OA\Property(property="id_webinaire", type="integer", description=""),
     *         @OA\Property(property="id_master_theme", type="integer", description=""),
     *         @OA\Property(property="id_theme", type="integer", description=""),
     *         @OA\Property(property="google_analytics", type="string", description="")
     *       )
     *     )
     *   ),
     *   @OA\Response(
     *     response=200,
     *     description="",
     *     @OA\JsonContent(
     *       ref="#/components/schemas/ApiResponse"
     *     )
     *   )
     * )
     *
     */
    #[Route(path: '/api/v2/tunnels/{id_tunnel}/pages/', name: 'api_v2_tunnels_pages_create', methods: ['POST'])]
    public function create()
    {
        // @TODO : to refact
        return $this->get(\TunnelsPages::class)->insert_page($this->currentRequestParams);
    }

    /**
     * @OA\Patch(
     *   path="/api/v2/tunnels/pages/{id_page}/",
     *   summary="Mise à jour d'une page d'un tunnel",
     *   tags={"Tunnels"},
     *   operationId="api_v2_tunnels_pages_edit",
     *   @OA\Parameter(
     *     name="id_page",
     *     description="Identifiant d'une page",
     *     in="path",
     *     required=true,
     *     @OA\Schema(type="integer")
     *   ),
     *   @OA\RequestBody(
     *     required=true,
     *     description="",
     *     @OA\MediaType(
     *       mediaType="application/x-www-form-urlencoded;charset=UTF-8",
     *       @OA\Schema(
     *         @OA\Property(property="id_page", type="integer", description="Identifiant d'un tunnel"),
     *         @OA\Property(property="nom", type="string", description=""),
     *         @OA\Property(property="type", type="string", description=""),
     *         @OA\Property(property="publication", type="string", description=""),
     *         @OA\Property(property="permalink", type="string", description=""),
     *         @OA\Property(property="permission", type="boolean", description=""),
     *         @OA\Property(property="disable_indexation", type="boolean", description=""),
     *         @OA\Property(property="seo_description", type="string", description=""),
     *         @OA\Property(property="seo_tags", type="string", description=""),
     *         @OA\Property(property="seo_image", type="string", description=""),
     *         @OA\Property(property="editor_level", type="string", description=""),
     *         @OA\Property(property="facebook_pixel", type="boolean", description=""),
     *         @OA\Property(property="facebook_pixel_id", type="integer", description=""),
     *         @OA\Property(property="facebook_pixel_event", type="string", description=""),
     *         @OA\Property(property="google_analytics", type="boolean", description=""),
     *         @OA\Property(property="google_analytics_id", type="integer", description=""),
     *         @OA\Property(property="google_optimize_id", type="string", description=""),
     *         @OA\Property(property="ligne", type="integer", description=""),
     *         @OA\Property(property="colonne", type="integer", description=""),
     *       )
     *     )
     *   ),
     *   @OA\Response(
     *     response=200,
     *     description="",
     *     @OA\JsonContent(
     *       ref="#/components/schemas/ApiResponse"
     *     )
     *   )
     * )
     *
     */
    #[Route(path: '/api/v2/tunnels/pages/{id_page}/', name: 'api_v2_tunnels_pages_edit', methods: ['PATCH'])]
    public function edit()
    {
        // @TODO : to refact
        return $this->get(\TunnelsPages::class)->update_page($this->currentRequestParams);
    }

    /**
     * @OA\Delete(
     *   path="/api/v2/tunnels/pages/{id_page}/",
     *   summary="Suppression d'une page",
     *   tags={"Tunnels"},
     *   operationId="api_v2_tunnels_pages_delete",
     *   @OA\Parameter(
     *     name="id_page",
     *     description="Identifiant d'une page",
     *     in="path",
     *     required=true,
     *     @OA\Schema(type="integer")
     *   ),
     *   @OA\Response(
     *     response=200,
     *     description="",
     *     @OA\JsonContent(
     *       ref="#/components/schemas/ApiResponse"
     *     )
     *   )
     * )
     *
     */
    #[Route(path: '/api/v2/tunnels/pages/{id_page}/', name: 'api_v2_tunnels_pages_delete', methods: ['DELETE'])]
    public function delete()
    {
        // @TODO : to refact
        return $this->get(\TunnelsPages::class)->delete_page($this->currentRequestParams['id_page']);
    }
}
