<?php

namespace Learnybox\Controllers\Ajax\Support;

use Learnybox\Controllers\Ajax\AbstractAjaxController;
use Learnybox\Forms\Support\SupportNoteForm;
use Learnybox\Helpers\Assets;
use Learnybox\Repositories\Support\SupportDepartmentRepository;
use Learnybox\Repositories\Support\SupportReplyRepository;
use Learnybox\Repositories\Support\SupportServiceRepository;
use Learnybox\Repositories\Support\SupportTicketRepository;
use Learnybox\Services\RightsService;
use Learnybox\Services\Support\SupportNoteService;
use Learnybox\Services\Support\SupportServiceService;
use Learnybox\Services\Support\SupportTicketService;
use Learnybox\Services\Users\UsersService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class SupportTicketAjaxController
 * @package Learnybox\Controllers\Ajax
 */
class SupportTicketAjaxController extends AbstractAjaxController
{
    #[Route(path: '/ajax/support/afficher-ticket/', name: 'ajax_support_afficher_ticket')]
    public function afficherTicket()
    {
        $ticketid = filter_input(INPUT_POST, 'ticketid', FILTER_SANITIZE_SPECIAL_CHARS);

        //initialisation
        $output = '';
        $ticketHeader = '';
        $ticketActions = '';
        $attachments = '';

        //récupération du ticket
        $ticket = $this->get(SupportTicketRepository::class)->getTicketById($ticketid);
        if (!$ticket) {
            $this->displayOutput([
                'status' => false,
                'message' => __('Ce ticket n\'existe pas.')
            ]);
        }

        //récupération du service
        $service = $this->get(SupportServiceRepository::class)->getServiceById($ticket['id_service']);
        if (!$service) {
            $this->displayOutput([
                'status' => false,
                'message' => __('Ce ticket n\'appartient à aucun service.')
            ]);
        }

        $strDepartement = '';
        $departement = $this->get(SupportDepartmentRepository::class)->getDepartementById($ticket['id_departement']);
        if ($departement) {
            $strDepartement .= '<span class="label label-small label-default">' . $departement['name'] . '</span>';
        }

        //affichage
        $ticketHeader .= '
			<div class="media">
				<img class="media-object img-circle pull-left m-r-10 p-0" src="' . $this->get(UsersService::class)->getGuestAvatar($ticket['email'], 64, $ticket['fname'], $ticket['lname']) . '" border="0" width="48" height="48" alt="' . $ticket['lname'] . ' ' . $ticket['fname'] . '" />
				<div class="media-body">
					<div class="pull-right" style="text-align: right">
						' . $this->get(SupportServiceService::class)->strService($ticket['id_service']) . '<br>
						' . $strDepartement . '
					</div>
					<span class="ticket-header">
					    <a href="' . \Tools::makeLink('app', 'support', 'ticket/' . $ticketid) . '">' . $ticket['subject'] . '</a><br/>
						<i class="fa fa-user"></i> ' . $ticket['lname'] . ' ' . $ticket['fname'] . '<br>
						<i class="fa fa-clock-o"></i> ' . datetimefr($ticket['date_creation']) . '
					</span>
				</div>

			</div>';

        if ($ticket['flag'] == 'important') {
            $ticketHeader .= '<div class="alert alert-warning" style="margin-top:10px"><em>' . __('Ce ticket est considéré comme') . ' <strong>' . __('important') . '</strong></em></div>';
        } elseif ($ticket['flag'] == 'spam') {
            $ticketHeader .= '<div class="alert alert-danger" style="margin-top:10px"><em>' . __('Ce ticket est considéré comme') . ' <strong>' . __('spam') . '</strong></em></div>';
        }

        $ticketHeader .= '<hr>';

        //actions
        $ticketActions .= '
				<div class="ticket_action">';

        $ticketActions .= '<div id="TicketStatus' . $ticketid . '" class="text-right" style="margin-left:5px; margin-right:5px">
            <a class="btn btn-sm btn-danger btn-sm" onclick="DeleteTicket(\'' . $ticketid . '\')"><i class="fa fa-trash-o"></i> ' . __('Supprimer') . '</a> ';

        if ($ticket['status'] == 'open') {
            $ticketActions .= '<a class="btn btn-sm btn-default btn-sm" onclick="SetTicket(\'' . $ticketid . '\', \'close\')"><i class="fa fa-folder"></i> ' . __('Fermer le ticket') . '</a>';
        } else {
            $ticketActions .= '<a class="btn btn-sm btn-default btn-sm" onclick="SetTicket(\'' . $ticketid . '\', \'open\')"><i class="fa fa-folder-open"></i> ' . __('Réouvrir le ticket') . '</a>';
        }

        $ticketActions .= '
    					<a class="btn btn-sm btn-primary btn-sm" href="' . \Tools::makeLink('app', 'support', 'ticket/' . $ticketid) . '"><i class="fa fa-reply"></i> ' . __('Répondre') . '</a>
                    </div>
					<div style="clear:both"></div>
				</div>';

        $ticketActionsHeader = '';

        //contenu du ticket
        $strAttachments = '';
        if ($ticket['attachments']) {
            $attachments = json_decode($ticket['attachments']);
            foreach ($attachments as $attachment) {
                $strAttachments .= '<i class="fa fa-paperclip"></i> <a target="_blank" href="' . $attachment . '">' . basename($attachment) . '</a><br>';
            }
        }

        $ticket_content = '
		    <h4>' . __('Message') . '</h4>
		    <div class="ticket-content">
		        ' . stripslashes($ticket['message']) . '
		        ' . $strAttachments . '
		    </div>';


        //dernière réponse
        $ticketReponse = '';
        $lastReponse = $this->get(SupportReplyRepository::class)->getLastTicketReponseById($ticketid);
        if ($lastReponse) {
            $strAttachments = '';
            if ($lastReponse['attachments']) {
                $attachments = json_decode($lastReponse['attachments']);
                foreach ($attachments as $attachment) {
                    $strAttachments .= '<i class="fa fa-file-text-o"></i> <a target="_blank" href="' . $attachment . '">' . basename($attachment) . '</a><br>';
                }
            }

            $ticketReponse .= '
			    <hr>
			    <h4>' . __('Dernière réponse') . '</h4>
                <div class="ticket-content alert">
                    <div class="media">';

            //utilisateur
            if ($lastReponse['admin_id'] == 0) {
                $ticketReponse .= '
					<img class="media-object pull-left img-circle p-0 m-r-10" src="' . $this->get(UsersService::class)->getGuestAvatar($ticket['email'], 48, $ticket['fname'], $ticket['lname']) . '" border="0" width="48" height="48" alt="' . $ticket['lname'] . ' ' . $ticket['fname'] . '" />
					<div class="media-body">
						<i class="fa fa-user"></i>&nbsp;' . $ticket['lname'] . ' ' . $ticket['fname'] . '
						&nbsp;(<a href="mailto:' . $ticket['email'] . '">' . $ticket['email'] . '</a>)<br>
						<i class="fa fa-clock-o"></i>&nbsp;' . datetimefr($lastReponse['date_reponse']) . '<br>';
            } else {
                if ($lastReponse['user_id']) {
                    $user = $this->get(\Utilisateurs::class)->getUserById($lastReponse['user_id']);
                } else {
                    $user = $this->get(\Utilisateurs::class)->getUserById($lastReponse['admin_id']);
                }

                if ($user) {
                    $ticketReponse .= '<img class="media-object pull-left img-circle p-0 m-r-10" src="' . $this->get(UsersService::class)->getAvatar($user['email'], 64) . '" border="0" width="48" height="48" />';

                    $userRoleName = __('Utilisateur');
                    if (RightsService::isEditor($user['user_id'])) {
                        $userRoleName = __('Administrateur');
                    }

                    $ticketReponse .= '
						<div class="media-body">
							<i class="fa fa-user"></i>&nbsp;' . $user['lname'] . ' ' . $user['fname'] . '
							&nbsp;<span class="label label-small label-info">' . $userRoleName . '</span><br>
							<i class="fa fa-clock-o"></i>&nbsp;' . datetimefr($lastReponse['date_reponse']);
                } else {
                    $ticketReponse .= '
						<img class="media-object pull-left img-circle p-0 m-r-10" src="' . $this->get(UsersService::class)->getGuestAvatar(null) . '" border="0" width="48" height="48" alt="' . __('Utilisateur inconnu') . '" />
						<div class="media-body">
							<span class="textl-danger"><i class="fa fa-warning"></i> ' . __('Utilisateur inconnu') . '</span><br>
							<i class="fa fa-clock-o"></i>&nbsp;' . datetimefr($lastReponse['date_reponse']);
                }
            }

            $ticketReponse .= '
							<hr>
							' . trim(stripslashes($lastReponse['message'])) . '
							' . $strAttachments . '
						</div>
					</div>
				</div>';
        }

        //contenu
        $output .=
            $ticketActionsHeader
            . $ticketHeader
            . $ticket_content
            . $ticketReponse
            . $ticketActions;

        if (!empty($output)) {
            $this->displayOutput([
                'status' => true,
                'content' => $output
            ]);
        } else {
            $this->displayOutput([
                'status' => true,
                'message' => __('Aucune donnée reçue')
            ]);
        }
    }

    #[Route(path: '/ajax/support/delete-ticket/', name: 'ajax_support_delete_ticket')]
    public function deleteTicket()
    {
        $ticketid = filter_input(INPUT_POST, 'ticketid', FILTER_SANITIZE_SPECIAL_CHARS);

        $delete = $this->get(SupportTicketService::class)->deleteTicket($ticketid);
        if (!$delete['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => $delete['message']
            ]);
        }

        $this->displayOutput([
            'status' => true
        ]);
    }

    #[Route(path: '/ajax/support/change-state-ticket/', name: 'ajax_support_change_state_ticket')]
    public function changeStateTicket()
    {
        $ticketid = filter_input(INPUT_POST, 'ticketid', FILTER_SANITIZE_SPECIAL_CHARS);

        $state = filter_input(INPUT_POST, 'state', FILTER_SANITIZE_SPECIAL_CHARS);

        $changeState = $this->get(SupportTicketService::class)->setTicketState($ticketid, $state);
        if (!$changeState['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => $changeState['message']
            ]);
        }

        $this->displayOutput([
            'status' => true
        ]);
    }

    #[Route(path: '/ajax/support/save-note/', name: 'ajax_support_save_note')]
    public function saveNote()
    {
        $ticketid = filter_input(INPUT_POST, 'ticketid', FILTER_SANITIZE_SPECIAL_CHARS);
        $content = filter_input(INPUT_POST, 'content', FILTER_UNSAFE_RAW);
        $content = nl2br($content);

        $cleanedPost = [
            'ticketid' => $ticketid,
            'content' => $content,
        ];

        $validateNote = $this->get(SupportNoteForm::class)->validatepostNoteAdd($cleanedPost);
        if (!$validateNote['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => $validateNote['message']
            ]);
        }

        $insertNote = $this->get(SupportNoteService::class)->insertNote($cleanedPost);
        if (!$insertNote['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => $insertNote['message']
            ]);
        }

        $cleanedPost['id_note'] = $insertNote['id_note'];
        $cleanedPost['user_id'] = $_SESSION['user_id'];
        $cleanedPost['date'] = date('Y-m-d H:i:s');
        $afficherNote = $this->get(SupportNoteService::class)->displayNote($ticketid, $cleanedPost);

        $this->displayOutput([
            'status' => true,
            'note' => $afficherNote
        ]);
    }

    #[Route(path: '/ajax/support/delete-note/', name: 'ajax_support_delete_note')]
    public function deleteNote()
    {
        $ticketid = filter_input(INPUT_POST, 'ticketid', FILTER_SANITIZE_SPECIAL_CHARS);
        $idNote = filter_input(INPUT_POST, 'id_note', FILTER_SANITIZE_SPECIAL_CHARS);

        if (!$ticketid or !$idNote) {
            $this->displayOutput([
                'status' => false,
                'message' => __('Paramètres manquants')
            ]);
        }

        $deleteNote = $this->get(SupportNoteService::class)->deleteNote($ticketid, $idNote);
        if (!$deleteNote['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => $deleteNote['message']
            ]);
        }

        $this->displayOutput([
            'status' => true
        ]);
    }

    #[Route(path: '/ajax/support/get-last-reponse/', name: 'ajax_support_get_last_reponse')]
    public function getLastReponse()
    {
        $ticketid = filter_input(INPUT_POST, 'ticketid', FILTER_SANITIZE_SPECIAL_CHARS);

        $lastReponse = $this->get(SupportReplyRepository::class)->getLastTicketReponseById($ticketid);
        if ($lastReponse and $lastReponse['date_reponse'] > date('Y-m-d H:i:s', strtotime('-5 minutes')) and $lastReponse['admin_id'] and $lastReponse['admin_id'] != $_SESSION['user_id']) {
            $message = __('Une réponse a été ajoutée à ce ticket par un autre administrateur il y a moins de 5 minutes.') . '<br>';

            $user = $this->get(\Utilisateurs::class)->getUserById($lastReponse['admin_id']);
            if ($user) {
                $message .= '<strong>' . __('Administrateur') . '</strong> : ' . $user['lname'] . ' ' . $user['fname'] . '<br>';
            }

            $message .= '<strong>' . __('Date') . '</strong> : ' . datetimefr($lastReponse['date_reponse']) . '<br>';
            $message .= '<strong>' . __('Réponse') . '</strong> : <br>' . trim(stripslashes($lastReponse['message']));
            $message .= '<hr>';
            $message .= '<div style="text-align:center"><a class="btn btn-secondary" data-dismiss="modal" style="margin-right:10px">' . __('Annuler et revenir au ticket') . '</a> <a class="btn btn-primary" onclick="$(\'#FormReponse\').submit();">' . __('Valider la réponse') . '</a></div>';

            $this->displayOutput([
                'status' => false,
                'message' => $message
            ]);
        }
        $this->displayOutput([
            'status' => true
        ]);
    }

    #[Route(path: '/ajax/support/recherche/', name: 'ajax_support_recherche')]
    public function recherche()
    {
        $recherche = filter_input(INPUT_POST, 'recherche', FILTER_SANITIZE_SPECIAL_CHARS);

        $afficherDepartementTickets = $this->get(SupportTicketService::class)->getRechercheTickets($recherche);

        if (!$afficherDepartementTickets) {
            $this->displayOutput([
                'status' => true,
                'content' => '<div class="alert alert-info">' . __('Aucun résultat') . '</div>'
            ]);
        } else {
            $this->displayOutput([
                'status' => true,
                'content' => $afficherDepartementTickets
            ]);
        }
    }

    #[Route(path: '/ajax/support/verif-email/', name: 'ajax_support_verif_email')]
    public function verifEmail()
    {
        $email = filter_input(INPUT_POST, 'email', FILTER_VALIDATE_EMAIL);
        if ($email == false) {
            $this->displayOutput([
                'status' => 'invalidemail',
            ]);
        } else {
            $verifUser = $this->get(SupportTicketRepository::class)->getLastTicketByUserEmail($email);
            if ($verifUser) {
                $this->displayOutput([
                    'status' => 'userexists',
                ]);
            } else {
                //verification dans les utilisateurs
                if (isset($_SESSION['id_client']) and $_SESSION['id_client'] == 0) {
                    $verifUser = $this->get(\Utilisateurs::class)->adminGetUserByEmail($email);
                } else {
                    $verifUser = $this->get(\Utilisateurs::class)->getUserByEmail($email);
                }

                if ($verifUser) {
                    $this->displayOutput([
                        'status' => 'userexists',
                    ]);
                } else {
                    $this->displayOutput([
                        'status' => 'usernotexists',
                    ]);
                }
            }
        }
    }
}
