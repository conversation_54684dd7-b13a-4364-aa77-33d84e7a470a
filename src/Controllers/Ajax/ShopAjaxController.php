<?php

namespace Learnybox\Controllers\Ajax;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Client\Client;
use Learnybox\Entity\Shop\Renseignement\ShopRenseignement;
use Learnybox\Enums\Transaction\ShopQuestionTypeEnum;
use Learnybox\Forms\Shop\ShopRenseignementForm;
use Learnybox\Forms\Shop\ShopRenseignementReponseForm;
use Learnybox\Renderers\Shop\ShopRenseignementRenderer;
use Learnybox\Services\Shop\Formulaire\ShopFormulaireService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ShopAjaxController
 * @package Learnybox\Controllers\Ajax
 */
class ShopAjaxController extends AbstractAjaxController
{
    #[Route(path: '/ajax/shop_products/', name: 'ajax_shop_products')]
    public function updateProductsPositions()
    {
        $idFormulaire = filter_input(INPUT_POST, 'id_formulaire', FILTER_VALIDATE_INT);
        if (!$idFormulaire) {
            $this->displayOutput([
                'status' => false,
                'message' => __('Aucun formulaire sélectionné.')
            ]);
            return;
        }

        $menu = filter_input(INPUT_POST, 'menu', FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
        if (!$menu) {
            $this->displayOutput([
                'status' => false,
                'message' => __('Les produits n\'ont pas été reçu.')
            ]);
            return;
        }

        $menu = array_filter($menu);

        $updateProducts = $this->get(\Shop_Produits::class)->updateProductsPositions($idFormulaire, $menu);
        if (!$updateProducts['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => $updateProducts['message']
            ]);
            return;
        }

        $this->displayOutput([
            'status' => true,
        ]);
    }

    #[Route(path: '/ajax/shop_payments_position/', name: 'ajax_shop_payments_position')]
    public function updatePaymentsPositions()
    {
        $idFormulaire = filter_input(INPUT_POST, 'id_formulaire', FILTER_VALIDATE_INT);
        if (!$idFormulaire) {
            $this->displayOutput([
                'status' => false,
                'message' => __('Aucun formulaire sélectionné.')
            ]);
            return;
        }

        $menu = filter_input(INPUT_POST, 'menu', FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
        if (!$menu) {
            $this->displayOutput([
                'status' => false,
                'message' => __('Les produits n\'ont pas été reçu.')
            ]);
            return;
        }

        $menu = array_filter($menu);

        $updatePayments = $this->get(\Shop_Paiements::class)->updatePaymentsPositions($idFormulaire, $menu);
        if (!$updatePayments['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => $updatePayments['message']
            ]);
            return;
        }

        $this->displayOutput(['status' => true]);
    }

    #[Route(path: '/ajax/shop_check_quantity/', name: 'ajax_shop_check_quantity')]
    public function checkQuantity()
    {
        $idFormulaire = filter_input(INPUT_POST, 'id_form', FILTER_VALIDATE_INT);
        if (!$idFormulaire) {
            $this->displayOutput([
                'status' => false,
                'message' => __('Aucun formulaire sélectionné.')
            ]);
        }

        $this->displayOutput([
            'status' => true,
            'display_continue_button' => $this->get(ShopFormulaireService::class)->checkQuantityByForm($idFormulaire)
        ]);
    }

    #[Route(path: '/ajax/shop_generate_default_customer_uniqid/', name: 'ajax_shop_generate_default_customer_uniqid')]
    public function generateDefaultCustomerUniqid()
    {
        $nom = filter_input(INPUT_POST, 'nom', FILTER_SANITIZE_SPECIAL_CHARS);
        $prenom = filter_input(INPUT_POST, 'prenom', FILTER_SANITIZE_SPECIAL_CHARS);
        if (!$nom || !$prenom) {
            $this->displayOutput([
                'status' => false,
                'uniqid' => ''
            ]);
        }

        $uniqId = null;
        $currentUniqId = $nom . '-' . $prenom;
        while (!$uniqId) {
            $client = $this->get(EntityManager::class)->getRepository(Client::class)->searchByUniqId($currentUniqId);
            if (null === $client) {
                $uniqId = $currentUniqId;
            } else {
                if (substr($currentUniqId, -1) != '-') {
                    $currentUniqId .= '-';
                }
                $currentUniqId .= rand(1, 9);
            }
        }

        $this->displayOutput([
            'status' => true,
            'uniqid' => $uniqId
        ]);
    }

    #[Route(path: '/ajax/shop_question_form/', name: 'ajax_shop_question_form')]
    public function shopQuestionForm()
    {
        $idFormulaire = filter_input(INPUT_POST, 'idFormulaire', FILTER_VALIDATE_INT);

        $shopRenseignement = null;
        if (isset($_POST['idQuestion'])) {
            $idQuestion = filter_input(INPUT_POST, 'idQuestion', FILTER_VALIDATE_INT);
            if ($idQuestion) {
                $shopRenseignement = $this->get(EntityManager::class)->getRepository(ShopRenseignement::class)->find($idQuestion);
                if (!$shopRenseignement) {
                    $this->displayOutput(['status' => false, 'message' => __('La question n\'existe pas.')]);
                }
            }
        }

        $this->displayOutput([
            'status' => true,
            'content' => $this->get(ShopRenseignementRenderer::class)->renderShopRenseignementModal($shopRenseignement, $idFormulaire)
        ]);
    }

    #[Route(path: '/ajax/shop_questions/', name: 'ajax_shop_questioms')]
    public function shopQuestions()
    {
        $cleanedPost = [];
        if (isset($_POST['data'])) {
            $_POST['data'] = urldecode($_POST['data']);
            parse_str($_POST['data'], $postarray);
            $cleanedPost = clean_post($postarray);
        }

        $validPost = $this->get(\Shop_Champs::class)->validatepost_renseignement_personnalise_add($cleanedPost);
        if (!$validPost['valid']) {
            $arrayErrors = [];
            foreach ($validPost['message'] as $id => $error) {
                $arrayErrors[] = $error;
            }
            $this->displayOutput(['status' => false, 'errors' => $arrayErrors]);
        }

        if (isset($cleanedPost['obligatoire']) and $cleanedPost['obligatoire'] == 'on') {
            $cleanedPost['obligatoire'] = 1;
        } else {
            $cleanedPost['obligatoire'] = 0;
        }

        if (isset($cleanedPost['id_renseignement'])) {
            $saveRenseignement = $this->get(ShopRenseignementForm::class)->update($cleanedPost);
        } else {
            $saveRenseignement = $this->get(ShopRenseignementForm::class)->create($cleanedPost);
        }

        if ($saveRenseignement['valid'] !== true) {
            $this->displayOutput(['status' => false, 'message' => $saveRenseignement['message']]);
        }

        $shopRenseignement = $this->get(EntityManager::class)->getRepository(ShopRenseignement::class)->find($saveRenseignement['idRens']);
        if (!$shopRenseignement) {
            $this->displayOutput(['status' => false, 'message' => __('Cette question n\'existe pas')]);
        }

        $shopRenseignementResponses = $shopRenseignement->getReponses();
        if (isset($cleanedPost['reponses'])) {
            $reponses = filter_var($cleanedPost['reponses'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
            if ($reponses) {
                foreach ($reponses as $id => $reponse) {
                    if (isset($shopRenseignementResponses[$id])) {
                        if ($reponse && $shopRenseignement->getType() !== ShopQuestionTypeEnum::TEXT) {
                            $shopRenseignementResponses[$id]->setReponse($reponse);
                            $this->get(ShopRenseignementReponseForm::class)->save($shopRenseignementResponses[$id]);
                        } else {
                            $this->get(ShopRenseignementReponseForm::class)->delete($shopRenseignementResponses[$id]);
                        }
                    } else {
                        if ($reponse) {
                            $createRenseignementReponse = $this->get(ShopRenseignementReponseForm::class)->create(['id_rens' => $shopRenseignement->getIdRens(), 'reponse' => $reponse]);
                            if ($createRenseignementReponse['valid'] !== true) {
                                $this->displayOutput(['status' => false, 'message' => $createRenseignementReponse['message']]);
                            }
                        }
                    }
                }
            }
        }

        $this->displayOutput([
            'status' => true,
            'content' => $this->get(ShopRenseignementRenderer::class)->renderShopRenseignement($shopRenseignement, $cleanedPost['id_formulaire']),
            'idRenseignement' => $shopRenseignement->getIdRens()
        ]);
    }

    #[Route(path: '/ajax/shop_question/delete/', name: 'ajax_shop_question_delete')]
    public function shopQuestionDelete()
    {
        $idQuestion = filter_input(INPUT_POST, 'idQuestion', FILTER_VALIDATE_INT);
        if (!$idQuestion) {
            $this->displayOutput(['status' => false, 'message' => __('Aucune question.')]);
        }

        $shopRenseignement = $this->get(EntityManager::class)->getRepository(ShopRenseignement::class)->find($idQuestion);
        if (!$shopRenseignement) {
            $this->displayOutput(['status' => false, 'message' => __('La question n\'existe pas.')]);
        }

        $deleteQuestion = $this->get(ShopRenseignementForm::class)->delete($shopRenseignement);
        if (!$deleteQuestion) {
            $this->displayOutput(['status' => false, 'message' => $deleteQuestion['message']]);
        }

        $this->displayOutput(['status' => true]);
    }
}
