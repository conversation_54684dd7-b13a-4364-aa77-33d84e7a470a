<?php

namespace Learnybox\Controllers\Ajax\App\Conference;

use Symfony\Component\Routing\Attribute\Route;
use Doctrine\ORM\EntityManager;
use Learnybox\Controllers\Ajax\AbstractAjaxController;
use Learnybox\Entity\Conference\Conference;
use Learnybox\Entity\Conference\Offre\ConferenceOffre;
use Learnybox\Enums\HttpResponseCodeEnum;
use Learnybox\Exceptions\Ajax\AjaxException;
use Learnybox\Renderers\Conferences\Offres\ConferenceOffreRenderer;

class ConferenceOffreAjaxController extends AbstractAjaxController
{
    #[Route(path: '/ajax/app/conference/{conferenceId}/offre/list/', name: 'ajax_app_conference_offre_liste', requirements: ['conferenceId' => '\d+'])]
    public function list(): void
    {
        $conferenceId = $this->getRequestAttributes()->getInt('conferenceId');
        $conference = $this->get(EntityManager::class)->getRepository(Conference::class)->find($conferenceId);

        if (!$conference) {
            throw new AjaxException(__('La conférence n\'existe pas.'), HttpResponseCodeEnum::NOT_FOUND);
        }

        $items = $this->get(EntityManager::class)
            ->getRepository(ConferenceOffre::class)
            ->getConferenceOffresByConference($conferenceId);

        $this->displayOutput([
            'status' => true,
            'content' => ConferenceOffreRenderer::renderList($items),
        ]);
    }
}
