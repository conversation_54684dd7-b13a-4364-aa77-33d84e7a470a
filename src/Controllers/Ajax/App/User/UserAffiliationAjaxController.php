<?php

namespace Learnybox\Controllers\Ajax\App\User;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Affiliation\Affilie\AffiliationAffilie;
use Learnybox\Entity\Affiliation\Commission\AffiliationCommission;
use Learnybox\Entity\Affiliation\Payment\AffiliationPayment;
use Learnybox\Enums\HttpResponseCodeEnum;
use Learnybox\Exceptions\Ajax\AjaxException;
use Learnybox\Factories\User\UserConfigFactory;
use Learnybox\Helpers\CurrencyHelper;
use Learnybox\Helpers\TemplateHelper;
use Learnybox\Services\Affiliation\Affilie\AffiliationAffilieService;
use Symfony\Component\Routing\Attribute\Route;
use Learnybox\Attributes\Universe;

#[Universe(UNIVERSE_APP_AFFILIATION)]
class UserAffiliationAjaxController extends AbstractUserAjaxController
{
    #[Route(path: '/ajax/app/user/{userId}/affiliation/affilie/add/', name: 'ajax_app_user_affiliation_affilie_add', requirements: ['userId' => '\d+'])]
    public function addAffiliate(): void
    {
        $user = $this->getUser();
        $data = $this->getRequestContent('formData');
        $parent = isset($data['parent']) ? filter_var($data['parent'], FILTER_VALIDATE_INT) : 0;
        $config = filter_var($data['config'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);

        if (!$config) {
            throw new AjaxException('Une erreur est survenue, veuillez nous excuser.');
        }

        $aff = $this->container->get(EntityManager::class)->getRepository(AffiliationAffilie::class)->findOneBy(['user' => $user->getUserId()]);
        if (!$aff) {
            $response = $this->container->get(AffiliationAffilieService::class)->addAffilie($user->getUserId(), $parent);
            if (!$response['valid']) {
                throw new AjaxException('Une erreur est survenue, veuillez nous excuser.');
            }
        }

        $this->container->get(UserConfigFactory::class)->createOrUpdateUserConfig($user->getUserId(), [
            'adresse' => $config['adresse'],
            'ville' => $config['ville'],
            'code_postal' => $config['code_postal'],
            'pays' => $config['pays']
        ], true);

        $this->displayOutput([
            'status' => true,
        ]);
    }

    #[Route(path: '/ajax/app/user/{userId}/affiliation/affilie/{affilieId}/edit/', name: 'ajax_app_user_affiliation_affilie_edit', requirements: ['userId' => '\d+', 'affilieId' => '\d+'])]
    public function editAffiliate(): void
    {
        $this->getUser();
        $this->getAffilie();

        $data = $this->getRequestContent('formData');

        $response = $this->get(\Aff_Affilies::class)->app_update_affilie($data);

        $this->handleResponse($response);
    }

    #[Route(path: '/ajax/app/user/{userId}/affiliation/commissions/{commissionId}/delete/', name: 'ajax_app_user_affiliation_commission_delete', requirements: ['userId' => '\d+', 'commissionId' => '\d+'])]
    public function deleteAffiliateCommission(): void
    {
        $this->getUser();
        $commissionId = $this->getRequestAttributes()->getInt('commissionId');

        $em = $this->get(EntityManager::class);

        $commission = $em->getRepository(AffiliationCommission::class)->find($commissionId);
        if (!$commission) {
            throw new AjaxException(__('La commission n\'existe pas.'), HttpResponseCodeEnum::NOT_FOUND);
        }

        try {
            $em->remove($commission);
            $em->flush();
        } catch (\Exception $e) {
            $this->displayError(__('Une erreur est survenue lors de la suppression de la commission.'));
        }

        $this->displayOutput([
            'status' => true,
        ]);
    }

    #[Route(path: '/ajax/app/user/{userId}/affiliation/commissions/{commissionId}/edit/', name: 'ajax_app_user_affiliation_commission_edit', requirements: ['userId' => '\d+', 'commissionId' => '\d+'])]
    public function editAffiliateCommission(): void
    {
        $this->getUser();
        $this->getCommission();

        $data = $this->getRequestContent('formData');

        $updateCommission = $this->get(\Aff_Commissions::class)->app_update_commission($data);

        $this->handleResponse($updateCommission);
    }

    #[Route(path: '/ajax/app/user/{userId}/affiliation/paiements/modal/', name: 'ajax_app_user_affiliation_payment_modal', requirements: ['userId' => '\d+'])]
    public function createPaymentModal(): void
    {
        $user = $this->getUser();

        $query = $this->getRequestQuery();
        $idPayment = $query->getInt('id_payment');
        $idAffilie = $query->getInt('id_affilie');

        $payment = null;
        if ($idPayment) {
            $em = $this->get(EntityManager::class);

            $payment = $em->getRepository(AffiliationPayment::class)->find($idPayment);
            if (!$payment) {
                throw new AjaxException(__('Le paiement n\'existe pas.'), HttpResponseCodeEnum::NOT_FOUND);
            }
        }

        $devise = DEFAULT_CURRENCY;
        $param = $this->get(\Reglages::class)->appGetParametreByName('affilie_devise');
        if ($param && $param['value']) {
            $devise = $param['value'];
        }

        $this->displayOutput([
            'status' => true,
            'content' => TemplateHelper::render('app/modals/users/affiliation/paiements/edit.html.twig', [
                'userId' => $user->getUserId(),
                'affiliateId' => $idAffilie,
                'payment' => $payment,
                'currencySymbol' => CurrencyHelper::getCurrencySymbol($devise),
                'title' => $payment ? __('Modifier un paiement') : __('Ajouter un paiement')
            ])
        ]);
    }

    #[Route(path: '/ajax/app/user/{userId}/affiliation/paiements/add/', name: 'ajax_app_user_affiliation_payment_add', requirements: ['userId' => '\d+'])]
    public function addAffiliatePayment(): void
    {
        $this->getUser();

        $idAffilie = $this->getRequestContent('affiliateId');
        $montant = $this->getRequestContent('montant');

        if (!$montant) {
            throw new AjaxException(__('Veuillez renseigner un montant.'), HttpResponseCodeEnum::BAD_REQUEST);
        }

        $addPayment = $this->get(\Aff_Payments::class)->insert_payment(['id_affilie' => $idAffilie, 'montant' => $montant]);

        $this->handleResponse($addPayment);
    }

    #[Route(path: '/ajax/app/user/{userId}/affiliation/paiements/{paymentId}/edit/', name: 'ajax_app_user_affiliation_payment_edit', requirements: ['userId' => '\d+', 'paymentId' => '\d+'])]
    public function editAffiliatePayment(): void
    {
        $this->getUser();
        $payment = $this->getPayment();

        $montant = $this->getRequestContent('montant');
        $montant = filter_var($montant, FILTER_VALIDATE_FLOAT);
        ;

        if (!$montant) {
            throw new AjaxException(__('Veuillez renseigner un montant.'), HttpResponseCodeEnum::BAD_REQUEST);
        }

        $em = $this->get(EntityManager::class);

        try {
            $payment->setMontant($montant);
            $em->flush();
        } catch (\Exception $e) {
            $this->displayError(__('Une erreur est survenue lors de la modification du paiement.'));
        }

        $this->displayOutput([
            'status' => true,
        ]);
    }

    #[Route(path: '/ajax/app/user/{userId}/affiliation/paiements/{paymentId}/delete/', name: 'ajax_app_user_affiliation_payment_delete', requirements: ['userId' => '\d+', 'paymentId' => '\d+'])]
    public function deleteAffiliatePayment(): void
    {
        $this->getUser();
        $payment = $this->getPayment();
        $em = $this->get(EntityManager::class);

        try {
            $em->remove($payment);
            $em->flush();
        } catch (\Exception $e) {
            $this->displayError(__('Une erreur est survenue lors de la suppression du paiement.'));
        }

        $this->displayOutput([
            'status' => true,
        ]);
    }

    private function getAffilie(): AffiliationAffilie
    {
        $affilieId = $this->getRequestAttributes()->getInt('affilieId');

        $em = $this->get(EntityManager::class);

        $affilie = $em->getRepository(AffiliationAffilie::class)->find($affilieId);
        if (!$affilie) {
            throw new AjaxException(__('L\'affilié n\'existe pas.'), HttpResponseCodeEnum::NOT_FOUND);
        }

        return $affilie;
    }

    private function getPayment(): AffiliationPayment
    {
        $paymentId = $this->getRequestAttributes()->getInt('paymentId');

        $em = $this->get(EntityManager::class);

        $payment = $em->getRepository(AffiliationPayment::class)->find($paymentId);
        if (!$payment) {
            throw new AjaxException(__('Le paiement n\'existe pas.'), HttpResponseCodeEnum::NOT_FOUND);
        }

        return $payment;
    }

    private function getCommission(): AffiliationCommission
    {
        $commissionId = $this->getRequestAttributes()->getInt('commissionId');

        $em = $this->get(EntityManager::class);

        $commission = $em->getRepository(AffiliationCommission::class)->find($commissionId);
        if (!$commission) {
            throw new AjaxException(__('La commission n\'existe pas.'), HttpResponseCodeEnum::NOT_FOUND);
        }

        return $commission;
    }
}
