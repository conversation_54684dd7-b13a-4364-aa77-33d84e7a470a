<?php

namespace Learnybox\Controllers\Ajax\App\User\Formation;

use Doctrine\ORM\EntityManager;
use Learnybox\Attributes\Universe;
use Learnybox\Components\ObjectList\Enum\ObjectListEnum;
use Learnybox\Components\ObjectList\ObjectListManager;
use Learnybox\Controllers\Ajax\App\User\AbstractUserAjaxController;
use Learnybox\Entity\Formation\Formation;
use Learnybox\Entity\Formation\Membre\FormationMembreHasFormation;
use Learnybox\Enums\HttpResponseCodeEnum;
use Learnybox\Exceptions\Ajax\AjaxException;
use Learnybox\Helpers\TemplateHelper;
use Symfony\Component\Routing\Attribute\Route;

#[Universe(UNIVERSE_APP_FORMATIONS)]
class UserFormationBadgeAjaxController extends AbstractUserAjaxController
{
    #[Route(path: '/ajax/app/user/{userId}/formation/{formationId}/badge/{badgeId}/delete/', name: 'ajax_app_user_formation_badge_delete', requirements: ['userId' => '\d+', 'formationId' => '\d+', 'badgeId' => '\d+'])]
    public function delete(): void
    {
        $user = $this->getUser();

        $formation = $this
            ->get(EntityManager::class)
            ->getRepository(Formation::class)
            ->find($this->getRequestAttributes()->getInt('formationId'));

        if (!$formation) {
            throw new AjaxException(__('La formation n\'existe pas.'), HttpResponseCodeEnum::NOT_FOUND);
        }

        $data = [
            'user_id' => $user->getUserId(),
            'id_badge' => $this->getRequestAttributes()->getInt('badgeId'),
        ];

        $response = $this->get(\Formation_BadgesUsers::class)->membre_delete_badge($data);

        $this->handleResponse($response);
    }

    #[Route(path: '/ajax/app/user/{userId}/formation/badge/create/modal/', name: 'ajax_app_user_formation_badge_create_modal', requirements: ['userId' => '\d+'])]
    public function createModal(): void
    {
        $user = $this->getUser();

        $formations = $this->get(EntityManager::class)->getRepository(FormationMembreHasFormation::class)->findBy(['user' => $user->getUserId()]);

        if (!$formations) {
            throw new AjaxException(__('Aucune formation trouvée pour cet utilisateur.'), HttpResponseCodeEnum::NOT_FOUND);
        }

        $formationIds = array_map(fn($item) => $item->getFormation()->getIdformation(), $formations);

        $formationBadges = $this
            ->get(ObjectListManager::class)
            ->handle(ObjectListEnum::FORMATIONS_BADGES, ['formationIds' => $formationIds]);

        $this->displayOutput([
            'status' => true,
            'content' => TemplateHelper::render('app/modals/users/formations/badges/create.html.twig', [
                'title' => __("Ajout d'un badge de formation"),
                'formationBadges' => $formationBadges,
                'userId' => $user->getUserId(),
            ])
        ]);
    }

    #[Route(path: '/ajax/app/user/{userId}/formation/badge/create/', name: 'ajax_app_user_formation_badge_create', requirements: ['userId' => '\d+'])]
    public function create(): void
    {
        $user = $this->getUser();

        $formations = $this->get(EntityManager::class)->getRepository(FormationMembreHasFormation::class)->findBy(['user' => $user->getUserId()]);

        if (!$formations) {
            throw new AjaxException(__('Aucune formation trouvée pour cet utilisateur.'), HttpResponseCodeEnum::NOT_FOUND);
        }

        $content = $this->getRequestContent();
        $data['user_id'] = filter_var($content['userId'], FILTER_VALIDATE_INT);
        $data['nb_badges'] = filter_var($content['formationBadgeTotal'], FILTER_VALIDATE_INT);
        $data['id_badge'] = filter_var($content['formationBadgeId'], FILTER_VALIDATE_INT);

        $response = $this->get(\Formation_BadgesUsers::class)->membre_add_badge($data);

        $this->handleResponse($response);
    }
}
