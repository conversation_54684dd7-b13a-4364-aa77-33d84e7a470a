<?php

namespace Learnybox\Controllers\Ajax\App\Abonnement;

use Learnybox\Controllers\Ajax\AbstractAjaxController;
use Symfony\Component\Routing\Attribute\Route;

class AbonnementAjaxController extends AbstractAjaxController
{
    #[Route(path: '/ajax/app/add_abonnement/', name: 'ajax_app_add_abonnement', methods: ['POST'])]
    public function addAbonnement(): void
    {
        $response = $this->container->get(\ClientsAbonnements::class)->add_abonnement($_POST);
        if (!$response['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => $response['message'] ?? null,
            ]);
        }

        $html = __('Afin de poursuivre votre souscription d\'abonnement, vous allez être redirigé vers le site web de PayPal pour procéder au paiement.') . '<br/><br/>';
        $html .= $response['html'];

        $this->displayOutput([
            'status' => true,
            'html' => $html
        ]);
    }

    #[Route(path: '/ajax/app/change_abonnement/', name: 'ajax_app_change_abonnement', methods: ['POST'])]
    public function change(): void
    {
        $response = $this->container->get(\ClientsAbonnements::class)->change_abonnement($_POST);
        if (!$response['valid']) {
            $this->displayOutput([
                'status' => false,
                'message' => $response['message'] ?? null,
            ]);
        }

        $html = __('Afin de poursuivre votre changement d\'abonnement, vous allez être redirigé vers le site web de PayPal pour procéder au paiement.') . '<br/><br/>';
        $html .= $response['html'];

        $this->displayOutput([
            'status' => true,
            'html' => $html
        ]);
    }
}
