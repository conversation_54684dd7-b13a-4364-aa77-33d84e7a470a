<?php

namespace Learnybox\Controllers\Ajax\App\Formation;

use Doctrine\ORM\EntityManager;
use Learnybox\Attributes\Universe;
use Learnybox\Controllers\Ajax\AbstractAjaxController;
use Learnybox\Entity\Formation\Formation;
use Learnybox\Entity\Formation\Forum\FormationForum;
use Learnybox\Services\Formations\FormationForumService;
use Symfony\Component\Routing\Attribute\Route;

#[Universe(UNIVERSE_APP_FORMATIONS)]
class FormationForumAjaxController extends AbstractAjaxController
{
    #[Route(path: '/ajax/app/formation/{formationId}/forum/design/', name: 'ajax_app_formation_forum_design', requirements: ['formationId' => '\d+'])]
    public function forumDesign()
    {
        $formationId = $this->getRequest()->get('formationId');
        $formation = $this->get(EntityManager::class)->getRepository(Formation::class)->find($formationId);
        if (empty($formation)) {
            $this->displayOutput(['status' => false, 'message' => __('Aucune formation en cours.')]);
        }

        $forum = $this->get(EntityManager::class)->getRepository(FormationForum::class)->findOneBy(['idformation' => $formationId]);
        $forumMainColor = filter_var($this->cleaned_post['forumMainColor'], FILTER_UNSAFE_RAW);
        $forum->setMainColor($forumMainColor);
        try {
            $this->get(FormationForumService::class)->persistAndFlush($forum);
        } catch (\Exception $e) {
            $this->displayOutput(['status' => false, 'message' => __("Erreur lors de la mise de la personnalisation de la communauté.")]);
        }

        $this->displayOutput(['status' => true]);
    }
}
