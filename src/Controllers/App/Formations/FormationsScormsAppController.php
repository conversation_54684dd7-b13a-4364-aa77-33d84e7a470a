<?php

namespace Learnybox\Controllers\App\Formations;

use Learnybox\Attributes\Layout;
use Learnybox\Enums\Client\ClientLimitEnum;
use Learnybox\Factories\BlankStatePage\BlankStatePageServiceFactory;
use Learnybox\Helpers\Assets;
use Learnybox\Helpers\RouterHelper;
use Learnybox\Renderers\Menus\App\Reglages\CommonSettingsMenuRenderer;
use Learnybox\Services\RightsService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class FormationsScormsAppController
 * @package Learnybox\Controllers\App\Formations
 */
#[Layout('app/layouts/formation_reglages.html.twig')]
class FormationsScormsAppController extends AbstractFormationsAppController
{
    #[Route(path: '/app/scorms/', name: 'app_scorms_old')]
    #[Route(path: '/app/formation/{formationId}/scorms/', name: 'app_formation_scorms', requirements: ['formationId' => '\d+'])]
    public function scorms()
    {
        $this->checkClientLimit(ClientLimitEnum::FORMATION_SCORMS);

        if (RightsService::hasRole(ROLE_COACH)) {
            $this->get(RightsService::class)->display403();
        }

        $formationId = $this->getRequestAttributes()->getInt('formationId');
        $formation = $this->checkIfFormationIsDefined($formationId);

        $scos = $this->get(\Formation_Scorms::class)->getScos($formation['idformation']);

        if (empty($scos)) {
            $blankStateContent = BlankStatePageServiceFactory::create('formation_scorms', ['formation_id' => $formation['idformation']]);
            $content = $blankStateContent->render();
        } else {
            $subNavLinks = [
                [
                    'route' => RouterHelper::generate('app_formation_scorm_import', ['formationId' => $formationId]),
                    'icon' => 'fa-regular fa-upload',
                    'title' => __('Importer un module')
                ]
            ];

            $topTitle = $this->get(CommonSettingsMenuRenderer::class)->renderTopTitle(__('Modules SCORM'), '', '', $subNavLinks);

            $content = $this->parser
                ->set('formationId', $formation['idformation'])
                ->set('scos', $scos)
                ->set('topTitle', $topTitle)
                ->parsePHP(VIEWS_PATH . '/app/formation/scorms.php');
        }

        $this->displayOutput($content);
    }

    #[Route(path: '/app/scorm/{scormId}/', name: 'app_scorm_old', requirements: ['scormId' => '\d+'])]
    #[Route(path: '/app/formation/{formationId}/scorm/{scormId}/', name: 'app_formation_scorm', requirements: ['formationId' => '\d+', 'scormId' => '\d+'])]
    public function scorm()
    {
        if (RightsService::hasRole(ROLE_COACH)) {
            $this->get(RightsService::class)->display403();
        }

        $formationId = $this->getRequestAttributes()->getInt('formationId');
        $formation = $this->checkIfFormationIsDefined($formationId);

        $scormId = $this->getRequestAttributes()->getInt('scormId');
        if (!$scormId) {
            return $this->displayError(__('Impossible de récupérer le module SCORM'));
        }

        $sco = $this->get(\Formation_Scorms::class)->getScoById($scormId);
        if (!$sco) {
            return $this->displayError(__('Ce module SCORM n\'existe pas'));
        }

        $students = $this->get(\Formation_Scorms::class)->getDistinctUsersBySco($scormId);

        $topTitle = $this->get(CommonSettingsMenuRenderer::class)->renderTopTitle($sco['title']);

        $content = $this->parser
            ->set('formation', $formation)
            ->set('id_sco', $scormId)
            ->set('sco', $sco)
            ->set('students', $students)
            ->set('topTitle', $topTitle)
            ->parsePHP(VIEWS_PATH . '/app/formation/scorm.php');

        $this->displayLayout('app/formations/reglages/scorm.html.twig', [
            'content' => $this->get(\Csrf::class)->replaceForm($content),
            'formationId' => $formationId
        ]);
    }

    #[Route(path: '/app/scorm_import/', name: 'app_scorm_import_old')]
    #[Route(path: '/app/formation/{formationId}/scorm/import/', name: 'app_formation_scorm_import', requirements: ['formationId' => '\d+'])]
    public function scormImport()
    {
        $formationId = $this->getRequestAttributes()->getInt('formationId');
        $formation = $this->checkIfFormationIsDefined($formationId);

        $topTitle = $this->get(CommonSettingsMenuRenderer::class)->renderTopTitle(__('Importation d\'un module SCORM'));

        Assets::addCssV5(Assets::CSS_TYPE_VENDORS, 'jquery.fileupload-ui.css');
        Assets::addJs('common/upload/jquery.ui.widget.js');
        Assets::addJs('app/upload/jquery.fileupload-init-zip2.js');

        $content = $this->parser
            ->set('formation', $formation)
            ->set('topTitle', $topTitle)
            ->parsePHP(FORMS_PATH . '/app/formation/scorm_import.php');

        $this->displayLayout('app/formations/reglages/scorm.html.twig', [
            'content' => $this->get(\Csrf::class)->replaceForm($content),
            'formationId' => $formationId
        ]);
    }
}
