<?php

namespace Learnybox\Controllers\App\TransactionForms;

use Learnybox\Helpers\Assets;
use Learnybox\Renderers\Transactions\TransactionRenderer;
use Learnybox\Services\Partials\ConfigAddressService;
use Symfony\Component\Routing\Attribute\Route;
use Learnybox\Attributes\Layout;

/**
 * Class ShopCustomersAppController
 * @package Learnybox\Controllers\App\TransactionForms
 */
#[Layout('app/layouts/transaction_form.html.twig')]
class ShopCustomersAppController extends AbstractTransactionFormsAppController
{
    #[Route(path: '/app/shop/prospects/', name: 'app_shop_prospects_old')]
    #[Route(path: '/app/shop/formulaire/{formulaireId}/prospects/', name: 'app_shop_prospects', requirements: ['formulaireId' => '\d+'])]
    public function shopProspects()
    {
        $formulaireId = $this->getRequestAttributes()->getInt('formulaireId');
        $formulaire = $this->checkIfShopIsDefined($formulaireId);

        $renseignements = $this->get(\Shop_Champs::class)->getRenseignementsByFormulaire($formulaire['id_formulaire']);
        $output = $this->parser->set('renseignements', $renseignements)
            ->set('formulaire', $formulaire)
            ->parsePHP(VIEWS_PATH . '/app/shop/prospects-ajax.php');
        Assets::addJs('app/tables-v3/shop-prospects.js');

        $this->displayOutput($output);
    }


    #[Route(path: '/app/shop/client_add/', name: 'app_shop_client_add_old')]
    #[Route(path: '/app/shop/formulaire/{formulaireId}/client_add/', name: 'app_shop_client_add', requirements: ['formulaireId' => '\d+'])]
    public function shopCustomerAdd()
    {
        $formulaireId = $this->getRequestAttributes()->getInt('formulaireId');
        $formulaire = $this->checkIfShopIsDefined($formulaireId);

        $output = $this->parser
            ->set('id_customer', 0)
            ->set('id_formulaire', $formulaire['id_formulaire'])
            ->set('formulaire', $formulaire)
            ->set('cleaned_post', (isset($this->cleaned_post) ? $this->cleaned_post : []))
            ->set('action', 'shop_customer_add')
            ->set('configAddressService', $this->get(ConfigAddressService::class))
            ->parsePHP(FORMS_PATH . '/app/shop/shop_customer.php');

        $this->displayOutput($output);
    }


    #[Route(path: '/app/shop/client_edit/{clientId}/', name: 'app_shop_client_edit_old', requirements: ['clientId' => '\d+'])]
    #[Route(path: '/app/shop/formulaire/{formulaireId}/client_edit/{clientId}/', name: 'app_shop_client_edit', requirements: ['formulaireId' => '\d+', 'clientId' => '\d+'])]
    public function shopCustomerEdit()
    {
        $formulaireId = $this->getRequestAttributes()->getInt('formulaireId');
        $formulaire = $this->checkIfShopIsDefined($formulaireId);

        $id_customer = $this->getRequestAttributes()->getInt('clientId');

        $output = $this->parser
            ->set('id_customer', $id_customer)
            ->set('id_formulaire', $formulaire['id_formulaire'])
            ->set('formulaire', $formulaire)
            ->set('cleaned_post', (isset($this->cleaned_post) ? $this->cleaned_post : []))
            ->set('action', 'shop_customer_edit')
            ->set('configAddressService', $this->get(ConfigAddressService::class))
            ->parsePHP(FORMS_PATH . '/app/shop/shop_customer.php');

        $this->displayOutput($output);
    }


    #[Route(path: '/app/shop/clients/rens/', name: 'app_shop_clients_rens_old')]
    #[Route(path: '/app/shop/formulaire/{formulaireId}/clients/rens/', name: 'app_shop_clients_rens', requirements: ['formulaireId' => '\d+'])]
    public function shopCustomersRens()
    {
        $formulaireId = $this->getRequestAttributes()->getInt('formulaireId');
        $formulaire = $this->checkIfShopIsDefined($formulaireId);

        $clients_rens = $this->get(\Shop_Champs::class)->app_afficher_customers_rens($formulaire['id_formulaire']);
        $output = $clients_rens['output'];
        Assets::addJs('common/jquery/jquery.flot.min.js');
        Assets::addJs('common/jquery/jquery.flot.pie.min.js');
        Assets::addJs('common/jquery/jquery.flot.stack.js');
        Assets::addJs('common/jquery/jquery.flot.resize.min.js');
        Assets::addInlineJs($clients_rens['js']);

        $this->displayOutput($output);
    }


    #[Route(path: '/app/shop/clients/', name: 'app_shop_clients_old')]
    #[Route(path: '/app/shop/formulaire/{formulaireId}/clients/', name: 'app_shop_clients', requirements: ['formulaireId' => '\d+'])]
    public function shopCustomers()
    {
        $formulaireId = $this->getRequestAttributes()->getInt('formulaireId');
        $formulaire = $this->checkIfShopIsDefined($formulaireId);

        $renseignements = $this->get(\Shop_Champs::class)->getRenseignementsByFormulaire($formulaire['id_formulaire']);
        $output = $this->parser->set('renseignements', $renseignements)
            ->set('formulaire', $formulaire)
            ->parsePHP(VIEWS_PATH . '/app/shop/customers-ajax.php');
        Assets::addJs('app/tables-v3/shop-customers.js');

        $this->displayOutput($output);
    }

    #[Route(path: '/app/shop/client/{clientId}/', name: 'app_shop_client_old', requirements: ['clientId' => '\d+'])]
    #[Route(path: '/app/shop/formulaire/{formulaireId}/client/{clientId}/', name: 'app_shop_client', requirements: ['formulaireId' => '\d+', 'clientId' => '\d+'])]
    #[Layout('app/layouts/shop_customer.html.twig')]
    public function shopCustomer()
    {
        $formulaireId = $this->getRequestAttributes()->getInt('formulaireId');
        $formulaire = $this->checkIfShopIsDefined($formulaireId);

        $id_customer = $this->getRequestAttributes()->getInt('clientId');
        $customer = $this->get(\Shop_Customers::class)->getCustomerById($id_customer);

        $output = $this->parser
            ->set('transactionRenderer', $this->get(TransactionRenderer::class))
            ->set('customer', $customer)
            ->set('formulaire', $formulaire)
            ->parsePHP(VIEWS_PATH . '/app/shop/customer.php');

        $this->displayOutput($output);
    }
}
