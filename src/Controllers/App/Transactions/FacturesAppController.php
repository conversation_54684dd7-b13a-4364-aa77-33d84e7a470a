<?php

namespace Learnybox\Controllers\App\Transactions;

use Doctrine\ORM\EntityManager;
use Learnybox\Components\Exporter\Enum\ExporterExtensionTypeEnum;
use Learnybox\Entity\Facture\Text\FactureText;
use Learnybox\Factories\BlankStatePage\BlankStatePageServiceFactory;
use Learnybox\Helpers\Assets;
use Learnybox\Helpers\InfraHelper;
use Learnybox\Helpers\RouterHelper;
use Learnybox\Helpers\TemplateHelper;
use Learnybox\Repositories\TransactionsPaymentsRepository;
use Learnybox\Services\Partials\ConfigAddressService;
use Learnybox\Services\RightsService;
use Learnybox\Services\Factures\FacturesEmailsService;
use Learnybox\Services\Menus\App\FacturesAppMenuService;
use Learnybox\Services\Transaction\TransactionsEcrituresComptablesService;
use Symfony\Component\Routing\Attribute\Route;
use Learnybox\Attributes\Layout;

/**
 * Class FacturesAppController
 * @package Learnybox\Controllers\App\Transactions
 */
#[Layout('app/layouts/transactions.html.twig')]
class FacturesAppController extends AbstractTransactionsAppController
{
    #[Route(path: '/app/factures/', name: 'app_factures')]
    public function factures()
    {
        $nbFactures = $this->get(\Factures::class)->getCountFactures();
        if (!$nbFactures) {
            $factory = BlankStatePageServiceFactory::create('factures');
            $this->displayOutput($factory->render());
            return;
        }

        $output = $this->parser
            ->set('isInfraUS', (InfraHelper::isUS()))
            ->parsePHP(VIEWS_PATH . '/app/factures/factures-ajax.php');
        Assets::addJs('app/tables-v3/factures.js');
        $this->displayOutput($output);
    }

    #[Route(path: '/app/facture/', name: 'app_facture_add')]
    public function factureAdd()
    {
        $configAddressHtml = $this->container->get(ConfigAddressService::class)->render($this->cleaned_post ?? []);
        $output = $this->parser->set('id_facture', 0)
            ->set('action', 'insert_facture')
            ->set('cleaned_post', ($this->cleaned_post ?? []))
            ->set('configAddressHtml', $configAddressHtml)
            ->parsePHP(FORMS_PATH . '/app/factures/facture.php');

        Assets::addJs('app/transaction_add.js');
        Assets::addInlineJs(\Tools::getJsAddress());
        $this->displayOutput($output);
    }

    #[Route(path: '/app/facture_detail/{id}/', name: 'app_facture_detail', requirements: ['id' => '\d+'])]
    #[Layout('app/layouts/facture.html.twig')]
    public function factureDetail()
    {
        $idFacture = filter_var($this->param, FILTER_VALIDATE_INT);
        if (!$idFacture) {
            return $this->displayError(__('Erreur : impossible de récupérer la facture'), \Tools::makeLink('app', 'factures'));
        }

        $facture = $this->get(\Factures::class)->getFactureById($idFacture);
        if (!$facture) {
            return $this->displayError(__("Erreur : cette facture n'existe pas"), \Tools::makeLink('app', 'factures'));
        }

        $payments = [];
        if ($facture['mode_paiement'] == 'virement' or $facture['mode_paiement'] == 'cheque') {
            $payments = $this->get(TransactionsPaymentsRepository::class)->getByTransactionId($facture['id_transaction']);
        }

        $configAddressHtml = $this->container->get(ConfigAddressService::class)->render(json_decode($facture['contenu_personnel'], true) ?? []);
        $output = $this->parser->set('facture', $facture)
            ->set('id_facture', $idFacture)
            ->set('payments', $payments)
            ->set('configAddressHtml', $configAddressHtml)
            ->parsePHP(VIEWS_PATH . '/app/factures/facture.php');
        $this->displayOutput($output);
    }

    #[Route(path: '/app/exporter_facture/{id}/', name: 'app_exporter_facture', requirements: ['id' => '\d+'])]
    public function exportFacture()
    {
        $error = '';
        $idFacture = filter_var($this->param, FILTER_VALIDATE_INT);
        if (!$idFacture) {
            $error = __('Erreur : impossible de récupérer la facture');
        } else {
            $facture = $this->get(\Factures::class)->getFactureById($idFacture);
            if (!$facture) {
                $error = __("Erreur : cette facture n'existe pas");
            }
        }

        if ($error) {
            $this->displayError($error, \Tools::makeLink('app', 'factures'));
            return;
        }

        $this->get(\Factures::class)->export_facture_pdf($idFacture);
        exit();
    }

    #[Route(path: '/app/export_factures/', name: 'app_exporter_factures')]
    public function exportFactures()
    {
        if (!$this->get(\UserConfig::class)->canSeePrivateInfos()) {
            $this->displayError(__('Vous n\'avez pas les droits suffisants pour exécuter cette opération'), \Tools::makeLink('app', 'factures'));
            return;
        }
        if (!$_SESSION['id_client'] and !RightsService::hasAccess(UNIVERSE_ADMIN_STATS)) {
            $this->displayError(_('Vous n\'avez pas les droits suffisants pour exécuter cette opération'), \Tools::makeLink('app', 'factures'));
            return;
        }

        $nbFactures = $this->get(\Factures::class)->getCountFactures();
        if (!$nbFactures) {
            $this->displayError(__('Aucune facture trouvée'), \Tools::makeLink('app', 'factures'));
            return;
        }

        $this->displayOutput(TemplateHelper::render('app/factures/_export_form.html.twig', [
            'returnLink' => RouterHelper::generate('app_factures'),
            'fileType' => ExporterExtensionTypeEnum::EXTENSION_TYPE_PDF
        ]));
    }

    #[Route(path: '/app/export_factures_csv/', name: 'app_export_factures_csv')]
    public function export_factures_csv()
    {
        if (!$this->get(\UserConfig::class)->canSeePrivateInfos()) {
            $this->displayError(__('Vous n\'avez pas les droits suffisants pour exécuter cette opération'), \Tools::makeLink('app', 'factures'));
            return;
        }
        if (!$_SESSION['id_client'] and !RightsService::hasAccess(UNIVERSE_ADMIN_STATS)) {
            $this->displayError(_('Vous n\'avez pas les droits suffisants pour exécuter cette opération'), \Tools::makeLink('app', 'factures'));
            return;
        }

        $nbFactures = $this->get(\Factures::class)->getCountFactures();
        if (!$nbFactures) {
            $this->displayError(__('Aucune facture trouvée'), \Tools::makeLink('app', 'factures'));
            return;
        }

        $output = $this->parser->set('nb_factures', $nbFactures)
            ->parsePHP(VIEWS_PATH . '/app/factures/export_factures.php');

        Assets::addJs('app/export_factures_csv.js');
        $this->displayOutput($output);
    }

    #[Route(path: '/app/reglages_factures/', name: 'app_reglages_factures')]
    #[Route(path: '/app/reglages_factures/{slug}/', name: 'app_reglages_factures_slug')]
    #[Layout('app/layouts/transactions_reglages.html.twig')]
    public function reglagesFactures()
    {
        $this->checkIfOwnerIsGranted();

        $formParam = $this->param;

        $formFile = FORMS_PATH . '/app/factures/coordonnees.php';
        $action = 'reglages_factures_coordonnees';
        if ($formParam) {
            $file = FORMS_PATH . '/app/factures/' . $formParam . '.php';
            if (!file_exists($file)) {
                $this->display404();
            }
            $action = 'reglages_factures_' . $formParam;
            $formFile = $file;
        }

        $settings = $this->get(\Reglages::class)->appGetParametres();

        $invoiceSettings = [];
        if (isset($settings['facturation'])) {
            $invoiceSettings = json_decode($settings['facturation'], true);
        }

        $this->parser->set('settings', $settings)
            ->set('invoiceSettings', $invoiceSettings)
            ->set('action', $action)
            ->set('cleaned_post', ($this->cleaned_post ?? []));

        if (!$formParam || $formParam === 'coordonnees') {
            $this->parser->set('selectPays', $this->get(ConfigAddressService::class)->renderSelectCountry($invoiceSettings));

            Assets::addCssV5(Assets::CSS_TYPE_PAGES, 'facture_preview.css');
            Assets::addJs('app/facture_preview.js');
        } elseif ($formParam === 'texts') {
            $factureTexts = $this->get(EntityManager::class)->getRepository(FactureText::class)->findBy([
                'client' => $_SESSION['id_client']
            ]);
            $this->parser->set('factureTexts', $factureTexts);
        } elseif ($formParam === 'emails') {
            $this->parser->set('invoiceEmail', $this->get(FacturesEmailsService::class)->getInvoiceEmail());
            $this->parser->set('abonnementEmail', $this->get(FacturesEmailsService::class)->getAbonnementEmail());
            $this->parser->set('errorEmail', $this->get(FacturesEmailsService::class)->getErrorEmail());
            $this->parser->set('cancelEmail', $this->get(FacturesEmailsService::class)->getCancelEmail());
            $this->parser->set('bankWireEmail', $this->get(FacturesEmailsService::class)->getBankWireEmail());
        } elseif ($formParam === 'avoirs') {
            $this->parser->set('avoirEmail', $this->get(FacturesEmailsService::class)->getAvoirEmail());
        }

        $content = $this->parser->parsePHP($formFile);

        $this->displayOutput($content);
    }

    #[Route(path: '/app/exporter_ecritures_comptables/', name: 'app_exporter_ecritures_comptables')]
    public function exporterEcrituresComptables()
    {
        if (!$this->get(\UserConfig::class)->canSeePrivateInfos()) {
            $this->displayError(__('Vous n\'avez pas les droits suffisants pour exécuter cette opération'), \Tools::makeLink('app', 'factures'));
            return;
        }

        $params = $this->get(\Factures::class)->getParamsEcritures();
        if (!$params) {
            return $this->parametrerEcrituresComptables();
        }

        $startDate = $endDate = null;
        if (!empty($_GET['start_date']) && !empty($_GET['end_date'])) {
            try {
                $startDate = new \DateTime(filter_input(INPUT_GET, 'start_date', FILTER_SANITIZE_SPECIAL_CHARS));
                $endDate = new \DateTime(filter_input(INPUT_GET, 'end_date', FILTER_SANITIZE_SPECIAL_CHARS));

                $endDate->add(new \DateInterval('P1D'));
            } catch (\Exception $e) {
                $startDate = $endDate = null;
            }
        }

        $this->get(TransactionsEcrituresComptablesService::class)->getCsvComptabilite($params, $startDate, $endDate);
        return;
    }

    #[Route(path: '/app/parametrer_ecritures_comptables/', name: 'app_parametrer_ecritures_comptables')]
    public function parametrerEcrituresComptables()
    {
        if (!$this->get(\UserConfig::class)->canSeePrivateInfos()) {
            $this->displayError(__('Vous n\'avez pas les droits suffisants pour exécuter cette opération'), \Tools::makeLink('app', 'factures'));
            return;
        }

        $allProduits = $this->get(\Shop_Produits::class)->getAllProduits();
        $params = $this->get(\Factures::class)->getParamsEcritures();

        $output = $this->parser->set('params', $params)
            ->set('allProduits', $allProduits)
            ->set('isInfraUS', (InfraHelper::isUS()))
            ->parsePHP(FORMS_PATH . '/app/parametrages_ecritures.php');

        Assets::addJs('app/exportsComptables.js');
        $this->displayOutput($output);
    }

    #[Route(path: '/app/attestation_conformite/', name: 'app_attestation_conformite')]
    public function attestation_conformite()
    {
        $output = $this->get(\Learnybox\Services\ControleDeGestionService::class)->attestationComptable();
        if ($output) {
            $this->displayOutput($output);
        }
        exit();
    }
}
