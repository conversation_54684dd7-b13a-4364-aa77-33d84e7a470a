<?php

namespace Learnybox\Controllers\App\Blog;

use Learnybox\Factories\BlankStatePage\BlankStatePageServiceFactory;
use Learnybox\Helpers\Assets;
use Learnybox\Services\Filters\PagesFiltersService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class BlogCategoriesAppController
 * @package Learnybox\Controllers\App\Blog
 */
class BlogCategoriesAppController extends AbstractBlogAppController
{
    #[Route(path: '/app/categories/', name: 'app_categories')]
    public function categories()
    {
        $allCategories = $this->get(\Categories::class)->getAllCategories();
        $factory = BlankStatePageServiceFactory::create('categories');

        if (!$allCategories) {
            $output = $factory->render();
            $this->displayOutput($output);
            return;
        }

        $idDomaine = null;
        if (isset($_GET['id_domaine']) and $_GET['id_domaine'] !== '') {
            $idDomaine = filter_input(INPUT_GET, 'id_domaine', FILTER_VALIDATE_INT);
        } elseif (isset($this->get(PagesFiltersService::class)->getFilters()['id_domaine']) && $this->get(PagesFiltersService::class)->getFilters()['id_domaine']) {
            $idDomaine = $this->get(PagesFiltersService::class)->getFilters()['id_domaine'];
        }

        $categories = $this->get(\Categories::class)->getAllCategories($idDomaine);
        $helpTooltip = $factory->renderTooltip();

        //filtres
        $this->get(PagesFiltersService::class)->setOptions($idDomaine);
        $this->get(PagesFiltersService::class)->setTableSearchOptions(__('Rechercher une catégorie'));
        $filters = $this->get(PagesFiltersService::class)->render();

        $output = $this->parser->set('categories', $categories)
            ->set('allCategories', $allCategories)
            ->set('filters', $filters)
            ->set('helpTooltip', $helpTooltip)
            ->parsePHP(VIEWS_PATH . '/app/categories.php');

        $this->displayOutput($output);
    }

    #[Route(path: '/app/categorie/', name: 'app_categorie')]
    #[Route(path: '/app/categorie/{id}/', name: 'app_categorie_id', requirements: ['id' => '\d+'])]
    public function categorie()
    {
        if (!$this->param) {
            Assets::addJs('app/uniqidNom.js');
        }
        Assets::addJs('app/uniqidNomUniqid.js');

        $this->displayForm('categorie', 'categorie');
    }
}
