<?php

namespace Learnybox\Controllers\Affiliation;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Affiliation\Invoice\AffiliationInvoice;
use Learnybox\Helpers\Assets;
use Learnybox\Helpers\RouterHelper;
use Learnybox\Renderers\Panels\PanelStatsRenderer;
use Learnybox\Services\Affiliation\AffiliationLearnyBoxService;
use Learnybox\Services\Builder\BuilderPages;
use Learnybox\Services\Integration\Facebook\FacebookPixelsService;
use Learnybox\Services\Integration\Google\GoogleAnalytics\GoogleAnalyticsService;
use Learnybox\Services\Pages\Users\UsersResetService;
use Learnybox\Services\PagesService;
use Learnybox\Services\Users\UsersService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class AffiliationController
 * @package Learnybox\Controllers\Affiliation
 */
class AffiliationController extends AbstractAffiliationController
{
    #[Route(path: '/affiliation/reset/', name: 'affiliation_reset')]
    public function reset(): void
    {
        $this->get(UsersResetService::class)->render();
    }

    #[Route(path: '/affiliation/reset_password/', name: 'affiliation_reset_password')]
    public function resetPassword(): void
    {
        UsersService::displayResetPasswordConfirm();
    }

    #[Route(path: '/affiliation/logout/', name: 'affiliation_logout')]
    public function logout(): void
    {
        UsersService::logout();
    }

    #[Route(path: '/affiliation/index/', name: 'affiliation_index')]
    public function index()
    {
        $js = '';
        $jsHead = '';
        $output = $this->parser->parsePHP(VIEWS_PATH . '/common/adblock.php');

        Assets::addJs('common/ads.js');
        Assets::addJs('common/detectblocker.js');

        if (MASTER_UNIQID == $_SESSION['client_uniqid']) {
            $this->view->set('title', __('Tableau de bord'));

            $stats = $this->get(AffiliationLearnyBoxService::class)->getStats();
            if (!$stats) {
                return $this->displayError(__('Une erreur est survenue'));
            }
            $filleuls = $this->get(\AffiliationLB::class)->getLastFilleuls();
            $commissions = $this->get(\Aff_Commissions::class)->getAllCommissionsByAffilie($_SESSION['client']['id_affilie'], 0);

            $cardsOutput = $this->get(PanelStatsRenderer::class)->renderCardStatProgress(
                __('Clics'),
                __('%d dans les 30 derniers jours', $stats['nb_clics_last_month']),
                $stats['nb_clics'],
                [],
                ['href' => RouterHelper::generate('affiliation_clics')],
                'fa-regular fa-arrow-pointer'
            );
            $cardsOutput .= $this->get(PanelStatsRenderer::class)->renderCardStatProgress(
                __('Clients'),
                __('%d dans les 30 derniers jours', $stats['nb_filleuls_last_month']),
                $stats['nb_filleuls'],
                [],
                ['href' => RouterHelper::generate('affiliation_lb_filleuls')],
                'fa-regular fa-users'
            );
            $cardsOutput .= $this->get(PanelStatsRenderer::class)->renderCardStatProgress(
                __('Commissions'),
                __('%d dans les 30 derniers jours', $stats['nb_transactions_last_month']),
                $stats['nb_transactions'],
                [],
                ['href' => RouterHelper::generate('affiliation_commissions')],
                'fa-regular fa-coins'
            );
            $cardsOutput .= $this->get(PanelStatsRenderer::class)->renderCardStatNumber(
                __('Montant total des commissions'),
                null,
                \Tools::formatAmount($stats['total_amount'], DEFAULT_CURRENCY),
                n__('%s transaction', '%s transactions', $stats['nb_transactions'], $stats['nb_transactions']),
                ['href' => RouterHelper::generate('affiliation_commissions'), 'title' => __('Correspond au montant total de toutes les commissions enregistrées.')]
            );
            $cardsOutput .= $this->get(PanelStatsRenderer::class)->renderCardStatNumber(
                __('Solde total'),
                null,
                \Tools::formatAmount($stats['solde'], DEFAULT_CURRENCY),
                '',
                ['href' => RouterHelper::generate('affiliation_commissions'), 'title' => __('Le solde total correspond au montant total des commissions qu\'il vous reste à percevoir.')]
            );
            $cardsOutput .= $this->get(PanelStatsRenderer::class)->renderCardStatNumber(
                __('Solde disponible'),
                null,
                \Tools::formatAmount($stats['solde_disponible'], DEFAULT_CURRENCY),
                '',
                ['href' => RouterHelper::generate('affiliation_invoice_add'), 'title' => __('Le solde disponible correspond aux transactions enregistrées il y a plus de 60 jours.')]
            );

            $output .= $this->parser->set('stats', $stats)
                ->set('filleuls', $filleuls)
                ->set('commissions', $commissions)
                ->set('panelStats', $this->get(PanelStatsRenderer::class)->renderPanelStats($cardsOutput))
                ->parsePHP(VIEWS_PATH . '/affiliation/index_lb.php');

            $this->displayOutput($output);
            return;
        }

        $pageAccueil = false;
        $param = $this->get(\Reglages::class)->appGetParametreByName('affilie_accueil');
        if ($param and $param['value']) {
            $pageAccueil = $param['value'];
        }

        if ($pageAccueil) {
            $page = $this->get(\Pages::class)->getPageById($pageAccueil);
            if (!$page) {
                $pageAccueil = false;
            }
        }

        if ($pageAccueil) {
            $pageUrl = \Tools::makeLink('affiliation', 'index');

            $this->view->set('title', __('Accueil'))
                ->set('page_url', $pageUrl)
                ->set('seo_description', $page['seo_description'])
                ->set('seo_tags', $page['seo_tags']);

            //no header and no footer
            if (!$page['display_header']) {
                $this->view->set('display_header', false);
            }
            if (!$page['display_footer']) {
                $this->view->set('display_footer', false);
            }
            if (!$page['display_menu']) {
                $this->view->set('display_menu', false);
            }
            if ($page['disable_indexation']) {
                $this->view->set('robots', 'noindex');
            }

            $datasPage = [
                'page' => $page,
                'id_page' => $page['id_page'],
                'permalink_page' => $page['permalink'],
            ];

            if (isset($page['version']) && $page['version'] == 2) {
                Assets::addCssV5(Assets::CSS_TYPE_BASE, 'site/composev2.css');
                Assets::addCss('themes/' . THEME . '/css/composev2.css', null);

                $this->view->set('page_v2', true);
                $this->view->set('id_theme', $page['id_theme']);

                $displayPage = $this->get(BuilderPages::class)->displayObjet($datasPage);
            } else {
                Assets::addCssV5(Assets::CSS_TYPE_BASE, 'site/compose.css');
                Assets::addCssV5(Assets::CSS_TYPE_COMPONENTS, '_bootstrap-image-gallery-site.css');

                Assets::addJs('https://npmcdn.com/masonry-layout@4.0/dist/masonry.pkgd.min.js');
                Assets::addJs('site/ppages2.js');
                Assets::addInlineJs(\Tools::getJsGallery('site', false));

                $displayPage = $this->get(\Pages_Elements::class)->afficher_page($datasPage);
            }

            if (is_array($displayPage)) {
                $output .= $displayPage['output'];
                Assets::addInlineJs($displayPage['js']);
                if (isset($displayPage['banniere']) and $displayPage['banniere']) {
                    $this->view->set('banniere', $displayPage['banniere']);
                }
                if (isset($displayPage['css']) && $displayPage['css']) {
                    $this->view->set('theme_design_css', '<style type="text/css">' . $displayPage['css'] . '</style>');
                }
            } else {
                $output .= $displayPage;
            }

            $output = $this->get(\Csrf::class)->replaceForm($output);

            //handle javascript
            if ($page['additionnal_js_head']) {
                $jsHead = stripslashes($page['additionnal_js_head']);
            }
            if ($page['additionnal_js']) {
                $js .= stripslashes($page['additionnal_js']);
            }

            //replace [[PRENOM]], [[EMAIL]], etc.
            $replaceVars = $this->get(PagesService::class)->replaceVars($output, $js, $jsHead);
            $output = $replaceVars['output'];
            $js = $replaceVars['js'];
            $jsHead = $replaceVars['jsHead'];

            if ($page['display_sidebar'] and $page['version'] != 2) {
                $output = '
                <div class="container" id="main-container">
                    <div class="row">
                        <div class="col-md-8" id="page-compose">
                            ' . $output . '
                        </div>
                        <div class="col-md-4 sidebar">
                            ' . $this->get(\Sidebar::class)->displaySidebar() . '
                        </div>
                    </div>
                </div>';
            } else {
                $output = '
                <div id="main-container">
                    <div id="page-compose">
                        ' . $output . '
                    </div>
                </div>';
            }

            $integrations = $this->get(\Popups_Integrations::class)->getIntegrationsByPage($page['id_page']);
            if ($integrations) {
                $integrationsJs = $this->get(\Popups_Integrations::class)->makeJs($integrations);
                if ($integrationsJs) {
                    Assets::addInlineJs($integrationsJs);
                }
            }

            $facebookService = $this->get(FacebookPixelsService::class);
            $facebookPixel = $facebookService->getFacebookPixelByPage($page);
            if ($facebookPixel) {
                $this->view->set('facebook_pixel_id', $facebookPixel['facebook_pixel_id']);
                $this->view->set('facebook_pixel_event', $facebookPixel['facebook_pixel_event']);
            }

            $gaService = $this->get(GoogleAnalyticsService::class);
            $googleAnalyticsId = $gaService->getGoogleAnalyticsByPage($page);
            if ($googleAnalyticsId) {
                $this->view->set('google_analytics_id', $googleAnalyticsId);
                $this->view->set('google_optimize_id', $page['google_optimize_id']);
            }

            Assets::addInlineJs($js);
            Assets::addInlineJs($jsHead, true);

            $this->view->set('id_page', $page['id_page'])
                ->render($output);
        } else {
            $this->view->set('title', __('Tableau de bord'));
            $output = $this->get(\Aff_Display::class)->aff_afficher_affiliation();
            $this->displayOutput($output);
        }
    }

    #[Route(path: '/affiliation/invoices/', name: 'affiliation_invoices')]
    public function affiliationLbInvoices()
    {
        if (MASTER_UNIQID != $_SESSION['client_uniqid']) {
            $this->display404();
        }

        $affiliationInvoices = $this->get(EntityManager::class)->getRepository(AffiliationInvoice::class)->findByAffiliationAffilie($_SESSION['id_affilie']);
        $affInvoicesOutputHtml = $this->parser
            ->set('affiliationInvoices', $affiliationInvoices)
            ->set('libelleStatusChoices', AffiliationInvoice::getStatusChoices())
            ->parsePHP(VIEWS_PATH . '/app/affiliationlb/invoices.php');

        $output = $this->parser
            ->set('affInvoicesOutputHtml', $affInvoicesOutputHtml)
            ->parsePHP(VIEWS_PATH . '/affiliation/invoices.php');

        $this->displayOutput($output);
    }

    #[Route(path: '/affiliation/invoice/add/', name: 'affiliation_invoice_add')]
    public function affiliationLbInvoiceAdd()
    {
        if (MASTER_UNIQID != $_SESSION['client_uniqid']) {
            $this->display404();
        }

        $this->view->set('title', _('Ajouter une facture'));
        $addInvoiceForm = $this->get(AffiliationLearnyBoxService::class)->getPaymentPageOutputHtml(false, true);

        $output = $this->parser
            ->set('addInvoiceForm', $addInvoiceForm)
            ->parsePHP(VIEWS_PATH . '/affiliation/invoice_add.php');

        $this->displayOutput($output);
    }

    #[Route(path: '/affiliation/settings/payment/', name: 'affiliation_settings_payment')]
    public function affiliationPaymentSettings()
    {
        if (MASTER_UNIQID != $_SESSION['client_uniqid']) {
            $this->display404();
        }

        $paymentSettingsForm = $this->get(AffiliationLearnyBoxService::class)->getPaymentSettingsFormOutputHtml(true);

        $output = $this->parser
            ->set('addInvoiceForm', $paymentSettingsForm)
            ->parsePHP(VIEWS_PATH . '/affiliation/invoice_add.php');

        $this->displayOutput($output);
    }
}
