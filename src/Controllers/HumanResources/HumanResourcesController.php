<?php

namespace Learnybox\Controllers\HumanResources;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\HumanResources\Department;
use Learnybox\Entity\User\User;
use Learnybox\Helpers\Assets;
use Learnybox\Entity\HumanResources\Collaborator\CollaboratorDocument;
use Learnybox\Entity\HumanResources\Collaborator\CollaboratorDocumentInvoice;
use Learnybox\Entity\HumanResources\Collaborator\CollaboratorVacationCreditRecovery;
use Learnybox\Services\HumanResources\Collaborator\CollaboratorService;
use Learnybox\Services\HumanResources\Collaborator\CollaboratorVacationCreditRecoveryService;
use Learnybox\Services\Users\UsersService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class HumanResourcesController
 * @package Learnybox\Controllers\HumanResources
 */
class HumanResourcesController extends AbstractHumanResourcesController
{
    #[Route(path: '/hr/reset/', name: 'hr_reset')]
    public function reset(): void
    {
        UsersService::displayResetPassword();
    }

    #[Route(path: '/hr/reset_password/', name: 'hr_reset_password')]
    public function resetPassword(): void
    {
        UsersService::displayResetPasswordConfirm();
    }

    #[Route(path: '/hr/logout/', name: 'hr_logout')]
    public function logout(): void
    {
        UsersService::logout();
    }

    #[Route(path: '/hr/index/', name: 'hr_index')]
    public function index()
    {
        $em = $this->get(EntityManager::class);
        $documentsToSign = $em->getRepository(CollaboratorDocument::class)->findBy(['collaborator' => $this->collaborator, 'signedDocument' => null, 'toSign' => 1]);
        $totalInvoicesPaid = $em->getRepository(CollaboratorDocumentInvoice::class)->sumAllPaidByCollaborator($this->collaborator);
        $totalInvoicesPaidLast30Days = $em->getRepository(CollaboratorDocumentInvoice::class)->sumAllPaidLast30DaysByCollaborator($this->collaborator);
        $totalInvoicesPaidCurrentYear = $em->getRepository(CollaboratorDocumentInvoice::class)->sumAllPaidCurrentYearByCollaborator($this->collaborator);
        $lastInvoice = $em->getRepository(CollaboratorDocument::class)->findOneBy(['collaborator' => $this->collaborator, 'type' => 'invoice'], ['uploadedAt' => 'DESC']);
        $canInvoice = $this->get(CollaboratorService::class)->canInvoice($this->collaborator);

        $collaboratorVacationCreditRecoveryService = $this->get(CollaboratorVacationCreditRecoveryService::class);
        $vacationsCreditRecovery = $collaboratorVacationCreditRecoveryService->getRepository()->sumCreditByCollaborator($this->collaborator);

        $departments = $em->getRepository(Department::class)->findAll();

        Assets::addJs('common/moment.min.js');
        Assets::addJs('common/moment-with-locales.min.js');
        Assets::addJs('common/fullcalendar-3.9.0.min.js');
        Assets::addJs('hr/collaborator_vacation.js');
        Assets::addCssV5(Assets::CSS_TYPE_VENDORS, 'fullcalendar-3.9.0.min.css');

        // LearnyChristmas
//        $giftParticipants = [
//            591125 => 10407531,
//            3146893 => 6894420,
//            3410403 => 7789931,
//            3436082 => 13764019,
//            6894420 => 11656791,
//            7570724 => 12621490,
//            7789931 => 3436082,
//            7831858 => 591125,
//            8138118 => 3410403,
//            10407531 => 7831858,
//            11656791 => 11715851,
//            11715851 => 3146893,
//            12621490 => 12621794,
//            12621794 => 8138118,
//            13764019 => 7570724
//        ];

        $giftRecipient = null;
//        if (!empty($giftParticipants[$_SESSION['user_id']])) {
//            $giftRecipient = $this->get(EntityManager::class)->getRepository(User::class)->find($giftParticipants[$_SESSION['user_id']]);
//        }

        $output = $this->parser->set('collaborator', $this->collaborator)
            ->set('departments', $departments)
            ->set('collaborator', $this->collaborator)
            ->set('totalInvoicesPaid', $totalInvoicesPaid)
            ->set('totalInvoicesPaidLast30Days', $totalInvoicesPaidLast30Days)
            ->set('totalInvoicesPaidCurrentYear', $totalInvoicesPaidCurrentYear)
            ->set('documentsToSign', $documentsToSign)
            ->set('lastInvoice', $lastInvoice)
            ->set('canInvoice', $canInvoice)
            ->set('avatar', $this->get(UsersService::class)->getAvatar())
            ->set('vacationsCreditRecovery', $vacationsCreditRecovery)
            ->set('giftRecipient', $giftRecipient)
            ->parsePHP(VIEWS_PATH . '/hr/index.php');

        $this->displayOutput($output);
    }
}
