<?php

namespace Learnybox\Controllers\Conference;

use Symfony\Component\Routing\Attribute\Route;

/**
 * Class LinkConferenceController
 * @package Learnybox\Controllers\Conference
 */
class LinkConferenceController extends AbstractConferenceController
{
    #[Route(path: '/conference/link/{cid}/{link_rid}/', name: 'conference_link')]
    public function link()
    {
        $fatalError = '';
        $cid = '';
        $linkRid = '';

        $explode = explode('/', $this->param);
        if (isset($explode[0])) {
            $cid = $explode[0];
        }
        if (isset($explode[1])) {
            $linkRid = $explode[1];
        }

        $cid = filter_var($cid, FILTER_SANITIZE_SPECIAL_CHARS);
        if (!$cid) {
            $fatalError = __('Erreur : Accès non autorisé.');
        } else {
            if (511 == $_SESSION['id_client']) {
                $conference = $this->get(\Webinar_Conferences::class)->getConferenceByRandomIdWoClient($cid);
            } else {
                $conference = $this->get(\Webinar_Conferences::class)->getConferenceByRandomId($cid);
            }

            if (!$conference) {
                $fatalError = __('Erreur : ce webinaire n\'existe pas.');
            }
        }

        if (!$linkRid) {
            $fatalError = __('Accès non autorisé.');
        } else {
            $linkRid = filter_var($linkRid, FILTER_SANITIZE_SPECIAL_CHARS);
            if (!$linkRid) {
                $fatalError = __('Accès non autorisé.');
            } else {
                //verification du randomid dans la table conference
                $link = $this->get(\Webinar_Textes::class)->getLienByRandomIdWoClient($linkRid);
                if (!$link) {
                    $fatalError = __('Accès non autorisé.');
                } else {
                    //mise à jour du lien
                    $test = $this->get(\Webinar_Textes::class)->update_lien($conference['id_conference'], 'lien', $linkRid, $link['id_client']);

                    header('Location: ' . $link['lien']);
                    exit();
                }
            }
        }

        if ($fatalError) {
            $output = '
        		<div class="container">
        			<div class="col-md-12">
        				<div class="alert alert-danger" style="margin-top:150px; text-align:center">
        				    ' . $fatalError . '
        				</div>
        			</div>
        		</div>';

            return $this->displayOutput($output);
        }

        return $this->display404();
    }

    #[Route(path: '/conference/linkban/{cid}/', name: 'conference_link_ban')]
    public function linkban()
    {
        $fatalError = '';

        $cid = filter_var($this->param, FILTER_SANITIZE_SPECIAL_CHARS);
        if (!$cid) {
            $fatalError = __('Erreur : Accès non autorisé.');
        } else {
            $conference = $this->get(\Webinar_Conferences::class)->getConferenceByRandomId($cid);
            if (!$conference) {
                $fatalError = __('Erreur : ce webinaire n\'existe pas.');
            }
        }

        //mise à jour du lien de la banniere
        if (!$fatalError) {
            $this->get(\Webinar_Textes::class)->update_lien($conference['id_conference'], 'banniere');
            header('Location: ' . $conference['link_banniere']);
            exit();
        }

        if ($fatalError) {
            $output = '
        		<div class="container">
        			<div class="col-md-12">
        				<div class="alert alert-danger" style="margin-top:150px; text-align:center">
        				    ' . $fatalError . '
        				</div>
        			</div>
        		</div>';

            return $this->displayOutput($output);
        }

        return $this->display404();
    }
}
