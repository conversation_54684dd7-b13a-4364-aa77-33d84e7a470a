<?php

namespace Learnybox\Controllers;

use Learnybox\Services\Logger\LoggerService;
use Learnybox\Services\Monolog\Logger;
use Learnybox\Services\SSO\Saml2Service;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class SSOController
 * @package Learnybox\Controllers
 */
class SSOController extends AbstractController
{
    /**
     * @var Saml2Service
     */
    private $saml2Service;

    /**
     * @param string $action
     * @param string $param
     * @param string $query
     * @return mixed|void
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function dispatch(string $action = '', string $param = '', string $query = '')
    {
        $this->saml2Service = $this->get(Saml2Service::class);

        if (method_exists($this, $action)) {
            return $this->$action();
        }

        echo __('Cette action n\'existe pas.');
        exit();
    }

    #[Route(path: '/app/sso/test/', name: 'sso_test')]
    public function test()
    {
        $this->saml2Service->login(true);
        return;
    }

    #[Route(path: '/sso/login/', name: 'sso_login')]
    public function login()
    {
        $this->saml2Service->login();
        return;
    }

    #[Route(path: '/sso/acs/', name: 'sso_acs')]
    public function acs()
    {
        $processResponse = $this->saml2Service->processResponse();
        if (!$processResponse['valid']) {
            echo $processResponse['message'];
        } else {
            echo "OK";
        }
        exit();
    }

    #[Route(path: '/sso/logout/', name: 'sso_logout')]
    public function logout()
    {
        LoggerService::log(Logger::INFO, 'Call /sso/logout/');
        $this->saml2Service->logout();
    }

    #[Route(path: '/sso/slo/', name: 'sso_single_logout_service')]
    public function slo()
    {
        LoggerService::log(Logger::INFO, 'Call /sso/slo/');
        $logout = $this->saml2Service->processSLO();
        if (!$logout['valid']) {
            echo $logout['message'];
        } else {
            echo "OK";
        }
        exit();
    }

    #[Route(path: '/sso/', name: 'sso_metadatas')]
    public function metadatas()
    {
        $this->saml2Service->getMetaDatas();
        return;
    }
}
