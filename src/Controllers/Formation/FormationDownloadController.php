<?php

namespace Learnybox\Controllers\Formation;

use Symfony\Component\Routing\Attribute\Route;

/**
 * Class FormationDownloadController
 * @package Learnybox\Controllers\Formation
 */
class FormationDownloadController extends AbstractFormationController
{
    #[Route(path: '/formation/download/{slug}/', name: 'formation_download')]
    public function download()
    {
        $error = '';

        $file_rid = filter_var($this->param, FILTER_SANITIZE_SPECIAL_CHARS);
        if (!$file_rid) {
            $error = __('Erreur : aucun fichier spécifié');
        } else {
            $element = $this->get(\Formation_PagesElements::class)->getElementByName($file_rid);
            if (!$element) {
                $error = __('Erreur : fichier introuvable.');
            } else {
                $link = $element['urls'] ?: $element['url_webm'];
                if (!$link) {
                    $error = __('Erreur : fichier introuvable.');
                }
            }
        }

        if ($error) {
            $this->displayError($error);
            return;
        }

        $this->get(\Formation_PagesElements::class)->update_element_duree($element['ID'], ++$element['duree']);

        $link = str_replace('&#39;', '\'', $link);
        $link = str_replace('&#38;', '&', $link);

        if (false !== strpos($link, '.php') or false !== strpos($link, '.htaccess') or false !== strpos($link, '.ini')) {
            $this->display404();
            exit();
        }

        if (false === strpos($link, HTTP_DOMAINE_NAME_FORMATION) and false !== strpos($link, $_SESSION['client_uniqid'] . '.' . LB_COM)) {
            $link = str_replace('https://' . $_SESSION['client_uniqid'] . '.' . LB_COM, HTTP_DOMAINE_NAME_FORMATION, $link);
        }

        // verif si lien est un lien dropbox pour mettre directement le lien de direct download
        $pattern = "#(www\.|)dropbox\.com#";
        if (preg_match($pattern, $link)) {
            $parseUrl = parse_url($link);
            $parseUrl['host'] = "dl.dropboxusercontent.com";
            $link = \Tools::unparse_url($parseUrl);
        }

        header('Location: ' . $link);
        exit();
    }
}
