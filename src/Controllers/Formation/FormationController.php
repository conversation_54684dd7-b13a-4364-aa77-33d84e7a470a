<?php

namespace Learnybox\Controllers\Formation;

use Learnybox\Controllers\Site\SitePageController;
use Learnybox\Controllers\Site\SiteTunnelPageController;
use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Formation\Academy\FormationAcademy;
use Learnybox\Entity\Formation\Formation;
use Learnybox\Entity\Formation\Forum\FormationForum;
use Learnybox\Entity\Formation\Progression\FormationProgression;
use Learnybox\Entity\Formation\Progression\FormationProgressionConfirm;
use Learnybox\Enums\Formation\FormationProductTypeEnum;
use Learnybox\Enums\Formation\FormationAcademyEnum;
use Learnybox\Enums\Themes\MasterThemeEnum;
use Learnybox\Helpers\Assets;
use Learnybox\Helpers\ColorConverter;
use Learnybox\Helpers\Colors;
use Learnybox\Helpers\RouterHelper;
use Learnybox\Helpers\TemplateHelper;
use Learnybox\Renderers\Academy\AcademyFormationRenderer;
use Learnybox\Renderers\Builder\Elements\ProgressionRenderer;
use Learnybox\Services\Builder\BuilderFonts\BuilderFonts;
use Learnybox\Services\Formations\FormationAcademyService;
use Learnybox\Services\Formations\FormationsNewsService;
use Learnybox\Services\Pages\Users\UsersConnexionService;
use Learnybox\Services\Pages\Users\UsersResetService;
use Learnybox\Services\RightsService;
use Learnybox\Services\Users\UsersService;
use Learnybox\Services\Views\ViewFormation;
use Learnybox\Services\Views\ViewFormation2;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class FormationController
 * @package Learnybox\Controllers\Formation
 */
class FormationController extends AbstractFormationController
{
    #[Route(path: '/formation/index/', name: 'formation_index')]
    public function index(): void
    {
        $academy = $this->get(FormationAcademyService::class)->getAcademy();
        if (!$academy) {
            $this->get(\Utilisateurs::class)->checkIfConnected();

            if (!empty($this->formation)) {
                $this->home();
                return;
            }

            $query = $_SERVER['QUERY_STRING'] ?? [];
            parse_str($query, $data);
            header('Location: ' . RouterHelper::generate('formation_home', $data));
            exit();
        }

        $this->setViewForAcademy($academy);

        if (!$academy->isPublic()) {
            $this->get(\Utilisateurs::class)->checkIfConnected();
        }

        $query = [];
        if ($this->query !== null && $this->query !== '') {
            parse_str($this->query, $query);
        }

        $academyFormations = $this->get(AcademyFormationRenderer::class)->renderAcademyFormations($academy, $query);

        if (!isset($_SESSION['user']) or !$_SESSION['user']) {
            $userName = '';
            $avatar = Assets::getImageUrl('user/default_avatar.png');
        } else {
            $userName = $_SESSION['user']['fname'] . ' ' . $_SESSION['user']['lname'];
            $avatar = $this->get(UsersService::class)->getAvatar($_SESSION['user']['email'], 32);
        }

        $isEditor = RightsService::isEditor();
        $accessLevel = FormationAcademyEnum::getAccessLevel();
        $accessLevelName = FormationAcademyEnum::getAccessLevelName($accessLevel);

        $accessLevels = [
            FormationAcademyEnum::ACCESS_ADMIN->value => FormationAcademyEnum::getAccessLevelName(FormationAcademyEnum::ACCESS_ADMIN->value),
            FormationAcademyEnum::ACCESS_PUBLIC->value => FormationAcademyEnum::getAccessLevelName(FormationAcademyEnum::ACCESS_PUBLIC->value),
        ];

        $academyMenu = $academy->getMenu();
        if ($accessLevel === FormationAcademyEnum::ACCESS_PUBLIC->value) {
            $academyMenu = [];
        }

        $output = TemplateHelper::render('formations/academy/index.html.twig', [
            'academy' => $academy,
            'name' => $academy->getName() ?: __('Mon académie'),
            'academyFormations' => $academyFormations,
            'menu' => array_keys(FormationAcademyService::getAcademyMenu()),
            'academyMenu' => $academyMenu,
            'isEditor' => $isEditor,
            'action' => 'academy',
            'content' => $academyFormations,
            'userName' => $userName,
            'avatar' => $avatar,
            'additionalContent' => '',
            'accessLevel' => $accessLevel,
            'accessLevelName' => $accessLevelName,
            'accessLevels' => $accessLevels,
            'inEditor' => false,
        ]);
        $output = $this->defineOutputAsUserInterface($output);

        if ($isEditor) {
            $modalEditAcademy = TemplateHelper::render('pages/academy/modal-edit-academy.html.twig');
            Assets::addInlineJs($modalEditAcademy);
        }

        $this->displayOutput($output);
    }

    private function setViewForAcademy(FormationAcademy $academy): void
    {
        $this->setDatas();

        if ($this->view instanceof ViewFormation) {
            $newView = $this->get(ViewFormation2::class);
            Assets::reset($this->get(FormationController::class));
            $newView->set($this->view->getData());
            $this->view = $newView;
        }

        $this->view->set('title', __('Académie'));
        $this->view->set('action', $this->action);
        $this->view->set('param', $this->param);
        $this->view->set('theme', 'focuson');
        $this->view->set('idformation', 0);
        $this->view->remove('formation');

        if ($academy->getName()) {
            $this->view->set('title', $academy->getName());
        }

        Assets::addCssV5(Assets::CSS_TYPE_PAGES, 'formation/academy.css');
        Assets::addJs('formation/academy.js');

        // Add custom board color
        if ($academy->getMainColor()) {
            $color = $academy->getMainColor();
            $bgColor = ColorConverter::hexaToRgba($color, .1);
            $hoverColor = Colors::adjustBrightness($color, 50);
            $buttonTextColor = Colors::getTextColorForBackground($color);

            Assets::addInlineJs('
            <style id="academy-css">
            :root {
                --academy-main-color: ' . $color . ';
                --academy-bg-color: ' . $bgColor . ';
                --academy-hover-color: ' . $hoverColor . ';
                --academy-button-text-color: ' . $buttonTextColor . ';
            }
            </style>', true);
        }

        if ($academy->getFont()) {
            $fontFamily = $this->container->get(BuilderFonts::class)->getFontFamily($academy->getFont());
            if ($fontFamily) {
                Assets::addInlineJs('
                <style id="academy-font-css">
                .academy-container, .academy-container h2, .academy-container h4, .academy-container p, .academy-container .btn:not(.btn-edit-formation, .btn-build-formation)  { font-family: ' . $fontFamily . '!important; }
                </style>', true);
            }
        }
    }

    #[Route(path: '/formation/signin/', name: 'formation_signin')]
    public function signin()
    {
        if (isset($_GET['ct_token'])) {
            $this->get(\Utilisateurs::class)->autologin();

            header('Location: ' . \Tools::makeLink('formation', 'index'));
            exit();
        }

        if (isset($_SESSION['email']) && RightsService::hasAccess(UNIVERSE_FORMATION_DEFAULT)) {
            //verification que cet utilisateur a accès à l'admin
            $verifUser = $this->get(\Utilisateurs::class)->getUserByEmail($_SESSION['email']);
            if ($verifUser) {
                header('Location: ' . \Tools::makeLink('formation', 'index'));
                exit();
            }
            header('Location: ' . \Tools::makeLink('site', 'accueil'));
            exit();
        }

        $pageConnexion = $this->get(\Reglages::class)->appGetParametreByNameAndDomaine('connexion', $_SESSION['actual_domaine_id']);
        if ($pageConnexion and $pageConnexion['value']) {
            //tunnel
            if (false !== strpos($pageConnexion['value'], '-')) {
                $explode = explode('-', $pageConnexion['value']);
                $id_tunnel = $explode[0];
                $id_page = $explode[1];

                $tunnel_page = $this->get(\TunnelsPages::class)->getPageById($id_page, $id_tunnel);
                if ($tunnel_page and $tunnel_page['publication'] == 'encours') {
                    Assets::reset();
                    $this->get(SiteTunnelPageController::class)->displayTunnelPage($tunnel_page['permalink'], $tunnel_page);
                    exit();
                }
            } else {
                $page = $this->get(\Pages::class)->adminGetPageById($pageConnexion['value']);
                if ($page and $page['publiee']) {
                    Assets::reset();
                    $this->get(SitePageController::class)->displayPage($page['permalink'], $page);
                    exit();
                }
            }
        }

        if (isset($_SESSION['success-message'])) {
            unset($_SESSION['success-message']);
        }
        if (isset($_SESSION['error-message'])) {
            unset($_SESSION['error-message']);
        }

        $defaultTitle = '';
        if (isset($_SESSION['formation']) and $_SESSION['formation']) {
            $defaultTitle = $_SESSION['formation']['nomformation'];
        }
        $this->get(UsersConnexionService::class)->render($defaultTitle);
    }

    #[Route(path: '/formation/reset/', name: 'formation_reset')]
    public function reset(): void
    {
        $this->get(UsersResetService::class)->render();
    }

    #[Route(path: '/formation/reset_password/', name: 'formation_reset_password')]
    public function resetPassword(): void
    {
        UsersService::displayResetPasswordConfirm();
    }

    #[Route(path: '/formation/logout/', name: 'formation_logout')]
    public function logout(): void
    {
        UsersService::logout();
    }

    #[Route(path: '/formation/home/', name: 'formation_home')]
    public function home()
    {
        if (RightsService::isUser()) {
            $userFormation = $this->get(\Formation_Membres::class)->getUserFormation($this->idformation);
            if (!$userFormation or !$userFormation['active']) {
                $output = '
        			<div class="alert alert-danger" style="margin:75px 20px;">
        				<i class="fa fa-frown-o" style="font-size:44px"></i>
                        ' . __('Oups, cette formation n\'est pas accessible.') . '<br>
        				' . __('Choisissez une autre formation en cliquant sur la flèche à côté du nom de votre formation depuis le menu utilisateur.') . '
        			</div>';

                $this->displayOutput($output);
                return;
            }
        }

        if (!isset($this->formation) || !$this->formation) {
            $output = '
        			<div class="alert alert-danger" style="margin:75px 20px;">
        				<i class="fa fa-frown-o" style="font-size:44px"></i>
                        ' . __('Oups, cette formation n\'est pas accessible.') . '<br>
        				' . __('Choisissez une autre formation en cliquant sur la flèche à côté du nom de votre formation depuis le menu utilisateur.') . '
        			</div>';

            $this->displayOutput($output);
            return;
        }

        if (!empty($this->formation['id_page_accueil']) && 701 != $_SESSION['id_client']) {
            $this->accueil($this->idformation, $this->formation['id_page_accueil']);
            return;
        }

        if ($this->formation['product_type'] == FormationProductTypeEnum::COMMUNITY) {
            $forum = $this->get(EntityManager::class)->getRepository(FormationForum::class)->findOneBy(['idformation' => $this->formation['idformation']]);
            if ($forum) {
                header('Location: ' . \Tools::makeLink('formation', 'forum', $forum->getUniqid()));
                exit();
            }
        }

        $output = '';

        $formationNews = FormationsNewsService::displayLastNews($this->idformation);
        if ($formationNews) {
            $output = $formationNews['output'];
            Assets::addInlineJs($formationNews['js']);
            Assets::addJs('formation/formation_news.js');
        }

        if ($this->theme == 'focuson') {
            if (isset($this->formation['id_master_theme']) and $this->formation['id_master_theme'] and isset($this->formation['id_theme']) and $this->formation['id_theme']) {
                $theme = $this->get(\Builder_Themes::class)->getThemeById($this->formation['id_theme']);
                if ($theme and $theme['design']) {
                    $design = json_decode($theme['design'], true);
                    if (isset($design['fonts']) and $design['fonts']) {
                        $output .= $this->get(\Builder_Fonts::class)->getFontsHeader($design['fonts']);
                    }
                }
            }
        }

        if ($this->formation && isset($this->formation['id_master_theme']) && $this->formation['id_master_theme'] >= MasterThemeEnum::MASTER_THEME_6) {
            $element = [
                'ID' => 1
            ];
            $progression = eden()->Formation_Methode()->getInfosProgressionProgram($this->idformation);
            $progressionTemplate = $this->get(ProgressionRenderer::class)->renderElement($element, 'template2', null, $progression, 'btn_main_color0', '');
            $avatar = $this->get(UsersService::class)->getFormationAvatar($_SESSION['email'], 120);

            $sommaireTemplate = $this->get(\Formation_Formation::class)->DisplayModulesAccueil();
            $formations = $this->get(EntityManager::class)->getRepository(Formation::class)->getAllFormationsByUser();

            $formationData = [];
            foreach ($formations as $formation) {
                $formationProgression = $this->get(\Formation_Methode::class)->getProgressionProgram($formation->getIdformation());

                $formationData[] = [
                    'name' => $formation->getNomformation(),
                    'thumbnail' => $formation->getThumbnail(),
                    'vignette' => $formation->getVignette(),
                    'progression' => $formationProgression,
                    'idformation' => $formation->getIdFormation()
                ];
            }

            $listeFormations = TemplateHelper::render('app/builder/elements/formationsuser_template3.html.twig', [
                'formations' => $formationData,
                'displayTitle' => true
            ]);

            Assets::addJs('formation/jquery.multilevelpushmenuv3.js');
            Assets::addJs('formation/timeme.js');
            Assets::addJs('formation/time_tracking.js');
            Assets::addJs('formation/display_elements_v2.js');
            Assets::addJs('common/load-image.js');
            Assets::addJs('common/jquery/jquery.raty.min.js');

            Assets::addCss('themes/focuson/css/home_page.css', null);
            Assets::addCss('themes/focuson/css/sequence.css', null);
            Assets::addCss('themes/focuson/css/sommaire.css', null);

            $output .= TemplateHelper::render('app/formations/default_home_page.html.twig', [
                'formation' => $this->formation,
                'avatar' => $avatar,
                'progressionTemplate' => $progressionTemplate,
                'sommaireTemplate' => $sommaireTemplate,
                'listeFormations' => $listeFormations
            ]);
        } else {
            $output .= $this->parser->set('array_formations', $this->array_formations)
                ->set('formation', $this->formation)
                ->set('idformation', $this->idformation)
                ->parsePHP(THEMES_PATH . '/' . $this->theme . '/index.php');
        }

        Assets::addJs('common/jquery/jquery.raty.min.js');
        $js = '';

        if ((isset($_POST['form_action']) && 'connexion' == $_POST['form_action']) || (isset($_SESSION['freeSubscription']) && $_SESSION['freeSubscription'])) {
            unset($_SESSION['freeSubscription']);

            $userInfos = [
                'user_id' => $_SESSION['user_id'],
                'fname' => $_SESSION['fname'],
                'lname' => $_SESSION['lname'],
                'email' => $_SESSION['email'],
                'idformation' => $this->idformation,
                'formation' => $this->formation,
            ];

            $triggerLogin = $this->get(\Formation_BadgesTriggers::class)->executeTrigger('log_in', 0, $this->idformation, $userInfos);
            if (isset($triggerLogin['add_badge'])) {
                $js .= $this->get(\Formation_BadgesUsers::class)->displayBadgesNonLusByUser($this->idformation);
            }
            if (isset($triggerLogin['array_outputs']) and $triggerLogin['array_outputs']) {
                $arrayOutputs = [];
                foreach ($triggerLogin['array_outputs'] as $arrayOutput) {
                    array_push($arrayOutputs, $arrayOutput);
                }
                $js .= $this->get(\Formation_BadgesUsers::class)->displayReglesOutputs($arrayOutputs);
            }
        }

        $this->displayOutput($output, $js);
    }

    public function accueil($idformation = 0, $idpage = 0)
    {
        if (!$idpage) {
            $this->home();
            return;
        }

        $content = '';
        Assets::addJs('formation/enregistrements.js');
        Assets::addJs('https://npmcdn.com/masonry-layout@4.0/dist/masonry.pkgd.min.js');
        Assets::addJs('common/load-image.js');
        Assets::addJs('common/jquery/jquery.raty.min.js');
        Assets::addInlineJs(\Tools::getJsGallery('formation'));
        Assets::addCss('themes/' . $this->theme . '/css/sequence.css', null);
        Assets::addCss('themes/' . $this->theme . '/css/sommaire.css', null);

        //mise à jour en session
        if (!isset($_SESSION['idformation']) or !isset($_SESSION['formation']) or $_SESSION['idformation'] != $idformation) {
            $_SESSION['idformation'] = $idformation;

            $formation = $this->get(\Formation_Formation::class)->getFormationById($idformation);
            if ($formation) {
                $_SESSION['formation'] = $formation;
            }
        }

        if ($this->formation['product_type'] === FormationProductTypeEnum::COMMUNITY) {
            $forum = $this->get(EntityManager::class)->getRepository(FormationForum::class)->findOneBy(['idformation' => $this->formation['idformation']]);
            if ($forum) {
                header('Location: ' . \Tools::makeLink('formation', 'forum', $forum->getUniqid()));
                exit();
            }
        }

        $this->parser->set('idformation', $idformation);
        $this->view->set('idformation', $idformation);
        $this->view->set('title', $this->formation['nomformation']);

        $displayHome = $this->get(\Formation_Formation::class)->afficher_accueil($idformation, $idpage);
        if (!$displayHome) {
            if ($_SESSION['formation']['date_start'] and $_SESSION['formation']['date_start'] != '0000-00-00 00:00:00' and $_SESSION['formation']['date_start'] > date('Y-m-d H:i:s')) {
                $content .= '
        			<div class="alert alert-info" style="margin:75px 20px; text-align:center">
        				<i class="fa fa-history" style="font-size:70px"></i><br>
                        ' . __('Cette formation ne sera accessible qu\'à partir du') . ' ' . datetimefr($_SESSION['formation']['date_start']) . '.<br>
        				' . __('Choisissez une autre formation dans le menu ci-contre ou retournez à l\'accueil.') . '<br><br>
        				<a class="btn btn-primary" href="' . \Tools::getLink($_SESSION['actual_domaine_id'], 'site', 'formation') . '"><i class="fa fa-home"></i> ' . __('Retour à l\'accueil') . '</a>
        			</div>';
            } else {
                $content .= '
        			<div class="alert alert-danger" style="margin:75px 20px; text-align:center">
        				<i class="fa fa-frown-o" style="font-size:70px"></i><br>
                        ' . __('Oups, cette formation n\'est pas accessible.') . '<br>
        				' . __('Choisissez une autre formation dans le menu ci-contre ou retournez à l\'accueil.') . '<br><br>
        				<a class="btn btn-danger" href="' . \Tools::getLink($_SESSION['actual_domaine_id'], 'site', 'formation') . '"><i class="fa fa-home"></i> ' . __('Retour à l\'accueil') . '</a>
        			</div>';
            }
        } else {
            if (is_array($displayHome)) {
                if (isset($displayHome['version']) and 2 == $displayHome['version']) {
                    return $this->accueilV2($displayHome);
                }

                Assets::addJs('formation/jquery.multilevelpushmenu.js');
                Assets::addJs('formation/timeme.js');
                Assets::addJs('formation/time_tracking.js');
                Assets::addJs('formation/display_elements.js');

                $content = $displayHome['output'];
                Assets::addInlineJs($displayHome['js']);
            } else {
                $content = $displayHome;
            }
        }

        $formationNews = FormationsNewsService::displayLastNews($idformation);
        if ($formationNews) {
            $content .= $formationNews['output'];
            Assets::addInlineJs($formationNews['js']);
            Assets::addJs('formation/formation_news.js');
        }

        $this->parser
            ->set('menu', '')
            ->set('position', 'hide')
            ->set('content', $content);

        if (is_file($path = THEMES_PATH . '/' . $this->theme . '/formation.php')) {
            $output = $this->parser->parsePHP($path);
        } else {
            $output = $this->parser->parsePHP(VIEWS_PATH . '/formation/formation.php');
        }

        $js = '';
        if ((isset($_POST['form_action']) && 'connexion' == $_POST['form_action']) || (isset($_SESSION['freeSubscription']) && $_SESSION['freeSubscription'])) {
            unset($_SESSION['freeSubscription']);

            $userInfos = [
                'user_id' => $_SESSION['user_id'],
                'fname' => $_SESSION['fname'],
                'lname' => $_SESSION['lname'],
                'email' => $_SESSION['email'],
                'idformation' => $idformation,
                'formation' => $_SESSION['formation'],
            ];

            $triggerLogin = $this->get(\Formation_BadgesTriggers::class)->executeTrigger('log_in', 0, $idformation, $userInfos);
            if (isset($triggerLogin['add_badge'])) {
                $js .= $this->get(\Formation_BadgesUsers::class)->displayBadgesNonLusByUser($_SESSION['idformation']);
            }
            if (isset($triggerLogin['array_outputs']) and $triggerLogin['array_outputs']) {
                $arrayOutputs = [];
                foreach ($triggerLogin['array_outputs'] as $arrayOutput) {
                    array_push($arrayOutputs, $arrayOutput);
                }
                $js .= $this->get(\Formation_BadgesUsers::class)->displayReglesOutputs($arrayOutputs);
            }
        }

        $this->displayOutput($output, $js);
    }

    public function accueilV2($datas)
    {
        $datasFormation = $datas['datas_formation'];

        Assets::addJs('formation/jquery.multilevelpushmenuv3.js');
        Assets::addJs('formation/timeme.js');
        Assets::addJs('formation/time_tracking.js');
        Assets::addJs('formation/display_elements_v2.js');
        Assets::addJs('common/load-image.js');
        Assets::addJs('common/jquery/jquery.raty.min.js');

        Assets::addCss('themes/' . $this->theme . '/css/sequence.css', null);
        Assets::addCss('themes/' . $this->theme . '/css/sommaire.css', null);

        if (isset($datasFormation['idpage'])) {
            $this->view->set('display_page', true);
        }

        $idformation = $datasFormation['idformation'];

        $this->parser->set('idformation', $idformation);
        $this->view->set('idformation', $idformation);
        $this->view->set('formation', $datasFormation['formation']);

        $content = $datas['output'];
        $js = $datas['js'];
        $jsHead = $datas['js_head'];

        $this->view->set('theme', $this->theme);

        if ($datas['css']) {
            $this->view->set('additionnal_css', $datas['css']);
        }
        if ($datas['css_files']) {
            $this->view->set('css_files', $datas['css_files']);
        }

        if (isset($datasFormation['nomformation'])) {
            $this->view->set('title', $datasFormation['nomformation']);
        }

        $formationNews = FormationsNewsService::displayLastNews($idformation);
        if ($formationNews) {
            $content .= $formationNews['output'];
            Assets::addInlineJs($formationNews['js']);
            Assets::addJs('formation/formation_news.js');
        }

        //get main content
        $this->parser
            ->set('menu', '')
            ->set('position', 'hide')
            ->set('content', $content);

        if (is_file($path = THEMES_PATH . '/' . $this->theme . '/page-full.php')) {
            $output = $this->parser->parsePHP($path);
        } else {
            $output = $this->parser->parsePHP(VIEWS_PATH . '/formation/formation.php');
        }

        if ((isset($_POST['form_action']) && 'connexion' == $_POST['form_action']) || (isset($_SESSION['freeSubscription']) && $_SESSION['freeSubscription'])) {
            unset($_SESSION['freeSubscription']);

            $userInfos = [
                'user_id' => $_SESSION['user_id'],
                'fname' => $_SESSION['fname'],
                'lname' => $_SESSION['lname'],
                'email' => $_SESSION['email'],
                'idformation' => $idformation,
                'formation' => $_SESSION['formation'],
            ];

            $triggerLogin = $this->get(\Formation_BadgesTriggers::class)->executeTrigger('log_in', 0, $idformation, $userInfos);
            if (isset($triggerLogin['add_badge'])) {
                $js .= $this->get(\Formation_BadgesUsers::class)->displayBadgesNonLusByUser($_SESSION['idformation']);
            }
            if (isset($triggerLogin['array_outputs']) and $triggerLogin['array_outputs']) {
                $arrayOutputs = [];
                foreach ($triggerLogin['array_outputs'] as $arrayOutput) {
                    array_push($arrayOutputs, $arrayOutput);
                }
                $js .= $this->get(\Formation_BadgesUsers::class)->displayReglesOutputs($arrayOutputs);
            }
        }

        Assets::addInlineJs($jsHead, true);
        $this->displayOutput($output, $js);
    }

    #[Route(path: '/formation/configuration/', name: 'formation_configuration')]
    #[Route(path: '/formation/configuration/{id}', name: 'formation_configuration_id', requirements: ['id' => '\d+'])]
    public function configuration()
    {
        $output = $this->parser->set('idformation', ($this->param ?: $this->idformation))
            ->parsePHP(FORMS_PATH . '/formation/configuration.php');
        $this->displayOutput($output);
    }

    #[Route(path: '/formation/formation/', name: 'formation_formation')]
    #[Route(path: '/formation/formation/{id}/', name: 'formation_formation_id', requirements: ['id' => '\d+'])]
    public function formation()
    {
        $content = '';
        $menu = '';
        Assets::addJs('common/iframeResizer.contentWindow.min.js');
        Assets::addJs('https://npmcdn.com/masonry-layout@4.0/dist/masonry.pkgd.min.js');
        Assets::addJs('common/load-image.js');
        Assets::addJs('common/jquery/jquery.raty.min.js');
        Assets::addInlineJs(\Tools::getJsGallery('formation'));

        Assets::addCss('themes/' . $this->theme . '/css/sequence.css', null);
        Assets::addCss('themes/' . $this->theme . '/css/sommaire.css', null);

        $idformation = 0;
        if (isset($_GET['idformation'])) {
            $idformation = filter_input(INPUT_GET, 'idformation', FILTER_VALIDATE_INT);
        } elseif ($this->param) {
            $idformation = filter_var($this->param, FILTER_VALIDATE_INT);
        } elseif (isset($_SESSION['idformation'])) {
            $idformation = $_SESSION['idformation'];
        }

        $idformation = (int)$idformation;

        //mise à jour en session
        if (!isset($_SESSION['idformation']) or !isset($_SESSION['formation']) or $_SESSION['idformation'] != $idformation) {
            $_SESSION['idformation'] = $idformation;

            $formation = $this->get(\Formation_Formation::class)->getFormationById($idformation);
            if ($formation) {
                $_SESSION['formation'] = $formation;
            }
        }

        $this->parser->set('idformation', $idformation);
        $this->view->set('idformation', $idformation);
        $this->view->set('formation', $_SESSION['formation']);

        $displayFormation = $this->get(\Formation_Formation::class)->afficher_formation($idformation);
        if (!$displayFormation) {
            //selection de la formation
            $selectFormation = $this->get(\Formation_Formation::class)->generate_select_formation_user($idformation);
            if (!$selectFormation) {
                //generate_select_formation_user renvoie false dans le cas où il n'y a pas de formation diponible
                $menu .= '
        	        <div class="alert alert-danger">
        	            ' . __('Pas de formation dans votre parcours ou votre licence a expiré.') . '
        	            <br><a href="' . \Tools::makeLink('site', 'contact') . '">' . __('Veuillez contacter l\'administrateur') . '</a>.
        	        </div>';
            } else {
                //collapse menu
                $menu .= '
        			<form method="get" id="form_modules" style="margin-bottom:10px; width:95%; padding-left:5px;">
        				<select name="idformation" data-rel="select2" onchange="this.form.submit()">
        					' . $selectFormation . '
        				</select>
        			</form>';
            }

            $content .= '
    			<div class="alert alert-danger" style="margin:75px 20px; text-align:center">
    				<i class="fa fa-frown-o" style="font-size:70px"></i><br>
                    ' . __('Oups, cette formation n\'est pas accessible.') . '<br>
    				' . __('Choisissez une autre formation dans le menu ci-contre ou retournez à l\'accueil.') . '<br><br>
    				<a class="btn btn-danger" href="' . \Tools::makeLink('site', 'formation') . '"><i class="fa fa-home"></i> ' . __('Retour à l\'accueil') . '</a>
    			</div>';
        } else {
            if (is_array($displayFormation)) {
                if (isset($displayFormation['version']) and 2 == $displayFormation['version']) {
                    return $this->formationV2($displayFormation);
                }

                Assets::addJs('formation/jquery.multilevelpushmenu.js');
                Assets::addJs('formation/timeme.js');
                Assets::addJs('formation/time_tracking.js');
                Assets::addJs('formation/display_elements.js');

                $content = $displayFormation['output'];
                $menu = $displayFormation['menu'];
                Assets::addInlineJs($displayFormation['js']);

                if (isset($displayFormation['datas_formation']['nomformation'])) {
                    $this->view->set('title', $displayFormation['datas_formation']['nomformation']);
                }
            } else {
                $content = $displayFormation;
            }
        }

        $position = 'left';
        if (isset($_SESSION['formation']['theme'])) {
            $theme = $this->get(\Formation_Themes::class)->getThemeByName($_SESSION['formation']['theme']);
            if ($theme) {
                $themeConfiguration = $this->get(\Formation_Themes::class)->getThemeConfiguration($theme['id_theme'], $_SESSION['formation']['idformation']);
                if ($themeConfiguration) {
                    $design = $themeConfiguration['design'];
                    if ($design) {
                        $design = json_decode($design, true);
                        if ($design and isset($design['position_menu']) and $design['position_menu']) {
                            $position = $design['position_menu'];
                        }
                    }
                }
            }
        }

        $this->parser
            ->set('menu', $menu)
            ->set('position', $position)
            ->set('content', $content);

        if (is_file($path = THEMES_PATH . '/' . $this->theme . '/formation.php')) {
            $output = $this->parser->parsePHP($path);
        } else {
            $output = $this->parser->parsePHP(VIEWS_PATH . '/formation/formation.php');
        }

        $this->displayOutput($output);
    }

    public function formationV2($datas)
    {
        $datasFormation = $datas['datas_formation'];

        Assets::addJs('formation/jquery.multilevelpushmenuv3.js');
        Assets::addJs('formation/timeme.js');
        Assets::addJs('formation/time_tracking.js');
        Assets::addJs('formation/display_elements_v2.js');
        Assets::addJs('common/load-image.js');
        Assets::addJs('common/jquery/jquery.raty.min.js');
        Assets::addInlineJs(\Tools::getJsGallery('formation'));

        Assets::addCss('themes/' . $this->theme . '/css/sequence.css', null);
        Assets::addCss('themes/' . $this->theme . '/css/sommaire.css', null);

        $idformation = $datasFormation['idformation'];

        $this->parser->set('idformation', $idformation);
        $this->view->set('idformation', $idformation);
        $this->view->set('formation', $datasFormation['formation']);
        if (isset($datasFormation['idpage'])) {
            $this->view->set('display_page', true);
        }

        $content = $datas['output'];
        $menu = $datas['menu'];
        $typeMenu = $datas['type_menu'];
        $sequenceMenu = $datas['sequence_menu'];
        $js = $datas['js'];
        $jsHead = $datas['js_head'];

        $this->view->set('theme', $this->theme);

        if ($datas['css']) {
            $this->view->set('additionnal_css', $datas['css']);
        }
        if ($datas['css_files']) {
            $this->view->set('css_files', $datas['css_files']);
        }

        if (isset($datasFormation['nomformation'])) {
            $this->view->set('title', $datasFormation['nomformation']);
        }

        //get main content
        $this->parser
            ->set('menu', $menu)
            ->set('type_menu', $typeMenu)
            ->set('sequence_menu', $sequenceMenu)
            ->set('position', $datas['menu_position'])
            ->set('content', $content);

        if (isset($datasFormation['page']['design']) and $datasFormation['page']['design']) {
            $this->parser->set('page_design', $datasFormation['page']['design']);
            $this->view->set('page_design', $datasFormation['page']['design']);
        }

        if (is_file($path = THEMES_PATH . '/' . $this->theme . '/formation.php')) {
            $output = $this->parser->parsePHP($path);
        } else {
            $output = $this->parser->parsePHP(VIEWS_PATH . '/formation/formation.php');
        }

        Assets::addInlineJs($jsHead, true);
        $this->displayOutput($output, $js);
    }

    #[Route(path: '/formation/continue/', name: 'formation_continue')]
    public function continue()
    {
        // Get last page view & last page terminated
        $lastProgression = $this->get(EntityManager::class)->getRepository(FormationProgression::class)->findOneBy(['user' => $_SESSION['user_id'], 'client' => $_SESSION['id_client']], ['date' => 'DESC']);
        $lastProgressionConfirm = $this->get(EntityManager::class)->getRepository(FormationProgressionConfirm::class)->findOneBy(['user' => $_SESSION['user_id'], 'client' => $_SESSION['id_client']], ['date' => 'DESC']);
        if ($lastProgressionConfirm) {
            header('Location: ' . \Tools::makeLink('formation', 'formation', $lastProgressionConfirm->getPage()->getFormation()->getIdformation(), 'idmodule=' . ($lastProgressionConfirm->getPage()->getIdmodule() ? $lastProgressionConfirm->getPage()->getIdmodule() : 0) . '&idpage=' . $lastProgressionConfirm->getPage()->getIdpage()));
        } elseif ($lastProgression) {
            header('Location: ' . \Tools::makeLink('formation', 'formation', $lastProgression->getFormation()->getIdformation(), 'idmodule=' . ($lastProgression->getModule() ? $lastProgression->getModule()->getIdmodule() : 0) . '&idpage=' . $lastProgression->getPage()->getIdpage()));
        } else {
            header('Location: ' . \Tools::makeLink('formation', 'index'));
        }
    }
}
