<?php

namespace Learnybox\Controllers\Admin;

use Doctrine\ORM\EntityManager;
use Learnybox\Attributes\Universe;
use Learnybox\Entity\Coach\Coach;
use Learnybox\Entity\Coach\Coaching\CoachCoachingRate;
use Learnybox\Entity\Coach\Credit\CoachCredit;
use Learnybox\Helpers\Assets;
use Learnybox\Repositories\LearnyCoachings\LearnyCoachingsRatesRepository;
use Learnybox\Repository\Coach\Credit\CoachCreditRepository;
use Learnybox\Services\Coach\CoachService;
use Learnybox\Services\CoachsService;
use Learnybox\Services\CreditsService;
use Learnybox\Services\LearnyCoachings\LearnyCoachingsRatesService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class CoachingsAdminController
 * @package Learnybox\Controllers\Admin
 */
#[Universe(UNIVERSE_ADMIN_COACHINGS)]
class CoachingsAdminController extends AbstractAdminController
{
    #[Route(path: '/adminLB0410/coachings/', name: 'admin_coachings')]
    public function coachings()
    {
        $rendezVous = $this->get(\Coachs_Coachings::class)->adminGetAllRendezVous();
        $paiements = $this->get(\Coachs_Coachs::class)->getAllPayments();
        $durees = $this->get(\Coachs_Coachings::class)->getDurees();
        $coachingsRates = $this->get(LearnyCoachingsRatesRepository::class)->getAllRates();

        $output = $this->parser->set('rendezVous', $rendezVous)
            ->set('paiements', $paiements)
            ->set('durees', $durees)
            ->set('coachingsRates', $coachingsRates)
            ->parsePHP(VIEWS_PATH . '/adminlb0410/coachs/coachings.php');

        $js = $this->tableJs('coachings', 0, 'desc');
        $this->displayOutput($output, $js);
    }

    #[Route(path: '/adminLB0410/coachings/stats/', name: 'admin_coachings_stats')]
    public function coachingsStats()
    {
        $rendezVous = $this->get(\Coachs_Coachings::class)->adminGetAllRendezVous();
        $durees = $this->get(\Coachs_Coachings::class)->getDurees();
        $coachingsRates = $this->get(LearnyCoachingsRatesRepository::class)->getAllRates();
        $coachsRates = $this->get(LearnyCoachingsRatesService::class)->getStats();

        $tempCoachs = $this->get(\Coachs_Coachs::class)->getCoachs();
        $coachsData = $this->get(CoachService::class)->aggregateCoachsDataForAdminStats($rendezVous, $durees, $tempCoachs);

        Assets::addCssV5(Assets::CSS_TYPE_PAGES, 'done-for-you/stats.css');

        $output = $this->parser
            ->set('rendezVous', $rendezVous)
            ->set('durees', $durees)
            ->set('coachingsRates', $coachingsRates)
            ->set('coachsRates', $coachsRates)
            ->set('coachsData', $coachsData)
            ->parsePHP(VIEWS_PATH . '/adminlb0410/coachs/stats.php');

        $this->displayOutput($output);
    }

    #[Route(path: '/adminLB0410/coaching/{id}/', name: 'admin_coaching', requirements: ['id' => '\d+'])]
    public function coaching()
    {
        $id = filter_var($this->param, FILTER_VALIDATE_INT);
        $coaching = $this->get(\Coachs_Coachings::class)->adminGetRendezVousById($id);
        if (!$coaching) {
            $this->displayOutput('<div class="alert alert-danger">' . __('Ce rendez-vous n\'existe pas.') . '<br><a class="btn btn-danger btn-small" href="' . \Tools::makeLink('adminLB0410', 'coachings') . '"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a></div>');
            exit();
        }

        $coaching = $this->get(\Coachs_Coachings::class)->adminGetRendezVousById($id);
        $rate = $this->get(\Learnybox\Repositories\LearnyCoachings\LearnyCoachingsRatesRepository::class)->adminGetCoachingRateByIdCoaching($id);

        $output = $this->parser->set('coaching', $coaching)
            ->set('rate', $rate)
            ->parsePHP(VIEWS_PATH . '/adminlb0410/coachs/coaching.php');


        $rendezVous = $this->get(\Coachs_Coachings::class)->adminGetAllRendezVousByUser($coaching['user_id']);
        $durees = $this->get(\Coachs_Coachings::class)->getDurees();
        $coachingsRates = $this->get(\Learnybox\Repositories\LearnyCoachings\LearnyCoachingsRatesRepository::class)->getAllRates();
        $coachsRates = $this->get(\Learnybox\Services\LearnyCoachings\LearnyCoachingsRatesService::class)->getStats();

        $output .= $this->parser->set('rendezVous', $rendezVous)
            ->set('durees', $durees)
            ->set('exclude_id_coaching', $id)
            ->set('coachingsRates', $coachingsRates)
            ->set('coachsRates', $coachsRates)
            ->parsePHP(VIEWS_PATH . '/adminlb0410/coachs/coachings.php');

        $output = $this->get(\Csrf::class)->replaceForm($output);
        $this->displayOutput($output);
    }

    #[Route(path: '/adminLB0410/coach/{id}/', name: 'admin_coach', requirements: ['id' => '\d+'])]
    public function coach()
    {
        $id = filter_var($this->param, FILTER_VALIDATE_INT);
        $coach = $this->get(EntityManager::class)->getRepository(Coach::class)->find($id);
        if (!$coach) {
            $this->displayOutput('<div class="alert alert-danger">' . __('Ce coach n\'existe pas.') . '<br><a class="btn btn-danger btn-small" href="' . \Tools::makeLink('adminLB0410', 'coachings') . '"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a></div>');
            exit();
        }

        $rendezVous = $this->get(\Coachs_Coachings::class)->adminGetAllRendezVousByCoach($id);
        $paiements = $this->get(\Coachs_Coachs::class)->getPaymentsByCoach($id);
        $durees = $this->get(\Coachs_Coachings::class)->getDurees();
        $coachingsRates = $this->get(\Learnybox\Repositories\LearnyCoachings\LearnyCoachingsRatesRepository::class)->adminGetCoachingRatesByCoach($id);
        $coachsRates = $this->get(\Learnybox\Services\LearnyCoachings\LearnyCoachingsRatesService::class)->getStats();

        $output = $this->parser
            ->set('coach', $coach)
            ->set('rendezVous', $rendezVous)
            ->set('paiements', $paiements)
            ->set('durees', $durees)
            ->set('coachingsRates', $coachingsRates)
            ->set('coachsRates', $coachsRates)
            ->parsePHP(VIEWS_PATH . '/adminlb0410/coachs/coachings.php');

        $this->displayOutput($output);
    }

    #[Route(path: '/adminLB0410/coach/edit/{id}/', name: 'admin_coach_edit', requirements: ['id' => '\d+'])]
    public function coachEdit()
    {
        $idCoach = $this->getRequestAttributes()->getInt('id');
        $coach = $this->get(EntityManager::class)->getRepository(Coach::class)->find($idCoach);
        if (!$coach) {
            $this->displayOutput('<div class="alert alert-danger">' . __('Ce coach n\'existe pas.') . '</div>');
            exit();
        }

        $output = $this->parser
            ->set('coach', $coach)
            ->parsePHP(FORMS_PATH . '/adminlb0410/coachs/coach.php');

        $this->displayOutput($output);
    }

    #[Route(path: '/adminLB0410/coachs_calendars/', name: 'admin_coachs_calendars')]
    public function coachsCalendars()
    {
        $calendar = $this->get(CoachsService::class)->getCalendarOutput(null, 'allcoachs');
        $output = $this->get(\Csrf::class)->replaceForm($calendar['html']);
        $js = $calendar['js'];
        $this->displayOutput($output, $js);
    }

    #[Route(path: '/adminLB0410/coach_rates/', name: 'admin_coachs_rates_all')]
    #[Route(path: '/adminLB0410/coach_rates/{id}/', name: 'admin_coach_rates', requirements: ['id' => '\d+'])]
    public function coachRates()
    {
        $id = filter_var($this->param, FILTER_VALIDATE_INT);
        $coach = null;
        if ($id) {
            $coach = $this->get(EntityManager::class)->getRepository(Coach::class)->find($id);
            if (!$coach) {
                $this->displayOutput('<div class="alert alert-danger">' . __('Ce coach n\'existe pas.') . '<br><a class="btn btn-danger btn-small" href="' . \Tools::makeLink('adminLB0410', 'coachings') . '"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a></div>');
                exit();
            }
            $rates = $this->get(EntityManager::class)->getRepository(CoachCoachingRate::class)->getByCoach($id);
        } else {
            $rates = $this->get(EntityManager::class)->getRepository(CoachCoachingRate::class)->getAll();
        }

        $output = $this->parser->set('coach', $coach)
            ->set('rates', $rates)
            ->parsePHP(VIEWS_PATH . '/adminlb0410/coachs/coach_rates.php');

        $this->displayOutput($output);
    }

    #[Route(path: '/adminLB0410/coach_payment/', name: 'admin_coach_payment')]
    public function coachPayment()
    {
        $this->displayForm('coach_payment', 'coach_payment', __('Edition d\'un paiement'));
    }

    #[Route(path: '/adminLB0410/nb_credits_coachings/', name: 'admin_nb_credits_coachings')]
    public function nbCreditsCoaching()
    {
        $date = date('YmdHi');
        $fileName = $date . '_ExportCreditsCoachings.xlsx';

        $file = $this->get(CreditsService::class)->generateCreditsExcel($fileName);
        if (!$file['valid']) {
            $this->displayError($file['message']);
            return;
        }

        header('Content-Disposition: attachment; filename=' . $fileName);
        header("location: " . $file['url']);
        exit();
    }
}
