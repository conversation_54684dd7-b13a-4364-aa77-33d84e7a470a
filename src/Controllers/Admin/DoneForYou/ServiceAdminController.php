<?php

namespace Learnybox\Controllers\Admin\DoneForYou;

use Learnybox\Components\Workflow\WorkflowRegistry;
use Learnybox\Enums\ApplicationEnum;
use Learnybox\Helpers\Assets;
use Learnybox\Renderers\DoneForYou\HeaderRenderer;
use Learnybox\Renderers\DoneForYou\IntercomLinkRenderer;
use Learnybox\Renderers\DoneForYou\ServiceRenderer;
use Learnybox\Repository\DoneForYou\DoneForYouServiceRepository;
use Learnybox\Services\DoneForYou\DoneForYouServiceService;
use Learnybox\Services\Filters\DoneForYou\Services\AllServicesFiltersService;
use Learnybox\Services\Filters\DoneForYou\Services\ClaimServicesFiltersService;
use Learnybox\Services\SessionService;
use Learnybox\Services\Views\ViewAdminDoneForYouService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class CollaboratorController
 * @package Learnybox\Controllers\Admin\HumanResources
 */
class ServiceAdminController extends AbstractDoneForYouAdminController
{
    #[Route(path: '/adminLB0410/done_for_you/service/', name: 'admin_done_for_you_service_index')]
    public function index()
    {
        $filterService = $this->get(AllServicesFiltersService::class);
        $serviceListRender = $this->get(ServiceRenderer::class)->renderServiceList($filterService, null, true, null, true);

        $ouput = $this->parser
            ->set('serviceListRender', $serviceListRender)
            ->parsePHP(VIEWS_PATH . '/adminlb0410/done_for_you/service/index.php');

        $this->displayOutput($ouput);
    }

    #[Route(path: '/adminLB0410/done_for_you/service/show/{id}/', name: 'admin_done_for_you_service_show', requirements: ['id' => '\d+'])]
    public function show()
    {
        $serviceId = filter_var($this->param, FILTER_VALIDATE_INT);

        $doneForYouServiceService = $this->get(DoneForYouServiceService::class);
        /** @var DoneForYouServiceRepository $repo */
        $repo = $doneForYouServiceService->getRepository();
        $service = $repo->adminFindOneById($serviceId);

        if ($service === null) {
            $this->display404();
        }

        $doneForYouServiceService->updateNotifyAdmin($service, false);

        $workflow = $this->get(WorkflowRegistry::class)->get($service);

        $timelinePosition = $doneForYouServiceService->getServiceTimelinePosition($service);
        $workInProgressPercentage = $doneForYouServiceService->getWorkProgressPercentage($service);

        // Intercom Form
        $intercomLinkRenderer = $this->get(IntercomLinkRenderer::class);
        $intercomFormRender = $intercomLinkRenderer->renderForm($service);

        Assets::addCssV5(Assets::CSS_TYPE_PAGES, 'done-for-you/show.css');
        Assets::addJs('app/done-for-you/service/rating_stars.js');
        Assets::addJs('app/done-for-you/service/attached_file_handler.js');
        Assets::addJs('app/done-for-you/service/show_handler.js');

        $content = $this->parser
            ->set('application', ApplicationEnum::ADMIN)
            ->set('service', $service)
            ->set('workflow', $workflow)
            ->set('timelinePosition', $timelinePosition)
            ->set('workInProgressPercentage', $workInProgressPercentage)
            ->set('intercomFormRender', $intercomFormRender)
            ->parsePHP(VIEWS_PATH . '/adminlb0410/done_for_you/service/show.php');

        $header = $this->get(HeaderRenderer::class)->renderHeader($service, ApplicationEnum::ADMIN);

        $content = $this->createBox($header, $content);

        $this->displayOutput($content);
    }

    #[Route(path: '/adminLB0410/done_for_you/service/claim/', name: 'admin_done_for_you_service_claim')]
    public function claim()
    {
        $filterService = $this->get(ClaimServicesFiltersService::class);
        $serviceListRender = $this->get(ServiceRenderer::class)->renderServiceList($filterService, null, true, null, true);

        $ouput = $this->parser
            ->set('serviceListRender', $serviceListRender)
            ->parsePHP(VIEWS_PATH . '/adminlb0410/done_for_you/service/claim.php');

        $this->displayOutput($ouput);
    }
}
