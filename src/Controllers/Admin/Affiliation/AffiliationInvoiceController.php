<?php

namespace Learnybox\Controllers\Admin\Affiliation;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Affiliation\Affilie\AffiliationAffilie;
use Learnybox\Entity\Affiliation\Invoice\AffiliationInvoice;
use Learnybox\Entity\Affiliation\Payment\AffiliationPayment;
use Learnybox\Entity\Client\Client;
use Learnybox\Entity\Config\Config;
use Learnybox\Helpers\Assets;
use Learnybox\Services\Affiliation\AffiliationInvoiceService;
use Learnybox\Services\RightsService;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class AffiliationInvoiceController
 * @package Learnybox\Controllers\Admin\Affiliation
 */
class AffiliationInvoiceController extends AbstractAffiliationController
{
    #[Route(path: '/adminLB0410/affiliation/invoices/', name: 'admin_affiliation_invoices')]
    public function affiliation()
    {

        $em = $this->get(EntityManager::class);

        $startAt = isset($_POST['startAt']) && !empty($_POST['startAt']) ? new \DateTime(filter_input(INPUT_POST, 'startAt', FILTER_SANITIZE_SPECIAL_CHARS)) : (new \DateTime())->modify('-1 year');
        $endAt = isset($_POST['endAt']) && !empty($_POST['endAt']) ? new \DateTime(filter_input(INPUT_POST, 'endAt', FILTER_SANITIZE_SPECIAL_CHARS)) : new \DateTime();

        $affInvoiceRep = $em->getRepository(AffiliationInvoice::class);

        $statuses = $this->getRequestQuery()->get('status', null);

        $affiliatesTotals = null;
        if ($statuses !== null) {
            $invoices = $affInvoiceRep->findAllByDateAndStatus($startAt, $endAt, $statuses);
        } else {
            $invoices = $affInvoiceRep->findAllByDateAndStatus($startAt, $endAt);
            $affiliatesTotals = $em->getRepository(AffiliationAffilie::class)->findAllWithSumInvoice($startAt, $endAt, ['paid']);
        }

        Assets::addJs('admin/partners.js');
        Assets::addJs('admin/affiliation/invoice_list_handler.js');

        $output = $this->parser
            ->set('startAt', $startAt)
            ->set('endAt', $endAt)
            ->set('invoices', $invoices)
            ->set('statuses', $statuses)
            ->set('affiliatesTotals', $affiliatesTotals)
            ->set('statusValidated', AffiliationInvoice::STATUS_VALIDATED)
            ->set('statusSentToPayment', AffiliationInvoice::STATUS_SENT_TO_PAYMENT)
            ->set('statusPaid', AffiliationInvoice::STATUS_PAID)
            ->set('refusalConditions', AffiliationInvoiceService::getRefusalConditions())
            ->set('clientRepository', $this->get(EntityManager::class)->getRepository(Client::class))
            ->set('configRepository', $this->get(EntityManager::class)->getRepository(Config::class))
            ->parsePHP(VIEWS_PATH . '/adminlb0410/affiliation/affiliation_invoices.php');

        $this->displayOutput($output);
    }

    #[Route(path: '/adminLB0410/affiliation/invoice/{id}/', name: 'admin_affiliation_invoice', requirements: ['id' => '\d+'])]
    public function affiliationInvoice()
    {
        $id = filter_var($this->param, FILTER_SANITIZE_NUMBER_INT);
        $invoice = $this->get(AffiliationInvoiceService::class)->getRepository()->find($id);

        $hasIban = true;
        $client = $this->get(EntityManager::class)->getRepository(Client::class)->findByAffiliationAffilie($invoice->getAffiliationAffilie());
        if ($client) {
            $config = $this->get(EntityManager::class)->getRepository(Config::class)->findOneBy(['client' => $client, 'name' => 'aff_lb_bank_transfer_noiban']);
            if ($config && $config->getValue() == '1') {
                $hasIban = false;
            }
        }

        $output = $this->parser
            ->set('invoice', $invoice)
            ->set('statusValidated', AffiliationInvoice::STATUS_VALIDATED)
            ->set('statusSentToPayment', AffiliationInvoice::STATUS_SENT_TO_PAYMENT)
            ->set('statusPaid', AffiliationInvoice::STATUS_PAID)
            ->set('refusalConditions', AffiliationInvoiceService::getRefusalConditions())
            ->set('hasIban', $hasIban)
            ->parsePHP(VIEWS_PATH . '/adminlb0410/affiliation/affiliation_invoice.php');

        $this->displayOutput($output);
    }

    #[Route(path: '/adminLB0410/affiliation/invoice/refuse/{id}/', name: 'admin_affiliation_invoice_edit_status_refuse', requirements: ['id' => '\d+'])]
    public function affiliationInvoiceRefuse()
    {

        $id = filter_var($this->param, FILTER_VALIDATE_INT);
        $affiliationInvoice = $this->get(AffiliationInvoiceService::class)->getRepository()->find($id);
        if (!$affiliationInvoice) {
            return $this->display404();
        }

        $optionsRefusalConditionsHtml = $this->parser
            ->set('options', AffiliationInvoiceService::getRefusalConditions())
            ->set('selectedValue', $affiliationInvoice->getRefusedReason())
            ->parsePHP(FORMS_PATH . '/partials/select_options.php');

        $selectRefusalConditionsHtml = $this->parser
            ->set('select', $optionsRefusalConditionsHtml)
            ->set('name', 'refused_reason')
            ->set('required', true)
            ->set('label', _('Champs obligatoire'))
            ->set('class', '')
            ->parsePHP(FORMS_PATH . '/partials/select.php');

        Assets::addJs('admin/partners.js');

        $output = $this->parser
            ->set('redirect', \Tools::makeLink('adminLB0410', 'affiliation/invoices'))
            ->set('affiliationInvoice', $affiliationInvoice)
            ->set('selectRefusalConditionsHtml', $selectRefusalConditionsHtml)
            ->parsePHP(FORMS_PATH . '/adminlb0410/affiliation/affiliationInvoiceRefuseForm.php');

        $this->displayOutput($output);
    }
}
