<?php

namespace Learnybox\Controllers\Admin\HumanResources;

use Learnybox\Components\EntityComparer\EntityComparerFactory;
use Learnybox\Event\EntityEvents;
use Learnybox\Event\EntityUpdatedEvent;
use Learnybox\Services\FlashBagService;
use Learnybox\Services\HumanResources\Collaborator\CollaboratorDocumentService;
use Learnybox\Services\HumanResources\Collaborator\CollaboratorService;
use Learnybox\Services\RightsService;
use Symfony\Component\EventDispatcher\EventDispatcher;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class CollaboratorDocumentController
 * @package Learnybox\Controllers\Admin\HumanResources
 */
class CollaboratorDocumentController extends AbstractHumanResourcesController
{
    #[Route(path: '/adminLB0410/collaborator/document/add/', name: 'admin_collaborator_document_add')]
    #[Route(path: '/adminLB0410/collaborator/document/add/{id?}/', name: 'admin_collaborator_id_document_add', requirements: ['id' => '\d+'])]
    public function collaboratorDocumentAdd()
    {
        $idCollaborator = ($this->param ? filter_var($this->param, FILTER_VALIDATE_INT) : 0);

        $collaborator = $this->get(CollaboratorService::class)->getRepository()->find($idCollaborator);
        if (!$collaborator) {
            return $this->display404();
        }

        $selectCollaborator = $this->get(CollaboratorService::class)->generateSelectCollaborator($idCollaborator);
        $selectDocumentType = $this->get(CollaboratorDocumentService::class)->generateSelectCollaboratorDocumentType('', $collaborator->getExternal());

        $output = $this->parser
            ->set('idCollaborator', $idCollaborator)
            ->set('selectCollaborator', $selectCollaborator)
            ->set('selectDocumentType', $selectDocumentType)
            ->set('admin', true)
            ->set('redirect', \Tools::makeLink('adminLB0410', $idCollaborator ? 'collaborator/view' : 'collaborator', $idCollaborator ? $idCollaborator : null))
            ->parsePHP(FORMS_PATH . '/adminlb0410/human_resources/collaborator/collaboratorDocumentForm.php');

        $this->displayOutput($output);
    }

    #[Route(path: '/adminLB0410/collaborator/document/edit/{id?}/', name: 'admin_collaborator_document_edit', requirements: ['id' => '\d+'])]
    public function collaboratorDocumentEdit()
    {
        $idDocument = filter_var($this->param, FILTER_VALIDATE_INT);

        $document = $this->get(CollaboratorDocumentService::class)->getRepository()->find($idDocument);
        if (!$document) {
            return $this->display404();
        }

        $collaborator = $this->get(CollaboratorService::class)->getRepository()->find($document->getCollaborator()->getId());
        if (!$collaborator) {
            return $this->display404();
        }

        $isEditable = true;

        // Document already signed cannot be edited
        if ($document->getToSign() && $document->isSigned()) {
            $isEditable = false;
        }

        $type = ($document ? $document->getType() : '');
        $selectDocumentType = $this->get(CollaboratorDocumentService::class)->generateSelectCollaboratorDocumentType($type, $collaborator->getExternal());

        $output = $this->parser
            ->set('document', $document)
            ->set('idCollaborator', $document->getCollaborator()->getId())
            ->set('selectDocumentType', $selectDocumentType)
            ->set('admin', true)
            ->set('redirect', \Tools::makeLink('adminLB0410', 'collaborator/view', $document->getCollaborator()->getId()))
            ->set('isEditable', $isEditable)
            ->parsePHP(FORMS_PATH . '/adminlb0410/human_resources/collaborator/collaboratorDocumentForm.php');

        $this->displayOutput($output);
    }

    #[Route(path: '/adminLB0410/collaborator/signed-document/refuse/{id}/', name: 'admin_collaborator_signed_document_refuse', requirements: ['id' => '\d+'])]
    public function collaboratorSignedDocumentRefuse()
    {
        $id = filter_var($this->param, FILTER_VALIDATE_INT);
        $collaboratorDocument = $this->get(CollaboratorDocumentService::class)->getRepository()->find($id);
        if (!$collaboratorDocument) {
            return $this->display404();
        }

        if (null !== $collaboratorDocument->getSignedDocument()) {
            $this->get(CollaboratorDocumentService::class)->deleteAndFlush($collaboratorDocument->getSignedDocument());

            $entityComparer = $this->get(EntityComparerFactory::class)->createOne();
            $entityComparer->setOld($collaboratorDocument);

            $collaboratorDocument->setSignedDocument(null);
            $this->get(CollaboratorDocumentService::class)->persistAndFlush($collaboratorDocument);

            // Dispatch Event
            $entityComparer->setNew($collaboratorDocument);
            $event = new EntityUpdatedEvent($collaboratorDocument, $entityComparer->getChanges(['signed_document' => true]));
            $this->get(EventDispatcher::class)->dispatch($event, EntityEvents::HR_COLLABORATOR_DOCUMENT_UPDATED);

            $this->get(FlashBagService::class)->addSuccess(__('Le document signé a été refusé.'));
        }
        header('Location: ' . \Tools::makeLink('adminLB0410', 'collaborator', 'view/' . $collaboratorDocument->getCollaborator()->getId()));
    }
}
