<div class="panel-header">
    <h3 class="panel-title">{{ __('Application mobile') }}</h3>
    <button class="close-tab"><i class="fa-solid fa-xmark"></i></button>
</div>
<div class="panel-content">
    <form>
        <div class="form-group switch-input m-b-5">
            <div class="switch-right">
                <label class="control-label enabled-label" for="app_mobile_enabled">
                    {% if enabled %}
                        {{ __('Activer l\'app mobile') }}
                    {% else %}
                        {{ __('Désactiver l\'app mobile') }}
                    {% endif %}
                </label>
            </div>
            <div class="switch-left">
                <div class="switch">
                    <input type="checkbox"
                           name="app_mobile_enabled"
                           id="app_mobile_enabled"
                           class="ios-toggle ios-toggle-round ios-toggle-switch"
                           data-no-uniform="true"
                           data-config-id="{{ configId }}"
                        {% if enabled %}
                            {{ 'checked' }}
                        {% endif %}
                    >
                    <label for="app_mobile_enabled"></label>
                </div>
            </div>
        </div>
        <div class="help m-b-20">
            {{ __('Si vous désactivez votre espace de formation sur l\'application mobile, aucun membre ne peut accéder à votre contenu depuis l\'application mobile %s.', constant('LB')) }}
        </div>

        <div class="mobile-config-container {{ enabled is same as false ? 'd-none' : '' }}">
            <div class="form-group">
                <label class="control-label">{{ __('Formations') }}</label>
                <div class="help">{{ __('Choisissez les formations qui seront disponibles sur l\'application mobile.') }}</div>
                <div class="controls">
                    <div class="input-group">
                        <select name="app_mobile_formations[]" id="app_mobile_formations" class="form-control"
                                data-rel="select2" multiple="multiple">
                            {% for formation in formations %}
                                <option value="{{ formation.idformation }}" {{ formation.mobileApp ? 'selected' : '' }}>
                                    {{ formation.nomformation|raw }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="app_mobile_thumbnail" class="control-label">{{ __('Vignette') }}</label>
                <div class="help">{{ __('La vignette sera affichée sur la session correspondant à votre espace de formation dans l\'application mobile.') }}</div>
                <div class="form-group-image" id="app_mobile_thumbnail-container">
                    <div class="form-group-image-preview" {% if thumbnail %}style="background-image: url('{{ thumbnail }}');"{% endif %}></div>
                    <div class="form-group-image-overlay"></div>
                    <div class="form-group-image-buttons">
                        <button type="button" onclick="return selectFileWithCKFinder('app_mobile_thumbnail');"><i class="nc-icon nc-icon-photo-1"></i> Parcourir</button>
                        <button type="button" onclick="return openBgGallery('app_mobile_thumbnail');"><i class="nc-icon nc-icon-url-1"></i> Unsplash</button>
                        <button type="button" onclick="removeImage('app_mobile_thumbnail');"><i class="nc-icon nc-icon-trash"></i> Supprimer</button>
                    </div>
                </div>
                <input id="app_mobile_thumbnail" name="app_mobile_thumbnail" type="text" placeholder="{{ __('URL de la vignette') }}" class="form-control form-group-image-input" value="{{ thumbnail }}">
            </div>

            <div class="form-group">
                <label class="control-label">{{ __('Thème') }}</label>
                <div class="help">{{ __('Sélectionnez un thème pour votre application mobile.') }}</div>
                {{ themeBlockSelector|raw }}
            </div>

            <div class="form-group">
                <label class="control-label" for="vignette">{{ __('Email de promotion') }}</label>
                <span class="help-block">{{ __('Envoyez un email de promotion de l\'application mobile à tous vos membres de formation actifs.') }}</span>
                <div class="controls">
                    <a class="btn btn-default" id="showModalAppMobileEmailBtn">{{ __('Envoyer un email personnalisé') }}</a>
                </div>
            </div>
        </div>
    </form>
</div>
