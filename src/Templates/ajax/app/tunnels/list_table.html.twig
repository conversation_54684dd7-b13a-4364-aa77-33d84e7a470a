{% if data is empty %}
    {{ include("common/filters/_empty.html.twig", {search: search | raw}) }}
{% else %}
    <table class="table table-striped table-paginated m-b-5">
        <thead>
        <tr>
            <th>{{ render_sort(data, __('Nom'), 't.nom') }}</th>
            <th class="number-position">{{ render_sort(data, __('Inscrits'), 'count_subscriptions') }}</th>
            {#            <th class="number-position">{{ render_sort(data, __('Ventes'), 'count_transactions') }}</th> #}
            {#            {% if can_see_private_infos %} #}
            {#                <th class="number-position">{{ render_sort(data, __('Montant total'), 'total_amount') }}</th> #}
            {#            {% endif %} #}

            <th>{{ render_sort(data, __('Etat'), 't.publication') }}</th>
            <th>{{ render_sort(data, __('Favoris'), 'is_fav') }}</th>
            <th></th>
        </tr>
        </thead>

        <tbody>
        {% for row in data %}
            {% set tunnel = row['tunnel'] %}
            {% set tr_class =
                constant('Learnybox\\Enums\\Tunnel\\TunnelStateEnum::IN_PROGRESS') is not same as tunnel.publication ? 'inactive' : '' %}

            <tr class="{{ tr_class }}">
                <td>
                    <a href="{{ generate_route('app_tunnel', {'tunnelId': tunnel.idTunnel}) }}">
                        <strong>{{ tunnel.nom | raw }}</strong>
                    </a>
                </td>
                <td class="number-position">
                    {{ row['count_subscriptions'] | format_number }}
                </td>
                {#                <td class="number-position"> #}
                {#                    {{ row['count_transactions'] | format_number }} #}
                {#                </td> #}
                {#                {% if can_see_private_infos %} #}
                {#                    <td class="number-position" data-order=""> #}
                {#                        {{ row['total_amount'] | format_currency(main_devise) }} #}
                {#                    </td> #}
                {#                {% endif %} #}
                <td>
                    {% if constant('Learnybox\\Enums\\Tunnel\\TunnelStateEnum::IN_PROGRESS') is same as tunnel.publication %}
                        <span class="label label-status label-success cursor-pointer"
                              onclick="$('#desactive #id_tunnel').val({{ tunnel.idTunnel }}); $('#desactive').submit();"
                              data-toggle="tooltip"
                              title="{{ __('Désactiver') }}">
                                {{ __('Activé') }}
                            </span>
                    {% else %}
                        <span class="label label-status label-danger cursor-pointer"
                              onclick="$('#active #id_tunnel').val({{ tunnel.idTunnel }}); $('#active').submit();"
                              data-toggle="tooltip"
                              title="{{ __('Activer') }}">
                                {{ __('Désactivé') }}
                            </span>
                    {% endif %}
                </td>
                <td class="text-center">
                    {{ include("common/favorites/_index.html.twig", {
                        is_fav: row['is_fav'],
                        fav_url: generate_route('ajax_favoris_update_tunnel', {'idTunnel': tunnel.idTunnel}),
                        not_fav_icon_class: 'fa-regular'
                    }) }}
                </td>
                <td>
                    <div class="btn-group action">
                        <a type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                           class="dropdown-toggle ">
                            <i class="dropdown-icon fa-solid fa-ellipsis-vertical"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li>
                                <a href="{{ generate_route('app_tunnel_edit', {'tunnelId': tunnel.idTunnel}) }}">
                                    <i class="fa-regular fa-pen-to-square"></i>
                                    {{ __('Modifier') }}
                                </a>
                            </li>
                            <li>
                                <a href="{{ generate_route('app_tunnel_duplicate', {'tunnelId': tunnel.idTunnel}) }}">
                                    <i class="fa-regular fa-copy"></i>
                                    {{ __('Dupliquer') }}
                                </a>
                            </li>
                            <li>
                                <a href="{{ generate_route('app_partage_add', {'type': 'Learnybox\\Enums\\PartageTypeEnum::TUNNELS', 'id_objet': tunnel.idTunnel}) }}">
                                    <i class="fa-regular fa-share-nodes"></i>
                                    {{ __('Partager') }}
                                </a>
                            </li>
                            <li>
                                <a onclick="$('#ModalReinitTunnel #id_tunnel').val({{ tunnel.idTunnel }}); $('#ModalReinitTunnel').modal('show'); return false;">
                                    <i class="fa-regular fa-arrows-rotate"></i>
                                    {{ __('Réinitialiser') }}
                                </a>
                            </li>
                            {% if tunnel.thumbnail and 'error' is not same as tunnel.thumbnail %}
                                <li>
                                    <a class="js-modal-handler"
                                       data-method="createModal"
                                       data-title="{{ __('Confirmation') }}"
                                       data-content="{{ __('Êtes-vous sûr de vouloir régénérer la capture d\'écran de ce tunnel ?') }}"
                                       data-action="$('#regenerate_thumbnail #id_tunnel').val({{ tunnel.idTunnel }}); $('#regenerate_thumbnail').submit(); return false;"
                                       data-button="{{ __('Régénérer la capture d\'écran') }}"
                                       data-type="success"
                                       data-blocker="{{ __('Oui, je confirme.') }}"
                                    >
                                        <i class="fa fa-refresh"></i>
                                        {{ __('Régénérer la capture d\'écran') }}
                                    </a>
                                </li>
                            {% endif %}
                            <li class="divider"></li>
                            <li>
                                <a class="js-modal-handler"
                                   data-method="createModal"
                                   data-title="{{ __('Confirmation') }}"
                                   data-content="{{ __('Êtes-vous sûr de vouloir supprimer ce tunnel ?') }}"
                                   data-action="$('#suppr #id_tunnel').val({{ tunnel.idTunnel }}); $('#suppr').submit(); return false;"
                                   data-button="{{ __('Supprimer ce tunnel') }}"
                                   data-type="error"
                                   data-blocker="{{ __('Oui, je confirme la suppression.') }}"
                                >
                                    <i class="fa fa-trash-o"></i> {{ __('Supprimer') }}
                                </a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    {{ render_pagination(data) }}
{% endif %}
