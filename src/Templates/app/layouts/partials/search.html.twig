<div id="morphsearch" class="morphsearch">
    <form class="morphsearch-form" autocomplete="off">
        <span class="search-icon"><i class="fa fa-search"></i></span>
        <input class="morphsearch-input-launcher form-control" type="search" id="inputOpenSearch" placeholder="{{ __('Rechercher') }}" autocomplete="off">
    </form>
    <div id="morphsearch-content">
        <div id="morphsearch-footer">
            <ul class="cs-select cs-skin-slide">
                <li>
                    <a href="javascript:void(0)" data-type="all" data-class="morphsearch-icon-aide" class="cs-selected cs-select-a">{{ __('Tout') }}</a>
                </li>
                {% if has_access(constant('UNIVERSE_APP_CONTACTS')) %}
                <li>
                    <a href="javascript:void(0)" data-type="membres" data-class="morphsearch-icon-membres" class="cs-select-a" >{{ __('Utilisateurs') }}</a>
                </li>
                {% endif %}
                {% if has_access(constant('UNIVERSE_APP_FORMATIONS')) %}
                <li>
                    <a href="javascript:void(0)" data-type="formations" data-class="morphsearch-icon-formations" class="cs-select-a">{{ __('Formations') }}</a>
                </li>
                {% endif %}
                {% if has_access(constant('UNIVERSE_APP_BLOG')) %}
                <li>
                    <a href="javascript:void(0)" data-type="articles" data-class="morphsearch-icon-articles" class="cs-select-a">{{ __('Articles') }}</a>
                </li>
                {% endif %}
                {% if has_access(constant('UNIVERSE_APP_SITE')) %}
                <li>
                    <a href="javascript:void(0)" data-type="pages" data-class="morphsearch-icon-pages" class="cs-select-a">{{ __('Pages') }}</a>
                </li>
                {% endif %}
                {% if has_access(constant('UNIVERSE_APP_TUNNELS')) %}
                <li>
                    <a href="javascript:void(0)" data-type="tunnels" data-class="morphsearch-icon-tunnels" class="cs-select-a">{{ __('Tunnels') }}</a>
                </li>
                {% endif %}
                {% if has_access(constant('UNIVERSE_APP_CONFERENCES')) %}
                <li>
                    <a href="javascript:void(0)" data-type="conferences" data-class="morphsearch-icon-conferences" class="cs-select-a">{{ __('Conférences') }}</a>
                </li>
                {% endif %}
                {% if has_access(constant('UNIVERSE_APP_MAIL')) %}
                <li>
                    <a href="javascript:void(0)" data-type="sequences" data-class="morphsearch-icon-sequences" class="cs-select-a">{{ __('Séquences') }}</a>
                </li>
                {% endif %}
                {% if has_access(constant('UNIVERSE_APP_MAIL')) %}
                <li>
                    <a href="javascript:void(0)" data-type="mails" data-class="morphsearch-icon-emails" class="cs-select-a">{{ __('Emails') }}</a>
                </li>
                {% endif %}
            </ul>
        </div>
        <div id="results"></div>
    </div>
</div>
