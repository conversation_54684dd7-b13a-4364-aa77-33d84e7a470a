{% embed "app/layouts/modals/modal_form.html.twig" with {
    'modalId': 'userFormationCreateModal',
    'formId': 'userFormationCreateForm',
    'formClass': 'home-form-actions',
    'title': title,
    'buttonText': __('Valider'),
} %}
    {% block modal_classes %}{{ parent() }} modal-form form{% endblock %}
    {% block modal_backdrop %}true{% endblock %}
    {% block modal_body %}
        <div class="form-group">
            <label for="user-formation-input-select-formations" class="control-label">
                {{ __('Sélectionner une ou plusieurs formations') }}*
            </label>
            <div class="controls">
                <select id="user-formation-input-select-formations" name="formationIds[0]" multiple data-rel="select2" required>
                    {% for formation in formations %}
                        <option value="{{ formation.id }}">{{ formation.label }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <input type="hidden" name="userId" value="{{ userId }}">
    {% endblock %}
{% endembed %}
