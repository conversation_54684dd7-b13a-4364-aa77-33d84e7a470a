{% extends "app/formations/forums/messages/form.html.twig" %}

{% set cancelLink = generate_route('app_formation_community_messages', {'formationId': formationId}) %}
{% set cancelLinkTitle = __('Retour aux discussions') %}

{% set cancelUrl = generate_route('app_formation_community_messages', {'formationId': formationId}) %}
{% set submitLabel = __('Modifier le message') %}
{% set formAction = 'forum_message_edit' %}
{% set redirectUrl = generate_route('app_formation_community_messages', {'formationId': formationId}) %}

{% block inputs %}
    <input type="hidden" name="id_message" value="{{ messageId }}" />
{% endblock %}
