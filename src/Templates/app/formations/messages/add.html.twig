{% extends "app/layouts/formation_suivi.html.twig" %}

{% set cancelLink = generate_route('app_formation_messages', {'formationId': formationId}) %}
{% set cancelLinkTitle = __('Retour à la liste des messages') %}

{% block reglages_content %}
    <div class="message-container">
        <div class="message-user">
            <div class="message-user-avatar">
                <img
                    class="img-circle pull-left"
                    src="{{ avatarUrl | escape('html_attr') }}"
                    width="40"
                    height="40"
                    alt="{{ userName | escape('html_attr') }}"
                />
            </div>
            <div class="message-user-name">
                {{ userName }}
            </div>
        </div>

        <form class="message-form form js-form-handler" method="post" action="">
            <div class="form-group">
                <label class="control-label" for="destinataires">{{ __('Destinataires') }}</label>
                <span class="help-block">{{ __('A qui souhaitez-vous envoyer ce message ?') }}</span>

                <div class="form-inline">
                    <div class="form-group" style="margin-right: 10px;">
                        <div class="checkbox">
                            <label for="membre" style="padding-left: 20px;">
                                <input
                                    type="radio"
                                    name="destinataire"
                                    id="membre"
                                    value="membre"
                                    {{ (idestinataire and 'membre' == destinataire ? 'checked' : '') }}
                                > {{ __('À un membre') }}
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <select name="user_id" data-rel="select2">
                           {{ selectMembres |raw }}
                        </select>
                    </div>
                </div>

                {% if (selectGroupe) %}
                    <div class="form-inline">
                        <div class="form-group" style="margin-right: 10px;">
                            <div class="checkbox">
                                <label for="groupe" style="padding-left: 20px;">
                                    <input
                                        type="radio"
                                        name="destinataire"
                                        id="groupe"
                                        value="groupe"
                                        {{ (destinataire and 'groupe' == destinataire) ? 'checked' : '' }}
                                    > {{ __('À un groupe de membres') }}
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <select name="id_groupe" data-rel="select2">
                                {{ selectGroupe | raw }}
                            </select>
                        </div>
                    </div>
                {% endif %}

                <div class="form-group" style="margin-right: 10px;">
                    <div class="checkbox">
                        <label for="membres" style="padding-left: 20px;">
                            <input
                                type="radio"
                                name="destinataire"
                                id="membres"
                                value="membres"
                                {{ (destinataire and 'membres' == destinataire) ? 'checked' : '' }}
                            > {{ formation and formation.product_type is constant('Learnybox\\Enums\\Formation\\FormationProductTypeEnum::COMMUNITY') ? __('À tous les membres de cette communauté') : __('À tous les membres de cette formation') }}
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="control-label" for="subject">{{ __('Sujet') ~ ' *' }}</label>
                <div class="controls">
                    <input type="text" class="form-control input" id="subject" name="subject" value="{{ subject | default('') }}">
                </div>
            </div>
            <div class="form-group">
                <label for="message" class="control-label">{{ __('Message') }}</label>
                <textarea class="form-control ckeditor-ultraLight" name="message" id="message">{{ message | default('') }}</textarea>
            </div>
            <div class="form-group">
                <label class="control-label" for="attachment">{{ __('Pièce jointe') }}</label>
                <div class="controls">
			<span class="btn btn-default fileinput-button">
		        <i class="fa fa-plus"></i> {{ __('Ajouter des fichiers...') }}
                <input id="fileupload" type="file" name="files[]" multiple>
		    </span>
                    <br><br>
                    <div id="progress" class="progress">
                        <div class="progress-bar progress-bar-success"></div>
                    </div>
                    <div id="files" class="files"></div>
                    <div id="attachments" style="display:none"></div>
                </div>
            </div>
            <div class="form-group message-form-footer">
                <div class="message-form-files">
                    {# TODO Intégrer le nouveau design de téléchargement #}
                </div>
                <div class="message-form-buttons">
                    <a class="btn btn-secondary" href="{{ generate_route('app_formation_messages', {'formationId': formationId}) }}">{{ __('Annuler') }}</a>
                    <button type="submit" class="btn btn-primary" id="btn-submit">{{ __('Envoyer le message') }}</button>
                </div>
            </div>
            {{ display_csrf_form() }}
            <input type="hidden" name="form_action" id="form_action" value="fmessage" />
            <input type="hidden" name="redirect" value="{{ generate_route('app_formation_messages', {'formationId': formationId}) }}" />
            <input type="hidden" name="idformation" value="{{ formationId }}" />
        </form>
    </div>
{% endblock %}
