<form class="form" method="post" action="">
    <div class="form-group">
        <label class="control-label" for="title">{{ __('Nom de l\'évènement') }} *</label>
        <div class="controls">
            <input class="form-control" type="text" id="title" name="title" value="{{ event ? event.getTitle() : '' }}" required placeholder="{{ __('Nom de l\'évènement') }}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label" for="description">{{ __('Description') }}</label>
        <div class="controls">
            <textarea class="ckeditor-extraLight" id="modal-event-ckeditor" name="description">{{ event ? event.getDescription() : '' }}</textarea>
        </div>
    </div>

    <div class="form-group">
        <div class="row">
            <div class="col-md-6">
                <label class="control-label" for="event-image">{{ __('Image de couverture de l\'évènement') }}</label>
                <span class="help-block">
                    {{ __('Cette image sera affichée dans la liste de vos évènements.') }}
                    <br>
                    {{ __('Taille recommandée : 700px x 300px') }}
                </span>
            </div>
            <div class="col-md-6">
                <div class="form-group-image input-hidden" id="event-image-container">
                    <div class="form-group-image-preview" style="background-image: url('{{ event and event.getImage() ? event.getImage() : '' }}');"></div>
                    <div class="form-group-image-overlay"></div>
                    <div class="form-group-image-buttons">
                        <button type="button" onclick="return selectFileWithCKFinder('event-image');"><i class="nc-icon nc-icon-photo-1"></i> {{ __('Parcourir') }}</button>
                        <button type="button" onclick="return openBgGallery('event-image');"><i class="nc-icon nc-icon-url-1"></i> Unsplash</button>
                        <button type="button" onclick="removeImage('event-image');"><i class="nc-icon nc-icon-trash"></i> {{ __('Supprimer') }}</button>
                    </div>
                </div>
                <input id="event-image" name="event-image" type="hidden" value="{{ event and event.getImage() ? event.getImage() : null }}">
            </div>
        </div>
    </div>

    <div class="form-group">
        <div class="form-row m-b-10">
            <div class="controls">
                <label class="control-label" for="date_start">{{ __('Date de début') }} *</label>
                <div class="controls" id="date_start_container">
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                        <input class="form-control input-large datepicker" id="date_start" name="date_start" type="text"
                               value="{{ event ? event.getDateStart().format('Y-m-d H:i:s') : '' }}" required/>
                    </div>
                </div>
            </div>
            <div class="controls">
                <label class="control-label" for="date_end">{{ __('Date de fin') }} *</label>
                <div class="controls" id="date_end_container">
                    <div class="input-group">
                        <span class="input-group-addon"><i class="fa fa-clock-o"></i></span>
                        <input class="form-control input-large datepicker" id="date_end" name="date_end" type="text"
                               value="{{ event ? event.getDateEnd().format('Y-m-d H:i:s') : '' }}" required/>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <select id="timezone" name="timezone" data-rel="select2">
                {{ selectTimeZones|raw }}
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label" for="location">{{ __('Emplacement') }}</label>
        <div class="controls">
            <div class="form-inline">
                <div class="checkbox">
                    <label for="place_type_online">
                        <input type="radio" name="place_type" id="place_type_online" value="online" {{ not event or event.getPlaceType() == constant('Learnybox\\Entity\\Formation\\Event\\FormationEvent::PLACE_TYPE_ONLINE') ? 'checked' : '' }} onchange="if ($(this).is(':checked')) $('#location').attr('placeholder', 'Lien de la visioconférence'); else $('#location').attr('placeholder', 'Adresse postale');"> {{ __('En ligne') }}
                    </label>
                </div>
                <div class="checkbox">
                    <label for="place_type_on_site" style="padding-left: 20px;">
                        <input type="radio" name="place_type" id="place_type_on_site" value="on_site" {{ event and event.getPlaceType() == constant('Learnybox\\Entity\\Formation\\Event\\FormationEvent::PLACE_TYPE_ON_SITE') ? 'checked' : '' }} onchange="if ($(this).is(':checked')) $('#location').attr('placeholder', 'Adresse postale'); else $('#location').attr('placeholder', 'Lien de la visioconférence');"> {{ __('En présentiel') }}
                    </label>
                </div>
            </div>
            <input class="form-control" type="text" id="location" name="location" value="{{ event ? event.getLocation() : '' }}" placeholder="{{ not event or event.getPlaceType() == constant('Learnybox\\Entity\\Formation\\Event\\FormationEvent::PLACE_TYPE_ONLINE') ? __('Lien de la visioconférence') : __('Adresse postale') }}">
            <div class="checkbox m-t-10">
                <label for="hide_location" style="padding-left: 0">
                    <input type="checkbox" name="hide_location" id="hide_location" {{ event and event.getHideLocation() ? 'checked' : '' }}> {{ __('Cacher l\'adresse postale ou le lien de la visioconférence') }}
                </label>
            </div>
        </div>
    </div>

    <div class="form-group {{ not formationGroups ? 'm-b-0 border-0' : '' }}">
        <label class="control-label" for="max_subscribers">{{ __('Nombre de participants maximum') }}</label>
        <div class="controls">
            <input class="form-control" type="number" step="1" min="0" id="max_subscribers" name="max_subscribers" value="{{ event ? event.getMaxSubscribers() : 0 }}">
        </div>
    </div>

    {% if formationGroups %}
        <div class="form-group m-b-0 border-0">
            <label class="control-label m-b-0" for="formation_groups">{{ __('Groupes de membres') }}</label>
            <span class="help-block">{{ __('Sélectionnez les groupes de membres qui pourront voir et s\'inscrire à cet évènement.') }}<br>{{ __('Par défaut, tous les membres peuvent le voir.') }}</span>
            <div class="controls">
                <select class="form-control" name="formation_groups[]" id="formation_groups" data-rel="select2" multiple>
                    {% for formationGroup in formationGroups %}
                        <option value="{{ formationGroup.getIdgroupe() }}" {{ event and formationGroup.getIdgroupe() in event.getFormationGroups() ? 'selected' : '' }}>{{ formationGroup.getNomgroupe() }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
    {% endif %}

    <p>{{ __('Des rappels automatiques par email seront envoyés aux inscrits 24h et 2h avant le début de l’évènement.') }}</p>

    <div class="form-actions" id="formactions">
        {{ display_csrf_form() }}
        <input type="hidden" name="form_action" value="{{ event ? 'formation_event_edit' : 'formation_event_add' }}"/>
        <input type="hidden" name="redirect" value="{{ generate_route('app_formation_events', {'formationId': idFormation}) }}"/>
        <input type="hidden" name="id_formation" value="{{ idFormation }}"/>
        {% if event %}
            <input type="hidden" name="event_id" value="{{ event.getId() }}"/>
        {% endif %}
        <button type="submit" name="submit" class="btn btn-primary {{ event ? 'btn-lg' : '' }}">{{ event ? __('Modifier') : __('Valider') }}</button>
        <a class="btn btn-default btn-lg" href="{{ generate_route('app_formation_events', {'formationId': idFormation}) }}">{{ __('Annuler') }}</a>
    </div>
</form>
