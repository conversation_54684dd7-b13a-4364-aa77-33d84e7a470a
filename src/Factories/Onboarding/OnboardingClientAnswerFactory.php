<?php

namespace Learnybox\Factories\Onboarding;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Client\Client;
use Learnybox\Entity\Onboarding\Answer\OnboardingAnswer;
use Learnybox\Entity\Onboarding\Client\OnboardingClientAnswer;

class OnboardingClientAnswerFactory
{
    private EntityManager $em;

    public function __construct(EntityManager $em)
    {
        $this->em = $em;
    }

    public function createOne(OnboardingAnswer $answer, int $clientId): OnboardingClientAnswer
    {
        $client = $this->em->getRepository(Client::class)->find($clientId);

        $onboardingClientAnswer = new OnboardingClientAnswer();
        $onboardingClientAnswer
            ->setQuestion($answer->getQuestion())
            ->setAnswer($answer)
            ->setClient($client)
            ->setDate(new \DateTime('now'));

        return $onboardingClientAnswer;
    }
}
