<?php

namespace Learnybox\Factories\Formation\Document;

use Learnybox\Enums\Formation\Document\FormationDocumentTypeEnum;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Services\Html\AlertHtmlService;

class PifParticulierBuilder extends AbstractFormationDocumentBuilder implements FormationDocumentInterface
{
    protected \DI\Container $container;

    public function __construct()
    {
        $this->container = ContainerBuilderService::getInstance();

        $this->type = FormationDocumentTypeEnum::PIF_PARTICULIER;
        $this->belongToUser = true;
        $this->file = VIEWS_PATH . '/app/formation/documents/templates/PifParticulier.php';
        $this->template = VIEWS_PATH . '/app/formation/documents/templates/PifParticulierTemplate.php';
        $this->publishable = false;
        $this->exportFields = [self::USER_ID, self::GROUP_ID, self::DATE_START, self::DATE_END, self::SEND_MAIL];
    }

    public function getName(): string
    {
        return __('Protocole individuel de formation - Particulier');
    }

    public function getForm(): array
    {
        return [
            'fields' => [
                'content' => [
                    'label' => __('Contenu du document'),
                    'type' => 'textarea',
                    'class' => 'ckeditor-formationDocument',
                    'required' => true,
                ],
                'header' => [
                    'label' => __('En-tête de page'),
                    'type' => 'textarea',
                    'class' => 'ckeditor-small',
                    'required' => false,
                    'alert_value' => $this->container->get(AlertHtmlService::class)->renderInfoAlert(__('Veuillez noter que la hauteur de l’en-tête est limitée. En cas de problème d’affichage, merci d’en réduire le contenu.'))
                ],
                'footer' => [
                    'label' => __('Pied de page'),
                    'type' => 'textarea',
                    'class' => 'ckeditor-small',
                    'required' => false,
                ],
            ],
            'action' => 'formation_document',
            'hidden_fields' => [
                'id_formation' => $this->formationId,
                'type_document' => $this->type
            ]
        ];
    }
}
