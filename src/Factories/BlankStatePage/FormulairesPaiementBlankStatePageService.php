<?php

namespace Learnybox\Factories\BlankStatePage;

use Learnybox\Helpers\Assets;
use Learnybox\Helpers\RouterHelper;

class FormulairesPaiementBlankStatePageService extends AbstractBlankStatePageService implements BlankStatePageServiceInterface
{
    public function __construct(array $data = [])
    {
        $this->title = __('Ajoutez un moyen de paiement afin de vendre votre produit en ligne.');
        $this->createUrl = isset($data['id_formulaire']) && $data['id_formulaire'] ?
            RouterHelper::generate('app_shop_payment_add', ['formulaireId' => $data['id_formulaire']]) :
            ''
        ;
        $this->createUrlLibelle = __('Ajouter un moyen de paiement');

        $this->imageSrc = Assets::getImageUrl('blankstate/payment-methods.svg');
        $this->imageAlt = __('icône liste des moyens de paiement d\'un formulaire de paiement');


//        $this->videoUrlFr = '4pD5FunA3Fw';
//        $this->videoUrlEn = '8W2GoPmCihA';
        parent::__construct($data);
    }
}
