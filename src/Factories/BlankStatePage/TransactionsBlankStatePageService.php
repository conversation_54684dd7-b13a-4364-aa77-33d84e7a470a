<?php

namespace Learnybox\Factories\BlankStatePage;

use Learnybox\Helpers\Assets;

class TransactionsBlankStatePageService extends AbstractBlankStatePageService implements BlankStatePageServiceInterface
{
    public function __construct()
    {
        $this->title = __('Aucune transaction n\'a été générée pour le moment. %s Vendez vos produits et services pour commencer à générer des transactions !', '<br>');

        $this->imageSrc = Assets::getImageUrl('blankstate/transactions.svg');
        $this->imageAlt = __('icône transactions');

        parent::__construct();
    }
}
