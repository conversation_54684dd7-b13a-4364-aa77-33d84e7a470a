<?php

namespace Learnybox\Factories\BlankStatePage;

use Learnybox\Helpers\Assets;

class EvaluationsDistanceBlankStatePageService extends AbstractBlankStatePageService implements BlankStatePageServiceInterface
{
    public function __construct(array $data = [])
    {
        $this->title = __('Retrouvez la liste des demandes d\'évaluation initiées par vos membres.');

        $this->imageSrc = Assets::getImageUrl('blankstate/evaluations.svg');
        $this->imageAlt = __('icône liste des évaluations à distance d\'une formation');

//        $this->videoUrlFr = 'OcnOvuReUTY';
//        $this->videoUrlEn = 'H0xs56rk-L8';
        parent::__construct($data);
    }
}
