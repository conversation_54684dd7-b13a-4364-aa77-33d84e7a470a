<?php

namespace Learnybox\Factories\BlankStatePage;

use Learnybox\Helpers\Assets;
use Learnybox\Helpers\RouterHelper;

class FormulairesProduitsBlankStatePageService extends AbstractBlankStatePageService implements BlankStatePageServiceInterface
{
    public function __construct(array $data = [])
    {
        $this->setData($data);
        $this->title = __('Créez un produit à intégrer dans votre formulaire de paiement afin de vendre votre produit / formation / service en ligne.');
        $this->createUrl = isset($data['id_formulaire']) && $data['id_formulaire'] ?
            RouterHelper::generate('app_shop_produit_add', ['formulaireId' => $data['id_formulaire']]) :
            ''
        ;
        $this->createUrlLibelle = __('Créer un produit');
        $this->createOtherImportUrl = isset($this->data['id-form']) && $this->data['id-form'] ?
            'ImportProduts(' . $this->data['id-form'] . '); return false;' :
            ''
        ;
        $this->createOtherUrlLibelle = __('Importer un produit');

        $this->imageSrc = Assets::getImageUrl('blankstate/products.svg');
        $this->imageAlt = __('icône liste des produits d\'un formulaire de paiement');


//        $this->videoUrlFr = 'L4SD-LmsYTA';
//        $this->videoUrlEn = 'M3O7Y5W27wM';
        parent::__construct($data);
    }
}
