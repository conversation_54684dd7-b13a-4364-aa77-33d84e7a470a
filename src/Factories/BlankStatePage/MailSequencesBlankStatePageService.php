<?php

namespace Learnybox\Factories\BlankStatePage;

use Learnybox\Helpers\Assets;

class MailSequencesBlankStatePageService extends AbstractBlankStatePageService implements BlankStatePageServiceInterface
{
    public function __construct()
    {
        $this->title = __('Envoyez automatiquement des séries d\'emails ou de SMS à vos contacts.');
        $this->createUrl = \Tools::makeLink('app', 'autorepondeur', 'sequence_tutoriel');
        $this->createUrlLibelle = __('Créer une séquence');

        $this->imageSrc = Assets::getImageUrl('blankstate/email-sequences.svg');
        $this->imageAlt = __('icône liste des séquences d\'emails');

//        $this->videoUrlFr = 'fKzqSDTjwWo';
//        $this->videoUrlEn = 'jNpw5PwQPBk';
        parent::__construct();
    }
}
