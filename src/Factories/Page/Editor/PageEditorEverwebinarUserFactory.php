<?php

namespace Learnybox\Factories\Page\Editor;

use Learnybox\Entity\Page\Editor\PageEditorEverwebinarUser;

class PageEditorEverwebinarUserFactory extends PageEditorFactory
{
    /**
     * @param int $idObject
     * @return PageEditorEverwebinarUser
     */
    public function createOne(int $idObject = 0): PageEditorEverwebinarUser
    {
        $pageEditor = new PageEditorEverwebinarUser();

        return $pageEditor;
    }
}
