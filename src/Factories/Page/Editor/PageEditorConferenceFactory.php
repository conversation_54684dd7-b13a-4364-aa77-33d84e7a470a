<?php

namespace Learnybox\Factories\Page\Editor;

use Learnybox\Entity\Conference\Conference;
use Learnybox\Entity\Page\Editor\PageEditorConference;

class PageEditorConferenceFactory extends PageEditorFactory
{
    /**
     * @param int $idConference
     * @return PageEditorConference|null
     */
    public function createOne(int $idConference): ?PageEditorConference
    {
        $conference = $this->em->getRepository(Conference::class)->find($idConference);
        if (!$conference) {
            return null;
        }

        $pageEditor = new PageEditorConference();
        $pageEditor->setConference($conference);

        return $pageEditor;
    }
}
