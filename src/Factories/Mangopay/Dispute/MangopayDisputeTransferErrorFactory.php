<?php

namespace Learnybox\Factories\Mangopay\Dispute;

use Learnybox\Entity\Client\Client;
use Learnybox\Entity\Mangopay\Dispute\MangopayDispute;
use Learnybox\Entity\Mangopay\Dispute\MangopayDisputeTransfertError;
use Learnybox\Enums\Mangopay\Dispute\MangopayDisputeTransfertErrorStatusEnum;

class MangopayDisputeTransferErrorFactory
{
    public static function createOne(
        MangopayDispute $dispute,
        Client $client,
        MangopayDisputeTransfertErrorStatusEnum $status
    ): MangopayDisputeTransfertError {
        $disputeTransfertError = new MangopayDisputeTransfertError();
        $disputeTransfertError->setDispute($dispute);
        $disputeTransfertError->setClient($client);
        $disputeTransfertError->setNbRetries(0);
        $disputeTransfertError->setStatus($status);

        return $disputeTransfertError;
    }
}
