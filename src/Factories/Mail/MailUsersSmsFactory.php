<?php

namespace Learnybox\Factories\Mail;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Mail\Envoi\MailEnvoi;
use Learnybox\Entity\Mail\Regle\MailRegle;
use Learnybox\Entity\Mail\Sequence\MailSequence;
use Learnybox\Entity\Mail\Sequence\MailSequenceStep;
use Learnybox\Entity\Mail\Sms\MailSms;
use Learnybox\Entity\Mail\User\MailUserSms;
use Learnybox\Entity\User\User;
use Learnybox\Services\BaseEntityService;

/**
 * Class MailUsersSmsFactory
 * @package Learnybox\Factories\Mail
 */
class MailUsersSmsFactory extends BaseEntityService
{
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
    }

    public function createOne(User $user, MailSms $mailSms, ?MailEnvoi $mailEnvoi, ?MailSequence $mailSequence, ?MailSequenceStep $mailStep, ?MailRegle $mailRegle, int $open = 0): array
    {
        try {
            $mailUserSms = new MailUserSms();
            $mailUserSms->setClient($user->getClient());
            $mailUserSms->setUser($user);
            $mailUserSms->setSms($mailSms);
            $mailUserSms->setEnvoi($mailEnvoi);
            $mailUserSms->setSequence($mailSequence);
            $mailUserSms->setStep($mailStep);
            $mailUserSms->setRegle($mailRegle);
            $mailUserSms->setOpen($open);
            $mailUserSms->setDateEnvoi(new \DateTime('now'));
            $this->persistAndFlush($mailUserSms);
        } catch (\Exception $e) {
            return ['valid' => false];
        }

        return ['valid' => true, 'id_user_sms' => $mailUserSms->getIdUserSms()];
    }
}
