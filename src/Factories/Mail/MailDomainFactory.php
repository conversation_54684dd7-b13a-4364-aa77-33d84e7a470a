<?php

namespace Learnybox\Factories\Mail;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Client\Client;
use Learnybox\Entity\Mail\Domain\MailDomain;

class MailDomainFactory
{
    private EntityManager $em;

    public function __construct(EntityManager $em)
    {
        $this->em = $em;
    }

    public function createOrGetDomain(string $domainName, int $idClient, string $email = null): ?MailDomain
    {
        $mailDomain = $this->em->getRepository(MailDomain::class)->findOneBy(['name' => $domainName]);
        if ($mailDomain) {
            return $mailDomain;
        }

        $client = $this->em->getRepository(Client::class)->findOneBy(['idClient' => $idClient]);

        $mailDomain = new MailDomain();
        $mailDomain->setName($domainName);
        $mailDomain->setAuthenticated(false);
        $mailDomain->setEmail($email);
        $mailDomain->setConfirmKey(createRandomID());
        $mailDomain->setClient($client);
        $mailDomain->setDate(new \DateTime('now'));
        $this->em->persist($mailDomain);
        $this->em->flush();

        return $mailDomain;
    }
}
