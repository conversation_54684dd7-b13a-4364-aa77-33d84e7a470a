<?php

namespace Learnybox\Factories\Import;

use Learnybox\Entity\Client\Client;
use Learnybox\Entity\Mail\Import\MailImport;

class MailImportFactory
{
    public static function createOne(
        Client $client,
        string $type,
        string $fileUrl,
        string $fields,
        int $idSequence,
        string $tags,
        bool $dontStartSequence,
        bool $rgpd,
        bool $rgpdAff,
        int $idAffilie,
        string $etat,
        string $rapport,
        string $data = '',
        int $nbContacts = 0,
        string $emailsDne = '',
    ): MailImport {
        $mailImport = new MailImport();
        $mailImport->setClient($client);
        $mailImport->setType($type);
        $mailImport->setFileUrl($fileUrl);
        $mailImport->setFields($fields);
        $mailImport->setIdSequence($idSequence);
        $mailImport->setTags($tags);
        $mailImport->setData($data);
        $mailImport->setDontStartSequence($dontStartSequence);
        $mailImport->setRgpd($rgpd);
        $mailImport->setRgpdAff($rgpdAff);
        $mailImport->setIdAffilie($idAffilie);
        $mailImport->setNbContacts($nbContacts);
        $mailImport->setEmailsDne($emailsDne);
        $mailImport->setEtat($etat);
        $mailImport->setRapport($rapport);
        $mailImport->setDate(new \DateTime());
        $mailImport->setDateProcessed(null);

        return $mailImport;
    }
}
