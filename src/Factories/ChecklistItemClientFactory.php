<?php

namespace Learnybox\Factories;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Checklist\Item\ChecklistItem;
use Learnybox\Entity\Checklist\Item\ChecklistItemClient;
use Learnybox\Entity\Client\Client;
use Learnybox\Repository\Checklist\Item\ChecklistItemRepository;

/**
 * Class ChecklistItemClientFactory
 * @package Learnybox\Factories
 */
class ChecklistItemClientFactory
{
    /**
     * @var ChecklistItemRepository
     */
    private $checklistItemRepository;

    /**
     * ChecklistItemClientFactory constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->checklistItemRepository = $em->getRepository(ChecklistItem::class);
    }

    /**
     * @param ChecklistItem $checklistItem
     * @param int $sectionId
     * @param Client $client
     * @param int|null $objectId
     * @param bool $checked
     * @param string|null $checkedDate
     * @return ChecklistItemClient|null
     */
    public function createOne(ChecklistItem $checklistItem, int $sectionId, Client $client, int $objectId = null, bool $checked = false, string $checkedDate = null): ?ChecklistItemClient
    {
        $checklistItemClient = new ChecklistItemClient();
        $checklistItemClient->setChecklistItem($checklistItem);
        $checklistItemClient->setSectionId($sectionId);
        $checklistItemClient->setClient($client);
        $checklistItemClient->setObjectId($objectId);
        $checklistItemClient->setChecked($checked);
        $checklistItemClient->setCheckedDate($checkedDate);

        return $checklistItemClient;
    }

    /**
     * Create many ChecklistItemClients for one section (without objects)
     *
     * @param Client $client
     * @param string $sectionType
     * @param int $sectionId
     * @return ChecklistItemClient[]
     */
    public function createManyFromSection(Client $client, string $sectionType, int $sectionId): array
    {
        $checklistItemsClients = [];

        $checklistItems = $this->checklistItemRepository->getBySectionTypeByObjectType($sectionType, null);
        $checklistItems = $checklistItems !== null ? $checklistItems : [];

        foreach ($checklistItems as $checklistItem) {
            $checklistItemsClients[] = $this->createOne(
                $checklistItem,
                $sectionId,
                $client
            );
        }

        return $checklistItemsClients;
    }

    /**
     * Create ChecklistItemClients for one object
     *
     * @param Client $client
     * @param string $sectionType
     * @param int $sectionId
     * @param string $objectType
     * @param int $objectId
     * @param string| $objectSubtype
     * @return ChecklistItemClient[]
     */
    public function createManyFromObject(Client $client, string $sectionType, int $sectionId, string $objectType, int $objectId, string $objectSubtype = null)
    {
        $checklistItemsClients = [];

        $checklistItems = $this->checklistItemRepository->getForObject($sectionType, $objectType, $objectSubtype);
        $checklistItems = $checklistItems !== null ? $checklistItems : [];

        foreach ($checklistItems as $checklistItem) {
            $checklistItemsClients[] = $this->createOne(
                $checklistItem,
                $sectionId,
                $client,
                $objectId
            );
        }

        return $checklistItemsClients;
    }
}
