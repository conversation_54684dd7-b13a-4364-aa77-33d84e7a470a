<?php

namespace Learnybox\Factories\Abonnement;

use Learnybox\Entity\Braintree\Abonnement\BraintreeAbonnement;
use Learnybox\Entity\Braintree\Charge\BraintreeCharge;
use Learnybox\Enums\Transaction\TransactionTypeEnum;

/**
 * Class AbonnementBraintreeFactory
 * @package Learnybox\Factories\Abonnement
 */
class AbonnementBraintreeFactory extends AbstractAbonnementFactory implements AbonnementFactoryInterface
{
    use ShopDiscountRecurrenceTrait;

    /**
     * @param BraintreeCharge $charge
     * @return array
     */
    public function createAbonnementFromCharge($charge): array
    {
        $log = "createAbonnementIfNecessary\n";
        $this->abonnementEntity = BraintreeAbonnement::class;

        $abonnement = $this->createAbonnementIfNecessary($charge);
        if (!$abonnement) {
            $log .= __("Aucun abonnement à créer\n");
            return ['valid' => true, 'id_abonnement' => 0, 'log' => $log];
        }

        $log .= __("Abonnement à créer :\n");
        $log .= "nb_payments : " . $abonnement->getNbPayments() . "\n";
        $log .= "amount : " . $abonnement->getAmount() . "\n";
        $log .= "next_date : " . $abonnement->getDate()->format('Y-m-d H:i:s') . "\n";

        //add missing vars
        $abonnement->setPaymentToken($charge->getPaymentToken());
        $abonnement->setCustomerId($charge->getCustomerId());
        $this->persistAndFlush($abonnement);

        $idAbonnement = $abonnement->getIdAbonnement();
        $log .= $this->createShopDiscountRecurrence($abonnement, $charge, TransactionTypeEnum::BRAINTREE);
        $log .= $this->updateSubscriptionAmount($abonnement, $charge);

        //mise à jour de la charge
        $charge->setIdAbonnement($idAbonnement);
        $this->persistAndFlush($charge);

        return ['valid' => true, 'id_abonnement' => $idAbonnement, 'log' => $log];
    }

    /**
     * @param float $amount
     * @return string
     */
    public function formatAmount(float $amount): string
    {
        return number_format($amount, 2, '.', '');
    }
}
