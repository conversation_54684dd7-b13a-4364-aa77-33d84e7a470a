<?php

namespace Learnybox\Services\User\Type;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\User\User;
use Learnybox\Enums\User\UserTypeEnum;
use Learnybox\Services\DI\ContainerBuilderService;

class UserTypeService
{
    public function getTypes(int $userId, bool $getTypesWithLabels = false): array
    {
        $container = ContainerBuilderService::getInstance();

        $types = $container->get(EntityManager::class)
            ->getRepository(User::class)
            ->getUserTypes($userId);

        if (!$types) {
            return [];
        }

        if ($getTypesWithLabels) {
            return array_map(
                fn($item) => UserTypeEnum::getI18nLibelle($item),
                array_keys(array_filter(
                    $types,
                    fn($type) => $type > 0
                ))
            );
        }

        return array_keys(array_filter($types, fn($type) => $type > 0));
    }

    public function hasType(int $userId, UserTypeEnum $type): bool
    {
        return in_array($type->value, $this->getTypes($userId));
    }
}
