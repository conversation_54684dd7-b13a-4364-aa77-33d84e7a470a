<?php

namespace Learnybox\Services\User\Profile\Light\Activity\Detail\Builder;

use DI\Container;
use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Formation\Comment\FormationComment;
use Learnybox\Entity\Formation\Module\FormationModule;
use Learnybox\Helpers\RouterHelper;
use Learnybox\Helpers\TextHelper;
use Learnybox\Services\DI\ContainerBuilderService;

class LightCommentActivityDetailBuilder
{
    private Container $container;
    private FormationComment $comment;

    public function __construct(FormationComment $comment)
    {
        $this->comment = $comment;
        $this->container = ContainerBuilderService::getInstance();
    }

    public function build(): array
    {
        $data = [
            [
                'label' => __('Formation'),
                'value' => html_entity_decode($this->comment->getFormation()->getNomformation()),
            ]
        ];

        if ($this->comment->getIdmodule()) {
            $module = $this->container
                ->get(EntityManager::class)
                ->getRepository(FormationModule::class)
                ->find($this->comment->getIdmodule());

            if ($module) {
                $data[] = [
                    'label' => __('Module'),
                    'value' => html_entity_decode($module->getNommodule()),
                ];
            }
        }

        if ($this->comment->getFormationPage()) {
            $data[] = [
                'label' => __('Page'),
                'value' => html_entity_decode($this->comment->getFormationPage()->getTitre()),
                'url' => RouterHelper::generate('formation_formation', [
                    'id' => $this->comment->getFormation()->getIdformation(),
                    'idmodule' => $this->comment->getIdmodule(),
                    'idpage' => $this->comment->getFormationPage()->getIdpage()
                ])
            ];
        }

        $data[] = [
            'label' => __('Commentaire'),
            'value' => TextHelper::truncateHtml(
                html_entity_decode(stripslashes($this->comment->getCommentaire())),
                100
            ),
        ];

        return $data;
    }
}
