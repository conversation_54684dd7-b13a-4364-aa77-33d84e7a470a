<?php

namespace Learnybox\Services\User\Profile\Full\Info;

use Learnybox\Entity\User\Config\UserConfig;
use Learnybox\Entity\User\User;
use Learnybox\Enums\TwoFactorEnum;
use Learnybox\Helpers\CountryHelper;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Services\Partials\ConfigAddressService;
use Learnybox\Services\User\Profile\AbstractUserProfile;
use Learnybox\Services\User\Profile\UserProfileInterface;
use Learnybox\Services\UserConfigService;

class UserProfileFullInfoService extends AbstractUserProfile implements UserProfileInterface
{
    public function get(int $userId, ?array $params = null): array
    {
        $userInfos = $this->getUserGeneralInfos($userId);

        foreach ($userInfos as $categoryKey => &$category) {
            foreach ($category as $itemKey => $item) {
                if (empty($item['value'])) {
                    unset($category[$itemKey]);
                }
            }

            if (empty($category)) {
                unset($userInfos[$categoryKey]);
            }
        }

        unset($category);

        return $userInfos;
    }

    public function getUserGeneralInfos(int $userId): array
    {
        $user = $this->getEntityManager()->getRepository(User::class)->find($userId);
        $userConfig = $this->getEntityManager()->getRepository(UserConfig::class)->findBy(['user' => $userId]);

        $userConfigArray = [];
        foreach ($userConfig as $config) {
            $userConfigArray[$config->getName()] = $config->getValue();
        }

        $street = !empty($userConfigArray['adresse']) ? $userConfigArray['adresse'] : '';
        $street2 = !empty($userConfigArray['adresse2']) ? $userConfigArray['adresse2'] : '';
        $postalCode = !empty($userConfigArray['code_postal']) ? $userConfigArray['code_postal'] : '';
        $city = !empty($userConfigArray['ville']) ? $userConfigArray['ville'] : '';

        $country = $region = '';
        if (!empty($userConfigArray['pays'])) {
            $country = CountryHelper::getCountryByCode($userConfigArray['pays']);
            if (!empty($userConfigArray['region'])) {
                $region = ConfigAddressService::getStateByCountry($userConfigArray['pays'], $userConfigArray['region']);
            }
        }

        $adresse = "$street <br>";
        $adresse .= $street2 ? "$street2 <br>" : '';
        $adresse .= $postalCode ? "$postalCode " : '';
        $adresse .= $city ? "$city <br>" : '';
        $adresse .= $region ? "$region, $country" : $country;

        $userInfos[] = [
            [
                'icon' => [
                    'class' => 'fa-regular fa-phone',
                    'background' => '#FFEEDB',
                    'color' => '#FFC56E',
                ],
                'label' => __('Téléphone'),
                'value' => $userConfigArray['tel'] ?? ''
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-phone-office',
                    'background' => '#FCEBF6',
                    'color' => '#E339A4',
                ],
                'label' => __('Bureau'),
                'value' => $userConfigArray['tel_office'] ?? ''
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-mobile',
                    'background' => '#DBECDD',
                    'color' => '#74BB7D',
                ],
                'label' => __('Mobile'),
                'value' => $userConfigArray['mobile'] ?? ''
            ]
        ];

        $userInfos[] = [
            [
                'icon' => [
                    'class' => 'fa-regular fa-location-dot',
                    'background' => '#FCEBF6',
                    'color' => '#E339A4',
                ],
                'label' => __('Adresse postale'),
                'value' => $adresse
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-earth-europe',
                    'background' => '#FFEEDB',
                    'color' => '#FFC56E',
                ],
                'label' => __('Adresse IP'),
                'value' => $user->getIp()
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-input-numeric',
                    'background' => '#DBECDD',
                    'color' => '#74BB7D',
                ],
                'label' => __('Numéro de TVA intracommunautaire'),
                'value' => $userConfigArray['tva_intracom'] ?? ''
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-building',
                    'background' => '#FAD3CB',
                    'color' => '#EE5340',
                ],
                'label' => __('Société'),
                'value' => $userConfigArray['societe'] ?? ''
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-briefcase',
                    'background' => '#E0F2FE',
                    'color' => '#38BDF8',
                ],
                'label' => __('Profession'),
                'value' => $userConfigArray['profession'] ?? ''
            ]
        ];

        $userInfos[] = [
            [
                'icon' => [
                    'class' => 'fa-regular fa-user',
                    'background' => '#FCEBF6',
                    'color' => '#E339A4',
                ],
                'label' => __('Pseudo'),
                'value' => $userConfigArray['pseudo'] ?? ''
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-globe'
                ],
                'label' => __('Site Web'),
                'value' => $userConfigArray['site_web'] ?? ''
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-brands fa-x-twitter',
                    'background' => '#F4F4F4',
                    'color' => '#34364B',
                ],
                'label' => __('Twitter'),
                'value' => $userConfigArray['twitter'] ?? ''
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-brands fa-facebook',
                    'background' => '#F1F2FF',
                    'color' => '#4A58E4',
                ],
                'label' => __('Facebook'),
                'value' => $userConfigArray['facebook'] ?? ''
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-brands fa-youtube',
                    'background' => '#F2C9C3',
                    'color' => '#AB2328',
                ],
                'label' => __('YouTube'),
                'value' => $userConfigArray['youtube'] ?? ''
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-brands fa-linkedin-in',
                    'background' => '#F1F2FF',
                    'color' => '#4A58E4',
                ],
                'label' => __('LinkedIn'),
                'value' => $userConfigArray['linkedin'] ?? ''
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-brands fa-instagram',
                    'background' => '#FCEBF6',
                    'color' => '#E339A4',
                ],
                'label' => __('Instagram'),
                'value' => $userConfigArray['instagram'] ?? ''
            ],
            [
                'icon' => [
                    'class' => 'fa-regular fa-brands fa-tiktok',
                    'background' => '#34364B',
                    'color' => '#FFFFFF',
                ],
                'label' => __('TikTok'),
                'value' => $userConfigArray['tiktok'] ?? ''
            ]
        ];


        $technicalInformations = [
            [
                'icon' => [
                    'class' => 'fa-regular fa-envelope',
                    'background' => '#E0F2FE',
                    'color' => '#38BDF8',
                ],
                'label' => __('Notification par email'),
                'value' => $user->getNewsletter() ? __('Oui') : __('Non')
            ],
            [
                'icon' => [
                    'class' => 'fa-solid fa-shield-halved',
                    'background' => '#FAD3CB',
                    'color' => '#EE5340',
                ],
                'label' => __('Accès aux données sensibles'),
                'value' => $user->getContactTechnique() ? __('Oui') : __('Non'),
                'tooltip' => [
                    'title' => __('Un administrateur qui a accès aux données sensibles peut accéder à votre abonnement et factures %s, aux moyens de paiement, clés API, etc.', LB) . ' ' . __('Seul le propriétaire du compte %s peut modifier cette option.', LB)
                ]
            ]
        ];
        if ($userId === $_SESSION['user_id']) {
            $container = ContainerBuilderService::getInstance();
            $twoFactor = $container->get(UserConfigService::class)->get(TwoFactorEnum::TWO_FACTOR->value, $user);
            $mfaActivated = $twoFactor && $twoFactor !== TwoFactorEnum::TWO_FACTOR_NONE;
            if ($mfaActivated) {
                $value = '<a class="user_edit_action text-success cursor-pointer" data-tab-action="user_mfa">' . __('Double authentification activée') . '</a>';
            } else {
                $value = '<a class="user_edit_action text-danger cursor-pointer" data-tab-action="user_mfa">' . __('Double authentification désactivée') . '</a>';
            }
            $technicalInformations[] = [
                'icon' => [
                    'class' => 'fa-regular fa-lock',
                    'background' => '#FAD3CB',
                    'color' => '#EE5340',
                ],
                'label' => __('Double authentification'),
                'value' => $value
            ];
        }
        $userInfos[] = $technicalInformations;

        $userInfos[] = [
            [
                'icon' => [
                    'class' => 'fa-regular fa-file-lines',
                    'background' => '#F1F2FF',
                    'color' => '#4A58E4',
                ],
                'label' => __('Description'),
                'value' => $userConfigArray['description'] ?? ''
            ]
        ];

        $userConfigs = [];
        $customFields = $this->getContainer()->get(UserConfigService::class)->getCustomFields($userId);
        /** @var UserConfig $customField */
        foreach ($customFields as $customField) {
            $userConfigs[] = [
                [
                    'icon' => [
                        'class' => 'fa-regular fa-input-text',
                        'background' => '#F1F2FF',
                        'color' => '#4A58E4',
                    ],
                    'label' => $customField->getName(),
                    'value' => $customField->getValue()
                ]
            ];
        }

        $userInfos[] = array_merge(...$userConfigs);

        return $userInfos;
    }
}
