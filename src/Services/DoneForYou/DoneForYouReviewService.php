<?php

namespace Learnybox\Services\DoneForYou;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\DoneForYou\DoneForYouReview;
use Learnybox\Entity\DoneForYou\DoneForYouService;
use Learnybox\Entity\Facture\Text\FactureTextCondition;
use Learnybox\Enums\Facture\Text\FactureTextConditionFieldEnum;
use Learnybox\Enums\Facture\Text\FactureTextConditionOperatorEnum;
use Learnybox\Services\BaseEntityService;

class DoneForYouReviewService extends BaseEntityService
{
    /**
     * @param EntityManager $entityManager
     */
    public function __construct(EntityManager $entityManager)
    {
        $this->em = $entityManager;
        $this->repository = $this->em->getRepository(DoneForYouReview::class);
    }
}
