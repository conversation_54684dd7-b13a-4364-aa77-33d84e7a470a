<?php

namespace Learnybox\Services\HumanResources\Collaborator;

use Doctrine\ORM\EntityManager;
use Learnybox\Helpers\Assets;
use Learnybox\Entity\HumanResources\Collaborator\Collaborator;
use Learnybox\Entity\HumanResources\Collaborator\CollaboratorDocument;
use Learnybox\Factories\HumanResources\Collaborator\CollaboratorDocumentFactory;
use Learnybox\Forms\ObjectHydrator;
use Learnybox\Repository\HumanResources\Collaborator\CollaboratorDocumentRepository;
use Learnybox\Services\BaseEntityService;
use Learnybox\Services\Aws\S3\MediasService;

class CollaboratorDocumentService extends BaseEntityService
{
    const COLLABORATOR_DOCUMENT_UPLOAD_FOLDER = 'master/human-resources/%s/';
    const CONFIDENTIALITY_AGREEMENT_EXTERNAL_FILE_PATH_PDF = LB_HTTP . '/assets/pdf/hr/Accord_confidentialite_prestataire.pdf';
    const HEALTH_INSURANCE_AGREEMENT_FILE_PATH_PDF = LB_HTTP . '/assets/pdf/hr/mutuelle_swisslife.pdf';
    const HEALTH_INSURANCE_REJECTION_FILE_PATH_PDF = LB_HTTP . '/assets/pdf/hr/dispense_adhesion_mutuelle.pdf';
    const MEAL_TICKETS_AGREEMENT_FILE_PATH_PDF = LB_HTTP . '/assets/pdf/hr/Bulletin_adhesion_TR.pdf';

    /**
     * @var MediasService
     */
    private $s3Container;

    /**
     * @var CollaboratorDocumentFactory
     */
    private $collaboratorDocumentFactory;

    /**
     * @var ObjectHydrator
     */
    private $objectHydrator;

    /**
     * CollaboratorDocumentService constructor.
     * @param EntityManager $entityManager
     * @param MediasService $mediasService
     * @param CollaboratorDocumentFactory $collaboratorDocumentFactory
     * @param ObjectHydrator $objectHydrator
     */
    public function __construct(
        EntityManager $entityManager,
        MediasService $mediasService,
        CollaboratorDocumentFactory $collaboratorDocumentFactory,
        ObjectHydrator $objectHydrator
    ) {
        $this->em = $entityManager;
        $this->repository = $this->em->getRepository(CollaboratorDocument::class);
        $this->s3Container = $mediasService;
        $this->collaboratorDocumentFactory = $collaboratorDocumentFactory;
        $this->objectHydrator = $objectHydrator;
    }

    /**
     * @param string $selectedType
     * @param bool $external
     * @return mixed
     */
    public function generateSelectCollaboratorDocumentType(string $selectedType = '', $external = false)
    {
        $parser = \Eden_Template::i();

        $documentTypes = CollaboratorDocument::getTypesChoicesForm($external);

        $output = $parser->set('documentTypes', $documentTypes)
            ->set('selectedType', $selectedType)
            ->parsePHP(FORMS_PATH . '/adminlb0410/human_resources/collaborator/selectCollaboratorDocumentType.php');

        return $output;
    }

    /**
     * @param Collaborator $collaborator
     * @return string
     */
    public function getUploadDirectory(Collaborator $collaborator): string
    {
        return sprintf(self::COLLABORATOR_DOCUMENT_UPLOAD_FOLDER, $collaborator->getId());
    }

    /**
     * @param $collaborator
     * @return array
     */
    public function uploadDocumentFile($collaborator): array
    {
        $fileName = $_FILES['file_path']['name'];
        $directory = $this->getUploadDirectory($collaborator);

        $uploadDocumentS3 = $this->uploadDocumentFileS3($directory, $fileName, $_FILES['file_path']['tmp_name']);
        if (false === $uploadDocumentS3['valid']) {
            return ['valid' => false, 'message' => $uploadDocumentS3['message']];
        }

        return ['valid' => true, 'url' => $uploadDocumentS3['url']];
    }

    private function uploadDocumentFileS3(string $directory, string $fileName, string $filePath): array
    {
        $createDirectory = $this->s3Container->createDirectory($directory);
        if (false === $createDirectory['valid']) {
            return ['valid' => false, 'message' => __('Impossible de créer le dossier.')];
        }

        $uploadFile = $this->s3Container->uploadFile($filePath, $directory . $fileName);
        if (false === $uploadFile['valid']) {
            return ['valid' => false, 'message' => __('Impossible d\'enregistrer le document.')];
        }

        return ['valid' => true, 'url' => $uploadFile['url']];
    }

    /**
     * @param int $collaboratorDocumentId
     * @return array
     */
    public function deleteDocument(int $collaboratorDocumentId): array
    {
        $document = $this->repository->find($collaboratorDocumentId);
        if (false === $document) {
            return ['valid' => false, 'message' => __('Ce document n\'existe pas.')];
        }

        $deleteFile = $this->s3Container->deleteFile($document->getFilePath());
        if (false === $deleteFile['valid']) {
            return ['valid' => false, 'message' => __('Impossible de supprimer le document.')];
        }

        return ['valid' => true];
    }

    /**
     * @param Collaborator $collaborator
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function insertDocument(Collaborator $collaborator, array $params): array
    {
        $params['uploaded_at'] = new \DateTime();

        $collaboratorDocument = $this->collaboratorDocumentFactory->createOne($collaborator);
        $this->objectHydrator->hydrate($collaboratorDocument, $params);

        try {
            $this->persistAndFlush($collaboratorDocument);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Une erreur est survenue lors de la création du document.')];
        }

        return ['valid' => true, 'collaboratorDocument' => $collaboratorDocument];
    }

    /**
     * @param Collaborator $collaborator
     * @return array
     * @throws \Exception
     */
    public function insertDocumentConfidentialityAgreement(Collaborator $collaborator): array
    {
        $params = [
            'type' => CollaboratorDocument::TYPE_CONFIDENTIALITY_AGREEMENT,
            'name' => __('Accord de confidentialité'),
            'file_path' => self::CONFIDENTIALITY_AGREEMENT_EXTERNAL_FILE_PATH_PDF,
            'to_sign' => 1
        ];

        return $this->insertDocument($collaborator, $params);
    }

    /**
     * @param Collaborator $collaborator
     * @return array
     * @throws \Exception
     */
    public function insertDocumentHealthInsuranceAgreement(Collaborator $collaborator): array
    {
        $params = [
            'type' => CollaboratorDocument::TYPE_OTHER,
            'name' => __('Formulaire d\'adhésion à la mutuelle'),
            'file_path' => self::HEALTH_INSURANCE_AGREEMENT_FILE_PATH_PDF,
            'to_sign' => 1
        ];

        return $this->insertDocument($collaborator, $params);
    }

    /**
     * @param Collaborator $collaborator
     * @return array
     * @throws \Exception
     */
    public function insertDocumentHealthInsuranceRejection(Collaborator $collaborator): array
    {
        $params = [
            'type' => CollaboratorDocument::TYPE_OTHER,
            'name' => __('Formulaire de refus de la mutuelle'),
            'file_path' => self::HEALTH_INSURANCE_REJECTION_FILE_PATH_PDF,
            'to_sign' => 1
        ];

        return $this->insertDocument($collaborator, $params);
    }

    /**
     * @param Collaborator $collaborator
     * @return array
     * @throws \Exception
     */
    public function insertDocumentMealTicketsAgreement(Collaborator $collaborator): array
    {
        $params = [
            'type' => CollaboratorDocument::TYPE_OTHER,
            'name' => __('Formulaire d\'adhésion aux tickets restaurant'),
            'file_path' => self::MEAL_TICKETS_AGREEMENT_FILE_PATH_PDF,
            'to_sign' => 1
        ];

        return $this->insertDocument($collaborator, $params);
    }
}
