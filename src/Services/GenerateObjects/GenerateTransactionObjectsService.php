<?php

namespace Learnybox\Services\GenerateObjects;

use DateTime;
use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Client\Client;
use Learnybox\Entity\Gocardles\Order\GocardlesOrder;
use Learnybox\Entity\Mangopay\Account\MangopayAccount;
use Learnybox\Entity\Mangopay\Order\MangopayOrder;
use Learnybox\Entity\Shop\Cart\ShopCart;
use Learnybox\Entity\Shop\Produit\ShopProduit;
use Learnybox\Entity\Stripe\Charge\StripeCharge;
use Learnybox\Entity\Transaction\Error\TransactionError;
use Learnybox\Entity\Transaction\Error\TransactionErrorStatus;
use Learnybox\Entity\Transaction\Transaction;
use Learnybox\Enums\IntegrationEnum;
use Learnybox\Enums\Subscription\SubscriptionTypeEnum;
use Learnybox\Factories\Abonnement\AbonnementFactoryInterface;
use Learnybox\Factories\Abonnement\AbonnementGocardlessFactory;
use Learnybox\Factories\Abonnement\AbonnementLearnypayFactory;
use Learnybox\Factories\Abonnement\AbonnementStripeFactory;
use Learnybox\Forms\Transaction\FactureTextForm;
use Learnybox\Services\Abonnement\AbonnementService;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Services\Transaction\Error\TransactionErrorHistoryService;

class GenerateTransactionObjectsService
{
    private EntityManager $em;
    private \Shop_Customers $customers;
    private \Transactions $transactions;
    private AbonnementLearnypayFactory $abonnementLearnypayFactory;
    private AbonnementStripeFactory $abonnementStripeFactory;
    private AbonnementGocardlessFactory $abonnementGocardlessFactory;
    private \Reglages $reglages;
    private AbonnementService $abonnementService;
    private TransactionErrorHistoryService $transactionErrorHistoryService;
    private \Avoirs $avoirs;
    private \Factures $factures;
    private FactureTextForm $factureTextForm;

    public function __construct(
        EntityManager $em,
        \Shop_Customers $customers,
        \Transactions $transactions,
        AbonnementLearnypayFactory $abonnementLearnypayFactory,
        AbonnementStripeFactory $abonnementStripeFactory,
        AbonnementGocardlessFactory $abonnementGocardlessFactory,
        \Reglages $reglages,
        AbonnementService $abonnementService,
        TransactionErrorHistoryService $transactionErrorHistoryService,
        \Avoirs $avoirs,
        \Factures $factures,
        FactureTextForm $factureTextForm
    ) {
        $this->em = $em;
        $this->customers = $customers;
        $this->transactions = $transactions;
        $this->abonnementLearnypayFactory = $abonnementLearnypayFactory;
        $this->abonnementStripeFactory = $abonnementStripeFactory;
        $this->abonnementGocardlessFactory = $abonnementGocardlessFactory;
        $this->reglages = $reglages;
        $this->abonnementService = $abonnementService;
        $this->transactionErrorHistoryService = $transactionErrorHistoryService;
        $this->avoirs = $avoirs;
        $this->factures = $factures;
        $this->factureTextForm = $factureTextForm;
    }

    public function generateTransaction(string $shopPaymentType, array $productsIds = [], int $shopPaymentId = 0, int $shopFormulaireId = 0, string $cancelUrl = '', string $nbPayments = 'x', int $iteration = 1): array
    {
        $cartId = $customerId = 0;
        if ($shopFormulaireId && $productsIds && $shopPaymentId) {
            $insertCustomer = $this->generateCustomer($shopPaymentType, $shopFormulaireId, $productsIds, $shopPaymentId, $iteration);
            if (!$insertCustomer['valid']) {
                return $insertCustomer;
            }
            $customerId = $insertCustomer['id_customer'];

            $cart = $this->em->getRepository(ShopCart::class)->findOneBy(['customer' => $customerId], ['date' => 'DESC']);
            $cartId = $cart->getIdCart();
        }

        $dateNow = new DateTime('now');
        $date1year = new \DateTime('-1 year');
        $dateTimestamp = rand($dateNow->getTimestamp(), $date1year->getTimestamp());

        return $this->insertTransaction($shopPaymentType, $dateTimestamp, $productsIds, $customerId, $shopFormulaireId, $cartId, $cancelUrl, $nbPayments, $iteration);
    }

    public function generateSubscription(string $shopPaymentType, string $nbPayments, int $iteration = 1): array
    {
        $generateTransaction = $this->generateTransaction($shopPaymentType, [], 0, 0, '', $nbPayments, $iteration);
        if (!$generateTransaction['valid'] || !$generateTransaction['charge']) {
            return $generateTransaction;
        }

        $factory = $this->getAbonnementFactory($shopPaymentType);
        if ($factory && method_exists($factory, 'createAbonnementFromCharge')) {
            return $factory->createAbonnementFromCharge($generateTransaction['charge']);
        } else {
            return ['valid' => false, 'message' => __('Type d\'abonnement non pris en charge')];
        }
    }

    public function generateUnpaid(Transaction $transaction): array
    {
        $abonnementId = 0;
        $abonnement = $this->abonnementService->getAbonnementByTransaction($transaction->getIdTrans(), $transaction->getType());
        if ($abonnement) {
            $abonnementId = $abonnement['id_abonnement'];
        }

        $transactionError = $this->em->getRepository(TransactionError::class)->findOneBy(['idTrans' => $transaction->getIdTrans()]);
        if ($transactionError) {
            return ['valid' => true];
        }

        $client = $this->em->getRepository(Client::class)->find($_SESSION['id_client']);

        try {
            $transactionError = new TransactionError();
            $transactionError->setClient($client);
            $transactionError->setIdTrans($transaction->getIdTrans());
            $transactionError->setIdAbonnement($abonnementId);
            $transactionError->setType($transaction->getType());
            $transactionError->setTransactionErrorStatus($this->em->getRepository(TransactionErrorStatus::class)->findOneByName('open'));
            $transactionError->setProduct($transaction->getDescriptor());
            $transactionError->setCustom($transaction->getCustom());

            if ($transaction->getUser()) {
                $transactionError->setUser($transaction->getUser());
            }

            $this->em->persist($transactionError);
            $this->em->flush();
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => $e->getMessage()];
        }

        $status = $this->em->getRepository(TransactionErrorStatus::class)->findOneByName('open');
        $insert = $this->transactionErrorHistoryService->insertHistory($transactionError, $status->getId());
        if (!$insert['valid']) {
            return $insert;
        }

        return ['valid' => true];
    }

    public function generateVoucher(Transaction $transaction): array
    {
        return $this->avoirs->insert_avoir_automatiquement(["id_transaction" => $transaction->getIdTrans()]);
    }

    public function generateInvoice(Transaction $transaction): array
    {
        return $this->factures->insert_facture_automatiquement(["id_transaction" => $transaction->getIdTrans()]);
    }

    public function generateConditionedInformation(): array
    {
        $information = [
            "name" => "Information conditionnée",
            "conditions" => [
                1 => [
                    "id" => "",
                    "field" => "societe",
                    "operator" => "is_not_empty",
                ]
            ],
            "value" => "Texte à afficher"
        ];

        return $this->factureTextForm->create($information);
    }

    private function generateCustomer(string $shopPaymentType, int $shopFormulaireId, array $productsIds, int $shopPaymentId, int $iteration = 1): array
    {
        $customer = [
            "cart" => [
                $shopFormulaireId => $productsIds
            ],
            "discount" => "",
            "id_formulaire" => $shopFormulaireId,
            "nom" => "Shop",
            "prenom" => "Customer $iteration",
            "email" => "Customer-$<EMAIL>",
            "adresse" => "1025 Rue Henri Becquerel",
            "cp" => "34000",
            "ville" => "Montpellier",
            "pays" => "FR",
            "region" => "",
            "telephone" => "",
            "payment_term_button_radio" => ["on"],
            "payment_button_radio" => ["on"],
            "selected_payment" => $shopPaymentId,
            "selected_payment_type" => $shopPaymentType,
        ];

        return $this->customers->insert_customer($customer);
    }

    private function insertTransaction(string $paymentType, int $timestamp, array $productsIds = [], int $customerId = 0, int $shopFormulaireId = 0, int $cartId = 0, string $cancelUrl = '', string $nbPayments = 'x', int $iteration = 1): array
    {
        $products = [];
        foreach ($productsIds as $productId) {
            $shopProduct = $this->em->getRepository(ShopProduit::class)->findOneBy(['idProduit' => $productId]);

            $TTC = rand(150, 2500);
            $TVA = 20;
            $HT = $TTC / (1 + $TVA);

            $products[] = [
                "quantite" => "1",
                "nom" => isset($shopProduct) ? $shopProduct->getNom() : 'Produit',
                "montant_ht" => isset($shopProduct) ? $shopProduct->getMontantHt() : $HT,
                "tva" => isset($shopProduct) ? $shopProduct->getTva() : $TVA,
                "montant_ttc" => isset($shopProduct) ? $shopProduct->getMontantTtc() : $TTC,
                "id_produit" => isset($shopProduct) ? $shopProduct->getIdProduit() : 0
            ];
        }

        if (!$products) {
            $products[] = [
                "quantite" => "1",
                "nom" => 'Produit',
                "montant_ht" => 125,
                "tva" => 20,
                "montant_ttc" => 150,
                "id_produit" => 0
            ];
        }

        $transaction = [
            "id_transaction" => "",
            "date" => date("Y-m-d H:i:s", $timestamp),
            "mode_paiement" => $paymentType,
            "etat" => $iteration % 2 ? Transaction::ETAT_OK : Transaction::ETAT_ERROR,
            "devise" => "EUR",
            "produits" => $products,
            "nom" => "Fake",
            "prenom" => "Customer $iteration",
            "email" => "Fake-customer-$<EMAIL>",
            "adresse" => "1025 Rue Henri Becquerel",
            "code_postal" => "34000",
            "ville" => "Montpellier",
            "pays" => "FR",
            "region" => "",
            "telephone" => "",
            "societe" => "",
            "tva_intracom" => "",
            "nb_credits" => "",
            "id_customer" => $customerId,
            "id_formulaire" => $shopFormulaireId,
            "id_cart" => $cartId,
            'cancel_url' => $cancelUrl
        ];

        if ($paymentType === SubscriptionTypeEnum::LEARNYPAY) {
            $lpAccount = $this->em->getRepository(MangopayAccount::class)->findOneBy(['client' => $_SESSION['id_client']], ['date' => 'DESC']);
            if ($lpAccount) {
                $transaction['lp_account'] = $lpAccount->getIdAccount();
            }
        }

        $generateTransaction = $this->transactions->insert_transaction_v2($transaction);
        if (!$generateTransaction['valid']) {
            return $generateTransaction;
        }

        $transaction = $this->em->getRepository(Transaction::class)->findOneBy(['idTrans' => $generateTransaction['id_trans']]);

        $charge = null;
        if (method_exists($this, 'generate' . ucfirst($paymentType) . 'Charge')) {
            $charge = $this->{'generate' . ucfirst($paymentType) . 'Charge'}($transaction, $nbPayments);
        }

        return ['valid' => true, 'id_trans' => $transaction->getIdTrans(), 'charge' => $charge];
    }

    private function generateLearnypayCharge(Transaction $transaction, string $nbPayments = 'x'): ?MangopayOrder
    {
        $lpAccount = $this->em->getRepository(MangopayAccount::class)->findOneBy(['client' => $_SESSION['id_client']], ['date' => 'DESC']);
        if (!$lpAccount) {
            return null;
        }

        $container = ContainerBuilderService::getInstance();
        return $container->get(GenerateLearnypayObjectsService::class)->generateCharge($lpAccount, $transaction, $nbPayments);
    }

    private function generateStripeCharge(Transaction $transaction, string $nbPayments = 'x'): StripeCharge
    {
        $client = $this->em->getRepository(Client::class)->find($_SESSION['id_client']);

        $checkout = [
            "stripe_secret_key" => $this->reglages->appGetParametreByName('stripe_secret_key')['value'] ?? '',
            "stripe_public_key" => $this->reglages->appGetParametreByName('stripe_public_key')['value'] ?? '',
            "devise" => "EUR",
            "payment" => "subscription",
            "nb_payments" => $nbPayments,
            "subscription_time" => 1,
            "subscription_timetype" => "mois",
            "augmentation" => 0,
            "recurrences" => ["option" => false],
            "trial_period" => 0,
            "montant_min" => 0,
            "amount" => $transaction->getMontant(),
            "provenance" => ""
        ];

        $custom = 'c=' . $_SESSION['id_client'] . '&ct=0&fo=0&cart=0&p=0&cu=' . urlencode('') . '&ip=::1';

        $charge = new StripeCharge();
        $charge->setClient($client);
        $charge->setToken('');
        $charge->setIdCart(0);
        $charge->setIdTransaction($transaction->getIdTrans());
        $charge->setIdCustomer('');
        $charge->setIdStripeCharge('');
        $charge->setIdAbonnement(0);
        $charge->setPaymentMethod('');
        $charge->setSecretKey($this->reglages->appGetParametreByName('stripe_secret_key')['value'] ?? '');
        $charge->setProduit($transaction->getDescriptor());
        $charge->setAmount($transaction->getMontant());
        $charge->setCurrency($transaction->getDevise());
        $charge->setNom($transaction->getNom());
        $charge->setPrenom($transaction->getPrenom());
        $charge->setEmail($transaction->getEmail());
        $charge->setCustom($custom);
        $charge->setCheckout(json_encode($checkout));
        $charge->setIp('::1');
        $charge->setResult('');
        $charge->setError('');
        $this->em->persist($charge);
        $this->em->flush();

        return $charge;
    }

    private function generateGocardlessCharge(Transaction $transaction, string $nbPayments = 'x'): GocardlesOrder
    {
        $client = $this->em->getRepository(Client::class)->find($_SESSION['id_client']);

        $checkout = [
            "gocardless_token" => $this->reglages->appGetParametreByName('gocardless_token')['value'] ?? '',
            "devise" => "EUR",
            "payment" => "subscription",
            "nb_payments" => $nbPayments,
            "subscription_time" => 1,
            "subscription_timetype" => "mois",
            "augmentation" => 0,
            "recurrences" => ["option" => false],
            "trial_period" => 0,
            "montant_min" => 0,
            "amount" => $transaction->getMontant(),
            "provenance" => ""
        ];

        $custom = 'c=' . $_SESSION['id_client'] . '&ct=0&fo=0&cart=0&p=0&cu=' . urlencode('') . '&ip=::1';

        $charge = new GocardlesOrder();
        $charge->setClient($client);
        $charge->setIdOrder($transaction->getIdTrans());
        $charge->setIdElement(0);
        $charge->setIdCart(0);
        $charge->setIdCustomer(0);
        $charge->setIdAbonnement(0);
        $charge->setType('');
        $charge->setProduit($transaction->getDescriptor());
        $charge->setAmount($transaction->getMontant());
        $charge->setCurrency($transaction->getDevise());
        $charge->setCheckout(json_encode($checkout));
        $charge->setNom($transaction->getNom());
        $charge->setPrenom($transaction->getPrenom());
        $charge->setEmail($transaction->getEmail());
        $charge->setIdRedirect('');
        $charge->setMandate('');
        $charge->setGcCustomer('');
        $charge->setGcPayment('');
        $charge->setSecretKey($this->reglages->appGetParametreByName('gocardless_token')['value'] ?? '');
        $charge->setStatus('');
        $charge->setCustom($custom);
        $charge->setIp('::1');
        $this->em->persist($charge);
        $this->em->flush();

        return $charge;
    }

    private function getAbonnementFactory(string $type): ?AbonnementFactoryInterface
    {
        switch ($type) {
            case IntegrationEnum::STRIPE:
                $factory = $this->abonnementStripeFactory;
                break;
            case IntegrationEnum::GOCARDLESS:
                $factory = $this->abonnementGocardlessFactory;
                break;
            case SubscriptionTypeEnum::LEARNYPAY:
                $factory = $this->abonnementLearnypayFactory;
                break;
            default:
                return null;
        }

        return $factory;
    }
}
