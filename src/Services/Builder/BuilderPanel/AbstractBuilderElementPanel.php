<?php

namespace Learnybox\Services\Builder\BuilderPanel;

use Learnybox\Enums\BuilderTypeEnum;
use Learnybox\Services\Builder\BuilderAnimations;
use Learnybox\Services\Builder\BuilderElements\Elements\PresettingType\PresettingType;
use Learnybox\Services\Builder\BuilderObjects\BuilderObjects;

/**
 * Class AbstractBuilderElementPanel
 * @package Learnybox\Services\Builder\BuilderPanel
 */
abstract class AbstractBuilderElementPanel extends AbstractBuilderPanel
{
    /**
     * @var array
     */
    protected $element;

    /**
     * @var int
     */
    protected $idElement;

    /**
     * @var PresettingType|null
     */
    protected $presettingType;

    /**
     * @return mixed
     */
    abstract protected function renderContentTab();

    /**
     * @param array $swatches
     * @return mixed
     */
    abstract protected function renderDesignTab(array $swatches);

    /**
     * @return mixed
     */
    protected function renderAdvancedTab()
    {
        extract($this->content);

        $html = $this->renderMarginsGroup();

        $deviceHtml = $this->builderPanelService->renderAlert('<p>' . __(
            'Vous verrez toujours les éléments cachés dans l\'éditeur, vous pouvez modifier cela avec %s dans le menu',
            '<i class="fa fa-eye"></i>'
        ) . '</p>', 'info');

        if ($this->type !== 'mails') {
            $hideDesktop = $this->element['hide_desktop'];
            $deviceHtml .= $this->builderPanelFormService->renderSwitch(
                'hide_desktop',
                ['on' => '1', 'off' => '0'],
                __('Cacher sur ordinateur'),
                $hideDesktop
            );
        }

        $hidePhone = '0';
        if ($this->isMobile && isset($this->element['hide'])) {
            $hidePhone = $this->element['hide'];
        } elseif (!$this->isMobile && isset($this->element['contenu_mobile']) && !empty($this->element['contenu_mobile'])) {
            $mobileContent = json_decode($this->element['contenu_mobile'], true);
            $hidePhone = isset($mobileContent['hide']) && $mobileContent['hide'] ? $mobileContent['hide'] : '0';
        }
        $deviceHtml .= $this->builderPanelFormService->renderSwitch('hide_phone', ['on' => '1', 'off' => '0'], __('Cacher sur mobile'), $hidePhone);

        $deviceGroup = $this->builderPanelService->renderGroup($deviceHtml);
        $html .= $this->builderPanelService->renderPanelGroup('builder-panel-group-device', __('Visibilité Appareil'), $deviceGroup);

        $idHtml = $this->builderPanelFormService->renderInput('identifiant', __('Id'), $this->element['identifiant']);
        $idGroup = $this->builderPanelService->renderGroup($idHtml);
        $html .= $this->builderPanelService->renderPanelGroup('builder-panel-group-id', __('Attributs'), $idGroup);

        return $html;
    }

    /**
     * @param string $type
     * @param int $idObjet
     * @param int $idElement
     * @param array $swatches
     * @param bool $isMobile
     * @return mixed
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function renderSettingsPanel(string $type, int $idObjet, int $idElement, array $swatches, bool $isMobile)
    {
        $element = $this->container->get('Learnybox\Services\Builder\BuilderElements\BuilderElements' . ucfirst($type))->getElementById($idElement);
        if (!$element) {
            return $this->builderPanelService->renderAlert(__('L\’élément demandé n\'a pas été trouvée.'));
        }

        $objet = $this->container->get('Learnybox\Services\Builder\Builder' . ucfirst($type))->getObjetById($idObjet);

        $this->type = $type;
        $this->idObjet = $idObjet;
        $this->objet = $objet;
        $this->element = $element;
        $this->idElement = $idElement;
        $this->isMobile = $isMobile;

        $this->setDesignData();
        $this->setElementData($isMobile, $swatches);
        $this->setPresettingType();

        $contentTab = $this->renderContentTab();

        $designTab = $this->renderDesignTab($swatches);
        $designTab .= $this->renderBackgroundGroup($swatches);
        $designTab .= $this->renderAnimationGroup();

        $tabs = [];
        if ($contentTab) {
            $tabs['element_content'] = [
                'title' => __('Contenu'),
                'content' => $contentTab,
            ];
        }

        $tabs['element_design'] = [
            'title' => __('Design'),
            'content' => $designTab,
        ];
        $tabs['element_advanced'] = [
            'title' => __('Avancé'),
            'content' => $this->renderAdvancedTab(),
            'class' => 'advanced-tab'
        ];

        $hiddenFields = [
            'id_objet' => $idObjet,
            'id_element' => $idElement
        ];

        $title = ucfirst($element['objet']);
        $object = $this->container->get(BuilderObjects::class)->getObject($element['objet']);
        if ($object) {
            $title = $object['titre_panel'] ?? $object['titre'];
        }

        return $this->builderPanelService->renderTabs(
            $tabs,
            __('Elément %s', $title),
            $hiddenFields,
            null,
            null,
            null,
            null,
            'action-element-update',
            'action-element-cancel',
            $idElement,
            null,
            [
                'name' => 'panel:edit-element-closed',
                'data' => ['id-objet' => $idObjet, 'id-element' => $idElement]
            ],
            'var(--builder-element-color)',
            'var(--builder-element-color-ultra-light)'
        );
    }

    /**
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    protected function setDesignData()
    {
        $objet = $this->container->get('Learnybox\Services\Builder\Builder' . ucfirst($this->type))->getObjetById($this->idObjet);

        if (!empty($objet['design'])) {
            $design = $objet['design'];
            if ($design) {
                $design = json_decode($design, true);
            }
            $this->design = $design;

            if (!empty($this->design['fonts'])) {
                foreach ($this->design['fonts'] as $font) {
                    $formattedFont = strtolower(str_replace(' ', '_', $font));
                    $this->fonts[] = ['label' => $font, 'value' => $formattedFont, 'family' => $font];
                }
            }

            if (!empty($this->design['font_sizes'])) {
                foreach ($this->design['font_sizes'] as $target => $fontSize) {
                    $this->fontsThemes[$target] += $fontSize;
                }
            }
        }
    }

    /**
     * @param bool $isMobile
     * @param array $swatches
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    protected function setContentData(array $swatches)
    {
        $element = $this->element;

        if (!empty($element['contenu'])) {
            $content = $element['contenu'];
            if ($content) {
                $content = json_decode($content, true);
            }

            $content = array_map(function ($value) use ($swatches) {
                foreach ($swatches as $index => $swatch) {
                    if ($value === sprintf('main_color%s', $index)) {
                        return $swatch;
                    }
                }
                return $value;
            }, $content);

            $this->content = $content;
        }
    }

    /**
     * @param bool $isMobile
     * @param array $swatches
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    protected function setElementData(bool $isMobile, array $swatches)
    {
        $element = $this->element;
        if ($isMobile) {
            $this->element = $this->container->get(\Builder_Tools::class)->getElementForMobile($element);
        }

        $this->setContentData($swatches);
    }


    protected function setPresettingType()
    {
        $elementPresettingTypeClass = sprintf('Learnybox\Services\Builder\BuilderElements\Elements\PresettingType\%sPresettingType', ucfirst(toCamelCase($this->element['objet'])));
        if (class_exists($elementPresettingTypeClass)) {
            $this->presettingType = $this->container->get($elementPresettingTypeClass);
        } else {
            $this->presettingType = $this->container->get(PresettingType::class);
        }
    }

    /**
     * @param array $swatches
     * @return mixed
     */
    protected function renderBackgroundGroup(array $swatches)
    {
        $backgroundGroup = $this->renderBackgroundColorSubgroup();
        $backgroundGroup .= $this->renderBackgroundBorderSubgroup('bg_border_style', 'bg_border_radius', 'bg_border', 'bg_border_color', 'bg_border_color_opacity');
        $backgroundGroup .= $this->renderBackgroundShadowSubgroup();

        $presettings = $this->presettingType->getBackgroundPresettings($swatches);
        $backgroundGroup .= $this->builderPanelService->renderPresettingSelector($presettings, 'background_presetting', __('Fond de l\'élément'));

        return $this->builderPanelService->renderPanelGroup(
            'builder-panel-group-element-background',
            __('Fond de l\'élément'),
            $backgroundGroup,
            true,
            null,
            [$this->presettingType->getActionConfiguration()]
        );
    }

    /**
     * @return mixed|string
     */
    protected function renderBackgroundColorSubgroup()
    {
        $bgcolor1 = $bg_color = '';
        $bgcolor2 = '';
        $element_bg_gradient_angle = 0;
        $bg_color_opacity = 1;
        $bg_color_opacity2 = 1;
        $bg_opacity = 1;
        $bg_opacity2 = 1;

        extract($this->content);

        // Used for retrocompatibility
        if (empty($bgcolor1) && !empty($bg_color)) {
            $bgcolor1 = $bg_color;
        }

        $bgType = !empty($bgcolor2) ? 'gradient' : (!empty($bgcolor1) ? 'color' : '');

        $backgroundColorSubgroup = $this->builderPanelFormService->renderSelect('bgtype', [['value' => '', 'label' => __('Aucun')], ['value' => 'color', 'label' => __('Couleur')], ['value' => 'gradient', 'label' => __('Dégradé')]], __('Type de fond'), $bgType);
        $backgroundColorSubgroup .= $this->builderPanelFormService->renderColor('bgcolor1', __('Couleur de fond'), $bgcolor1);
        $backgroundColorSubgroup .= $this->builderPanelFormService->renderColor('bgcolor2', null, $bgcolor2);
        $backgroundColorSubgroup .= $this->builderPanelFormService->renderSlider('element_bg_gradient_angle', __('Angle dégradé'), $element_bg_gradient_angle, '°', 0, 360);
        $backgroundColorSubgroup .= $this->builderPanelFormService->renderHidden('bg_color_opacity', $bg_color_opacity);
        $backgroundColorSubgroup .= $this->builderPanelFormService->renderHidden('bg_color_opacity2', $bg_color_opacity2);
        $backgroundColorSubgroup .= $this->builderPanelFormService->renderHidden('bg_opacity', $bg_opacity);
        $backgroundColorSubgroup .= $this->builderPanelFormService->renderHidden('bg_opacity2', $bg_opacity2);
        $backgroundColorSubgroup .= $this->builderPanelFormService->renderHidden('bg_color', ''); // fix to remove previous colors saved

        return $this->builderPanelService->renderPanelSubGroup('builder-panel-subgroup-element-background-color', __('Couleur fond'), $backgroundColorSubgroup);
    }

    /**
     * @return mixed
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    protected function renderAnimationGroup()
    {
        $animation_appear_effect = '';
        $animation_disappear_effect = '';
        $prevent_global_animation = false;

        extract($this->content);

        $animationAppearTab = $this->container->get(BuilderAnimations::class)->generateSelectElementAnimation('animation_appear_effect', 'animation', $this->element['animation']);

        $this->element['waypoint'] = $this->element['delay'] != 0 ? '0' : ($this->element['waypoint'] == 1 ? '1' : '');
        $animationAppearTab .= $this->builderPanelFormService->renderSelect('waypoint', [
            [
                'value' => '',
                'label' => __('Aucun')
            ],
            [
                'value' => '0',
                'label' => __('Délai secondes chargement page')
            ],
            [
                'value' => '1',
                'label' => __('Au défilement élément vue')
            ],
        ], __('Déclenchement apparition'), $this->element['waypoint'], 'animation-waypoint');

        $delayHtml = $this->builderPanelFormService->renderInputGroup('delay', __('secondes'), 'number', false, __('Délai d\'apparition'), null, $this->element['delay'], null, ['min' => 0]);
        $animationAppearTab .= '<div class="row"><div class="col-sm-12">' . $delayHtml . '</div></div>';
        $animationAppearTab .= $this->builderPanelFormService->renderSwitch(
            'save_appear',
            ['on' => '1', 'off' => '0'],
            __('Animer une seule fois'),
            $this->element['save_appear'],
            $this->type !== 'tunnels' && $this->type !== 'pages' ? 'hidden' : ''
        );
        $animationAppearTab .= $this->builderPanelFormService->renderSwitch('prevent_global_animation', ['on' => '1', 'off' => '0'], __('Exclure de l\'animation globale'), $prevent_global_animation ? 1 : 0);

        $animationDisappearTab = $this->container->get(BuilderAnimations::class)->generateSelectElementAnimation('animation_disappear_effect', 'animation_disappear', $this->element['animation_disappear'], '', true);

        $this->element['waypoint_disappear'] = $this->element['delay_disappear'] != 0 ? '0' : '';
        $animationDisappearTab .= $this->builderPanelFormService->renderSelect('waypoint_disappear', [
            [
                'value' => '',
                'label' => __('Aucun')
            ],
            [
                'value' => '0',
                'label' => __('Délai secondes chargement page')
            ]
        ], __('Déclenchement disparition'), $this->element['waypoint_disappear'], 'animation-waypoint');
        $animationDisappearTab .= $this->builderPanelFormService->renderInputGroup('delay_disappear', __('secondes'), 'number', false, __('Délai de disparition'), null, $this->element['delay_disappear']);

        $tabs = [
            'animation_appear' => [
                'title' => __('Apparition'),
                'content' => $this->builderPanelService->renderGroup($animationAppearTab),
            ],
            'animation_disappear' => [
                'title' => __('Disparition'),
                'content' => $this->builderPanelService->renderGroup($animationDisappearTab),
            ]
        ];

        $animationGroup = $this->builderPanelService->renderSubTabs($tabs);

        $animationPanel = $this->builderPanelService->renderPanelGroup('builder-panel-group-animation', __('Animation de l\'élément'), $animationGroup);
        if (BuilderTypeEnum::BUILDER_MAILS === $this->type) {
            $animationPanel = '<div style="display:none;">' . $animationPanel . '</div>';
        }

        return $animationPanel;
    }

    public function getIdDomaine(): int
    {
        return $this->objet && isset($this->objet['id_domaine']) && $this->objet['id_domaine'] ? $this->objet['id_domaine'] : 0;
    }

    /**
     * @param array $swatches
     * @param string|null $label
     * @return string
     */
    public function renderButtonDesign(array $swatches = [], string $label = null): string
    {
        $states = ['', 'Hover', 'Clicked'];

        foreach ($states as $state) {
            ${'bg_color1' . $state} = ${'bg_color2' . $state} = ${'font_text' . $state} = '';
            ${'bg_gradient_angle' . $state} = ${'decalage_icone' . $state} = '0';
            ${'size_text' . $state} = 14;
            ${'color_text' . $state} = ${'color_icone' . $state} = '';
            ${'size_icone' . $state} = ${'size_text' . $state};
            ${'contenu' . $state} = ${'border_color' . $state} = '';
            ${'font_styles' . $state} = [];
            ${'border' . $state} = ${'border_radius' . $state} = ${'button_box_shadow_x' . $state} = 0;
            ${'button_box_shadow_y' . $state} = ${'button_box_shadow_blur' . $state} = 0;
            ${'button_box_shadow_color' . $state} = '';
            ${'button_box_shadow_opacity' . $state} = 1;
            ${'btn_padding_top' . $state} = ${'btn_padding_bottom' . $state} = 0;
            ${'btn_padding_left' . $state} = ${'btn_padding_right' . $state} = 0;
        }

        extract($this->content);

        // Set default paddings if all are 0
        if ((!empty($size) && 'btn-large' == $size) || empty($size)) {
            foreach ($states as $state) {
                if (${'btn_padding_top' . $state} == 0 && ${'btn_padding_bottom' . $state} == 0 && ${'btn_padding_left' . $state} == 0 && ${'btn_padding_right' . $state} == 0) {
                    ${'btn_padding_top' . $state} = ${'btn_padding_bottom' . $state} = 10;
                    ${'btn_padding_left' . $state} = ${'btn_padding_right' . $state} = 16;
                }
            }
        } elseif (!empty($size) && 'btn-xlarge' == $size) {
            foreach ($states as $state) {
                if (${'btn_padding_top' . $state} == 0 && ${'btn_padding_bottom' . $state} == 0 && ${'btn_padding_left' . $state} == 0 && ${'btn_padding_right' . $state} == 0) {
                    ${'btn_padding_top' . $state} = ${'btn_padding_bottom' . $state} = 15;
                    ${'btn_padding_left' . $state} = ${'btn_padding_right' . $state} = 16;
                }
            }
        }

        $tabs = [];
        $states = ['', 'Hover', 'Clicked'];

        //the hover and clicked CSS does not work in mails
        if (BuilderTypeEnum::BUILDER_MAILS === $this->type) {
            $states = [''];
        }
        foreach ($states as $state) {
            $index = sprintf('design_style%s', '_' . $state);
            $title = __('Normal');
            if ($state === 'Hover') {
                $title = __('Survol');
            } elseif ($state === 'Clicked') {
                $title = __('Cliqué');
            }
            $values = [
                'bg_color1' => ${'bg_color1' . $state},
                'bg_color2' => ${'bg_color2' . $state},
                'bg_gradient_angle' => ${'bg_gradient_angle' . $state},
                'font_text' => ${'font_text' . $state},
                'size_text' => ${'size_text' . $state},
                'color_text' => ${'color_text' . $state},
                'color_icone' => ${'color_icone' . $state},
                'size_icone' => ${'size_icone' . $state},
                'decalage_icone' => ${'decalage_icone' . $state},
                'contenu' => ${'contenu' . $state},
                'contenutexte' => !empty($this->element['contenutexte']) ? $this->element['contenutexte'] : __('Cliquez ici pour valider'),
                'font_styles' => ${'font_styles' . $state},
                'border_color' => ${'border_color' . $state},
                'border' => ${'border' . $state},
                'border_radius' => ${'border_radius' . $state},
                'button_box_shadow_x' => ${'button_box_shadow_x' . $state},
                'button_box_shadow_y' => ${'button_box_shadow_y' . $state},
                'button_box_shadow_blur' => ${'button_box_shadow_blur' . $state},
                'button_box_shadow_color' => ${'button_box_shadow_color' . $state},
                'button_box_shadow_opacity' => ${'button_box_shadow_opacity' . $state},
                'btn_padding_top' => ${'btn_padding_top' . $state},
                'btn_padding_bottom' => ${'btn_padding_bottom' . $state},
                'btn_padding_left' => ${'btn_padding_left' . $state},
                'btn_padding_right' => ${'btn_padding_right' . $state},
            ];
            if (BuilderTypeEnum::BUILDER_MAILS === $this->type) {
                unset($values['color_icone']);
                unset($values['size_icone']);
                unset($values['decalage_icone']);
            }
            $tabs[$index] = [
                'title' => $title,
                'content' => $this->builderPanelService->renderButtonDesignTab(
                    $state,
                    $this->fonts,
                    $values,
                    false
                )
            ];
        }

        $designStyleGroup = $this->builderPanelService->renderSubTabs($tabs);

        if ($this->type !== BuilderTypeEnum::BUILDER_MAILS_FORMULAIRES) {
            $presettings = $this->presettingType->getButtonPresettings($swatches);
            $designStyleGroup .= $this->builderPanelService->renderPresettingSelector($presettings, 'button-presetting', __('Style du bouton'));

            $actions[] = $this->presettingType->getActionConfiguration();
        }

        if (BuilderTypeEnum::BUILDER_MAILS !== $this->type) {
            $actions[] = $this->presettingType->getCopyStateConfiguration();
        }

        return $this->builderPanelService->renderPanelGroup(
            'builder-panel-group-style',
            $label ?? __('Style du bouton'),
            $designStyleGroup,
            true,
            'design-button-builder-panel-group',
            $actions
        );
    }
}
