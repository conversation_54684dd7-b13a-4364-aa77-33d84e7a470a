<?php

namespace Learnybox\Services\Builder\BuilderElements\Elements\PresettingType;

class ProgressBarPresettingType extends PresettingType
{
    /**
     * @param array $swatches
     * @return array
     */
    public function getPresettings(array $swatches): array
    {
        $presettings = [];

        if ($swatches) {
            foreach ($swatches as $c) {
                $presettings[] = [
                    'html' => '
                        <div class="progressbar-style" style="background-color:' . $c . '; color:white; text-align:center; border-radius:4px; margin-bottom:10px;">
                            <a style="color:white;">' . __('100% complété') . '</a>
                        </div>
                    ',
                    'values' => [
                        'bar_bgcolor' => $c,
                        'bar_color' => $c,
                        'text_color' => 'white'
                    ]
                ];
            }
        }

        return $presettings;
    }
}
