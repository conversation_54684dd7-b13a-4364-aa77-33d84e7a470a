<?php

namespace Learnybox\Services\Builder\BuilderElements\Elements;

use Learnybox\Helpers\Assets;
use Learnybox\Services\Builder\BuilderPanel\AbstractBuilderElementPanel;

/**
 * Class Blogsearch
 * @package Learnybox\Services\Builder\BuilderElements\Elements
 */
class Blogsearch extends AbstractBuilderElementPanel
{
    protected function renderContentTab()
    {
        $icone = '';
        $color_icone = '';
        $position_icone = 'left';
        $contenuTexte = str_replace('<br />', "\n", $this->element['contenutexte'] ?? '');

        extract($this->content);

        $html = $this->builderPanelFormService->renderTextarea('contenu', $contenuTexte, __('Texte du bouton'));
        $html .= $this->builderPanelFormService->renderIconPicker('icone', $icone, $color_icone, __('Icône'));
        $html .= $this->builderPanelFormService->renderIconPositionSelect('position_icone', $position_icone);

        return $this->builderPanelService->renderGroup($html);
    }

    protected function renderDesignTab(array $swatches)
    {
        return $this->renderButtonDesign($swatches);
    }
}
