<?php

namespace Learnybox\Services\Builder\BuilderElements;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Formation\Page\FormationPageElement;
use Learnybox\Services\Builder\BuilderPanel\BuilderPanelFormService;
use Learnybox\Services\Builder\BuilderPanel\BuilderPanelService;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Services\Icons\FontawesomeIconsService;

/**
 * Class AbstractBuilderElements
 * @package Learnybox\Services\Builder\BuilderElements
 */
abstract class AbstractBuilderElements
{
    protected $database;
    public $table;
    public $table_backups;
    public $var_id_objet;
    protected $type;
    private $container;
    public $champ_comment;
    public $comment_approuve = 'auto';
    public $fonts = [];
    public $examples;

    public function __construct()
    {
        $this->database = \MySQL::getInstance();

        $this->container = ContainerBuilderService::getInstance();

        switch ($this->type) {
            case 'tunnels':
                $this->var_id_objet = 'id_page';
                $this->table = DB_PREFIX . 'tunnels_pages_elements';
                $this->table_backups = DB_PREFIX . 'tunnels_pages_elements_backups';
                break;

            case 'pages':
                $this->var_id_objet = 'id_page';
                $this->table = DB_PREFIX . 'pages_elements';
                $this->table_backups = DB_PREFIX . 'pages_elements_backups';
                break;

            case 'popups':
                $this->var_id_objet = 'id_popup';
                $this->table = DB_PREFIX . 'popups_elements';
                $this->table_backups = DB_PREFIX . 'popups_elements_backups';
                break;

            case 'formations':
                $this->var_id_objet = 'idpage';
                $this->table = 'ptf_pages_elements';
                $this->table_backups = 'ptf_pages_elements_backups';
                break;

            case 'mails':
                $this->var_id_objet = 'id_mail';
                $this->table = 'lbar_mails_elements';
                $this->table_backups = 'lbar_mails_elements_backups';
                break;

            case 'mailsFormulaires':
                $this->var_id_objet = 'id_formulaire';
                $this->table = 'lbar_formulaires_elements';
                $this->table_backups = 'lbar_formulaires_elements_backups';
                break;

            case 'articles':
                $this->var_id_objet = 'id_article';
                $this->table = DB_PREFIX . 'articles_elements';
                $this->table_backups = DB_PREFIX . 'articles_elements_backups';
                break;
        }

        if (!$this->table) {
            throw new \Exception(__('Error table'));
        }
        $this->champ_comment = [
            'prenom' => ['titre' => __('Prénom'), 'active' => 'non', 'obligatoire' => 'non'],
            'nom' => ['titre' => __('Nom'), 'active' => 'non', 'obligatoire' => 'non'],
            'telephone' => ['titre' => __('Téléphone'), 'active' => 'non', 'obligatoire' => 'non'],
            'adresse' => ['titre' => __('Votre adresse'), 'active' => 'non', 'obligatoire' => 'non'],
            'site' => ['titre' => __('Votre site'), 'active' => 'non', 'obligatoire' => 'non'],
        ];

        $this->fonts['default'] = ['name' => __('Par défaut'), 'family' => ''];
        $this->fonts['arial'] = ['name' => 'Arial', 'family' => 'arial,helvetica,sans-serif'];
        $this->fonts['book_antiqua'] = ['name' => 'Book Antiqua', 'family' => 'book antiqua'];
        $this->fonts['calibri'] = ['name' => 'Calibri', 'family' => 'calibri'];
        $this->fonts['candara'] = ['name' => 'Candara', 'family' => 'candara'];
        $this->fonts['cambria'] = ['name' => 'Cambria', 'family' => 'cambria'];
        $this->fonts['century'] = ['name' => 'Century', 'family' => 'century'];
        $this->fonts['century_gothic'] = ['name' => 'Century Gothic', 'family' => 'century gothic'];
        $this->fonts['comic_sans_ms'] = ['name' => 'Comic Sans MS', 'family' => 'comic sans ms,cursive'];
        $this->fonts['consolas'] = ['name' => 'Consolas', 'family' => 'consolas'];
        $this->fonts['constantia'] = ['name' => 'Constantia', 'family' => 'constantia'];
        $this->fonts['courier_new'] = ['name' => 'Courier New', 'family' => 'courier new,courier,monospace'];
        $this->fonts['franklin_gothic'] = ['name' => 'Franklin Gothic', 'family' => 'franklin gothic medium'];
        $this->fonts['garamond'] = ['name' => 'Garamond', 'family' => 'garamond'];
        $this->fonts['georgia'] = ['name' => 'Georgia', 'family' => 'georgia,serif'];
        $this->fonts['helvetica_neue'] = ['name' => 'Helvetica Neue', 'family' => 'helvetica neue'];
        $this->fonts['helvetica'] = ['name' => 'Helvetica', 'family' => 'helvetica'];
        $this->fonts['impact'] = ['name' => 'Impact', 'family' => 'impact'];
        $this->fonts['lucida'] = ['name' => 'Lucida', 'family' => 'lucida sans unicode,lucida grande,sans-serif'];
        $this->fonts['segoe'] = ['name' => 'Segoe Print', 'family' => 'segoe print'];
        $this->fonts['tahoma'] = ['name' => 'Tahoma', 'family' => 'tahoma,geneva,sans-serif'];
        $this->fonts['times_new_roman'] = ['name' => 'Times New Roman', 'family' => 'times new roman,times,serif'];
        $this->fonts['trebuchet'] = ['name' => 'Trebuchet MS', 'family' => 'trebuchet ms,helvetica,sans-serif'];
        $this->fonts['verdana'] = ['name' => 'Verdana', 'family' => 'verdana,geneva,sans-serif'];
    }

    /********************************************************/
    /************************ FONTS *************************/
    /********************************************************/

    public function getFonts()
    {
        return $this->fonts;
    }

    /********************************************************/
    /*********************** ELEMENTS ***********************/
    /********************************************************/

    /**
     * getAllElements
     * Retourne les éléments de l'objet.
     *
     * @param   $id_objet int
     *
     * @return array
     */
    public function getAllElements($id_objet)
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter($this->var_id_objet . "='$id_objet' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('position', 'ASC')
            ->getRows();

        return $result;
    }

    /**
     * getAllElementsWithoutParent
     * Retourne les éléments de l'objet.
     *
     * @param   $id_objet int
     *
     * @return array
     */
    public function getAllElementsWithoutParent($id_objet)
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter($this->var_id_objet . "='$id_objet' AND parent='0' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('position', 'ASC')
            ->addSort('ID', 'ASC')
            ->getRows();

        return $result;
    }

    /**
     * getElementsByLine
     * Retourne les éléments d'une section.
     *
     * @param   $id_objet int
     *
     * @return array
     */
    public function getElementsByLine($id_objet, $id_line)
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter($this->var_id_objet . "='$id_objet' AND id_line='$id_line' AND parent='0' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('position', 'ASC')
            ->getRows();

        return $result;
    }

    /**
     * getElementsByCol
     * Retourne les éléments d'une colonne.
     *
     * @param   $id_objet  int
     * @param   $id_line   int
     * @param   $id_col    int
     * @param   $id_client int (default: null)
     *
     * @return array
     */
    public function getElementsByCol($id_objet, $id_line, $id_col, $id_client = null)
    {
        if (null === $id_client) {
            $id_client = $_SESSION['id_client'];
        }
        $result = $this->database
            ->search($this->table)
            ->addFilter($this->var_id_objet . "='$id_objet' AND id_line='$id_line' AND id_col='$id_col' AND parent='0' AND id_client='$id_client'")
            ->addSort('position', 'ASC')
            ->getRows();

        return $result;
    }

    /**
     * getElementsByBox
     * Retourne les éléments d'une boîte.
     *
     * @param  $id_objet int
     *
     * @return array
     */
    public function getElementsByBox($id_objet, $id_box)
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter($this->var_id_objet . "='$id_objet' AND parent='$id_box' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('position', 'ASC')
            ->getRows();

        return $result;
    }

    /**
     * getElementById
     * Retourne les éléments de l'objet.
     *
     * @param  $id_element int
     *
     * @return array
     */
    public function getElementById($id_element)
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter("ID='$id_element' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getRow();

        return $result;
    }

    /**
     * getElementsByObjet
     * Retourne les éléments de l'objet.
     *
     * @param  $id_objet int
     *
     * @return array
     */
    public function getElementsByObjet($id_objet, $objet, $id_client = null)
    {
        if (null === $id_client) {
            $id_client = $_SESSION['id_client'];
        }
        $result = $this->database
            ->search($this->table)
            ->addFilter($this->var_id_objet . "='$id_objet' AND objet='$objet' AND id_client='$id_client'")
            ->getRows();

        return $result;
    }

    /**
     * getElementsByObjetAndType
     * Retourne les éléments de l'objet.
     *
     * @param  $id_objet int
     *
     * @return array
     */
    public function getElementsByObjetAndType($id_objet, $objet, $type)
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter($this->var_id_objet . "='$id_objet' AND objet='$objet' AND type='$type' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getRows();

        return $result;
    }

    /**
     * getLastObjetElement function.
     *
     * @param int $id_objet
     *
     * @return array
     */
    public function getLastObjetElement($id_objet)
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter($this->var_id_objet . "='$id_objet' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('position', 'DESC')
            ->getRow();

        return $result;
    }

    /**
     * getLastElementByBox function.
     *
     * @param int $id_objet
     *
     * @return array
     */
    public function getLastElementByBox($id_objet, $id_parent)
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter($this->var_id_objet . "='$id_objet' AND parent='$id_parent' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('position', 'DESC')
            ->getRow();

        return $result;
    }

    /**
     * getElementByName function.
     *
     * @param string $name
     */
    public function getElementByName($name)
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter("name='$name' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getRow();

        return $result;
    }

    /**
     * getElementsWithIdentifiants
     * Retourne les éléments de l'objet avec un identifiant.
     *
     * @param   $id_objet int
     *
     * @return array
     */
    public function getElementsWithIdentifiants($id_objet)
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter($this->var_id_objet . "='$id_objet' AND identifiant != '' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getRows();

        return $result;
    }

    /********************************************************/
    /************************* ADMIN ************************/
    /********************************************************/

    public function insert_element($id_objet, $type, $args = [], $id_example = 0, $afterElement = 0, $id_line = 0, $id_col = 0, $line_position = 0, $id_parent = 0, $position = 0, $new_col = '', $new_zone = false)
    {
        $return_line = false;

        $array_insert = [
            'id_client' => $_SESSION['id_client'],
            $this->var_id_objet => $id_objet,
            'objet' => $type,
            'span' => 'span6',
            'parent' => $id_parent,
            'datecreation' => date('Y-m-d H:i:s'),
            'datemodification' => date('Y-m-d H:i:s'),
        ];

        if ('sommaire' === $type) {
            $sommaire = $this->container->get(EntityManager::class)->getRepository(FormationPageElement::class)->count(['formationPage' => $id_objet, 'objet' => 'sommaire']);
            if ($sommaire) {
                return ['valid' => false, 'message' => __('Un élément sommaire est déjà présent dans la page, il n\'est pas possible d\'en ajouter')];
            }
        }

        //idline
        if (!$id_line) {
            $line = eden()->{'Learnybox\Services\Builder\BuilderLines\BuilderLines' . ucfirst($this->type)}()->getLastLine($id_objet);
            if (!$line) {
                //create first line
                $return_line = true;
                $line = eden()->{'Learnybox\Services\Builder\BuilderLines\BuilderLines' . ucfirst($this->type)}()->insert_line(['id_objet' => $id_objet, 'dont_create_cols' => true]);
                if (!$line) {
                    return ['valid' => false, 'message' => $line['message']];
                }
            }
            $id_line = $line['id_line'];
        }
        $array_insert['id_line'] = $id_line;

        //idline
        if (!$id_col) {
            if ($new_col) {
                //create a new column
                $array_insert_col = [
                    'id_objet' => $id_objet,
                    'id_line' => $id_line,
                    'nb_cols' => '100',
                    'new_col' => true,
                    'line_position' => $line_position,
                ];

                if ($new_col === 'left') {
                    $array_insert_col['new_col'] = 'left';
                }

                $col = eden()->{'Learnybox\Services\Builder\BuilderCols\BuilderCols' . ucfirst($this->type)}()->insert_cols($array_insert_col);
                if (!$col['valid']) {
                    return ['valid' => false, 'message' => $col['message']];
                }

                $id_col = $col['id_col'];
            } elseif ($new_zone) {
                //create new section
                $array_insert_col = [
                    'id_objet' => $id_objet,
                    'id_line' => $id_line,
                    'nb_cols' => '100',
                    'new_zone' => true,
                ];

                if (!$line_position) {
                    $array_insert_col['new_zone'] = 'top';
                }

                $col = eden()->{'Learnybox\Services\Builder\BuilderCols\BuilderCols' . ucfirst($this->type)}()->insert_cols($array_insert_col);
                if (!$col['valid']) {
                    return ['valid' => false, 'message' => $col['message']];
                }

                $id_col = $col['id_col'];
            } else {
                $col = eden()->{'Learnybox\Services\Builder\BuilderCols\BuilderCols' . ucfirst($this->type)}()->getFirstCol($id_objet, $id_line);
                if (!$col) {
                    //create first column
                    $array_insert_col = [
                        'id_objet' => $id_objet,
                        'id_line' => $id_line,
                        'nb_cols' => '100',
                    ];
                    $col = eden()->{'Learnybox\Services\Builder\BuilderCols\BuilderCols' . ucfirst($this->type)}()->insert_cols($array_insert_col);
                    if (!$col['valid']) {
                        return ['valid' => false, 'message' => $col['message']];
                    }
                }
                $id_col = $col['id_col'];
            }
        }
        $array_insert['id_col'] = $id_col;

        //calcul de la position
        if (!$position and $afterElement) {
            $element = $this->getElementById($afterElement);
            if ($element) {
                $position = $element['position'];
                ++$position;

                //mise à jour de tous les éléments suivants
                $elements = $this->getElementsByLine($id_objet, $id_line);
                if ($elements) {
                    foreach ($elements as $_element) {
                        if ($_element['position'] < $position) {
                            continue;
                        }

                        $temp_position = $_element['position'];
                        ++$temp_position;

                        try {
                            $this->database->updateRows($this->table, ['position' => $temp_position], "ID='" . $_element['ID'] . "' AND " . $this->var_id_objet . "='$id_objet' AND id_client='" . $_SESSION['id_client'] . "'");
                        } catch (\Eden_Error $e) {
                            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de la position d'un élément.")];
                        }
                    }
                }
            }
        } elseif (!$position) {
            //mise à jour de tous les éléments suivants
            $elements = $this->getElementsByLine($id_objet, $id_line);
            if ($elements) {
                foreach ($elements as $_element) {
                    $temp_position = $_element['position'];
                    ++$temp_position;

                    try {
                        $this->database->updateRows($this->table, ['position' => $temp_position], "ID='" . $_element['ID'] . "' AND " . $this->var_id_objet . "='$id_objet' AND id_client='" . $_SESSION['id_client'] . "'");
                    } catch (\Eden_Error $e) {
                        return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de la position d'un élément.")];
                    }
                }
            }
        }

        if (!$position and $id_parent) {
            $lastElement = $this->getLastElementByBox($id_objet, $id_parent);
            if ($lastElement) {
                $position = $lastElement['position'];
                ++$position;
            }
        }

        $array_insert['position'] = $position;

        //args
        if ($args) {
            foreach ($args as $type_args => $content) {
                $array_insert[$type_args] = $content;
            }
        }

        //span
        $array_insert['span'] = 'span12';

        //comments
        if ('comments' == $type) {
            $array_insert['type'] = 'commentaires';
        }

        //button_lightbox
        if ('button_lightbox' == $type) {
            $array_insert['objet'] = 'box';
            $array_insert['eval'] = 1;
        }

        //examples
        if ($id_example) {
            $objet = eden()->{'Learnybox\Services\Builder\Builder' . ucfirst($this->type)}()->getObjetById($id_objet);
            if (!$objet) {
                return ['valid' => false, 'message' => __('Erreur : cet objet n\'existe pas')];
            }

            $replaceExample = eden()->Builder_Examples()->replaceExample($objet, $type, $id_example);
            if ($replaceExample) {
                foreach ($replaceExample as $var_name => $var_value) {
                    $array_insert[$var_name] = $var_value;
                }
            }
        }

        //contenu
        if (!isset($array_insert['contenu'])) {
            $array_insert['contenu'] = [];
        } elseif (!is_array($array_insert['contenu']) && str_starts_with($array_insert['contenu'], '{')) {
            $array_insert['contenu'] = json_decode($array_insert['contenu'], true);
        }

        if (!isset($array_insert['contenu_mobile'])) {
            $array_insert['contenu_mobile']['contenu'] = [];
        } else {
            if (!is_array($array_insert['contenu_mobile']) && str_starts_with($array_insert['contenu_mobile'], '{')) {
                $array_insert['contenu_mobile'] = json_decode($array_insert['contenu_mobile'], true);
            }

            if (is_array($array_insert['contenu_mobile'])) {
                if (!isset($array_insert['contenu_mobile']['contenu'])) {
                    $array_insert['contenu_mobile']['contenu'] = [];
                } elseif (!is_array($array_insert['contenu_mobile']['contenu']) && str_starts_with($array_insert['contenu_mobile']['contenu'], '{')) {
                    $array_insert['contenu_mobile']['contenu'] = json_decode($array_insert['contenu_mobile']['contenu'], true);
                }
            }
        }

        if (empty($array_insert['contenu_mobile'])) {
            $array_insert['contenu_mobile'] = [];
            $array_insert['contenu_mobile']['contenu'] = [];
        }

        //design par défaut
        if (!isset($array_insert['contenu']['padding_top'])) {
            $array_insert['contenu']['padding_top'] = 0;
        }
        if (!isset($array_insert['contenu']['padding_bottom'])) {
            $array_insert['contenu']['padding_bottom'] = 0;
        }
        if (!isset($array_insert['contenu']['padding_left'])) {
            $array_insert['contenu']['padding_left'] = 0;
        }
        if (!isset($array_insert['contenu']['padding_right'])) {
            $array_insert['contenu']['padding_right'] = 0;
        }
        if (!isset($array_insert['contenu']['margin_bottom'])) {
            $array_insert['contenu']['margin_bottom'] = 10;
        }
        if (!isset($array_insert['contenu']['margin_top'])) {
            $array_insert['contenu']['margin_top'] = 10;
        }

        if ('txt' == $type) {
            if (!isset($array_insert['contenu']['padding_top'])) {
                $array_insert['contenu']['padding_top'] = 10;
            }
            if (!isset($array_insert['contenu']['padding_bottom'])) {
                $array_insert['contenu']['padding_bottom'] = 10;
            }
            if (!isset($array_insert['contenu']['padding_left'])) {
                $array_insert['contenu']['padding_left'] = 10;
            }
            if (!isset($array_insert['contenu']['padding_right'])) {
                $array_insert['contenu']['padding_right'] = 10;
            }
        } elseif ('image' == $type) {
            if (!isset($array_insert['contenu']['img_border_radius_top_left'])) {
                $array_insert['contenu']['img_border_radius_top_left'] = 12;
            }
            if (!isset($array_insert['contenu']['img_border_radius_top_right'])) {
                $array_insert['contenu']['img_border_radius_top_right'] = 12;
            }
            if (!isset($array_insert['contenu']['img_border_radius_bottom_left'])) {
                $array_insert['contenu']['img_border_radius_bottom_left'] = 12;
            }
            if (!isset($array_insert['contenu']['img_border_radius_bottom_right'])) {
                $array_insert['contenu']['img_border_radius_bottom_right'] = 12;
            }
        } elseif ('videoplayer' == $type || 'image_play' === $type) {
            if (!isset($array_insert['contenu']['bg_border_radius_top_left'])) {
                $array_insert['contenu']['bg_border_radius_top_left'] = 12;
            }
            if (!isset($array_insert['contenu']['bg_border_radius_top_right'])) {
                $array_insert['contenu']['bg_border_radius_top_right'] = 12;
            }
            if (!isset($array_insert['contenu']['bg_border_radius_bottom_left'])) {
                $array_insert['contenu']['bg_border_radius_bottom_left'] = 12;
            }
            if (!isset($array_insert['contenu']['bg_border_radius_bottom_right'])) {
                $array_insert['contenu']['bg_border_radius_bottom_right'] = 12;
            }
        }

        $array_insert['contenu'] = json_encode($array_insert['contenu']);

        //mobile
        if (is_array($array_insert['contenu_mobile'])) {
            if (!isset($array_insert['contenu_mobile']['contenu']['padding_top'])) {
                $array_insert['contenu_mobile']['contenu']['padding_top'] = 0;
            }
            if (!isset($array_insert['contenu_mobile']['contenu']['padding_bottom'])) {
                $array_insert['contenu_mobile']['contenu']['padding_bottom'] = 0;
            }
            if (!isset($array_insert['contenu_mobile']['contenu']['padding_left'])) {
                $array_insert['contenu_mobile']['contenu']['padding_left'] = 0;
            }
            if (!isset($array_insert['contenu_mobile']['contenu']['padding_right'])) {
                $array_insert['contenu_mobile']['contenu']['padding_right'] = 0;
            }
            if (!isset($array_insert['contenu_mobile']['contenu']['margin_bottom'])) {
                $array_insert['contenu_mobile']['contenu']['margin_bottom'] = 10;
            }
            if (!isset($array_insert['contenu_mobile']['contenu']['margin_top'])) {
                $array_insert['contenu_mobile']['contenu']['margin_top'] = 10;
            }

            $array_insert['contenu_mobile']['contenu'] = json_encode($array_insert['contenu_mobile']['contenu'] ?? []);
            $array_insert['contenu_mobile'] = json_encode($array_insert['contenu_mobile']);
        }

        //insertion
        try {
            $this->database->insertRow($this->table, $array_insert);
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de l'insertion de l'élément.")];
        }

        $id_element = $this->database->getLastInsertedId();

        if ($return_line) {
            return ['valid' => true, 'id_line' => $id_line, 'id_col' => $id_col, 'id_element' => $id_element, 'return_line' => true];
        }
        if ($new_col or $new_zone) {
            return ['valid' => true, 'id_line' => $id_line, 'id_col' => $id_col, 'id_element' => $id_element, 'return_col' => true];
        }

        return ['valid' => true, 'id_line' => $id_line, 'id_col' => $id_col, 'id_element' => $id_element];
    }

    public function validatepostElement($cleanedPost)
    {
        $idElement = filter_var($cleanedPost['id_element'], FILTER_VALIDATE_INT);
        $idObjet = filter_var($cleanedPost['id_objet'], FILTER_VALIDATE_INT);

        if (!$idObjet) {
            return ['valid' => false, 'message' => __('Erreur : le numéro de l\'objet n\'a pas été reçu')];
        }
        $objet = eden()->{'Learnybox\Services\Builder\Builder' . ucfirst($this->type)}()->getObjetById($idObjet);
        if (!$objet) {
            return ['valid' => false, 'message' => __('Erreur : cet objet n\'existe pas')];
        }

        //element
        if (!$idElement) {
            return ['valid' => false, 'message' => __('Erreur : le numéro de l\'élément n\'a pas été reçu')];
        }
        $element = $this->getElementById($idElement);
        if (!$element or $element[$this->var_id_objet] != $idObjet) {
            return ['valid' => false, 'message' => __('Erreur : cet élément n\'existe pas')];
        }

        $class = ucfirst(str_replace(['-', '_'], '', $element['objet']));
        if (!class_exists('Builder_Elements_' . $class)) {
            return ['valid' => false, 'message' => __('Erreur : type d\'élément inconnu')];
        }
        $validatepost = eden()->{'Builder_Elements_' . $class}()->validatepost_element($cleanedPost);
        if (!$validatepost['validPost']) {
            return ['valid' => false, 'message' => $validatepost['error']];
        }

        return ['valid' => true, 'message' => ''];
    }

    public function updateElement($cleaned_post, $swatches = [])
    {
        $id_element = filter_var($cleaned_post['id_element'], FILTER_VALIDATE_INT);
        $id_objet = filter_var($cleaned_post['id_objet'], FILTER_VALIDATE_INT);

        if (!$id_objet) {
            return ['valid' => false, 'message' => __('Erreur : le numéro de l\'objet n\'a pas été reçu')];
        }
        if (!$id_element) {
            return ['valid' => false, 'message' => __('Erreur : le numéro de l\'élément n\'a pas été reçu')];
        }

        $objet = eden()->{'Learnybox\Services\Builder\Builder' . ucfirst($this->type)}()->getObjetById($id_objet);
        if (!$objet) {
            return ['valid' => false, 'message' => __('Erreur : cet objet n\'existe pas')];
        }

        //element
        $element = $this->getElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas.")];
        }

        if ($element[$this->var_id_objet] != $id_objet) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas.")];
        }

        $class = ucfirst(str_replace(['-', '_'], '', $element['objet']));
        if (!class_exists('Builder_Elements_' . $class)) {
            return ['valid' => false, 'message' => __("Erreur : type d\'élément inconnu.")];
        }

        //sauvegarde de l'élément
        $saveElementBackup = $this->saveElementBackup($element);
        if (!$saveElementBackup['valid']) {
            return ['valid' => false, 'message' => $saveElementBackup['message']];
        }

        $array_update = [
            'contenutexte' => '',
            'datemodification' => date('Y-m-d H:i:s'),
        ];

        if ($swatches) {
            $swatches = array_map('strtolower', $swatches);
        }

        if (isset($cleaned_post['bgcolor1']) && $cleaned_post['bgcolor1'] && empty(isset($cleaned_post['bgcolor2']))) {
            $cleaned_post['bgcolor1'] = filter_var($cleaned_post['bgcolor1'], FILTER_SANITIZE_SPECIAL_CHARS);
            $cleaned_post['bgcolor1'] = strtolower($cleaned_post['bgcolor1']);
            if ($swatches) {
                $i = array_search($cleaned_post['bgcolor1'], $swatches);
                if (false !== $i) {
                    $cleaned_post['bgcolor1'] = 'main_color' . $i;
                }
            }
        }

        //????
        $cleaned_post['tunnel'] = true;

        //type (tunnels, formations, etc.)
        $cleaned_post['type_objet'] = $this->type;
        $cleaned_post['type'] = $this->type;

        //update element
        $update = eden()->{'Builder_Elements_' . $class}()->update_element($cleaned_post, $element, $swatches);
        if (isset($update['valid']) && !$update['valid']) {
            return [
                'valid' => false,
                'message' => $update['message']
            ];
        }

        //update design
        if (method_exists('Builder_Elements_' . $class, 'update_element_design')) {
            $update = array_replace_recursive($update, eden()->{'Builder_Elements_' . $class}()->update_element_design($cleaned_post, $element, $swatches));
        }

        //update parametres
        if (method_exists('Builder_Elements_' . $class, 'update_element_parametres')) {
            $update = array_replace_recursive($update, eden()->{'Builder_Elements_' . $class}()->update_element_parametres($cleaned_post, $element, $swatches));
        }


        if ($update) {
            foreach ($update as $key => $val) {
                if ('contenu' == $key) {
                    $contenu = json_decode($element['contenu'], true);
                    if (null === $contenu) {
                        $contenu = [];
                    }

                    $array_update['contenu'] = array_merge($contenu, $val);
                } else {
                    $array_update[$key] = $val;
                }
            }

            if (isset($update['objet'])) {
                $element['objet'] = $update['objet'];
            }
        }

        //cas spécifiques
        if ('optin' == $element['objet']) {
            unset($array_update['contenutexte']);
        }

        //strip PHP tags
        if (isset($array_update['contenutexte']) and false !== strpos($array_update['contenutexte'], '<?')) {
            $array_update['contenutexte'] = preg_replace('/\<(\?|\%)(.*)(\?|\%)\>/s', '', $array_update['contenutexte']);
        }

        //update apparence
        $contenu = $this->updateElementApparence($cleaned_post, $element);
        if (!isset($array_update['contenu']) or !$array_update['contenu']) {
            $array_update['contenu'] = $contenu;
        } else {
            $array_update['contenu'] = array_merge($array_update['contenu'], $contenu);
        }

        //update animations
        $array_update = array_merge($array_update, $this->updateElementAnimations($cleaned_post));

        //prevent global animations
        $prevent_global_animation = filter_var($cleaned_post['prevent_global_animation'], FILTER_VALIDATE_INT);
        $array_update['contenu']['prevent_global_animation'] = $prevent_global_animation == 1 ? true : false;

        //id
        $array_update['identifiant'] = filter_var($cleaned_post['identifiant'], FILTER_SANITIZE_SPECIAL_CHARS);
        $array_update['identifiant'] = str_replace('#', '', $array_update['identifiant']);

        //apparence
        if (isset($array_update['contenu'])) {
            $icon = $array_update['contenu']['icone'] ?? null;
            if ($icon) {
                $array_update['contenu']['icone'] = FontawesomeIconsService::handlePrefixCompatibility($icon);
            }

            $array_update['contenu'] = json_encode($array_update['contenu']);
        }

        $hidePhone = filter_var($cleaned_post['hide_phone'], FILTER_VALIDATE_INT);

        if (isset($cleaned_post['is_mobile']) and $cleaned_post['is_mobile']) {
            //hide phone
            if ($hidePhone) {
                $array_update['hide'] = $hidePhone;
            }

            $array_update = eden()->Builder_Tools()->getUpdateForMobile($element, $array_update);
            if (!$array_update) {
                return ['valid' => true];
            }
        } else {
            //hide phone
            $mobileContent = isset($element['contenu_mobile']) ? json_decode($element['contenu_mobile'], true) : [];
            if ($hidePhone) {
                $mobileContent['hide'] = $hidePhone;
            } elseif (isset($mobileContent['hide'])) {
                unset($mobileContent['hide']);
            }
            $array_update['contenu_mobile'] = !empty($mobileContent) ? json_encode($mobileContent) : '';
        }

        $array_update['hide_desktop'] = filter_var($cleaned_post['hide_desktop'] ?? 0, FILTER_VALIDATE_INT);

        //mise à jour
        try {
            $this->database->updateRows($this->table, $array_update, $this->var_id_objet . "=$id_objet AND ID=$id_element AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
        }

        if ($this->type == 'tunnels' and $objet['date_modification'] == '0000-00-00 00:00:00') {
            eden()->Builder_TunnelsPages()->update_page_date_modification($id_objet);
        }

        //pièce jointe
        if ('attachment' == $element['objet']) {
            $attachment = eden()->Lbar_Mails_Attachments()->getAttachmentByIdElement($id_element);
            if ($attachment) {
                $update = eden()->Lbar_Mails_Attachments()->update_attachment($id_objet, $id_element, $array_update);
            } else {
                $update = eden()->Lbar_Mails_Attachments()->insert_attachment($id_objet, $id_element, $array_update);
            }

            if (!$update['valid']) {
                return ['valid' => false, 'message' => $update['message']];
            }
        }

        return ['valid' => true];
    }

    public function updateElementApparence($cleaned_post, $element)
    {
        if (isset($cleaned_post['is_mobile']) and $cleaned_post['is_mobile'] and $element['contenu_mobile']) {
            $contenu_mobile = json_decode($element['contenu_mobile'], true);
            foreach ($contenu_mobile as $var_mobile => $content_mobile) {
                $element[$var_mobile] = $content_mobile;
            }
        }

        //apparence
        $contenu = [];
        $contenu['bgcolor1'] = filter_var($cleaned_post['bgcolor1'], FILTER_SANITIZE_SPECIAL_CHARS);
        $contenu['bgcolor2'] = filter_var($cleaned_post['bgcolor2'], FILTER_SANITIZE_SPECIAL_CHARS);
        $contenu['element_bg_gradient_angle'] = filter_var($cleaned_post['element_bg_gradient_angle'], FILTER_VALIDATE_INT);

        // fix to override old index value
        if (isset($cleaned_post['bg_color'])) {
            $contenu['bg_color'] = filter_var($cleaned_post['bg_color'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        $bg_opacity = filter_var($cleaned_post['bg_opacity'], FILTER_SANITIZE_SPECIAL_CHARS);
        if (!$bg_opacity) {
            $bg_opacity = 1;
        }
        $contenu['bg_opacity'] = $bg_opacity;

        $bg_opacity2 = filter_var($cleaned_post['bg_opacity2'], FILTER_SANITIZE_SPECIAL_CHARS);
        if (!$bg_opacity2) {
            $bg_opacity2 = 1;
        }
        $contenu['bg_opacity2'] = $bg_opacity2;

        $contenu['bg_border'] = filter_var($cleaned_post['bg_border'], FILTER_VALIDATE_INT);
        $contenu['bg_border_color'] = filter_var($cleaned_post['bg_border_color'], FILTER_SANITIZE_SPECIAL_CHARS);
        $contenu['bg_border_color_opacity'] = filter_var($cleaned_post['bg_border_color_opacity'], FILTER_SANITIZE_SPECIAL_CHARS);
        $contenu['bg_border_style'] = filter_var($cleaned_post['bg_border_style'], FILTER_SANITIZE_SPECIAL_CHARS);
        $contenu['bg_border_radius_top_left'] = filter_var($cleaned_post['bg_border_radius_top_left'], FILTER_VALIDATE_INT);
        $contenu['bg_border_radius_top_right'] = filter_var($cleaned_post['bg_border_radius_top_right'], FILTER_VALIDATE_INT);
        $contenu['bg_border_radius_bottom_left'] = filter_var($cleaned_post['bg_border_radius_bottom_left'], FILTER_VALIDATE_INT);
        $contenu['bg_border_radius_bottom_right'] = filter_var($cleaned_post['bg_border_radius_bottom_right'], FILTER_VALIDATE_INT);

        $contenu['margin_top'] = filter_var($cleaned_post['margin_top'], FILTER_VALIDATE_INT);
        $contenu['margin_bottom'] = filter_var($cleaned_post['margin_bottom'], FILTER_VALIDATE_INT);
        $contenu['margin_left'] = filter_var($cleaned_post['margin_left'], FILTER_VALIDATE_INT);
        $contenu['margin_right'] = filter_var($cleaned_post['margin_right'], FILTER_VALIDATE_INT);

        $contenu['padding_top'] = filter_var($cleaned_post['padding_top'], FILTER_VALIDATE_INT);
        $contenu['padding_bottom'] = filter_var($cleaned_post['padding_bottom'], FILTER_VALIDATE_INT);
        $contenu['padding_left'] = filter_var($cleaned_post['padding_left'], FILTER_VALIDATE_INT);
        $contenu['padding_right'] = filter_var($cleaned_post['padding_right'], FILTER_VALIDATE_INT);

        $contenu['box_shadow'] = 0;
        if (isset($cleaned_post['box_shadow']) && $cleaned_post['box_shadow'] == 1) {
            $contenu['box_shadow'] = 1;
        }

        $contenu['box_shadow_x'] = filter_var($cleaned_post['box_shadow_x'], FILTER_VALIDATE_INT);
        $contenu['box_shadow_y'] = filter_var($cleaned_post['box_shadow_y'], FILTER_VALIDATE_INT);
        $contenu['box_shadow_blur'] = filter_var($cleaned_post['box_shadow_blur'], FILTER_VALIDATE_INT);
        $contenu['box_shadow_color'] = filter_var($cleaned_post['box_shadow_color'], FILTER_SANITIZE_SPECIAL_CHARS);
        $contenu['box_shadow_opacity'] = filter_var($cleaned_post['box_shadow_opacity'], FILTER_VALIDATE_INT) / 100;

        $contenu['absolute_position'] = filter_var($cleaned_post['absolute_position'], FILTER_VALIDATE_INT);

        $contenu['lazy_loading'] = 0;
        if (isset($cleaned_post['lazy_loading']) && $cleaned_post['lazy_loading'] == 1) {
            $contenu['lazy_loading'] = 1;
        }

        return $contenu;
    }

    public function updateElementAnimations($cleanedPost)
    {
        $delay = filter_var($cleanedPost['delay'], FILTER_VALIDATE_INT);
        $animation = filter_var($cleanedPost['animation'], FILTER_SANITIZE_SPECIAL_CHARS);
        $delay_disappear = filter_var($cleanedPost['delay_disappear'], FILTER_VALIDATE_INT);
        $save_appear = filter_var($cleanedPost['save_appear'], FILTER_VALIDATE_INT);
        $waypoint = filter_var($cleanedPost['waypoint'], FILTER_VALIDATE_INT);
        $animation_disappear = filter_var($cleanedPost['animation_disappear'], FILTER_SANITIZE_SPECIAL_CHARS);

        if (0 !== $waypoint && 0 !== $delay) {
            $delay = 0;
        }

        $arrayUpdate = [
            'delay' => $delay,
            'animation' => $animation,
            'save_appear' => $save_appear,
            'waypoint' => !$animation ? 0 : $waypoint,
            'delay_disappear' => $delay_disappear,
            'animation_disappear' => $animation_disappear,
        ];

        return $arrayUpdate;
    }

    public function update_element_anchor($cleaned_post)
    {
        $id_element = filter_var($cleaned_post['id_element'], FILTER_VALIDATE_INT);
        $id_objet = filter_var($cleaned_post['id_objet'], FILTER_VALIDATE_INT);
        $anchor = filter_var($cleaned_post['anchor'], FILTER_SANITIZE_SPECIAL_CHARS);

        //mise à jour
        try {
            $this->database->updateRows($this->table, ['identifiant' => $anchor], $this->var_id_objet . "=$id_objet AND ID=$id_element AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
        }

        return ['valid' => true];
    }

    public function update_element_action($cleaned_post)
    {
        $id_element = filter_var($cleaned_post['id_element'], FILTER_VALIDATE_INT);
        $id_objet = filter_var($cleaned_post['id_objet'], FILTER_VALIDATE_INT);
        $action = filter_var($cleaned_post['action'], FILTER_SANITIZE_SPECIAL_CHARS);
        $param = filter_var($cleaned_post['param'], FILTER_SANITIZE_SPECIAL_CHARS);

        $element = $this->getElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __('Une erreur est survenue')];
        }

        $contenu = [];
        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);
        }

        $contenu['action'] = 'popup';
        $contenu['param'] = $param;

        //mise à jour
        try {
            $this->database->updateRows($this->table, ['contenu' => json_encode($contenu)], $this->var_id_objet . "=$id_objet AND ID=$id_element AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
        }

        return ['valid' => true];
    }

    public function update_element_padding($cleaned_post)
    {
        $id_element = filter_var($cleaned_post['id_element'], FILTER_VALIDATE_INT);
        $id_objet = filter_var($cleaned_post['id_objet'], FILTER_VALIDATE_INT);

        //element
        $element = $elementDesktop = $this->getElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas.")];
        }

        //sauvegarde de l'élément
        $saveElementBackup = $this->saveElementBackup($element);
        if (!$saveElementBackup['valid']) {
            return ['valid' => false, 'message' => $saveElementBackup['message']];
        }

        if (isset($cleaned_post['is_mobile']) and $cleaned_post['is_mobile'] and $element['contenu_mobile']) {
            $contenu_mobile = json_decode($element['contenu_mobile'], true);
            foreach ($contenu_mobile as $var_mobile => $content_mobile) {
                $element[$var_mobile] = $content_mobile;
            }
        }

        //apparence
        $contenu = [];
        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);
        }

        $contenu['padding_top'] = filter_var($cleaned_post['padding_top'], FILTER_VALIDATE_INT);
        $contenu['padding_bottom'] = filter_var($cleaned_post['padding_bottom'], FILTER_VALIDATE_INT);
        $contenu['padding_left'] = filter_var($cleaned_post['padding_left'], FILTER_VALIDATE_INT);
        $contenu['padding_right'] = filter_var($cleaned_post['padding_right'], FILTER_VALIDATE_INT);

        $contenu = json_encode($contenu);

        $array_update = [
            'contenu' => $contenu,
            'datemodification' => date('Y-m-d H:i:s'),
        ];

        if (isset($cleaned_post['is_mobile']) and $cleaned_post['is_mobile']) {
            $array_update = eden()->Builder_Tools()->getUpdateForMobile($elementDesktop, $array_update);
            if (!$array_update) {
                return ['valid' => true];
            }
        }

        //mise à jour
        try {
            $this->database->updateRows($this->table, $array_update, $this->var_id_objet . "=$id_objet AND ID=$id_element AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
        }

        return ['valid' => true];
    }

    public function update_element_margin($cleaned_post)
    {
        $id_element = filter_var($cleaned_post['id_element'], FILTER_VALIDATE_INT);
        $id_objet = filter_var($cleaned_post['id_objet'], FILTER_VALIDATE_INT);

        //element
        $element = $elementDesktop = $this->getElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas.")];
        }

        //sauvegarde de l'élément
        $saveElementBackup = $this->saveElementBackup($element);
        if (!$saveElementBackup['valid']) {
            return ['valid' => false, 'message' => $saveElementBackup['message']];
        }

        if (isset($cleaned_post['is_mobile']) and $cleaned_post['is_mobile'] and $element['contenu_mobile']) {
            $contenu_mobile = json_decode($element['contenu_mobile'], true);
            foreach ($contenu_mobile as $var_mobile => $content_mobile) {
                $element[$var_mobile] = $content_mobile;
            }
        }

        //apparence
        $contenu = [];
        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);
        }

        $contenu['margin_top'] = filter_var($cleaned_post['margin_top'], FILTER_VALIDATE_INT);
        $contenu['margin_bottom'] = filter_var($cleaned_post['margin_bottom'], FILTER_VALIDATE_INT);

        $contenu = json_encode($contenu);

        $array_update = [
            'contenu' => $contenu,
            'datemodification' => date('Y-m-d H:i:s'),
        ];

        if (isset($cleaned_post['is_mobile']) and $cleaned_post['is_mobile']) {
            $array_update = eden()->Builder_Tools()->getUpdateForMobile($elementDesktop, $array_update);
            if (!$array_update) {
                return ['valid' => true];
            }
        }

        //mise à jour
        try {
            $this->database->updateRows($this->table, $array_update, $this->var_id_objet . "=$id_objet AND ID=$id_element AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
        }

        return ['valid' => true];
    }

    public function update_element_duree($id_element, $duree)
    {
        try {
            $this->database->updateRows($this->table, ['duree' => $duree], "ID='$id_element' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour d'un élément.")];
        }

        return ['valid' => true];
    }

    public function update_element_alignement($cleaned_post)
    {
        $id_element = filter_var($cleaned_post['id_element'], FILTER_VALIDATE_INT);
        $id_objet = filter_var($cleaned_post['id_objet'], FILTER_VALIDATE_INT);
        $alignement = filter_var($cleaned_post['alignement'], FILTER_SANITIZE_SPECIAL_CHARS);

        //element
        $element = $this->getElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas.")];
        }

        //apparence
        $contenu = [];
        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);
        }

        $array_update = [
            'datemodification' => date('Y-m-d H:i:s'),
        ];

        if ('button_link' == $element['objet'] or 'fa-icon' == $element['objet']) {
            $contenu['align'] = $alignement;
            $contenu = json_encode($contenu);
            $array_update['contenu'] = $contenu;
        } elseif ('txt' == $element['objet']) {
            $element['contenutexte'] = str_replace('text-align: left', 'text-align: ' . $alignement, $element['contenutexte']);
            $element['contenutexte'] = str_replace('text-align: center', 'text-align: ' . $alignement, $element['contenutexte']);
            $element['contenutexte'] = str_replace('text-align: right', 'text-align: ' . $alignement, $element['contenutexte']);
            $element['contenutexte'] = str_replace('text-align:left', 'text-align: ' . $alignement, $element['contenutexte']);
            $element['contenutexte'] = str_replace('text-align:center', 'text-align: ' . $alignement, $element['contenutexte']);
            $element['contenutexte'] = str_replace('text-align:right', 'text-align: ' . $alignement, $element['contenutexte']);
            $element['contenutexte'] = str_replace('<h1>', '<h1 style="text-align: ' . $alignement . '">', $element['contenutexte']);
            $element['contenutexte'] = str_replace('<h2>', '<h2 style="text-align: ' . $alignement . '">', $element['contenutexte']);
            $element['contenutexte'] = str_replace('<h3>', '<h3 style="text-align: ' . $alignement . '">', $element['contenutexte']);
            $element['contenutexte'] = str_replace('<h4>', '<h4 style="text-align: ' . $alignement . '">', $element['contenutexte']);
            $element['contenutexte'] = str_replace('<h5>', '<h5 style="text-align: ' . $alignement . '">', $element['contenutexte']);
            $element['contenutexte'] = str_replace('<h6>', '<h6 style="text-align: ' . $alignement . '">', $element['contenutexte']);
            $element['contenutexte'] = str_replace('<p>', '<p style="text-align: ' . $alignement . '">', $element['contenutexte']);
            $element['contenutexte'] = str_replace('<span>', '<span style="text-align: ' . $alignement . '">', $element['contenutexte']);
            $array_update['contenutexte'] = $element['contenutexte'];
        } elseif ('image' == $element['objet'] or 'mp3player' == $element['objet']) {
            $array_update['fleche'] = $alignement;
        }

        //mise à jour
        try {
            $this->database->updateRows($this->table, $array_update, $this->var_id_objet . "=$id_objet AND ID=$id_element AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
        }

        return ['valid' => true];
    }

    public function update_element_txt($id_objet, $id_element, $texte, $datas = [])
    {
        $objet = eden()->{'Learnybox\Services\Builder\Builder' . ucfirst($this->type)}()->getObjetById($id_objet);
        if (!$objet) {
            return ['valid' => false, 'message' => __('Erreur : cet objet n\'existe pas')];
        }

        $element = $this->getElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur : cet élément1 n'existe pas.")];
        }

        if ($element[$this->var_id_objet] != $id_objet) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas.")];
        }

        $saveElementBackup = $this->saveElementBackup($element);
        if (!$saveElementBackup['valid']) {
            return ['valid' => false, 'message' => $saveElementBackup['message']];
        }

        $texte = urldecode($texte);
        $texte = addslashes($texte);

        //strip PHP tags
        if (false !== strpos($texte, '<?')) {
            $texte = preg_replace('/\<(\?|\%)(.*)(\?|\%)\>/s', '', $texte);
        }

        $contenu = [];
        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);
        }

        if ($datas) {
            foreach ($datas as $data_name => $data_value) {
                if ('is_mobile' == $data_name) {
                    continue;
                }
                $contenu[$data_name] = $data_value;
            }
        }

        $array_update = [
            'contenutexte' => $texte,
            'contenu' => json_encode($contenu),
            'datemodification' => date('Y-m-d H:i:s'),
        ];

        if (isset($datas['is_mobile']) and $datas['is_mobile']) {
            $mobile_update = json_decode($element['contenu_mobile'], true);

            if ($texte != $element['contenutexte']) {
                $mobile_update['contenutexte'] = $texte;
            } elseif (isset($mobile_update['contenutexte'])) {
                unset($mobile_update['contenutexte']);
            }

            if (json_encode($contenu) != $element['contenu']) {
                $mobile_update['contenu'] = json_encode($contenu);
            } elseif (isset($mobile_update['contenu'])) {
                unset($mobile_update['contenu']);
            }

            $array_update = [
                'contenu_mobile' => !empty($mobile_update) ? json_encode($mobile_update) : '',
            ];
        }

        //mise à jour
        try {
            $this->database->updateRows($this->table, $array_update, "ID='$id_element' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
        }

        if ($this->type == 'tunnels' and $objet['date_modification'] == '0000-00-00 00:00:00') {
            eden()->Builder_TunnelsPages()->update_page_date_modification($id_objet);
        }

        return ['valid' => true];
    }

    public function update_element_image($cleaned_post)
    {
        $id_element = filter_var($cleaned_post['id_element'], FILTER_VALIDATE_INT);
        $id_objet = filter_var($cleaned_post['id_objet'], FILTER_VALIDATE_INT);
        $url = filter_var($cleaned_post['urls'], FILTER_VALIDATE_URL);

        //element
        $element = $this->getElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas.")];
        }

        //sauvegarde de l'élément
        $saveElementBackup = $this->saveElementBackup($element);
        if (!$saveElementBackup['valid']) {
            return ['valid' => false, 'message' => $saveElementBackup['message']];
        }

        $array_update = [
            'urls' => $url,
            'datemodification' => date('Y-m-d H:i:s'),
        ];

        $full_url = WEB_PATH . '/' . str_replace(SITE_URL, '', $array_update['urls']);
        $full_url = urldecode($full_url);

        $getID3 = new \getID3();
        $imageInfos = $getID3->analyze($full_url);
        if ($imageInfos and isset($imageInfos['video']['resolution_x']) and isset($imageInfos['video']['resolution_y'])) {
            $array_update['largeur'] = filter_var($imageInfos['video']['resolution_x'], FILTER_VALIDATE_INT);
            $array_update['hauteur'] = filter_var($imageInfos['video']['resolution_y'], FILTER_VALIDATE_INT);
        }

        $array_update['contenu'] = [];
        if ($element['contenu']) {
            $array_update['contenu'] = json_decode($element['contenu'], true);
        }

        if (isset($cleaned_post['original_src']) and $cleaned_post['original_src']) {
            $array_update['contenu']['original_src'] = filter_var($cleaned_post['original_src'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        if (isset($cleaned_post['crop_datas']) and $cleaned_post['crop_datas']) {
            $crop_datas = filter_var($cleaned_post['crop_datas'], FILTER_SANITIZE_SPECIAL_CHARS);
            $crop_datas = htmlspecialchars_decode($crop_datas);
            $array_update['contenu']['crop_datas'] = $crop_datas;
        }

        if (isset($cleaned_post['img_datas']) and $cleaned_post['img_datas']) {
            $img_datas = filter_var($cleaned_post['img_datas'], FILTER_SANITIZE_SPECIAL_CHARS);
            $img_datas = htmlspecialchars_decode($img_datas);
            $array_update['contenu']['img_datas'] = $img_datas;
        }

        if (isset($cleaned_post['crop_zoom']) and $cleaned_post['crop_zoom']) {
            $array_update['contenu']['crop_zoom'] = filter_var($cleaned_post['crop_zoom'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        if (isset($cleaned_post['cropbox_datas']) and $cleaned_post['cropbox_datas']) {
            $cropbox_datas = filter_var($cleaned_post['cropbox_datas'], FILTER_SANITIZE_SPECIAL_CHARS);
            $cropbox_datas = htmlspecialchars_decode($cropbox_datas);
            $array_update['contenu']['cropbox_datas'] = $cropbox_datas;

            $cropbox_datas = json_decode($cropbox_datas, true);
            if (isset($cropbox_datas['height'])) {
                $array_update['hauteur'] = $cropbox_datas['height'];
            }
            if (isset($cropbox_datas['width'])) {
                $array_update['largeur'] = $cropbox_datas['width'];
            }
        }

        if (isset($cleaned_post['crop_transform']) and $cleaned_post['crop_transform']) {
            //matrix(1, 0, 0, 1, -946, -4855.83)
            $matrix = filter_var($cleaned_post['crop_transform'], FILTER_SANITIZE_SPECIAL_CHARS);
            $matrix = str_replace(['matrix(', ')'], '', $matrix);

            $crop_transform = ['x' => 0, 'y' => 0];

            $explode = explode(', ', $matrix);
            if (isset($explode[4])) {
                $crop_transform['x'] = $explode[4];
            }
            if (isset($explode[5])) {
                $crop_transform['y'] = $explode[5];
            }

            $array_update['contenu']['crop_transform'] = json_encode($crop_transform);
        }

        //apparence
        $array_update['contenu'] = json_encode($array_update['contenu']);

        //mise à jour
        try {
            $this->database->updateRows($this->table, $array_update, $this->var_id_objet . "=$id_objet AND ID=$id_element AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
        }

        return ['valid' => true];
    }

    public function update_element_view($id_objet, $id_element, $is_mobile = false)
    {
        //element
        $element = $elementDesktop = $this->getElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas.")];
        }

        if ($is_mobile and $element['contenu_mobile']) {
            $contenu_mobile = json_decode($element['contenu_mobile'], true);
            foreach ($contenu_mobile as $var_mobile => $content_mobile) {
                $element[$var_mobile] = $content_mobile;
            }
        }

        $hide = true;
        if ($element['hide']) {
            $hide = false;
        }

        $array_update = [
            'hide' => $hide,
            'datemodification' => date('Y-m-d H:i:s'),
        ];

        if ($is_mobile) {
            $array_update = eden()->Builder_Tools()->getUpdateForMobile($elementDesktop, $array_update);
            if (!$array_update) {
                return ['valid' => true];
            }
        }

        //mise à jour
        try {
            $this->database->updateRows($this->table, $array_update, $this->var_id_objet . "=$id_objet AND ID=$id_element AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
        }

        return ['valid' => true, 'view' => !$hide];
    }

    public function updateElementViewDesktop(int $idObjet, int $idElement): array
    {
        $element = $this->getElementById($idElement);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas.")];
        }

        $arrayUpdate = [
            'hide_desktop' => !$element['hide_desktop'],
            'datemodification' => date('Y-m-d H:i:s'),
        ];
        try {
            $this->database->updateRows($this->table, $arrayUpdate, $this->var_id_objet . "=$idObjet AND ID=$idElement AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
        }

        return ['valid' => true, 'view' => !$arrayUpdate['hide_desktop']];
    }

    public function update_ajax_elements($id_objet, $id_line, $id_col, $elements, $line_position, $new_zone = false, $new_col = false, $is_mobile = false)
    {
        $return_line = false;

        $objet = eden()->{'Learnybox\Services\Builder\Builder' . ucfirst($this->type)}()->getObjetById($id_objet);
        if (!$objet) {
            return ['valid' => false, 'message' => __("Erreur : cet objet n'existe pas.")];
        }

        //idline
        if (!$id_col) {
            if ($new_col) {
                //create first col
                $array_insert_col = [
                    'id_objet' => $id_objet,
                    'id_line' => $id_line,
                    'nb_cols' => '100',
                    'new_col' => $new_col,
                    'line_position' => $line_position,
                ];
                $col = eden()->{'Learnybox\Services\Builder\BuilderCols\BuilderCols' . ucfirst($this->type)}()->insert_cols($array_insert_col);
                if (!$col['valid']) {
                    return ['valid' => false, 'message' => $col['message']];
                }

                $id_col = $col['id_col'];
            } elseif ($new_zone) {
                if (!$line_position) {
                    $new_zone = 'top';
                }
                //create new section
                $array_insert_col = [
                    'id_objet' => $id_objet,
                    'id_line' => $id_line,
                    'nb_cols' => '100',
                    'new_zone' => $new_zone,
                ];
                $col = eden()->{'Learnybox\Services\Builder\BuilderCols\BuilderCols' . ucfirst($this->type)}()->insert_cols($array_insert_col);
                if (!$col['valid']) {
                    return ['valid' => false, 'message' => $col['message']];
                }

                $id_col = $col['id_col'];
            } else {
                $col = eden()->{'Learnybox\Services\Builder\BuilderCols\BuilderCols' . ucfirst($this->type)}()->getFirstCol($id_objet, $id_line);
                if (!$col) {
                    //create first col
                    $array_insert_col = [
                        'id_objet' => $id_objet,
                        'id_line' => $id_line,
                        'nb_cols' => '33-33-33',
                    ];
                    $col = eden()->{'Learnybox\Services\Builder\BuilderCols\BuilderCols' . ucfirst($this->type)}()->insert_cols($array_insert_col);
                    if (!$col['valid']) {
                        return ['valid' => false, 'message' => $col['message']];
                    }
                }
                $id_col = $col['id_col'];
            }
        }

        $i = 0;
        foreach ($elements as $elementId) {
            $elementId = str_replace('element', '', $elementId);

            $arrayUpdate = [
                'id_line' => $id_line,
                'id_col' => $id_col,
                'position' => $i,
                'datemodification' => date('Y-m-d H:i:s')
            ];
            if ($is_mobile) {
                unset($arrayUpdate['position']);

                $element = $this->getElementById($elementId);
                if (!$element) {
                    continue;
                }

                $contenuMobile = ($element['contenu_mobile'] ? json_decode($element['contenu_mobile'], true) : []);
                $contenuMobile['position'] = $i;
                $arrayUpdate['contenu_mobile'] = json_encode($contenuMobile);
            }

            //mise à jour
            try {
                $this->database->updateRows($this->table, $arrayUpdate, "ID='$elementId' AND " . $this->var_id_objet . " = '$id_objet' AND id_client='" . $_SESSION['id_client'] . "'");
            } catch (\Eden_Error $e) {
                return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
            }
            ++$i;
        }

        if ($new_col or $new_zone) {
            return ['valid' => true, 'id_line' => $id_line, 'id_col' => $id_col, 'return_col' => true];
        }

        return ['valid' => true];
    }

    public function replace_element($id_element, $type)
    {
        try {
            $this->database->updateRows($this->table, ['objet' => $type], "ID='$id_element' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément")];
        }

        return ['valid' => true];
    }

    public function duplicate_element($id_element, $direction = 'bottom')
    {
        $id_element = filter_var($id_element, FILTER_VALIDATE_INT);

        $element = $this->getElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas")];
        }

        $id_objet = $element[$this->var_id_objet];
        $id_line = $element['id_line'];
        $id_col = $element['id_col'];

        if ('sommaire' === $element['objet']) {
            return ['valid' => false, 'message' => __('Un élément sommaire est déjà présent dans la page, il n\'est pas possible d\'en ajouter')];
        }

        if (!$direction or !in_array($direction, ['top', 'bottom'])) {
            $direction = 'bottom';
        }

        //find position
        $position = 1;
        if (isset($cleaned_post['position'])) {
            $position = filter_var($cleaned_post['position'], FILTER_VALIDATE_INT);
        } else {
            $position = $element['position'];

            if ('bottom' == $direction) {
                ++$position;
            } elseif ('top' == $direction) {
                //on garde la même position
            }

            //mise à jour de tous les éléments suivants
            $elements = $this->getElementsByLine($id_objet, $id_line);
            if ($elements) {
                foreach ($elements as $_element) {
                    if ($_element['position'] < $position) {
                        continue;
                    }

                    $temp_position = $_element['position'];
                    ++$temp_position;

                    try {
                        $this->database->updateRows($this->table, ['position' => $temp_position], "ID='" . $_element['ID'] . "' AND " . $this->var_id_objet . "='$id_objet' AND id_client='" . $_SESSION['id_client'] . "'");
                    } catch (\Eden_Error $e) {
                        return ['valid' => false, 'message' => __('Erreur lors de la mise à jour de la position des éléments suivants.')];
                    }
                }
            }
        }

        //insertion
        $array_insert = $element;
        $array_insert['position'] = $position;
        $array_insert['datecreation'] = date('Y-m-d H:i:s');
        $array_insert['datemodification'] = date('Y-m-d H:i:s');
        unset($array_insert['ID']);

        if ($element['objet'] == 'downloads') {
            $array_insert['duree'] = 0;
        }

        try {
            $this->database->insertRow($this->table, $array_insert);
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la duplication de l'élément.")];
        }

        $new_id_element = $this->database->getLastInsertedId();
        if (!$new_id_element) {
            return ['valid' => false, 'message' => __("Erreur lors de la duplication de l'élément.")];
        }

        //elements
        if ('box' == $element['objet']) {
            //duplication des éléments de cette box
            $sub_elements = $this->getElementsByBox($id_objet, $id_element);
            if ($sub_elements) {
                foreach ($sub_elements as $sub_element) {
                    $array_insert = $sub_element;
                    unset($array_insert['ID']);
                    $array_insert['parent'] = $new_id_element;
                    $array_insert['datecreation'] = date('Y-m-d H:i:s');
                    $array_insert['datemodification'] = date('Y-m-d H:i:s');

                    //insertion
                    try {
                        $this->database->insertRow($this->table, $array_insert);
                    } catch (\Eden_Error $e) {
                        return ['valid' => false, 'message' => __("Erreur lors de la duplication de l'élément.")];
                    }
                }
            }
        }

        return ['valid' => true, 'id_objet' => $id_objet, 'id_element' => $new_id_element];
    }

    public function delete_element($id_objet, $id_element)
    {
        $element = $this->getElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas")];
        }

        if ($element[$this->var_id_objet] != $id_objet) {
            return ['valid' => false, 'message' => __('Erreur : cet élément n\'existe pas')];
        }

        $saveElementBackup = $this->saveElementBackup($element);
        if (!$saveElementBackup['valid']) {
            return ['valid' => false, 'message' => $saveElementBackup['message']];
        }

        $filter = [];
        $filter[] = ['ID=%s', $id_element];
        $filter[] = ['id_client=%s', $_SESSION['id_client']];
        $filter[] = [$this->var_id_objet . '=%s', $id_objet];

        try {
            $this->database->deleteRows($this->table, $filter);
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la suppression de l'élément.")];
        }

        $filter = [];
        $filter[] = ['parent=%s', $id_element];
        $filter[] = ['id_client=%s', $_SESSION['id_client']];
        $filter[] = [$this->var_id_objet . '=%s', $id_objet];

        try {
            $this->database->deleteRows($this->table, $filter);
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la suppression de l'élément.")];
        }

        return ['valid' => true];
    }

    /********************************************************/
    /*********************** BACKUPS ************************/
    /********************************************************/

    /**
     * @param int $id_element
     * @return array|null
     */
    public function getLastBackupByelement(int $id_element): ?array
    {
        $result = $this->database
            ->search($this->table_backups)
            ->addFilter("ID='$id_element' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('id_backup', 'DESC')
            ->getRow();

        return $result;
    }

    /**
     * @param int $id_col_backup
     * @return array|null
     */
    public function getElementsBackupsByColBackup(int $id_col_backup): ?array
    {
        $result = $this->database
            ->search($this->table_backups)
            ->addFilter("id_col_backup='$id_col_backup' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getRows();

        return $result;
    }

    /**
     * @param array $element
     * @param int $id_col_backup
     * @return array
     */
    public function saveElementBackup(array $element, int $id_col_backup = 0): array
    {
        $array_insert = $element;
        $array_insert['id_client'] = $_SESSION['id_client'];
        $array_insert['datecreation'] = date('Y-m-d H:i:s');
        $array_insert['datemodification'] = date('Y-m-d H:i:s');
        $array_insert['id_col_backup'] = $id_col_backup;

        try {
            $this->database->insertRow($this->table_backups, $array_insert);
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de l'insertion de l'élément.")];
        }

        return ['valid' => true];
    }

    /**
     * @param int $id_element
     * @return array
     */
    public function restoreElement(int $id_element): array
    {
        $last_backup = $this->getLastBackupByelement($id_element);
        if (!$last_backup) {
            return ['valid' => false, 'message' => __('Aucune sauvegarde trouvée.')];
        }

        $this->deleteBackupElement($last_backup['id_backup']);

        $element = $this->getElementById($id_element);
        if (!$element) {
            $array_insert = $last_backup;
            unset($array_insert['id_backup']);
            unset($array_insert['id_col_backup']);

            try {
                $this->database->insertRow($this->table, $array_insert);
            } catch (\Eden_Error $e) {
                return ['valid' => false, 'message' => __("Erreur lors de l'insertion de l'élément.")];
            }
        } else {
            $array_update = $last_backup;
            unset($array_update['id_backup']);
            unset($array_update['id_col_backup']);

            try {
                $this->database->updateRows($this->table, $array_update, "ID='$id_element' AND id_client='" . $_SESSION['id_client'] . "'");
            } catch (\Eden_Error $e) {
                return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
            }
        }

        return ['valid' => true, 'id_line' => $last_backup['id_line']];
    }

    /**
     * @param int $id_col_backup
     * @param int $id_objet
     * @return array
     */
    public function restoreElementsByColBackup(int $id_col_backup, int $id_objet): array
    {
        $backups = $this->getElementsBackupsByColBackup($id_col_backup);
        if (!$backups) {
            return ['valid' => true];
        }

        foreach ($backups as $backup) {
            $this->deleteBackupElement($backup['id_backup']);

            $array_insert = $backup;
            unset($array_insert['id_backup']);
            unset($array_insert['id_col_backup']);

            try {
                $this->database->insertRow($this->table, $array_insert);
            } catch (\Eden_Error $e) {
                return ['valid' => false, 'message' => __("Erreur lors de l'insertion de l'élément.")];
            }
        }

        return ['valid' => true];
    }

    /**
     * @param int $id_backup
     * @return array
     */
    public function deleteBackupElement(int $id_backup): array
    {
        $filter = [];
        $filter[] = ['id_backup=%s', $id_backup];

        try {
            $this->database->deleteRows($this->table_backups, $filter);
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la suppression de la sauvegarde.")];
        }

        return ['valid' => true];
    }

    /**
     * @return array
     */
    public function delete_backups_elements(): array
    {
        $filter = [];
        $filter[] = ['datecreation<=%s', date('Y-m-d') . ' 00:00:00'];

        try {
            $this->database->deleteRows($this->table_backups, $filter);
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la suppression de l'élément.")];
        }

        return ['valid' => true];
    }

    /********************************************************/
    /********************** FUNCTIONS ***********************/
    /********************************************************/

    public function generateSelectFont($font = '')
    {
        $output = '';
        $fonts = [
            'Arial',
            'Book Antiqua',
            'Calibri',
            'Candara',
            'Cambria',
            'Century',
            'Century Gothic',
            'Comic Sans MS',
            'Consolas',
            'Constantia',
            'Courrier New',
            'Franklin Gothic Medium',
            'Garamond',
            'Georgia',
            'Helvetica',
            'Helvetica Neue',
            'Impact',
            'Lucida Sans Unicode',
            'Segoe Print',
            'Tahoma',
            'Times New Roman',
            'Trebuchet MS',
            'Verdana',
        ];

        foreach ($fonts as $_font) {
            $output .= '<option value="' . $_font . '"';
            if ($font and $font == $_font) {
                $output .= ' selected';
            }
            $output .= '><span style="font:' . $_font . '">' . $_font . '</span></option>';
        }

        return $output;
    }

    /**
     * generateSelectSpan function.
     *
     * @param string $span (default: '')
     *
     * @return string
     */
    public function generateSelectSpan($span = '')
    {
        $output = '';
        if (!$span) {
            $span = 'span6';
        }

        $array_spans = [
            'span1' => '8%',
            'span2' => '16%',
            'span3' => '25%',
            'span4' => '33%',
            'span5' => '41%',
            'span6' => '50%',
            'span7' => '58%',
            'span8' => '66%',
            'span9' => '75%',
            'span10' => '83%',
            'span11' => '91%',
            'span12' => '100%',
        ];
        foreach ($array_spans as $_span => $width) {
            $output .= '<option value="' . $_span . '"';
            if ($span == $_span) {
                $output .= ' selected';
            }
            $output .= '>' . $width . '</option>';
        }

        return $output;
    }

    /**
     * @param array $data
     * @return mixed|void
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function renderElementSelectPanel(array $data)
    {
        $elementGroup = $this->container->get('Learnybox\Services\Builder\BuilderObjects\BuilderObjects' . ucfirst($data['objet']))->getObject($data['type']);
        $lightObjects = $this->container->get('Learnybox\Services\Builder\BuilderObjects\BuilderObjects' . ucfirst($data['objet']))->getLightObjects();
        if (empty($elementGroup) || empty($elementGroup['objects'])) {
            return;
        }
        $options = [];
        $options[] = [
            'value' => '',
            'label' => __('Choisissez')
        ];
        foreach ($elementGroup['objects'] as $element) {
            // If is light builder, keep only light objects
            if (!empty($lightObjects) && !in_array($element['type'], $lightObjects)) {
                continue;
            }

            $options[] = [
                'value' => $element['type'],
                'label' => $element['titre']
            ];
        }

        $lib = __('Choisissez un type');
        if ($elementGroup['type'] === 'elements_group_comments') {
            $lib = __('Type de commentaires');
        }
        if ($elementGroup['type'] === 'elements_group_liste_articles') {
            $lib = __('Type de liste');
        }

        $html = $this->container->get(BuilderPanelFormService::class)->renderSelect('element', $options, $lib);
        $group = $this->container->get(BuilderPanelService::class)->renderGroup($html);

        $tabs = [
            'element_select' => [
                'title' => __('Type'),
                'content' => $group
            ]
        ];

        return $this->container->get(BuilderPanelService::class)->renderTabs(
            $tabs,
            $elementGroup['titre'],
            [],
            null,
            null,
            null,
            null,
            null,
            'action-element-group-cancel',
            null,
            null,
            []
        );
    }

    protected function validateExternalUrl(string $url): bool
    {
        $sanitizedUrl = filter_var($url, FILTER_SANITIZE_URL);
        if (!filter_var($sanitizedUrl, FILTER_VALIDATE_URL)) {
            return false;
        }

        return true;
    }
}
