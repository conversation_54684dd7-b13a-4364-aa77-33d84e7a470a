<?php

namespace Learnybox\Services\Builder;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityRepository;
use Learnybox\Entity\Config\Config;
use Learnybox\Entity\Formation\Page\FormationPage;
use Learnybox\Entity\Formation\Page\FormationPageLine;
use Learnybox\Entity\Mail\Formulaire\MailFormulaireElement;
use Learnybox\Entity\Page\Line\PageLine;
use Learnybox\Entity\Page\Page;
use Learnybox\Enums\BuilderTypeEnum;
use Learnybox\Services\Logger\LoggerService;
use Learnybox\Services\Monolog\Logger;

class BuilderService
{
    /**
     * @var EntityManager
     */
    protected $em;

    public function __construct(EntityManager $em)
    {
        $this->em = $em;
    }

    /**
     * @param int $idObjet
     * @param string $objet
     * @return bool
     */
    public function deleteAllMobileContent(int $idObjet, string $objet): bool
    {
        $fpeRep = $this->getMobileContentRepository($objet, 'element');
        $fpcRep = $this->getMobileContentRepository($objet, 'col');
        $fplRep = $this->getMobileContentRepository($objet, 'line');

        $findByParam = '';
        switch ($objet) {
            case 'formations':
            case 'formation':
                $findByParam = 'formationPage';
                break;
            case 'tunnels':
            case 'tunnel':
                $findByParam = 'tunnelPage';
                break;
            case 'popups':
            case 'popup':
                $findByParam = 'popup';
                break;
            case 'pages':
            case 'page':
                $findByParam = 'page';
                break;
            case 'mails':
                $findByParam = 'mail';
                break;
            case 'articles':
                $findByParam = 'article';
                break;
        }

        $nbDeletedElts = 0;
        try {
            $fpElts = $fpeRep->findBy([$findByParam => $idObjet]);
            $fpElts = array_merge($fpElts, $fpcRep->findBy([$findByParam => $idObjet]));
            $fpElts = array_merge($fpElts, $fplRep->findBy([$findByParam => $idObjet]));

            $headerPageId = $this->getHeaderPageId($objet, $idObjet);
            if ($headerPageId !== null) {
                $fpElts = array_merge($fpElts, $fpeRep->findBy([$findByParam => $headerPageId]));
                $fpElts = array_merge($fpElts, $fpcRep->findBy([$findByParam => $headerPageId]));
                $fpElts = array_merge($fpElts, $fplRep->findBy([$findByParam => $headerPageId]));
            }

            foreach ($fpElts as $fpElt) {
                if ($fpElt->getContenuMobile() !== '') {
                    $fpElt->setContenuMobile('');
                    $this->em->persist($fpElt);
                    $nbDeletedElts++;
                }
            }

            $this->em->flush();
            return true;
        } catch (\Exception $e) {
            LoggerService::log(Logger::ERROR, '[BUILDER-MOBILE]  ID CLIENT : ' . $_SESSION['client']['id_client'] . ' - ' . $e->getMessage());
            return false;
        }
    }

    public function getMobileContentRepository(string $objet, string $typeElt): ?EntityRepository
    {
        $rep = null;
        if ($objet !== '') {
            $repClassName = '';
            if ($typeElt !== '') {
                switch ($typeElt) {
                    case 'element':
                        $repClassName = 'Element';
                        break;
                    case 'col':
                        $repClassName = 'Col';
                        break;
                    case 'line':
                        $repClassName = 'Line';
                        break;
                }
            }
            switch ($objet) {
                case 'formations':
                case 'formation':
                    $repClassName = 'Learnybox\Entity\Formation\Page\FormationPage' . $repClassName;
                    break;
                case 'tunnels':
                case 'tunnel':
                    $repClassName = 'Learnybox\Entity\Tunnel\Page\TunnelPage' . $repClassName;
                    break;
                case 'popups':
                case 'popup':
                    $repClassName = 'Learnybox\Entity\Popup\\' . $repClassName . '\Popup' . $repClassName;
                    break;
                case 'pages':
                case 'page':
                    $repClassName = 'Learnybox\Entity\Page\\' . $repClassName . '\Page' . $repClassName;
                    break;
                case 'mails':
                    $repClassName = 'Learnybox\Entity\Mail\\' . $repClassName . '\Mail' . $repClassName;
                    break;
                case 'articles':
                    $repClassName = 'Learnybox\Entity\Article\\' . $repClassName . '\Article' . $repClassName;
                    break;
                case BuilderTypeEnum::BUILDER_MAILS_FORMULAIRES:
                    $repClassName = MailFormulaireElement::class;
                    break;
            }

            if (class_exists($repClassName)) {
                $repClass = new $repClassName();
                $rep = $this->em->getRepository(get_class($repClass));
            }
        }

        return $rep;
    }

    /**
     * @param int $id
     * @param string $objet
     * @param string $typeElt
     * @return bool
     */
    public function deleteMobileContent(int $id, string $objet, string $typeElt): bool
    {
        $rep = $this->getMobileContentRepository($objet, $typeElt);

        if ($rep) {
            $element = $rep->find($id);
            if ($element !== null) {
                $element->setContenuMobile('');
                $this->em->persist($element);
                $this->em->flush();
                return true;
            }
        }

        return false;
    }

    public function getHeaderPageId(string $objet, int $idObjet): ?int
    {
        $headerPageId = null;

        switch ($objet) {
            case 'formations':
            case 'formation':
                $formationPage = $this->em->getRepository(FormationPage::class)->find($idObjet);
                if ($formationPage) {
                    $headerLine = $this->em->getRepository(FormationPageLine::class)->find($formationPage->getFormation()->getIdLineHeader());
                    if ($headerLine) {
                        $headerPageId = $headerLine->getFormationPage()->getIdPage();
                    }
                }
                break;
            case 'pages':
            case 'page':
                $page = $this->em->getRepository(Page::class)->find($idObjet);
                if ($page) {
                    $idLineHeader = $this->em->getRepository(Config::class)->findOneBy(['name' => 'id_line_header']);
                    if ($idLineHeader) {
                        $headerLine = $this->em->getRepository(PageLine::class)->find($idLineHeader->getValue());
                        if ($headerLine) {
                            $headerPageId = $headerLine->getPage()->getIdPage();
                        }
                    }
                }
                break;
        }

        return $headerPageId;
    }
}
