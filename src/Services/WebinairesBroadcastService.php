<?php

namespace Learnybox\Services;

use Learnybox\Helpers\Assets;
use Learnybox\Services\Users\UsersService;

class WebinairesBroadcastService
{
    /**
     * @var \Csrf
     */
    private $csrfClass;

    /**
     * @var \EverWebinar_Webinaires
     */
    private $webinarClass;

    /**
     * @var \EverWebinar_Users
     */
    private $webinarUsers;

    /**
     * @var \EverWebinar_ChatSimulation
     */
    private $webinarChatSimulation;

    /**
     * @var \EverWebinar_Presentateurs
     */
    private $webinarPresentateurs;

    /**
     * @var \EverWebinar_Sondages
     */
    private $webinarSondages;

    /**
     * @var \EverWebinar_Textes
     */
    private $webinarTextes;

    /**
     * @var \EverWebinar_Offres
     */
    private $webinarOffres;

    /**
     * @var UsersService
     */
    private $usersService;

    /**
     * @var \Csrf
     */
    private $csrf;

    /**
     * WebinairesBroadcastService constructor.
     * @param \Csrf                       $csrfClass
     * @param \EverWebinar_Webinaires     $webinarClass
     * @param \EverWebinar_Users          $webinarUsers
     * @param \EverWebinar_ChatSimulation $webinarChatSimulation
     * @param \EverWebinar_Presentateurs  $webinarPresentateurs
     * @param \EverWebinar_Sondages       $webinarSondages
     * @param \EverWebinar_Textes         $webinarTextes
     * @param \EverWebinar_Offres         $webinarOffres,
     * @param UsersService                $usersService
     * @param \Csrf                       $csrf
     */
    public function __construct(
        \Csrf $csrfClass,
        \EverWebinar_Webinaires $webinarClass,
        \EverWebinar_Users $webinarUsers,
        \EverWebinar_ChatSimulation $webinarChatSimulation,
        \EverWebinar_Presentateurs $webinarPresentateurs,
        \EverWebinar_Sondages $webinarSondages,
        \EverWebinar_Textes $webinarTextes,
        \EverWebinar_Offres $webinarOffres,
        UsersService $usersService,
        \Csrf $csrf
    ) {
        $this->csrfClass = $csrfClass;
        $this->webinarClass = $webinarClass;
        $this->webinarUsers = $webinarUsers;
        $this->webinarChatSimulation = $webinarChatSimulation;
        $this->webinarPresentateurs = $webinarPresentateurs;
        $this->webinarSondages = $webinarSondages;
        $this->webinarTextes = $webinarTextes;
        $this->webinarOffres = $webinarOffres;
        $this->usersService = $usersService;
        $this->csrf = $csrf;
    }

    /**
     * @param array $webinaire
     * @param array $user
     * @param array $design
     * @param bool $replay
     * @return string
     */
    public function displayViewersRoom(array $webinaire, array $user, array $design, bool $replay = false)
    {
        $dateNow = date('Y-m-d H:i:s');
        $dateFinWebinaire = $this->webinarClass->getDateEndWebinaire($user['date_webinaire'], $webinaire['duree_video']);
        $dureeVideoSecondesTotal = $this->webinarClass->videoDurationInSeconds($webinaire['duree_video']);

        // set at 0 if replay
        $differenceSecondes = ($replay) ? 0 : $this->webinarClass->getSecondsBeforeEndWebinar($user['date_webinaire']);

        // Temps restant avant la fin du webinaire (en millisecondes)
        $timeEnd = strtotime($dateFinWebinaire);
        $timeNow = strtotime($dateNow);
        $timeToEnd = ($timeEnd - $timeNow) * 1000;

        $textes = $offres = '';
        if ($replay) {
            $infosReplay = $this->webinarClass->afficher_webinaire_replay($webinaire['random_id']);
            $textes = $infosReplay['textes'];
            $offres = $infosReplay['offres'];
        }

        $lastMessages = $this->webinarChatSimulation->displayChatSimulation($webinaire, $user['date_webinaire']);
        $chatContainer = \Eden_Template::i()->set('webinaire', $webinaire)
            ->set('lastMessages', $lastMessages)
            ->set('replay', $replay)
            ->parsePhp(VIEWS_PATH . '/common/chat.php');

        $sidebar = \Eden_Template::i()->set('webinaire', $webinaire)
            ->set('chatContainer', $chatContainer)
            ->set('replay', $replay)
            ->set('sondages', '')
            ->set('textes', $textes)
            ->set('offres', $offres)
            ->parsePHP(VIEWS_PATH . '/webinaire/broadcast-sidebar.php');


        Assets::addCssV5(Assets::CSS_TYPE_BASE, 'nucleo.css');

        Assets::addCssV5(Assets::CSS_TYPE_COMPONENTS, '_labels.css');
        Assets::addCssV5(Assets::CSS_TYPE_COMPONENTS, '_forms.css');
        Assets::addCssV5(Assets::CSS_TYPE_COMPONENTS, '_tooltips.css');
        Assets::addCssV5(Assets::CSS_TYPE_PAGES, 'webinaire.css');
        Assets::addCssV5(Assets::CSS_TYPE_PAGES, 'conference/chat.css');
        Assets::addCssV5(Assets::CSS_TYPE_PAGES, 'conference/offres.css');
        Assets::addCssV5(Assets::CSS_TYPE_PAGES, 'conference/sondages.css');

        // Emoji picker
        Assets::addJs('lib/jquery-emoji-picker/js/jquery.emojipicker.js', null);
        Assets::addJs('lib/jquery-emoji-picker/js/jquery.emojis.js', null);
        Assets::addJs('lib/jquery-emoji-picker/js/load.js', null);
        Assets::addCss('lib/jquery-emoji-picker/css/jquery.emojipicker.css', null);
        Assets::addCss('lib/jquery-emoji-picker/css/jquery.emojipicker.lb.css', null);

        //countdown
        Assets::addJs('common/jquery/jquery.plugin.js');
        Assets::addJs('common/jquery/jquery.countdown.min.js');
        if (false === \Learnybox\Helpers\I18nHelper::isEnglish()) {
            $language = substr(\Learnybox\Helpers\I18nHelper::getLang(), 0, 2);
            Assets::addJs('common/jquery/jquery.countdown-' . $language . '.js');
        }

        Assets::addJs('webinaire/webinaire-functions.js');
        if (!$replay) {
            Assets::addJs('https://js.pusher.com/4.1/pusher.min.js');
            Assets::addJs('app/ew-functions-pusher.js');
        }

        $dtz = new \DateTimeZone(SERVER_TIMEZONE);
        $datetime = new \DateTime('now', $dtz);
        $offset = round($dtz->getOffset($datetime) / 3600, 0);

        $date_webinaire = strtotime($user['date_webinaire']);
        $time_passed = time() - $date_webinaire;

        $prenom = '';
        if (isset($_SESSION['pseudo'])) {
            $prenom = addslashes($_SESSION['pseudo']);
        }
        if (!$prenom and isset($_SESSION['name'])) {
            $prenom = addslashes($_SESSION['name']);
        }

        if (!isset($_SESSION['uniqid'])) {
            $participant = eden()->EverWebinar_Participants()->getParticipantByUserId($user['id_user'], $webinaire['id_webinaire']);
            if ($participant) {
                $_SESSION['uniqid'] = $participant['uniqid'];
            } else {
                $isEditor = RightsService::isEditor();
                if ($isEditor and isset($_SESSION['random_id'])) {
                    $_SESSION['uniqid'] = $_SESSION['random_id'];
                }
            }
        }

        Assets::addInlineJs('
        <div style="display:none">' . $this->csrf->generateForm() . '</div>
        <script type="text/javascript">
            var prenom = \'' . $prenom . '\';
            var id_user = ' . $user['id_user'] . ';
            var in_webinaire = false;
            var rid = \'' . $_SESSION['uniqid'] . '\';
            var id_webinaire = ' . $webinaire['id_webinaire'] . ';
            var webinaire_state = \'' . $webinaire['etat'] . '\';
            var chat = \'' . $webinaire['chat'] . '\';
            var timezone_offset = ' . $offset . ';
            var user_date = ' . (strtotime($user['date_webinaire']) * 1000) . ';
            var time_passed = ' . $time_passed . ';
            var diff_sec = ' . $differenceSecondes . ';
            var pusher_key = \'' . PUSHER_KEY . '\';
            var pusher_cluster = \'' . PUSHER_CLUSTER . '\';
            var admin = false;
            var delay_start_video = 0;
        </script>');

        //prepare events
        $this->prepareEvents($webinaire, $user, $timeToEnd);

        //add modal
        if (!$design['inEditor'] and !$replay) {
            $modalEnterConf = $this->getModalEnterWebinaire($webinaire, $user);
            Assets::addInlineJs($modalEnterConf);
        }

        //get live video
        $liveVideo = $this->webinarClass->getVideoSource($webinaire['video'], $differenceSecondes, $replay, $webinaire['disable_pause'], $replay);
        $this->addLiveVideoJs($webinaire);

        $startJs = '
        <script type="text/javascript">
            function startVideo() {
                startPlayer();
                $(\'#modalEnterWebinaire\').fadeOut();
                showTimer(user_date + delay_start_video);
                Webinaire.StartConn();
        ';

        if ($webinaire['chat'] == "chat") {
            $startJs .= 'StartChat(id_webinaire, user_date, time_passed);' . "\n";
        }

        $notifications = eden()->EverWebinar_Notifications()->getNotifications($webinaire["id_webinaire"]);
        if ($notifications) {
            $startJs .= 'StartSalesNotifications(id_webinaire, user_date, time_passed);' . "\n";
        }

        if ($webinaire['afficher_participants'] && $webinaire['simuler_participants']) {
            $startJs .= 'StartDisplayViewers(id_webinaire, time_passed);' . "\n";
        }

        $startJs .= '
            }
        </script>';
        Assets::addInlineJs($startJs);


        if ('' != trim($webinaire['broadcast_js_head'])) {
            Assets::addInlineJs(stripslashes($webinaire['broadcast_js_head']), true);
        }
        if ('' != trim($webinaire['broadcast_js'])) {
            Assets::addInlineJs(stripslashes($webinaire['broadcast_js']));
        }

        $logo = '';
        if (isset($design['logo']) and $design['logo']) {
            $logo = $design['logo'];
        }

        $chatClass = 'hidden';
        $displayNbViewers = true;
        $nbViewers = 0;
        if ('chat' == $webinaire['chat'] or 'question' == $webinaire['chat']) {
            $chatClass = 'visible';
        }
        if (!$webinaire['afficher_participants']) {
            $displayNbViewers = false;
        }

        if ($replay and $webinaire['afficher_participants']) {
            if ($webinaire['simuler_participants']) {
                $nbViewers = $webinaire['simuler_participants_max'];
            } else {
                $nbViewers = $webinaire['delta_participants'];
            }
        }

        $presenters = [];
        $getPresenters = $this->webinarPresentateurs->getPresentateursByWebinaire($webinaire['id_webinaire']);
        if ($getPresenters) {
            foreach ($getPresenters as $presenter) {
                $presentateur = $this->webinarPresentateurs->getPresentateurById($presenter['id_presentateur']);
                if ($presentateur) {
                    $avatar = $presentateur['avatar'] ?? '';
                    if ($avatar === '') {
                        $avatar = $this->usersService->getGuestAvatar($presentateur['email'], 45, $presentateur['prenom']);
                    }
                    $presenters[] = [
                        'id' => $presentateur['id_presentateur'],
                        'fname' => $presentateur['prenom'],
                        'lname' => $presentateur['nom'],
                        'avatar' => $avatar,
                    ];
                }
            }
        }


        $output = \Eden_Template::i()->set('webinaire', $webinaire)
            ->set('user', $user)
            ->set('date_now', $dateNow)
            ->set('presenters', $presenters)
            ->set('sidebar', $sidebar)
            ->set('logo', $logo)
            ->set('design', $design)
            ->set('inEditor', $design['inEditor'])
            ->set('liveVideo', $liveVideo)
            ->set('chatClass', $chatClass)
            ->set('displayNbViewers', $displayNbViewers)
            ->set('nbViewers', $nbViewers)
            ->set('date_fin_webinaire', $dateFinWebinaire)
            ->set('duree_video_secondes_total', $dureeVideoSecondesTotal)
            ->set('difference_secondes', $differenceSecondes)
            ->set('timeToEnd', $timeToEnd)
            ->set('offres', $offres)
            ->parsePHP(VIEWS_PATH . '/webinaire/broadcast-room.php');
        $output = $this->csrf->replaceForm($output);
        return $output;
    }

    public function getModalEnterWebinaire(array $webinaire, array $user): string
    {
        $avatar = $this->usersService->getGuestAvatar($user['email'], 72, $user['fname']);
        return \Eden_Template::i()->set('user', $user)
            ->set('avatar', $avatar)
            ->set('webinaire', $webinaire)
            ->parsePhp(VIEWS_PATH . '/webinaire/modal-enter-webinaire.php');
    }

    public function prepareEvents($webinaire, $user, $timeToEnd)
    {
        $id_webinaire = $webinaire['id_webinaire'];
        $date_start = strtotime($user['date_webinaire']);
        $time = time() * 1000;

        $events = '';
        $sondages = $this->webinarSondages->getSondagesPublies($id_webinaire);
        $textes = $this->webinarTextes->getTextesPublies($id_webinaire);
        $offres = $this->webinarOffres->getOffresPubliees($id_webinaire);

        $id = 0;
        if ($sondages) {
            foreach ($sondages as $sondage) {
                if ('active' != $sondage['etat']) {
                    continue;
                }

                $start = explode(':', $sondage['heure_publication']);
                $end = explode(':', $sondage['heure_publication_fin']);

                $start = ($date_start + ($start[0] * 3600 + $start[1] * 60 + $start[2])) * 1000;
                $end = ($date_start + ($end[0] * 3600 + $end[1] * 60 + $end[2])) * 1000;

                //si le participant arrive après la fin du sondage on ne l'affiche pas
                if ('00:00:00' != $sondage['heure_publication_fin'] and $time >= $end) {
                    continue;
                }

                $events .= 'events[' . $id . '] = {\'time\': ' . $start . ', \'event\': \'sondage\', \'param\': ' . $sondage['id_sondage'] . ', \'done\': 0};' . "\n";
                ++$id;

                if ('00:00:00' != $sondage['heure_publication_fin']) {
                    $events .= 'events[' . $id . '] = {\'time\': ' . $end . ', \'event\': \'sondage_termine\', \'param\': ' . $sondage['id_sondage'] . ', \'done\': 0};' . "\n";
                    ++$id;
                }
            }
        }

        if ($textes) {
            foreach ($textes as $texte) {
                if ('active' != $texte['etat']) {
                    continue;
                }

                $start = explode(':', $texte['heure_publication']);
                $end = explode(':', $texte['heure_publication_fin']);

                $start = ($date_start + ($start[0] * 3600 + $start[1] * 60 + $start[2])) * 1000;
                $end = ($date_start + ($end[0] * 3600 + $end[1] * 60 + $end[2])) * 1000;

                //si le participant arrive après la fin du texte on ne l'affiche pas
                if ('00:00:00' != $texte['heure_publication_fin'] and $time >= $end) {
                    continue;
                }

                $events .= 'events[' . $id . '] = {\'time\': ' . $start . ', \'event\': \'texte\', \'param\': ' . $texte['id_texte'] . ', \'done\': 0};' . "\n";
                ++$id;

                if ('00:00:00' != $texte['heure_publication_fin']) {
                    $events .= 'events[' . $id . '] = {\'time\': ' . $end . ', \'event\': \'texte_termine\', \'param\': ' . $texte['id_texte'] . ', \'done\': 0};' . "\n";
                    ++$id;
                }
            }
        }

        if ($offres) {
            foreach ($offres as $offre) {
                if ('active' != $offre['etat']) {
                    continue;
                }

                $start = explode(':', $offre['heure_publication']);
                $end = explode(':', $offre['heure_publication_fin']);

                $start = ($date_start + ($start[0] * 3600 + $start[1] * 60 + $start[2])) * 1000;
                $end = ($date_start + ($end[0] * 3600 + $end[1] * 60 + $end[2])) * 1000;

                //si le participant arrive après la fin du offre on ne l'affiche pas
                if ('00:00:00' != $offre['heure_publication_fin'] and $time >= $end) {
                    continue;
                }

                $events .= 'events[' . $id . '] = {\'time\': ' . $start . ', \'event\': \'offre\', \'param\': ' . $offre['id_offre'] . ', \'done\': 0};' . "\n";
                ++$id;

                if ('00:00:00' != $offre['heure_publication_fin']) {
                    $events .= 'events[' . $id . '] = {\'time\': ' . $end . ', \'event\': \'offre_termine\', \'param\': ' . $offre['id_offre'] . ', \'done\': 0};' . "\n";
                    ++$id;
                }
            }
        }

        if ($webinaire['redirection_url']) {
            $redirection_url = $webinaire['redirection_url'];
        } else {
            $redirection_url = \Tools::getLink($webinaire['id_domaine'], 'webinaire', 'sondage', $webinaire['random_id'] . '/' . $user['random_id']);
        }

        if (filter_var($redirection_url, FILTER_VALIDATE_URL)) {
            $events .= 'events[' . $id . '] = {\'time\': ' . ($time + $timeToEnd) . ', \'event\': \'redirection\', \'param\': \'' . $redirection_url . '\', \'done\': 0};' . "\n";
            ++$id;
        }

        $js = '
        <script type="text/javascript">
            var events = [];
            ' . $events . '
        </script>';

        Assets::addInlineJs($js);
    }

    /**
     * @param array $webinaire
     */
    public function addLiveVideoJs(array $webinaire)
    {
        $js = '';

        //video javascript
        if (strpos($webinaire['video'], 'youtu') !== false) {
            $videoId = \Tools::getYoutubeId($webinaire['video']);
            if ($videoId) {
                $js .= '
                <script>
                    var tag = document.createElement(\'script\');
                    tag.src = "https://www.youtube.com/player_api";
                    var firstScriptTag = document.getElementsByTagName(\'script\')[0];
                    firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

                    // Replace the ytplayer element with an <iframe> and
                    // YouTube player after the API code downloads.
                    var player;
                    function onYouTubePlayerAPIReady() {
                        player = new YT.Player(\'ytplayer\', {
                            height: \'440\',
                            width: \'760\',
                            videoId: \'' . $videoId . '\',
                            playerVars: {
                                modestbranding: 1,
                                rel: 0,
                                showinfo: 0,
                                wmode: \'transparent\',
                                playsinline: 1,
                                controls: 0,
                                enablejsapi: 1
                            },
                            events: {
                                \'onReady\': function(event) {
                                    setVideoHeight();
                                    setTimeout(function(){
                                        $(\'#modalEnterWebinaire button\').attr(\'disabled\', false).html(\'' . __('Continuer') . '\');
                                    }, 500);
                                }
                            }
                        });
                    }

                    function startPlayer() {
                        if (diff_sec > 0) {
                            player.seekTo(diff_sec);
                        }
                        delay_start_video = new Date().getTime() - user_date - (diff_sec * 1000);
                        player.playVideo();
                        $(\'#hackYt\').css(\'display\', \'block\');
                    }
                </script>';
            }
        } else if (strpos($webinaire['video'], 'wistia') !== false or strpos($webinaire['video'], 'wi.st') !== false) {
            $videoId = \Tools::getWistiaId($webinaire['video']);
            if ($videoId) {
                $js .= '
                <script src="//fast.wistia.com/assets/external/E-v1.js" async></script>
                <script>
                    var video;
                    window._wq = window._wq || [];
                    _wq.push({ id: "' . $videoId . '", onReady: function(v) {
                        video = v;

                        setVideoHeight();
                        $(\'#modalEnterWebinaire button\').attr(\'disabled\', false).html(\'' . __('Continuer') . '\');

                        video.bind(\'play\', function() {
                            $(\'#hackYt\').css(\'display\', \'block\');
                        });

                        video.bind(\'pause\', function() {
                            $(\'#hackYt\').css(\'display\', \'none\');
                        });
                    }});

                    function startPlayer() {
                        if (diff_sec > 0) {
                            video.time(diff_sec);
                        }
                        delay_start_video = new Date().getTime() - user_date - (diff_sec * 1000);
                        video.play();
                    }
                </script>';
            }
        } else if (strpos($webinaire['video'], 'vimeo') !== false) {
            $js .= '
            <script src="https://player.vimeo.com/api/player.js"></script>
            <script>
                var iframe = document.querySelector(\'iframe\');
                var player = new Vimeo.Player(iframe);

                setVideoHeight();
                $(\'#modalEnterWebinaire button\').attr(\'disabled\', false).html(\'' . __('Continuer') . '\');

                player.on(\'play\', function() {
                    $(\'#hackYt\').css(\'display\', \'block\');
                });

                player.on(\'pause\', function() {
                    $(\'#hackYt\').css(\'display\', \'none\');
                });

                function startPlayer() {
                    if (diff_sec > 0) {
                        player.setCurrentTime(diff_sec);
                    }
                    delay_start_video = new Date().getTime() - user_date - (diff_sec * 1000);
                    player.play();
                }
            </script>';
        } else {
            $js .= '
            <script type="text/javascript">
            	var player = new MediaElementPlayer(\'video_conference\', {
                    success: function(mediaElement, domObject) {
            	        $(\'#modalEnterWebinaire button\').attr(\'disabled\', false).html(\'' . __('Continuer') . '\');
            	        setVideoHeight();
                        if (diff_sec > 0) {
                            mediaElement.addEventListener(\'loadeddata\', function() {
                                mediaElement.setCurrentTime(diff_sec);
                            }, false);
                        }
                        mediaElement.addEventListener(\'onplay\', function() {
                            $(\'#hackYt\').css(\'display\', \'block\');
                        }, false);
                    }
                });

                function startPlayer() {
                    player.play();
                    delay_start_video = new Date().getTime() - user_date - (diff_sec * 1000);
                    $(\'#hackYt\').css(\'display\', \'block\');
                }

                $(\'video\').bind(\'contextmenu\', function(e) {
                    return false;
                });
            </script>';
        }

        Assets::addInlineJs($js);
    }
}
