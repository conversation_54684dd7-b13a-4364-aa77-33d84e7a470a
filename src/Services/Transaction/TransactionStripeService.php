<?php

namespace Learnybox\Services\Transaction;

use Doctrine\ORM\EntityManager;
use Eden_Template;
use Exception;
use Learnybox\Entity\Stripe\Abonnement\StripeAbonnement;
use Learnybox\Entity\Stripe\Account\StripeAccount;
use Learnybox\Entity\Stripe\Charge\StripeCharge;
use Learnybox\Entity\Stripe\Intent\StripeIntent;
use Learnybox\Entity\Transaction\Transaction;
use Learnybox\Factories\Abonnement\AbonnementStripeFactory;
use Learnybox\Helpers\Assets;
use Learnybox\Helpers\RouterHelper;
use Learnybox\Services\Abonnement\AbonnementStripeService;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Services\Logger\LoggerService;
use Learnybox\Services\Stripe\StripeAccountsService;
use Learnybox\Services\Stripe\StripeIntentService;
use Learnybox\Services\Stripe\StripeService;
use Learnybox\Services\Transaction\Form\TransactionFormStripeService;
use Monolog\Logger;
use Tools;
use Stripe;

class TransactionStripeService extends AbstractTransactionService
{
    private StripeIntentService $stripeIntentService;

    /**
     * TransactionStripeService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $this->em->getRepository(StripeCharge::class);
        $this->abonnementRepository = $this->em->getRepository(StripeAbonnement::class);
        $this->paymentType = 'stripe';
        $this->paymentName = 'Stripe';

        parent::__construct($em);

        $this->transactionFormService = $this->container->get(TransactionFormStripeService::class);
        $this->abonnementFactory = $this->container->get(AbonnementStripeFactory::class);
        $this->stripeIntentService = $this->container->get(StripeIntentService::class);
    }

    public function charge()
    {
        $log = $this->transactionLogService->startLog('Stripe', __FUNCTION__);

        $custom = filter_input(INPUT_POST, 'custom', FILTER_SANITIZE_SPECIAL_CHARS);
        $custom = urldecode($custom);
        $prenom = filter_input(INPUT_POST, 'fname', FILTER_SANITIZE_SPECIAL_CHARS);
        $nom = filter_input(INPUT_POST, 'lname', FILTER_SANITIZE_SPECIAL_CHARS);
        $email = filter_input(INPUT_POST, 'email', FILTER_VALIDATE_EMAIL);

        $stripeToken = '';
        if (isset($_POST['stripeToken'])) {
            $stripeToken = filter_input(INPUT_POST, 'stripeToken', FILTER_SANITIZE_SPECIAL_CHARS);
        }

        //champ custom
        $datas = $this->parseCustom($custom);
        $log .= "Datas from custom : " . json_encode($datas) . "\n";
        extract($datas);
        $id_cart = $datas['id_cart'];
        $id_client = $datas['id_client'];
        $id_customer = $datas['id_customer'];
        $ip = $datas['ip'];

        //récupération des informations du paiement original
        $infos = $this->getPaymentInfos();
        if (!$infos) {
            return ['valid' => false, 'message' => __('Paiement inconnu')];
        }

        $type = $infos['type'];
        $log .= "type : $type\n";

        $checkout = $infos['checkout'];
        $amount = round($infos['amount'] * 100);
        $currency = $checkout['devise'];
        $produit = $infos['product_name'];

        if (isset($_POST['provenance'])) {
            $checkout['provenance'] = filter_input(INPUT_POST, 'provenance', FILTER_SANITIZE_SPECIAL_CHARS);
        }

        $stripeClient = StripeService::getStripeClientFromCheckout($checkout);
        if (!$stripeClient) {
            return ['valid' => false, 'message' => __('Ce moyen de paiement n\'est plus disponible.')];
        }

        $stripeAccount = StripeAccountsService::getStripeAccountFromCheckout($checkout);

        $stripeCreateCustomer = true;
        $stripeMakeCharge = true;
        $stripeIdCustomer = '';
        $idStripeCharge = '';
        $paymentMethodId = '';
        $last4 = '';

        $paymentIntentId = (isset($_POST['payment_intent_id']) ? filter_input(INPUT_POST, 'payment_intent_id', FILTER_UNSAFE_RAW) : null);
        $setupIntentId = (isset($_POST['setup_intent_id']) ? filter_input(INPUT_POST, 'setup_intent_id', FILTER_UNSAFE_RAW) : null);

        if (isset($_POST['stripe_use_token']) and isset($_SESSION['stripe_id_customer'])) {
            $customer = $this->repository->findOneBy(['idCustomer' => $_SESSION['stripe_id_customer'], 'client' => $_SESSION['id_client']], ['idCharge' => 'DESC']);
            if (!$customer) {
                return ['valid' => false, 'message' => __('Ce moyen de paiement n\'est plus disponible.')];
            }

            $stripeCreateCustomer = false;
            $stripeIdCustomer = $_SESSION['stripe_id_customer'];
            $stripeToken = $customer->getToken();
            $email = $customer->getEmail();
            $nom = $customer->getNom();
            $prenom = $customer->getPrenom();
            $last4 = $customer->getLast4();

            if ($customer->getPaymentMethod()) {
                $paymentMethodId = $customer->getPaymentMethod();
            }
        }

        if (!$nom or !$prenom) {
            return ['valid' => false, 'message' => __('Veuillez entrer votre nom et votre prénom.')];
        }
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return ['valid' => false, 'message' => __('Veuillez indiquer une adresse email valide.')];
        }

        // Si on a un payment intent, vérifier son statut
        if ($paymentIntentId or $setupIntentId) {
            try {
                if ($paymentIntentId) {
                    $log .= "paymentIntentId received : $paymentIntentId\n";
                    $stripeIntent = $this->stripeIntentService->getStripeIntent($paymentIntentId);
                    $intent = StripeService::retrievePaymentIntent($paymentIntentId);
                } else {
                    $log .= "setupIntentId received : $setupIntentId\n";
                    $stripeIntent = $this->stripeIntentService->getStripeIntent($setupIntentId);
                    $intent = StripeService::retrieveSetupIntent($setupIntentId);
                }
            } catch (\Exception $e) {
                LoggerService::log(Logger::ERROR, 'Error retrieve intent : ' . $e->getMessage());
                return ['valid' => false, 'message' => $e->getMessage()];
            }

            if ($intent) {
                $log .= "Intent found : " . $intent->id . "\n";
                $log .= "Status : " . $intent->status . "\n";
            } else {
                $log .= "Intent not found\n";
            }

            if ($stripeIntent) {
                $log .= "Stripe Intent found in database : " . $stripeIntent->getId() . "\n";
                $log .= "Transaction id : " . $stripeIntent->getIdTrans() . "\n";
                $log .= "Status : " . $stripeIntent->getStatus() . "\n";
            } else {
                $log .= "Stripe Intent not found in database\n";
            }

            if ($stripeIntent && $stripeIntent->getIdTrans()) {
                $log .= "search transaction " . $stripeIntent->getIdTrans() . "\n";
                $transaction = $this->transactionClass->getTransactionById($stripeIntent->getIdTrans());
                if ($transaction) {
                    $log .= "Transaction found : " . $transaction['id_trans'] . "\n";
                    $log .= "Transaction valid : " . $transaction['valid'] . "\n";
                }
                if ($transaction and $transaction['valid']) {
                    return ['valid' => false, 'message' => __('Cette transaction a déjà été validée.')];
                }
            }

            if ($intent->status === 'requires_payment_method' && isset($_POST['stripe_use_token']) && $paymentMethodId && !isset($_POST['after_3ds'])) {
                //confirm intent with paymentMethod
                try {
                    $intent = $intent->confirm([
                        'payment_method' => $paymentMethodId,
                        'return_url' => RouterHelper::generate('site_stripe_payment_confirm'),
                        'mandate_data' => [
                            'customer_acceptance' => [
                                'type' => 'online',
                                'online' => [
                                    'ip_address' => $ip,
                                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
                                ]
                            ]
                        ],
                    ], StripeService::getOptions());
                } catch (\Exception $e) {
                    LoggerService::log(\Monolog\Logger::ERROR, $e->getMessage());
                    return ['valid' => false, 'message' => $e->getMessage()];
                }
            }

            if (!$stripeIntent) {
                if ($paymentIntentId) {
                    LoggerService::log(\Monolog\Logger::ERROR, "Stripe PaymentIntent $paymentIntentId not found");
                } else {
                    LoggerService::log(\Monolog\Logger::ERROR, "Stripe SetupIntent $setupIntentId not found");
                }
            } else {
                $this->stripeIntentService->updateStripeIntent($stripeIntent, $intent);
            }

            if ($intent->status !== 'succeeded') {
                //requires_capture : quand la carte est valide, sans 3DS, et amount = 0
                //requires_action : quand la carte est valide, avant confirmation 3DS
                if ($amount <= 0 and ($intent->status == 'requires_confirmation' or $intent->status == 'requires_capture')) {
                    //ok
                } else {
                    if ($intent->status == 'requires_payment_method') {
                        return ['valid' => false, 'message' => __('Votre moyen de paiement a été refusé, merci d\'en utiliser un autre.')];
                    }

                    if ($setupIntentId) {
                        return $this->generateSetupIntentResponse($intent);
                    }
                    return $this->generateIntentResponse($intent);
                }
            }

            $stripeToken = $intent->id;
            $idStripeCharge = $intent->id;
            $stripeMakeCharge = false;
            $stripeCreateCustomer = false;
            $stripeIdCustomer = $intent->customer;
            $paymentMethodId = $intent->payment_method;

            if (!$stripeIdCustomer) {
                $createCustomer = StripeService::createStripeCustomer($nom, $prenom, $email, '', $id_customer);
                if (!$createCustomer['valid']) {
                    return ['valid' => false, 'message' => $createCustomer['message']];
                }

                $customer = $createCustomer['customer'];
                $stripeIdCustomer = $customer->id;

                //attach payment method
                StripeService::attachPaymentMethodToCustomer($paymentMethodId, $stripeIdCustomer);
            }

            //get card last 4 digits
            try {
                $paymentMethod = StripeService::retrievePaymentMethod($paymentMethodId);
                if ($paymentMethod && isset($paymentMethod->card) and $paymentMethod->card) {
                    $last4 = $paymentMethod->card->last4;
                }
                if ($paymentMethod && $paymentMethod->type === 'bancontact') {
                    $allPaymentMethods = Stripe\Customer::allPaymentMethods($stripeIdCustomer, ['type' => 'sepa_debit']);
                    if ($allPaymentMethods && $allPaymentMethods->data) {
                        $paymentMethodId = $allPaymentMethods->data[0]->id;
                    }
                }
            } catch (Exception $e) {
                LoggerService::log(Logger::ERROR, 'Error retrieve payment method : ' . $e->getMessage());
            }
        } else {
            //1. if we have a payment_method_id --> create payment intent and return result
            if (isset($_POST['payment_method_id'])) {
                $paymentMethodId = filter_input(INPUT_POST, 'payment_method_id', FILTER_SANITIZE_SPECIAL_CHARS);

                $createCustomer = StripeService::createStripeCustomer($nom, $prenom, $email, '', $id_customer);
                if (!$createCustomer['valid']) {
                    return ['valid' => false, 'message' => $createCustomer['message']];
                }

                $customer = $createCustomer['customer'];
                $stripeCreateCustomer = false;

                $hasFreeTrial = false;
                if (!$amount) {
                    //période d'essai à 0€ : on fait un premier paiement de 1€ qu'on annulera ensuite
                    $amount = 100;
                    $hasFreeTrial = true;
                }

                //Create the PaymentIntent
                $intentParams = [
                    'confirmation_method' => 'manual',
                    'save_payment_method' => true,
                    'setup_future_usage' => 'off_session'
                ];

                $cardNetwork = filter_input(INPUT_POST, 'card_network', FILTER_SANITIZE_SPECIAL_CHARS);
                if ($cardNetwork) {
                    $intentParams['payment_method_options'] = [
                        'card' => [
                            'network' => $cardNetwork
                        ]
                    ];
                }

                if ($hasFreeTrial) {
                    $intentParams['capture_method'] = 'manual';
                }
                $log .= "Make PaymentIntent with " . json_encode($intentParams) . "\n";
                $makePaymentIntent = StripeService::makePaymentIntent(
                    $customer->id,
                    $paymentMethodId,
                    $amount,
                    $currency,
                    $produit,
                    $intentParams
                );
                if (!$makePaymentIntent['valid']) {
                    return ['valid' => false, 'message' => $makePaymentIntent['error']];
                }

                $intent = $makePaymentIntent['charge'];

                $log .= "PaymentIntent status : " . $intent->status . "\n";
                //si $intent->status == 'succeeded' on continue mais pas de charge car déjà réalisée par le payment intent
                if ($intent->status != 'succeeded') {
                    //requires_capture : quand la carte est valide, sans 3DS, et amout = 0
                    //requires_action : quand la carte est valide, avant confirmation 3DS
                    if ($hasFreeTrial and ($intent->status == 'requires_confirmation' or $intent->status == 'requires_capture')) {
                        //ok
                    } else {
                        return $this->generateIntentResponse($intent);
                    }
                }

                $stripeToken = $intent->id;
                $idStripeCharge = $intent->id;
                $stripeMakeCharge = false;
                $stripeIdCustomer = $intent->customer;

                //get card last 4 digits
                try {
                    $paymentMethod = StripeService::retrievePaymentMethod($paymentMethodId);
                    if (isset($paymentMethod->card) and $paymentMethod->card) {
                        $last4 = $paymentMethod->card->last4;
                    }
                } catch (Exception $e) {
                    LoggerService::log(Logger::ERROR, 'Error retrieve payment method : ' . $e->getMessage());
                    return ['valid' => false, 'message' => $e->getMessage()];
                }

                if ($hasFreeTrial) {
                    //cancel paymentIntent
                    $log .= "amount = 0 => cancel paymentIntent\n";
                    try {
                        $intent->cancel();
                    } catch (Exception $e) {
                        LoggerService::log(Logger::ERROR, 'PaymentIntent Cancel Exception : ' . $e->getMessage());
                        return ['valid' => false, 'message' => $e->getMessage()];
                    }

                    $amount = 0;
                }
            }

            //2. if we have a payment_intent_id --> confirm intent and return response
            if (isset($_POST['payment_intent_id'])) {
                $paymentIntentId = filter_input(INPUT_POST, 'payment_intent_id', FILTER_SANITIZE_SPECIAL_CHARS);

                try {
                    $intent = StripeService::retrievePaymentIntent($paymentIntentId);
                    if ($intent->status === 'requires_confirmation') {
                        $intent->confirm();
                    }
                } catch (Exception $e) {
                    return ['valid' => false, 'message' => $e->getMessage()];
                }

                $log .= "paymentIntent status : " . $intent->status . "\n";
                //si $intent->status == 'succeeded' on continue mais pas de charge car déjà réalisée par le payment intent
                if ($intent->status != 'succeeded') {
                    //requires_capture : quand la carte est valide, après confirmation 3DS et amount = 0
                    if (!$amount and $intent->status == 'requires_capture') {
                        //ok
                    } else {
                        return $this->generateIntentResponse($intent);
                    }
                }

                $stripeToken = $intent->id;
                $idStripeCharge = $intent->id;
                $stripeMakeCharge = false;
                $stripeCreateCustomer = false;
                $stripeIdCustomer = $intent->customer;
                $paymentMethodId = $intent->payment_method;

                //get card last 4 digits
                try {
                    $paymentMethod = StripeService::retrievePaymentMethod($paymentMethodId);
                    if (isset($paymentMethod->card) and $paymentMethod->card) {
                        $last4 = $paymentMethod->card->last4;
                    }
                } catch (Exception $e) {
                    LoggerService::log(Logger::ERROR, 'Payment method error : ' . $e->getMessage());
                    return ['valid' => false, 'message' => $e->getMessage()];
                }

                if (!$amount) {
                    //cancel paymentIntent
                    $log .= "amount = 0 => cancel paymentIntent\n";
                    try {
                        $intent->cancel();
                    } catch (Exception $e) {
                        LoggerService::log(Logger::ERROR, 'PaymentIntent Exception : ' . $e->getMessage());
                        return ['valid' => false, 'message' => $e->getMessage()];
                    }
                }
            }
        }

        //3. otherwise, let's create a charge
        if (!$stripeToken) {
            $log .= "Aucun stripeToken détecté.EXIT\n";
            $this->transactionLogService->saveLog($log);
            return ['valid' => false, 'message' => __('Une erreur est survenue')];
        }

        $idTransaction = uniqid();
        $idTransaction = str_replace('e', '', $idTransaction);

        //page pour Dao avec liste des conférences
        if (isset($_POST['confs'])) {
            $customDecode = base64_decode($custom);
            $customDecode .= '&confs=' . urldecode($_POST['confs']);
            $custom = base64_encode($customDecode);
        }

        $client = $this->clientRepository->find($id_client);
        $charge = new StripeCharge();
        $charge->setClient($client);
        $charge->setStripeAccount($stripeAccount);
        $charge->setToken($stripeToken);
        $charge->setIdCart($id_cart);
        $charge->setIdTransaction($idTransaction);
        $charge->setIdCustomer($stripeIdCustomer);
        $charge->setIdStripeCharge('');
        $charge->setIdAbonnement(0);
        $charge->setPaymentMethod($paymentMethodId);
        $charge->setSecretKey(StripeService::getSecretKey());
        $charge->setProduit($produit);
        $charge->setAmount($amount);
        $charge->setCurrency($currency);
        $charge->setNom($nom);
        $charge->setPrenom($prenom);
        $charge->setEmail($email);
        $charge->setCustom($custom);
        $charge->setCheckout(json_encode($checkout));
        $charge->setIp($ip);
        $charge->setResult('');
        $charge->setError('');
        $charge->setLast4($last4);
        if (isset($intent)) {
            $charge->setResult(json_encode((array)$intent));
        }
        try {
            $this->persistAndFlush($charge);
        } catch (\Exception $e) {
            LoggerService::log(\Monolog\Logger::ERROR, $e->getMessage());
            return ['valid' => false, 'message' => $e->getMessage()];
        }

        if (isset($stripeIntent) && $stripeIntent) {
            $stripeIntent->setIdTrans($idTransaction);
            try {
                $this->stripeIntentService->persistAndFlush($stripeIntent);
            } catch (\Exception $e) {
                LoggerService::log(\Monolog\Logger::ERROR, $e->getMessage());
            }
        }

        if ($stripeCreateCustomer) {
            //création du customer
            $log .= "Create stripe customer with email $email\n";

            $createCustomer = StripeService::createStripeCustomer($nom, $prenom, $email, $stripeToken, $id_customer);
            if (!$createCustomer['valid']) {
                $message = $createCustomer['message'];
                $log .= "Exception : $message\n";
                $this->transactionLogService->saveLog($log);

                $charge->setError($message);
                $this->persistAndFlush($charge);

                return $this->handleError($message);
            }

            $stripeCustomer = $createCustomer['customer'];
            $log .= 'Customer stripe : ' . json_encode((array)$stripeCustomer) . "\n\n";
            $stripeIdCustomer = $stripeCustomer->id;
        }

        //on met le id_customer en session
        $_SESSION['stripe_id_customer'] = $stripeIdCustomer;

        $hasSubscription = false;
        if (isset($checkout['trial_period']) and $checkout['trial_period']) {
            $hasSubscription = true;
        } elseif (isset($checkout['payment']) and $checkout['payment'] == 'subscription') {
            $hasSubscription = true;
        }
        $log .= "has subscription : $hasSubscription\n";

        if (!$amount and !$hasSubscription) {
            $log .= "pas de montant et pas d'abonnement --> EXIT\n";
            $this->transactionLogService->saveLog($log);
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }

        if ($amount and $stripeMakeCharge) {
            //Process Payment
            $log .= "amount = $amount --> process payment\n";

            if ($paymentMethodId) {
                $log .= "make payment intent for customer $stripeIdCustomer with payment method = $paymentMethodId\n";
                $makeCharge = StripeService::makePaymentIntent(
                    $stripeIdCustomer,
                    $paymentMethodId,
                    $amount,
                    $currency,
                    $produit
                );

                if ($makeCharge['valid']) {
                    $intent = $makeCharge['charge'];
                    if ($intent->status != 'succeeded' and $intent->status == 'requires_action' and $intent->next_action->type == 'use_stripe_sdk') {
                        $log .= "status = " . $intent->status . "\n";
                        $log .= "next action type = " . $intent->next_action->type . "\n";

                        if ((isset($checkout['trial_period']) and $checkout['trial_period']) or (isset($checkout['payment']) and $checkout['payment'] == 'subscription')) {
                            //on ne peut pas valider la charge car elle nécessite la création d'un abonnement
                            $log .= "on ne peut pas valider la charge car elle nécessite la création d'un abonnement\n";
                            $this->transactionLogService->saveLog($log);

                            if (isset($_SESSION['stripe_id_customer'])) {
                                unset($_SESSION['stripe_id_customer']);
                            }
                            $_SESSION['error_message'] = __('Cette carte nécessite une authentification, veuillez réessayer en remplissant le formulaire ci-dessous.');
                            return $this->redirection();
                        }

                        //update charge
                        $charge->setToken($intent->id);
                        $charge->setIdStripeCharge($intent->id);
                        $this->persistAndFlush($charge);

                        $token = Tools::encrypt_decrypt('encrypt', $idTransaction);

                        $link = 'https://' . $_SERVER['HTTP_HOST'] . '/stripe/validate_charge/?token=' . $token;
                        $log .= "redirect to $link\n";
                        $this->transactionLogService->saveLog($log);
                        header('Location: ' . $link);
                        exit();
                    }
                }
            } else {
                $log .= "make charge for customer $stripeIdCustomer\n";
                $makeCharge = StripeService::makeCharge($stripeIdCustomer, $amount, $currency, $produit);
            }
            if (!$makeCharge['valid']) {
                $error = $makeCharge['error'];
                if (is_array($error)) {
                    $error = json_encode($error);
                }

                $log .= "Stripe Error : $error\n";
                $this->transactionLogService->saveLog($log);

                //update charge
                $charge->setError($error);
                $charge->setIdCustomer($stripeIdCustomer);
                $this->persistAndFlush($charge);

                return $this->handleError($error);
            }

            $stripeCharge = $makeCharge['charge'];
            $idStripeCharge = $stripeCharge->id;
            $source = $stripeCharge->source;

            $log .= "\nid Stripe Charge : $idStripeCharge\n\n";
            $log .= "\nRésultat Stripe : " . json_encode((array)$stripeCharge) . "\n\n";
            $log .= "\nRésultat Stripe Source : " . json_encode((array)$source) . "\n\n";

            //get card last 4 digits
            if ($paymentMethodId) {
                try {
                    $paymentMethod = StripeService::retrievePaymentMethod($paymentMethodId);
                    if (isset($paymentMethod->card) and $paymentMethod->card) {
                        $last4 = $paymentMethod->card->last4;
                    }
                } catch (Exception $e) {
                    LoggerService::log(Logger::ERROR, 'Payment method error : ' . $e->getMessage());
                }
            }

            $charge->setIdCustomer($stripeIdCustomer);
            $charge->setIdStripeCharge($idStripeCharge);
            $charge->setLast4($last4);
            $this->persistAndFlush($charge);
        } else {
            //si upsell et montant à 0 on n'a pas de idStripeCharge
            if (!isset($idStripeCharge) or !$idStripeCharge) {
                $idStripeCharge = uniqid();
            }

            //mise à jour de la charge
            $charge->setIdCustomer($stripeIdCustomer);
            $charge->setIdStripeCharge($idStripeCharge);
            $this->persistAndFlush($charge);
        }

        //création de l'abonnement si besoin
        $createAbonnement = $this->abonnementFactory->createAbonnementFromCharge($charge);
        if (!$createAbonnement['valid']) {
            $log .= $createAbonnement['message'];
        }
        $log .= $createAbonnement['log'];
        if ($createAbonnement['id_abonnement']) {
            $log .= "Abonnement " . $createAbonnement['id_abonnement'] . " créé\n";
        }

        //validation de la commande
        $log .= "Validation de la commande\n";

        if (isset($idStripeCharge) and $idStripeCharge) {
            $lockFile = CACHE_PATH . '/cron_locks/stripe_' . $idStripeCharge . '.lock';
        } else {
            $lockFile = CACHE_PATH . '/cron_locks/stripe_' . uniqid() . '.lock';
        }

        if (file_exists($lockFile)) {
            $log .= "Un lock est en place : $lockFile\n";
        } else {
            $log .= "send webhook\n";

            if (!isset($idStripeCharge)) {
                $idStripeCharge = '';
            }
            if (!isset($source)) {
                $source = [];
            }

            $status = 'succeeded';
            if (isset($intent) && isset($intent->status)) {
                $status = $intent->status;
            }

            $sendWebhook = $this->sendWebhook($idStripeCharge, $amount, $currency, $stripeIdCustomer, $status, $source);
            if (!$sendWebhook['valid']) {
                $log .= "Error send webhook : " . $sendWebhook['message'] . "\n";
            } else {
                $log .= "send webhook : OK\n";
            }
        }

        $redirectUrl = Tools::getLink($_SESSION['actual_domaine_id'], 'site', 'checkout', '', 'tx=' . $idTransaction);
        $log .= "Redirection vers $redirectUrl\n";
        $log .= "OK. EXIT\n";
        $this->transactionLogService->saveLog($log);

        return ['valid' => true, 'redirection' => $redirectUrl];
    }

    public function validation(string $body = '')
    {
        $log = $this->transactionLogService->startLog('Stripe', __FUNCTION__);

        $body = $body ?: @file_get_contents('php://input');
        $log .= "BODY :\n " . $body . "\n\n";

        $event = json_decode($body, true);

        if (!isset($event['type'])) {
            $log .= "Aucun type de transaction défini\n";
            $this->transactionLogService->saveLog($log);
            LoggerService::log(Logger::ERROR, 'Aucun type de transaction défini.');
            return ['valid' => false, 'message' => __('Aucun type de transaction défini.')];
        }

        $type = $event['type'];
        if (!in_array($type, ['charge.succeeded', 'charge.failed', 'charge.refunded', 'payment_intent.payment_failed'])) {
            $log .= "Type de transaction non géré\n";
            $this->transactionLogService->saveLog($log);
            return ['valid' => false, 'message' => __('Type de transaction non géré.')];
        }

        $amount = $event['data']['object']['amount'] / 100;
        $currency = $event['data']['object']['currency'];
        $idStripeCharge = $event['data']['object']['id'];
        $customer = $event['data']['object']['customer'];
        $amount = number_format($amount, 2, '.', '');

        if ('null' == $customer) {
            $log .= "Customer : null\n";
            $this->transactionLogService->saveLog($log);
            return ['valid' => false, 'message' => __('Customer : null')];
        }
        if (!$idStripeCharge or 'null' == $idStripeCharge) {
            $log .= "idStripeCharge : null\n";
            $this->transactionLogService->saveLog($log);
            return ['valid' => false, 'message' => __('idStripeCharge : null')];
        }

        if (isset($event['object'])) {
            //webhook sent by stripe
            sleep(5);
        }

        $charge = $this->repository->findOneBy(['idStripeCharge' => $idStripeCharge, 'client' => $_SESSION['id_client']], ['idCharge' => 'DESC']);
        if (!$charge and isset($event['data']['object']['payment_intent'])) {
            $idStripeCharge = $event['data']['object']['payment_intent'];
            $charge = $this->repository->findOneBy(['idStripeCharge' => $idStripeCharge, 'client' => $_SESSION['id_client']], ['idCharge' => 'DESC']);
        }

        if (!$charge && $type == 'charge.succeeded') {
            //find stripe intent
            $log .= "charge not found with $idStripeCharge : search intent\n";
            $stripeIntent = $this->stripeIntentService->getStripeIntent($idStripeCharge);
            if ($stripeIntent) {
                //create charge from stripe Intent
                $log .= "intent found --> create charge with intent " . $stripeIntent->getId() . "\n";
                $charge = $this->createChargeFromIntent($stripeIntent, $event);
                if ($charge && !$stripeIntent->getIdTrans()) {
                    $stripeIntent->setIdTrans($charge->getIdTransaction());
                    $this->stripeIntentService->persistAndFlush($stripeIntent);
                }
            }
        }

        if (!$charge) {
            $log .= "Impossible de trouver une commande pour le customer $customer avec $idStripeCharge pour le client " . $_SESSION['id_client'] . "\n";
            $this->transactionLogService->saveLog($log);
            return ['valid' => false, 'message' => __('Impossible de trouver une commande pour ce client.')];
        }

        $lockFile = CACHE_PATH . '/cron_locks/stripe_' . $idStripeCharge . '.lock';
        if (file_exists($lockFile)) {
            $log .= "Un lock est en place : $lockFile --> EXIT\n";
            $this->transactionLogService->saveLog($log);
            return ['valid' => true];
        }

        //création d'un lock
        exec('touch ' . $lockFile);

        $idTransaction = $charge->getIdTransaction();
        $transaction = $this->transactionClass->getTransactionById($idTransaction, $_SESSION['id_client']);
        if ($transaction) {
            $log .= "La transaction $idTransaction existe déjà\n";
            $this->transactionLogService->saveLog($log);
            return ['valid' => true];
        }

        $email = $charge->getEmail();
        $fname = $charge->getPrenom();
        $lname = $charge->getNom();
        $custom = $charge->getCustom();
        $product = $charge->getProduit();
        $ip = $charge->getIp();

        $log .= "Transaction_id : $idTransaction\n";
        $log .= 'Type de transaction : ' . $type . "\n";

        //etat de la transaction
        $paymentStatus = self::STATUS_ERROR;
        if ('charge.succeeded' == $type) {
            $paymentStatus = self::STATUS_COMPLETED;
        } elseif ('charge.refunded' == $type) {
            $paymentStatus = self::STATUS_REFUNDED;
            if (isset($event['data']['object']['amount_refunded'])) {
                $amount = $event['data']['object']['amount_refunded'] / 100 * -1;
                $amount = number_format($amount, 2, '.', '');
            }
        }

        //sauvegarde du numéro de carte
        if (!$charge->getLast4() and isset($event['data']['object']['source']['last4']) and $event['data']['object']['source']['last4']) {
            $log .= 'Sauvegarde du numéro de carte : ' . $event['data']['object']['source']['last4'] . "\n";
            $charge->setLast4($event['data']['object']['source']['last4']);
            $this->persistAndFlush($charge);
        }

        //save transaction
        $customer = [
            'nom' => $lname,
            'prenom' => $fname,
            'email' => $email,
            'ip' => $ip,
        ];

        $provenance = null;
        $checkout = json_decode($charge->getCheckout(), true);
        if (isset($checkout['provenance'])) {
            $provenance = $checkout['provenance'];
        }

        $saveOrder = $this->saveOrder($idTransaction, $paymentStatus, $this->paymentName, $product, $amount, $currency, $customer, $custom, $provenance);
        $log .= $saveOrder['log'];
        $log .= 'Stripe class finished';
        $this->transactionLogService->saveLog($log);

        @unlink($lockFile);

        if (!$saveOrder['valid']) {
            return ['valid' => false, 'message' => $saveOrder['message']];
        }

        return ['valid' => true];
    }

    /********************************************************/
    /******************** REMBOURSEMENTS ********************/
    /********************************************************/

    /**
     * @param string $idTransaction
     * @param float $amount
     * @return array
     */
    public function refundTransaction($idTransaction, $amount)
    {
        $transaction = $this->transactionsRepository->getOneByTransactionId($idTransaction);
        if (!$transaction) {
            return ['valid' => false, 'message' => __('Cette transaction n\'existe pas.')];
        }

        $idCharge = '';
        $charge = $this->repository->findOneBy(['idTransaction' => $idTransaction, 'client' => $_SESSION['id_client']], ['idCharge' => 'DESC']);
        if ($charge and $charge->getIdStripeCharge()) {
            $idCharge = $charge->getIdStripeCharge();
        }

        if (!$idCharge and $transaction['request']) {
            $request = json_decode($transaction['request'], true);
            if (isset($request['data']['object']['id'])) {
                $idCharge = $request['data']['object']['id'];
            }
        }

        if (!$idCharge) {
            return ['valid' => false, 'message' => __('Impossible de rembourser cette transaction.')];
        }

        $stripeClient = null;
        if ($charge && $charge->getStripeAccount()) {
            $stripeClient = StripeService::getStripeClientFromAccount($charge->getStripeAccount());
        } elseif ($charge && $charge->getCheckout()) {
            $stripeClient = StripeService::getStripeClientFromCheckout(json_decode($charge->getCheckout(), true));
        }
        if (!$stripeClient) {
            $stripeClient = StripeService::getDefaultStripeClient();
        }

        if (!$stripeClient) {
            return ['valid' => false, 'message' => __('Aucune clé API détectée pour Stripe.')];
        }

        return $this->makeRefund($idCharge, round($amount * 100));
    }

    /**
     * @param string $idCharge
     * @param int $amount
     * @return array
     */
    private function makeRefund(string $idCharge, int $amount): array
    {
        if (strpos($idCharge, 'pi_') !== false) {
            //refund payment intent
            try {
                $intent = StripeService::retrievePaymentIntent($idCharge);
                $charge = $intent->charges->data[0];
                Stripe\Refund::create([
                    'charge' => $charge->id,
                    'amount' => $amount
                ], StripeService::getOptions());
            } catch (Stripe\Exception\ApiErrorException $e) {
                return ['valid' => false, 'message' => $e->getMessage()];
            } catch (Exception $e) {
                return ['valid' => false, 'message' => $e->getMessage()];
            }

            return ['valid' => true];
        }

        // DEPRECATED: Cette partie utilise l'API Charges de Stripe qui est dépréciée.
        // La partie ci-dessus qui gère les PaymentIntents est la méthode recommandée.
        // Cette partie est conservée pour la compatibilité avec les anciennes transactions.
        try {
            Stripe\Charge::retrieve($idCharge, StripeService::getOptions());
            Stripe\Refund::create([
                'charge' => $idCharge,
                'amount' => $amount
            ], StripeService::getOptions());
        } catch (Stripe\Exception\ApiErrorException $e) {
            return ['valid' => false, 'message' => $e->getMessage()];
        } catch (Exception $e) {
            return ['valid' => false, 'message' => $e->getMessage()];
        }

        return ['valid' => true];
    }

    /**
     * @param mixed $error
     * @return array
     */
    public function handleError($error): array
    {
        $message = '';
        if (is_array($error)) {
            if (isset($error['message'])) {
                $message = $error['message'];
            } elseif (isset($error['error']['message'])) {
                $message = $error['error']['message'];
            }
        } else {
            $message = $error;
        }

        if (!$message) {
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }

        $stripeCommonErrors = [
            'Your card has insufficient funds',
            'Your card was declined',
            'Try again in a little bit',
            'Your card does not support this type of purchase',
            'Your card number is incorrect',
            'Your card\'s security code is incorrect',
            'You cannot use a Stripe token more than once',
            'No such customer',
            'Your card has expired',
        ];
        $logError = true;
        foreach ($stripeCommonErrors as $stripeCommonError) {
            if (strpos($message, $stripeCommonError) !== false) {
                $logError = false;
                break;
            }
        }

        if ('Your card has insufficient funds.' == $message) {
            return ['valid' => false, 'message' => __('Le plafond de votre carte est dépassé')];
        }
        if ('Amount must be at least 50 cents' == $message) {
            return ['valid' => false, 'message' => __('Le montant d\'une transaction doit être supérieur à 50 centimes')];
        }
        if (false !== strpos($message, 'Amount must convert to at least 50 cents')) {
            return ['valid' => false, 'message' => __('Le montant d\'une transaction doit être supérieur à 50 centimes')];
        }

        return ['valid' => false, 'message' => $message];
    }

    /**
     * @param Stripe\PaymentIntent $intent
     * @return array
     */
    public function generateIntentResponse(Stripe\PaymentIntent $intent): array
    {
        if ($intent->status == 'requires_action' and $intent->next_action->type == 'use_stripe_sdk') {
            //Tell the client to handle the action
            return ['requires_action' => true, 'payment_intent_client_secret' => $intent->client_secret];
        }
        if ($intent->status == 'requires_action' and $intent->next_action->type == 'redirect_to_url') {
            return ['redirection' => $intent->next_action->redirect_to_url->url];
        }

        //Invalid status
        return ['valid' => false, 'message' => __('Invalid PaymentIntent status')];
    }

    /**
     * @param Stripe\SetupIntent $intent
     * @return array
     */
    public function generateSetupIntentResponse(Stripe\SetupIntent $intent): array
    {
        if ($intent->status == 'requires_action' and $intent->next_action->type == 'use_stripe_sdk') {
            //Tell the client to handle the action
            return ['requires_action' => true, 'payment_intent_client_secret' => $intent->client_secret];
        }
        if ($intent->status == 'requires_action' and $intent->next_action->type == 'redirect_to_url') {
            return ['redirection' => $intent->next_action->redirect_to_url->url];
        }

        //Invalid status
        return ['valid' => false, 'message' => __('Invalid PaymentIntent status')];
    }

    /********************************************************/
    /********************* REDIRECTION **********************/
    /********************************************************/

    public function redirection()
    {
        //get element infos
        $infos = $this->getPaymentInfos();
        if (!$infos) {
            $this->throwErrorMessage();
        }

        //display payment form
        $this->displayStripeForm($infos);
        exit();
    }

    /**
     * @param array $infos
     */
    public function displayStripeForm(array $infos): void
    {
        $stripeClient = StripeService::getStripeClientFromCheckout($infos['checkout']);
        if (!$stripeClient) {
            $this->throwErrorMessage();
        }

        $stripeCustomerId = '';
        if (isset($_SESSION['stripe_id_customer']) and $_SESSION['stripe_id_customer']) {
            $stripeCustomerId = $_SESSION['stripe_id_customer'];
        } elseif (isset($infos['email']) && $infos['email']) {
            $stripeCustomer = StripeService::searchStripeCustomer($infos['email']);
            if ($stripeCustomer) {
                $stripeCustomerId = $stripeCustomer->id;
            } else {
                $createStripeCustomer = StripeService::createStripeCustomer($infos['nom'], $infos['prenom'], $infos['email'], '', $infos['id_customer']);
                if ($createStripeCustomer['valid']) {
                    $customer = $createStripeCustomer['customer'];
                    $stripeCustomerId = $customer->id;
                }
            }
        }

        $amount = $infos['amount'];
        if ($amount <= 0) {
            //create setup intent
            try {
                $intent = StripeService::createSetupIntent($infos['product_name'], $stripeCustomerId);
                if ($intent->status == 'canceled') {
                    $this->throwErrorMessage();
                }
                $this->transactionFormService->addHiddenField('setup_intent_id', $intent->id);
                $this->transactionFormService->addHiddenField('client_secret', $intent->client_secret);
            } catch (\Exception $e) {
                LoggerService::log(Logger::ERROR, 'SetupIntent Exception : ' . $e->getMessage());
                $this->throwErrorMessage();
            }
        } else {
            //create payment intent
            $amountPaymentIntent = (int)number_format(($amount * 100), 0, '', '');
            try {
                $intent = StripeService::createPaymentIntent($amountPaymentIntent, $infos['currency'], $infos['product_name'], $stripeCustomerId);
                if ($intent->status == 'canceled') {
                    $this->throwErrorMessage();
                }
                $this->transactionFormService->addHiddenField('payment_intent_id', $intent->id);
                $this->transactionFormService->addHiddenField('client_secret', $intent->client_secret);
            } catch (\Exception $e) {
                LoggerService::log(Logger::ERROR, 'PaymentIntent Exception : ' . $e->getMessage());
                $this->throwErrorMessage();
            }
        }

        $this->stripeIntentService->saveStripeIntent(
            $intent,
            $infos
        );

        $this->transactionFormService->autoSetParams($infos);

        //add stripe public key
        $this->transactionFormService->addHiddenField('stripe_public_key', StripeService::getPublicKey());
        $this->transactionFormService->addHiddenField('provenance', $infos['checkout']['provenance']);

        $stripeAccount = StripeService::getStripeAccount();
        if ($stripeAccount && $stripeAccount->getType() === StripeAccount::TYPE_STANDARD) {
            $this->transactionFormService->addHiddenField('stripe_account_id', $stripeAccount->getStripeAccountId());
        }

        //add upsell
        $this->addUpsell();

        //add css & js
        $this->addRequiredFiles();

        //add ajax route
        Assets::addInlineJs("<script>var ajaxRoute = 'ajax/stripe/charge/';</script>", true);

        $this->transactionFormService->renderPaymentForm();
    }

    private function addUpsell()
    {
        if (!isset($_SESSION['stripe_id_customer'])) {
            return;
        }

        $stripeCharge = $this->repository->findOneBy(['idCustomer' => $_SESSION['stripe_id_customer'], 'client' => $_SESSION['id_client']], ['idCharge' => 'ASC']);
        $stripeCustomer = StripeService::getStripeCustomer($_SESSION['stripe_id_customer']);
        if ($stripeCharge and $stripeCharge->getLast4() and $stripeCustomer) {
            $hiddenFields = ['stripe_use_token' => true];
            $formAction = '/stripe/charge/';
            $last4 = '****' . $stripeCharge->getLast4();
            $this->transactionFormService->setOneClickForm($formAction, $last4, $hiddenFields);
        }
    }

    private function addRequiredFiles()
    {
        $this->transactionFormService->addRequiredFiles();
        Assets::addCssV5(Assets::CSS_TYPE_PAGES, 'payments/stripe.css');
        Assets::addJs('https://js.stripe.com/v3/');
        Assets::addJs('common/payments/stripe.js');
    }

    public function updateCard()
    {
        $checkCardUpdateToken = $this->checkCardUpdateToken();
        if (!$checkCardUpdateToken['valid']) {
            $this->throwErrorMessage($checkCardUpdateToken['message']);
        }

        /** @var StripeAbonnement $abonnement */
        $abonnement = $checkCardUpdateToken['abonnement'];
        $token = $checkCardUpdateToken['token'];

        //get api key
        $stripeClient = null;
        if ($abonnement->getStripeAccount()) {
            $stripeClient = StripeService::getStripeClientFromAccount($abonnement->getStripeAccount());
        }
        if (!$stripeClient && $abonnement->getCheckout()) {
            $checkout = json_decode($abonnement->getCheckout(), true);
            $stripeClient = StripeService::getStripeClientFromCheckout($checkout, $abonnement->getClient()->getIdClient());
        }
        if (!$stripeClient && $abonnement->getSecretKey()) {
            $stripeAccount = $this->container->get(StripeAccountsService::class)->getRepository()->getByData($abonnement->getSecretKey());
            $publicKey = $stripeAccount?->getData()['stripe_public_key'] ?? '';

            $stripeClient = StripeService::getStripeClientFromKeys($publicKey, $abonnement->getSecretKey());
        }
        if (!$stripeClient) {
            $stripeClient = StripeService::getDefaultStripeClient();
        }
        if (!$stripeClient) {
            $this->throwErrorMessage();
        }

        try {
            $intent = StripeService::createSetupIntent(__('Pré-autorisation pour modification CB'), $abonnement->getIdCustomer());
            if ($intent->status == 'canceled') {
                $this->throwErrorMessage();
            }
            $this->transactionFormService->addHiddenField('setup_intent_id', $intent->id);
            $this->transactionFormService->addHiddenField('client_secret', $intent->client_secret);

            $this->stripeIntentService->saveStripeIntent(
                $intent,
                [
                    'nom' => $abonnement->getNom(),
                    'prenom' => $abonnement->getPrenom(),
                    'email' => $abonnement->getEmail(),
                    'amount' => $abonnement->getAmount() / 100,
                    'currency' => $abonnement->getCurrency(),
                    'custom' => $abonnement->getCustom(),
                    'id_cart' => 0,
                ],
                [
                    'update_card' => true,
                    'id_abonnement' => $abonnement->getIdAbonnement(),
                    'token' => $token,
                ]
            );
        } catch (\Exception $e) {
            LoggerService::log(Logger::ERROR, 'PaymentIntent Exception : ' . $e->getMessage());
            $this->throwErrorMessage();
        }

        $title = __('Modification de votre CB');
        $infos = __('Entrez ci-contre le numéro de votre nouvelle carte puis cliquez sur "Valider".');
        $cardFormFooter = __('Notez que notre processeur de paiement valide les informations de votre carte automatiquement et, de ce fait, vous pourrez constater une autorisation temporaire de') . ' ' . Tools::formatAmount(1, DEFAULT_CURRENCY) . ' ' . __('sur votre relevé de compte.') . ' ';
        $cardFormFooter .= __('Cette autorisation n\'est que temporaire, cette somme vous sera remboursée automatiquement dans quelques heures.');

        $customer = [
            'nom' => $abonnement->getNom(),
            'prenom' => $abonnement->getPrenom(),
            'email' => $abonnement->getEmail(),
            'postalCode' => ''
        ];
        $transaction = $this->transactionClass->getTransactionById($abonnement->getIdTransaction(), $abonnement->getClient()->getIdClient());
        if ($transaction and $transaction['id_customer']) {
            $customer = $this->container->get(\Shop_Customers::class)->getCustomerById($transaction['id_customer'], $abonnement->getClient()->getIdClient());
            if ($customer and $customer['datas']) {
                $datas = json_decode($customer['datas'], true);
                if ($datas and isset($datas['cp'])) {
                    $customer['postalCode'] = $datas['cp'];
                }
            }
        }

        $this->transactionFormService->setTitle($title);
        $this->transactionFormService->setPaymentEase($infos);
        $this->transactionFormService->addHiddenField('token', $token);
        $this->transactionFormService->addHiddenField('stripe_public_key', StripeService::getPublicKey());
        $this->transactionFormService->addHiddenField('prenom', $abonnement->getPrenom());
        $this->transactionFormService->addHiddenField('nom', $abonnement->getNom());
        $this->transactionFormService->addHiddenField('fname', $abonnement->getPrenom());
        $this->transactionFormService->addHiddenField('lname', $abonnement->getNom());
        $this->transactionFormService->addHiddenField('email', $abonnement->getEmail());
        $this->transactionFormService->setCustomer($customer);
        $this->transactionFormService->setCardFormFooter($cardFormFooter);

        $stripeAccount = StripeService::getStripeAccount();
        if ($stripeAccount && $stripeAccount->getType() === StripeAccount::TYPE_STANDARD) {
            $this->transactionFormService->addHiddenField('stripe_account_id', $stripeAccount->getStripeAccountId());
        }

        $this->addRequiredFiles();

        //add ajax route
        Assets::addInlineJs("<script>var ajaxRoute = 'ajax/stripe/update_card/';</script>", true);

        $this->transactionFormService->renderUpdateCardForm();
    }

    public function postUpdateCard()
    {
        $checkCardUpdateToken = $this->checkCardUpdateToken();
        if (!$checkCardUpdateToken['valid']) {
            $this->throwErrorMessage($checkCardUpdateToken['message']);
        }

        $abonnement = $checkCardUpdateToken['abonnement'];

        //get api key
        $stripeClient = null;
        if ($abonnement->getStripeAccount()) {
            $stripeClient = StripeService::getStripeClientFromAccount($abonnement->getStripeAccount());
        }
        if (!$stripeClient && $abonnement->getCheckout()) {
            $checkout = json_decode($abonnement->getCheckout(), true);
            $stripeClient = StripeService::getStripeClientFromCheckout($checkout, $abonnement->getClient()->getIdClient());
        }
        if (!$stripeClient) {
            $stripeClient = StripeService::getDefaultStripeClient();
        }
        if (!$stripeClient) {
            $this->throwErrorMessage();
        }

        $setupIntentId = (isset($_POST['setup_intent_id']) ? filter_input(INPUT_POST, 'setup_intent_id', FILTER_UNSAFE_RAW) : null);
        if ($setupIntentId) {
            try {
                $stripeIntent = $this->stripeIntentService->getStripeIntent($setupIntentId);
                $intent = StripeService::retrieveSetupIntent($setupIntentId);
            } catch (\Exception $e) {
                LoggerService::log(Logger::ERROR, 'Error retrieve intent : ' . $e->getMessage());
                return ['valid' => false, 'message' => $e->getMessage()];
            }


            if (!$stripeIntent) {
                LoggerService::log(\Monolog\Logger::ERROR, "Update card : Stripe SetupIntent $setupIntentId not found");
            } else {
                $this->stripeIntentService->updateStripeIntent($stripeIntent, $intent);
            }

            if ($intent->status !== 'succeeded') {
                //requires_capture : quand la carte est valide, sans 3DS, et amount = 0
                //requires_action : quand la carte est valide, avant confirmation 3DS
                if ($intent->status == 'requires_confirmation' or $intent->status == 'requires_capture') {
                    //ok
                } else {
                    if ($intent->status == 'requires_payment_method') {
                        return ['valid' => false, 'message' => __('Votre moyen de paiement a été refusé, merci d\'en utiliser un autre.')];
                    }

                    return $this->generateSetupIntentResponse($intent);
                }
            }
        } else {
            //1. if we have a payment_method_id --> create payment intent and return result
            if (isset($_POST['payment_method_id'])) {
                $paymentMethodId = filter_input(INPUT_POST, 'payment_method_id', FILTER_SANITIZE_SPECIAL_CHARS);
                $amount = 100;

                $intentParams = [
                    'confirmation_method' => 'manual',
                    'save_payment_method' => true,
                    'confirm' => false,
                    'setup_future_usage' => 'off_session',
                ];

                $makeIntent = StripeService::makePaymentIntent(
                    $abonnement->getIdCustomer(),
                    $paymentMethodId,
                    $amount,
                    DEFAULT_CURRENCY,
                    __('Pré-autorisation pour modification CB'),
                    $intentParams
                );
                if (!$makeIntent['valid']) {
                    return ['valid' => false, 'message' => $makeIntent['error']];
                }

                $intent = $makeIntent['charge'];
            }

            //2. if we have a payment_intent_id --> confirm intent and return response
            if (isset($_POST['payment_intent_id'])) {
                $paymentIntentId = filter_input(INPUT_POST, 'payment_intent_id', FILTER_SANITIZE_SPECIAL_CHARS);
                try {
                    $intent = StripeService::retrievePaymentIntent($paymentIntentId);
                    if ($intent->status === 'requires_confirmation') {
                        $intent->confirm();
                    }

                    //remboursement des 1€
                    if ($intent->status == 'succeeded') {
                        try {
                            $idCharge = $intent->charges->data[0]->id;
                            Stripe\Refund::create([
                                'charge' => $idCharge
                            ], StripeService::getOptions());
                        } catch (Exception $e) {
                            LoggerService::log(Logger::ERROR, 'PaymentIntent Exception : ' . $e->getMessage());
                            return ['valid' => false, 'message' => $e->getMessage()];
                        }
                    }
                } catch (Exception $e) {
                    LoggerService::log(Logger::ERROR, 'PaymentIntent Exception : ' . $e->getMessage());
                    return ['valid' => false, 'message' => $e->getMessage()];
                }
            }
        }

        if (!isset($intent)) {
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }

        //intent->status doit être 'succeeded' ou 'requires_confirmation'
        if ($intent->status != 'succeeded' and $intent->status != 'requires_confirmation') {
            if ($intent instanceof Stripe\SetupIntent) {
                return $this->generateSetupIntentResponse($intent);
            }
            return $this->generateIntentResponse($intent);
        }

        if ($intent->status == 'requires_confirmation') {
            //a paymentIntent has been created and is in requires_confirmation state, so we can cancel it
            try {
                $intent->cancel(null, StripeService::getOptions());
            } catch (Exception $e) {
                LoggerService::log(Logger::ERROR, 'PaymentIntent Exception : ' . $e->getMessage());
                return ['valid' => false, 'message' => $e->getMessage()];
            }
        }

        $paymentMethodId = $intent->payment_method;

        //update preferred card network
        if (isset($_POST['card_network']) && $_POST['card_network'] !== '') {
            $cardNetwork = filter_input(INPUT_POST, 'card_network', FILTER_SANITIZE_SPECIAL_CHARS);
            if ($cardNetwork) {
                try {
                    Stripe\PaymentMethod::update($paymentMethodId, [
                        'card' => [
                            'networks' => [
                                'preferred' => $cardNetwork
                            ],
                        ]
                    ], StripeService::getOptions());
                } catch (Exception $e) {
                    LoggerService::log(Logger::ERROR, 'Payment method error : ' . $e->getMessage());
                    return ['valid' => false, 'message' => __('Une erreur est survenue')];
                }
            }
        }

        //get card last 4 digits
        $last4 = '';
        try {
            $paymentMethod = StripeService::retrievePaymentMethod($paymentMethodId);
            if ($paymentMethod && isset($paymentMethod->card) and $paymentMethod->card) {
                $last4 = $paymentMethod->card->last4;
            }
            if ($paymentMethod && $paymentMethod->type === 'bancontact') {
                $allPaymentMethods = Stripe\Customer::allPaymentMethods($intent->customer, ['type' => 'sepa_debit'], StripeService::getOptions());
                if ($allPaymentMethods && $allPaymentMethods->data) {
                    $paymentMethodId = $allPaymentMethods->data[0]->id;
                }
            }
        } catch (Exception $e) {
            LoggerService::log(Logger::ERROR, 'Error retrieve payment method : ' . $e->getMessage());
        }


        //mise à jour de l'abonnement
        $abonnement->setPaymentMethod($paymentMethodId);
        $this->persistAndFlush($abonnement);

        //mise à jour de la charge correspondante
        $charge = $this->repository->findOneBy(['idTransaction' => $abonnement->getIdTransaction(), 'client' => $abonnement->getClient()]);
        if ($charge) {
            $charge->setLast4($last4);
            $this->persistAndFlush($charge);
        }

        //si client = master : mettre à jour tous les abonnements du client
        if (!$abonnement->getClient()->getIdClient() and $abonnement->getIdCustomer()) {
            $abonnements = $this->abonnementRepository->findBy(['idCustomer' => $abonnement->getIdCustomer(), 'client' => '0']);
            if ($abonnements) {
                foreach ($abonnements as $abonnement) {
                    $abonnement->setPaymentMethod($paymentMethodId);
                    $this->persistAndFlush($abonnement);
                }
            }
        }

        $_SESSION['success-message'] = __('Vos informations de paiement ont été mises à jour avec succès');

        if (!$abonnement->getClient()->getIdClient()) {
            $redirection = Tools::makeLink('app', 'abonnement');
        } else {
            $redirection = Tools::makeLink('formation', 'profil', 'abonnement');
        }

        return ['valid' => true, 'redirection' => $redirection];
    }

    public function validateCharge()
    {
        $token = '';
        if (isset($_GET['token'])) {
            $token = filter_input(INPUT_GET, 'token', FILTER_SANITIZE_SPECIAL_CHARS);
        }

        if (!$token) {
            $this->throwErrorMessage();
        }

        $idTransaction = Tools::encrypt_decrypt('decrypt', $token);
        if (!$idTransaction) {
            $this->throwErrorMessage();
        }

        $charge = $this->repository->findOneBy(['idTransaction' => $idTransaction, 'client' => $_SESSION['id_client']], ['idCharge' => 'DESC']);
        if (!$charge) {
            $this->throwErrorMessage();
        }

        if (!$charge->getIdStripeCharge()) {
            $this->throwErrorMessage(__('Erreur : impossible de vérifier cette transaction.'));
        }

        //on démarre stripe pour le client 0
        if ($charge->getStripeAccount()) {
            StripeService::getStripeClientFromAccount($charge->getStripeAccount());
        } else {
            StripeService::getStripeClientFromCheckout(json_decode($charge->getCheckout(), true), $charge->getClient()->getIdClient());
        }

        if (str_contains($charge->getIdStripeCharge(), 'pi_')) {
            $paymentIntentId = $charge->getIdStripeCharge();
            $paymentMethodId = $charge->getPaymentMethod();
            $last4 = $charge->getLast4();

            try {
                $paymentIntent = StripeService::retrievePaymentIntent($paymentIntentId);
            } catch (Exception $e) {
                $this->throwErrorMessage(__('Erreur : impossible de vérifier cette transaction. Le PaymentIntent n\'existe plus.'));
            }
        } else {
            try {
                $stripeCharge = Stripe\Charge::retrieve($charge->getIdStripeCharge(), StripeService::getOptions());
            } catch (Exception $e) {
                $this->throwErrorMessage(__('Erreur : impossible de vérifier cette transaction.'));
            }

            if (!isset($stripeCharge->payment_intent) or !$stripeCharge->payment_intent) {
                $this->throwErrorMessage(__('Erreur : cette transaction n\'est pas associée à un PaymentIntent.'));
            }

            $paymentIntentId = $stripeCharge->payment_intent;

            try {
                $paymentIntent = StripeService::retrievePaymentIntent($paymentIntentId);
            } catch (Exception $e) {
                $this->throwErrorMessage($e->getMessage());
            }

            $paymentMethodId = $paymentIntent->last_payment_error->payment_method->id;
            $last4 = $paymentIntent->last_payment_error->payment_method->card->last4;
        }

        $customer = [
            'nom' => $charge->getNom(),
            'prenom' => $charge->getPrenom(),
            'email' => $charge->getEmail(),
            'postalCode' => '',
        ];

        $transaction = $this->transactionClass->getTransactionById($charge->getIdTransaction(), $charge->getClient()->getIdClient());
        if ($transaction) {
            if ($transaction['valid']) {
                $this->throwErrorMessage(__('Cette commande a déjà été validée.'));
            } elseif ($transaction['id_customer']) {
                $customer = $this->container->get(\Shop_Customers::class)->getCustomerById($transaction['id_customer'], $charge->getClient()->getIdClient());
                if ($customer && $customer['datas']) {
                    $datas = json_decode($customer['datas'], true);
                    if ($datas and isset($datas['cp'])) {
                        $customer['postalCode'] = $datas['cp'];
                    }
                }
            }
        }

        try {
            $intent = StripeService::createPaymentIntent($charge->getAmount(), $charge->getCurrency(), $charge->getProduit(), $charge->getIdCustomer());
            if ($intent->status == 'canceled') {
                $this->throwErrorMessage();
            }
            $this->transactionFormService->addHiddenField('payment_intent_id', $intent->id);
            $this->transactionFormService->addHiddenField('client_secret', $intent->client_secret);

            $this->stripeIntentService->saveStripeIntent(
                $intent,
                [
                    'nom' => $charge->getNom(),
                    'prenom' => $charge->getPrenom(),
                    'email' => $charge->getEmail(),
                    'amount' => $charge->getAmount() / 100,
                    'currency' => $charge->getCurrency(),
                    'custom' => $charge->getCustom(),
                    'id_cart' => $charge->getIdCart(),
                ]
            );
        } catch (\Exception $e) {
            LoggerService::log(Logger::ERROR, 'PaymentIntent Exception : ' . $e->getMessage());
            $this->throwErrorMessage();
        }

        $title = __('Validation de votre commande') . '<br>';
        $infos = __('Cliquez sur le bouton ci-contre pour valider la commande du produit');
        $infos .= ' <strong>' . $charge->getProduit() . '</strong> ' . __('d\'un montant de') . ' ' . Tools::formatAmount($charge->getAmount() / 100, $charge->getCurrency());

        $cardFomHeader = Eden_Template::i()->set('paymentMethodId', $paymentMethodId)
            ->set('last4', $last4)
            ->set('token', $token)
            ->parsePhp(VIEWS_PATH . '/common/payments/stripe-validate-charge.php');

        $this->transactionFormService->setTitle($title);
        $this->transactionFormService->setPaymentEase($infos);
        $this->transactionFormService->addHiddenField('token', $token);
        $this->transactionFormService->addHiddenField('stripe_public_key', StripeService::getPublicKey());
        $this->transactionFormService->addHiddenField('prenom', $charge->getPrenom());
        $this->transactionFormService->addHiddenField('nom', $charge->getNom());
        $this->transactionFormService->addHiddenField('email', $charge->getEmail());
        $this->transactionFormService->setCustomer($customer);
        $this->transactionFormService->setCardFormHeader($cardFomHeader);

        $stripeAccount = StripeService::getStripeAccount();
        if ($stripeAccount && $stripeAccount->getType() === StripeAccount::TYPE_STANDARD) {
            $this->transactionFormService->addHiddenField('stripe_account_id', $stripeAccount->getStripeAccountId());
        }

        $this->addRequiredFiles();

        //add ajax route
        Assets::addInlineJs("<script>var ajaxRoute = 'ajax/stripe/validate_charge/';</script>", true);

        $this->transactionFormService->renderUpdateCardForm();
    }

    public function postValidateCharge()
    {
        $token = filter_input(INPUT_POST, 'token', FILTER_SANITIZE_SPECIAL_CHARS);
        if (!$token) {
            return ['valid' => false, 'message' => __('Une erreur est survenue1.')];
        }

        if (!isset($_POST['payment_method_id']) and !isset($_POST['payment_intent_id'])) {
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }

        $idTransaction = Tools::encrypt_decrypt('decrypt', $token);
        if (!$idTransaction) {
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }

        $charge = $this->repository->findOneBy(['idTransaction' => $idTransaction, 'client' => $_SESSION['id_client']], ['idCharge' => 'DESC']);
        if (!$charge) {
            return ['valid' => false, 'message' => __('Une erreur est survenue.')];
        }
        $transaction = $this->em->getRepository(Transaction::class)->findOneBy(['client' => $_SESSION['id_client'], 'idTrans' => $idTransaction]);
        if ($transaction && $transaction->getValid()) {
            return ['valid' => false, 'message' => __('Cette commande a déjà été validée.')];
        }

        //get api key
        $stripeClient = null;
        if ($charge->getStripeAccount()) {
            $stripeClient = StripeService::getStripeClientFromAccount($charge->getStripeAccount());
        }
        if (!$stripeClient && $charge->getCheckout()) {
            $checkout = json_decode($charge->getCheckout(), true);
            $stripeClient = StripeService::getStripeClientFromCheckout($checkout, $charge->getClient()->getIdClient());
        }
        if (!$stripeClient) {
            $stripeClient = StripeService::getDefaultStripeClient();
        }
        if (!$stripeClient) {
            return ['valid' => false, 'message' => __('Impossible de démarrer Stripe, veuillez réessayer ultérieurement.')];
        }


        //1. if we have a payment_method_id --> create payment intent and return result
        if (isset($_POST['payment_method_id'])) {
            $paymentMethodId = filter_input(INPUT_POST, 'payment_method_id', FILTER_SANITIZE_SPECIAL_CHARS);

            //Create the PaymentIntent
            $intentParams = [
                'confirmation_method' => 'manual',
                'save_payment_method' => true,
                'setup_future_usage' => 'off_session',
            ];
            $makePaymentIntent = StripeService::makePaymentIntent(
                $charge->getIdCustomer(),
                $paymentMethodId,
                $charge->getAmount(),
                $charge->getCurrency(),
                $charge->getProduit(),
                $intentParams
            );
            if (!$makePaymentIntent['valid']) {
                return ['valid' => false, 'message' => $makePaymentIntent['error']];
            }

            $intent = $makePaymentIntent['charge'];

            //si $intent->status == 'succeeded' on continue mais pas de charge, elle est déjà réalisée par le payment intent
            if ($intent->status != 'succeeded') {
                return $this->generateIntentResponse($intent);
            }
        }

        //confirm intent and return response
        if (isset($_POST['payment_intent_id'])) {
            $paymentIntentId = filter_input(INPUT_POST, 'payment_intent_id', FILTER_SANITIZE_SPECIAL_CHARS);

            try {
                $intent = StripeService::retrievePaymentIntent($paymentIntentId);
                if ($intent->status === 'requires_confirmation') {
                    $intent->confirm(null, StripeService::getOptions());
                }
            } catch (\Exception $e) {
                return ['valid' => false, 'message' => $e->getMessage()];
            }

            //si $intent->status == 'succeeded' on continue mais pas de charge
            //déjà réalisée par le payment intent
            if ($intent->status != 'succeeded') {
                return $this->generateIntentResponse($intent);
            }
        }

        //get card last 4 digits
        $paymentMethodId = $intent->payment_method;
        $last4 = '';
        try {
            $paymentMethod = StripeService::retrievePaymentMethod($paymentMethodId);
            if (isset($paymentMethod->card) and $paymentMethod->card) {
                $last4 = $paymentMethod->card->last4;
            }
        } catch (\Exception $e) {
            LoggerService::log(Logger::ERROR, 'Payment method error : ' . $e->getMessage());
        }

        //mise à jour de la charge
        $charge->setPaymentMethod($paymentMethodId);
        $charge->setToken($intent->id);
        $charge->setIdStripeCharge($intent->id);
        $charge->setLast4($last4);
        $charge->setError('');
        $this->persistAndFlush($charge);

        //mise à jour de l'abonnement
        if ($charge->getIdAbonnement()) {
            $abonnement = $this->abonnementRepository->find($charge->getIdAbonnement());
            if ($abonnement) {
                $abonnement->setPaymentMethod($paymentMethodId);
                $this->persistAndFlush($abonnement);

                $update = $this->container->get(AbonnementStripeService::class)->updateAbonnement($charge->getIdAbonnement(), $charge->getClient()->getIdClient());
                if (!$update['valid']) {
                    return $update;
                }
            }
        }

        //webhook validate order
        $this->sendWebhook($intent->id, $charge->getAmount(), $charge->getCurrency(), $charge->getIdCustomer());

        if ($charge->getCustom()) {
            $customDatas = $this->parseCustom($charge->getCustom());
            if (isset($customDatas['valid_url']) and $customDatas['valid_url']) {
                $redirectUrl = Tools::getLink($_SESSION['actual_domaine_id'], 'site', 'checkout', '', 'tx=' . $idTransaction);
                return ['valid' => true, 'redirect' => $redirectUrl];
            }
        }

        return ['valid' => true, 'message' => __('Le paiement a bien été réalisé avec succès.')];
    }

    /**
     * @param string $idStripeCharge
     * @param int $amount
     * @param string $currency
     * @param string $stripeIdCustomer
     * @param array $source
     * @param string $status
     * @param string $clientUniqid
     * @return array
     */
    public function sendWebhook($idStripeCharge, $amount, $currency, $stripeIdCustomer, $status = 'succeeded', $source = [], $clientUniqid = ''): array
    {
        $evt = 'evt_' . uniqid();
        if ($idStripeCharge) {
            $evt = 'evt_' . substr($idStripeCharge, 3);
        }

        if (!$clientUniqid and isset($_SESSION['client']['uniqid'])) {
            $clientUniqid = $_SESSION['client']['uniqid'];
        }

        $webhookUrl = $this->getWebhookUrl('validation', $clientUniqid);

        $fields = [
            'id' => $evt,
            'created' => time(),
            'livemode' => true,
            'type' => 'charge.' . $status,
            'data' => [
                'object' => [
                    'id' => $idStripeCharge,
                    'object' => 'charge',
                    'created' => time(),
                    'livemode' => true,
                    'paid' => ($status == 'succeeded' ? true : false),
                    'status' => $status,
                    'amount' => $amount,
                    'amount_refunded' => 0,
                    'currency' => $currency,
                    'refunded' => true,
                    'customer' => $stripeIdCustomer,
                    'source' => [],
                ],
            ],
        ];

        if (isset($source) and $source) {
            if (isset($source->id)) {
                $fields['data']['object']['source']['id'] = $source->id;
            }
            if (isset($source->customer)) {
                $fields['data']['object']['source']['customer'] = $source->customer;
            }
            if (isset($source->last4)) {
                $fields['data']['object']['source']['last4'] = $source->last4;
            }
        }

        $fieldsString = json_encode($fields);

        return $this->sendWebhookCurl($webhookUrl, $fields, $fieldsString);
    }

    public function createChargeFromIntent(StripeIntent $stripeIntent, array $stripeEvent): ?StripeCharge
    {
        $stripeIdCustomer = $stripeEvent['data']['object']['customer'];
        $paymentMethodId = $stripeEvent['data']['object']['payment_method'] ?? '';
        $product = $stripeEvent['data']['object']['description'] ?? '';

        $idTransaction = $stripeIntent->getIdTrans();
        if (!$idTransaction) {
            $idTransaction = uniqid();
            $idTransaction = str_replace('e', '', $idTransaction);
        }

        $paymentInfos = $this->getPaymentInfosFromCustom($stripeIntent->getCustom());
        $checkout = $paymentInfos['checkout'] ?? [];
        $stripeAccount = StripeAccountsService::getStripeAccountFromCheckout($checkout);

        if ($stripeAccount) {
            StripeService::getStripeClientFromAccount($stripeAccount);
        } else {
            StripeService::getStripeClientFromCheckout($checkout, $stripeIntent->getClient()->getIdClient());
        }

        if (!$stripeIdCustomer) {
            $intent = StripeService::retrievePaymentIntent($stripeIntent->getIntentId());
            if ($intent && $intent->customer) {
                $stripeIdCustomer = $intent->customer;
            }
        }

        if (!$stripeIdCustomer && isset($stripeEvent['data']['object']['billing_details']['email']) && $stripeEvent['data']['object']['billing_details']['email']) {
            $email = $stripeEvent['data']['object']['billing_details']['email'];
            $stripeCustomer = StripeService::searchStripeCustomer($email);
            if ($stripeCustomer) {
                $stripeIdCustomer = $stripeCustomer->id;
            }
        }

        $charge = new StripeCharge();
        $charge->setClient($stripeIntent->getClient());
        $charge->setStripeAccount($stripeAccount);
        $charge->setToken($stripeIntent->getIntentId());
        $charge->setIdCart($stripeIntent->getIdCart());
        $charge->setIdTransaction($idTransaction);
        $charge->setIdCustomer($stripeIdCustomer);
        $charge->setIdStripeCharge($stripeIntent->getIntentId());
        $charge->setIdAbonnement(0);
        $charge->setPaymentMethod($paymentMethodId);
        $charge->setSecretKey($checkout['stripe_secret_key'] ?? '');
        $charge->setProduit($product);
        $charge->setAmount($stripeIntent->getAmount());
        $charge->setCurrency($stripeIntent->getCurrency());
        $charge->setNom($stripeIntent->getLastName());
        $charge->setPrenom($stripeIntent->getFirstName());
        $charge->setEmail($stripeIntent->getEmail());
        $charge->setCustom($stripeIntent->getCustom());
        $charge->setCheckout(json_encode($checkout));
        $charge->setIp('');
        $charge->setResult('');
        $charge->setError('');
        $charge->setLast4('');
        $charge->setResult($stripeIntent->getResult());
        try {
            $this->persistAndFlush($charge);
        } catch (\Exception $e) {
            LoggerService::log(\Monolog\Logger::ERROR, $e->getMessage());
            return null;
        }

        //création de l'abonnement si besoin
        $createAbonnement = $this->abonnementFactory->createAbonnementFromCharge($charge);
        if (!$createAbonnement['valid']) {
            LoggerService::log(\Monolog\Logger::ERROR, $createAbonnement['message']);
        }

        return $charge;
    }

    public function createOrUpdatePaymentIntent(array $cleaned_post): array
    {
        $idFormulaire = $cleaned_post['id_formulaire'] ?? null;
        if (!$idFormulaire) {
            return ['valid' => false, 'message' => __('ID du formulaire manquant')];
        }

        $formulaire = $this->container->get(\Shop_Formulaires::class)->getFormulaireById($idFormulaire);
        if (!$formulaire) {
            return ['valid' => false, 'message' => __('Formulaire non trouvé')];
        }

        $nom = $cleaned_post['nom'] ?? '';
        $prenom = $cleaned_post['prenom'] ?? '';
        $email = filter_var($cleaned_post['email'], FILTER_VALIDATE_EMAIL);
        if (!$email) {
            return ['valid' => false, 'message' => __('Adresse email vide ou invalide')];
        }

        //stripe client
        $stripeClient = null;
        if (isset($cleaned_post['selected_payment']) && $cleaned_post['selected_payment']) {
            $selectedPaymentId = filter_var($cleaned_post['selected_payment'], FILTER_VALIDATE_INT);
            $payment = $this->container->get(\Shop_Paiements::class)->getPaiementById($selectedPaymentId);
            if ($payment && $payment['type'] === 'stripe' && $payment['checkout']) {
                $checkout = json_decode($payment['checkout'], true);
                $stripeClient = StripeService::getStripeClientFromCheckout($checkout);
            }
        }
        if (!$stripeClient) {
            $stripeClient = StripeService::getDefaultStripeClient();
        }
        if (!$stripeClient) {
            return ['valid' => false, 'message' => __('Impossible de démarrer Stripe, veuillez réessayer ultérieurement.')];
        }

        // Calculer le montant total
        $montants = $this->container->get(\Shop_Cart::class)->getFormulaireAmountToPay($formulaire, $cleaned_post);
        if (!$montants) {
            return ['valid' => false, 'message' => __('Erreur lors du calcul du montant')];
        }

        $amount = $montants['amount_to_pay'];
        $devise = $montants['devises'] ? $montants['devises'][0] : 'EUR';
        $amount = (int)number_format(($amount * 100), 0, '', '');

        $stripeCustomerId = null;
        $stripeCustomerEmail = null;
        if (isset($_SESSION['stripe_customer_id_' . $idFormulaire])) {
            $stripeCustomerId = $_SESSION['stripe_customer_id_' . $idFormulaire];
        }
        if (isset($_SESSION['stripe_customer_email'])) {
            $stripeCustomerEmail = $_SESSION['stripe_customer_email'];
            if ($stripeCustomerEmail != $email) {
                $stripeCustomerId = null;
                $stripeCustomerEmail = null;
            }
        }

        if (!$stripeCustomerId) {
            $stripeCustomer = StripeService::searchStripeCustomer($email);
            if ($stripeCustomer) {
                $stripeCustomerId = $stripeCustomer->id;
            } else {
                $customerData = [
                    'adresse' => $cleaned_post['adresse'] ?? '',
                    'ville' => $cleaned_post['ville'] ?? '',
                    'cp' => $cleaned_post['cp'] ?? '',
                    'pays' => $cleaned_post['pays'] ?? '',
                    'telephone' => $cleaned_post['telephone'] ?? '',
                ];
                $createStripeCustomer = StripeService::createStripeCustomer($nom, $prenom, $email, '', 0, $customerData);
                if (!$createStripeCustomer['valid']) {
                    return $createStripeCustomer;
                }
                $customer = $createStripeCustomer['customer'];
                $stripeCustomerId = $customer->id;
            }
        }

        $paymentIntentId = $cleaned_post['payment_intent_id'] ?? null;
        if (!$paymentIntentId) {
            $paymentIntentId = $_SESSION['stripe_payment_intent_' . $idFormulaire] ?? null;
        }
        if ($amount <= 0) {
            $paymentIntentId = $cleaned_post['setup_intent_id'] ?? null;
            if (!$paymentIntentId) {
                $paymentIntentId = $_SESSION['stripe_setup_intent_' . $idFormulaire] ?? null;
            }
        }

        if ($paymentIntentId) {
            try {
                if ($amount <= 0) {
                    $intent = StripeService::retrieveSetupIntent($paymentIntentId);
                } else {
                    $intent = StripeService::retrievePaymentIntent($paymentIntentId);
                }
                if (!$intent) {
                    $paymentIntentId = null;
                } else {
                    if ($intent->status != 'requires_source' && $intent->status != 'requires_payment_method') {
                        $paymentIntentId = null;
                    } elseif ($intent->customer != $stripeCustomerId) {
                        $paymentIntentId = null;
                    } else {
                        if ($amount > 0 && $intent->amount != $amount) {
                            $intent = \Stripe\PaymentIntent::update($paymentIntentId, [
                                'amount' => $amount,
                            ], StripeService::getOptions());
                        }

                        $stripeIntent = $this->stripeIntentService->getStripeIntent($paymentIntentId);
                        if ($stripeIntent) {
                            $this->stripeIntentService->updateStripeIntent($stripeIntent, $intent);
                        } else {
                            $this->stripeIntentService->saveStripeIntent(
                                $intent,
                                [
                                    'id_formulaire' => $idFormulaire,
                                    'nom' => $nom,
                                    'prenom' => $prenom,
                                    'email' => $email,
                                    'amount' => $amount,
                                    'currency' => $devise,
                                ]
                            );
                        }
                    }
                }
            } catch (\Exception $e) {
                $paymentIntentId = null; // Réinitialiser pour en créer un nouveau
            }
        }

        if (!$paymentIntentId) {
            try {
                if ($amount <= 0) {
                    $intent = StripeService::createSetupIntent(
                        $montants['product_name'],
                        $stripeCustomerId
                    );
                } else {
                    $intent = StripeService::createPaymentIntent(
                        $amount,
                        $devise,
                        $montants['product_name'],
                        $stripeCustomerId
                    );
                }

                $paymentIntentId = $intent->id;
                if ($amount <= 0) {
                    $_SESSION['stripe_setup_intent_' . $idFormulaire] = $paymentIntentId;
                } else {
                    $_SESSION['stripe_payment_intent_' . $idFormulaire] = $paymentIntentId;
                }

                $this->stripeIntentService->saveStripeIntent(
                    $intent,
                    [
                        'id_formulaire' => $idFormulaire,
                        'nom' => $nom,
                        'prenom' => $prenom,
                        'email' => $email,
                        'amount' => $amount / 100,
                        'currency' => $devise,
                    ]
                );
            } catch (\Exception $e) {
                if (str_contains($e->getMessage(), 'No such customer')) {
                    $stripeCustomerId = null;
                    unset($_SESSION['stripe_customer_id_' . $idFormulaire]);
                    unset($_SESSION['stripe_customer_email']);
                }
                return ['valid' => false, 'message' => __('Erreur lors de la création du paiement') . ' : ' . $e->getMessage()];
            }
        }

        $setupIntentId = null;
        if ($amount <= 0) {
            $setupIntentId = $paymentIntentId;
            $paymentIntentId = null;
        }

        $_SESSION['stripe_customer_id_' . $idFormulaire] = $stripeCustomerId;
        $_SESSION['stripe_customer_email'] = $email;

        return [
            'valid' => true,
            'payment_intent_id' => $paymentIntentId,
            'setup_intent_id' => $setupIntentId,
            'client_secret' => $intent->client_secret,
            'amount' => $amount
        ];
    }

    public function checkPaymentIntent(array $formulaire, array $cleanedPost): void
    {
        $selectedPaymentId = filter_var($cleanedPost['selected_payment'], FILTER_VALIDATE_INT);
        $payment = $this->container->get(\Shop_Paiements::class)->getPaiementById($selectedPaymentId);
        if (!$payment) {
            return;
        }
        $checkout = json_decode($payment['checkout'], true);
        $stripeClient = StripeService::getStripeClientFromCheckout($checkout);
        if (!$stripeClient) {
            return;
        }

        // Calculer le montant total
        $montants = $this->container->get(\Shop_Cart::class)->getFormulaireAmountToPay($formulaire, $cleanedPost);
        if (!$montants) {
            return;
        }

        $amount = $montants['amount_to_pay'];
        $amount = (int)number_format(($amount * 100), 0, '', '');

        $paymentIntentId = $cleanedPost['payment_intent_id'] ?? null;
        if (!$paymentIntentId) {
            $paymentIntentId = $_SESSION['stripe_payment_intent_' . $formulaire['id_formulaire']] ?? null;
        }
        if (!$paymentIntentId) {
            return;
        }

        try {
            $intent = StripeService::retrievePaymentIntent($paymentIntentId);
            if ($intent->amount != $amount) {
                \Stripe\PaymentIntent::update($paymentIntentId, [
                    'amount' => $amount,
                ], StripeService::getOptions());
            }
        } catch (\Exception $e) {
        }
    }
}
