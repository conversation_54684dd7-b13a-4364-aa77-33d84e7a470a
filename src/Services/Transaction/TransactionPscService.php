<?php

namespace Learnybox\Services\Transaction;

use Doctrine\ORM\EntityManager;
use Learnybox\Services\Logger\LoggerService;
use Monolog\Logger;
use Tools;

class TransactionPscService extends AbstractTransactionService
{
    const PSC_IP_URL = 'https://www.paysite-cash.com/ip_list.txt';

    /**
     * TransactionPscService constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->paymentType = 'psc';
        $this->paymentName = 'PSC';

        parent::__construct($em);
    }

    /********************************************************/
    /****************** WEBHOOK VALIDATION ******************/
    /********************************************************/

    public function validation()
    {
        $log = $this->transactionLogService->startLog('Paysite-Cash', __FUNCTION__);

        if (ENV === ENV_PROD) {
            $ipList = $this->getIpList();
            if (!$ipList) {
                $log .= "Problem connecting to the server : No communications transport available. EXIT\n";
                $this->transactionLogService->saveLog($log);
                LoggerService::log(Logger::ERROR, "PSC : Problem connecting to the server : No communications transport available.");
                return ['valid' => false, 'message' => __("PSC : Problem connecting to the server")];
            }

            $userIp = Tools::get_real_IP();
            $checkIp = $this->checkPscIp($userIp, $ipList);
            if (!$checkIp) {
                $log .= "Restricted access. EXIT\n";
                $this->transactionLogService->saveLog($log);
                LoggerService::log(Logger::ERROR, "PSC : Restricted access.");
                return ['valid' => false, 'message' => __("PSC : Restricted access")];
            }
        }

        $orderStatus = '';
        if (isset($_POST['etat'])) {
            $orderStatus = filter_input(INPUT_POST, 'etat', FILTER_SANITIZE_SPECIAL_CHARS);
        }

        $log .= "orderStatus = $orderStatus\n";
        if (!$orderStatus or $orderStatus == 'end') {
            $log .= "Pas de payment_status (etat) ou payment_status = end\n";
            if (isset($_POST['error_code'])) {
                $log .= 'On a un error_code : ' . $_POST['error_code'] . " --> EXIT\n";
            }
            $this->transactionLogService->saveLog($log);
            if (!$orderStatus) {
                LoggerService::log(Logger::ERROR, $log);
            }
            return ['valid' => false, 'message' => __("PSC : Pas de payment_status")];
        }

        $idTransaction = filter_input(INPUT_POST, 'id_trans', FILTER_SANITIZE_SPECIAL_CHARS);
        $custom = filter_input(INPUT_POST, 'divers', FILTER_SANITIZE_SPECIAL_CHARS);
        $fname = filter_input(INPUT_POST, 'fname', FILTER_SANITIZE_SPECIAL_CHARS);
        $lname = filter_input(INPUT_POST, 'lname', FILTER_SANITIZE_SPECIAL_CHARS);
        $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_SPECIAL_CHARS);
        $product = 'Paysite-Cash';

        $amount = 0;
        if (isset($_POST['montant'])) {
            $amount = filter_input(INPUT_POST, 'montant', FILTER_SANITIZE_SPECIAL_CHARS);
        }

        $currency = DEFAULT_CURRENCY;
        if (isset($_POST['devise']) and $_POST['devise']) {
            $currency = filter_input(INPUT_POST, 'devise', FILTER_SANITIZE_SPECIAL_CHARS);
        }

        $paymentStatus = self::STATUS_ERROR;
        if ($orderStatus == 'ok') {
            $paymentStatus = self::STATUS_COMPLETED;
        } elseif ($orderStatus == 'refund' or $orderStatus == 'refunded') {
            $paymentStatus = self::STATUS_REFUNDED;
            if ($amount < 0) {
                $amount *= -1;
            }
        }

        if ($paymentStatus != self::STATUS_COMPLETED) {
            //verification de l'id_transaction
            $lastChar = substr($idTransaction, strlen($idTransaction) - 1);
            if ('c' == $lastChar or 'r' == $lastChar or 'i' == $lastChar) {
                $idTransaction = substr($idTransaction, 0, -1);
            }
        }

        $log .= "Order status : $orderStatus\n";
        $log .= "Payment status : $paymentStatus\n";
        $log .= "idTransaction : $idTransaction\n";

        $ip = '';
        $provenance = null;
        if ($custom) {
            $parseCustom = $this->parseCustom($custom);
            if (isset($parseCustom['ip'])) {
                $ip = $parseCustom['ip'];
            }
            if (isset($parseCustom['id_cart']) and $parseCustom['id_cart']) {
                $productName = '';
                $cartProducts = $this->container->get(\Shop_CartProduits::class)->getProduitsByIdCart($parseCustom['id_cart']);
                if ($cartProducts) {
                    foreach ($cartProducts as $cartProduct) {
                        $produit = $this->container->get(\Shop_Produits::class)->getProduitById($cartProduct['id_produit']);
                        if (!$produit) {
                            continue;
                        }
                        if (!$produit['active']) {
                            continue;
                        }
                        $productName .= $produit['nom'] . "\n";
                    }
                }
                if ($productName) {
                    $product = $productName;
                }
            } elseif (isset($parseCustom['id_formation']) and $parseCustom['id_formation']) {
                $formation = $this->container->get(\Formation_Formation::class)->getFormationById($parseCustom['id_formation']);
                if ($formation) {
                    $product = $formation['nomformation'];
                }
            }
        }

        $customer = [
            'nom' => $lname,
            'prenom' => $fname,
            'email' => $email,
            'ip' => $ip,
        ];

        $saveOrder = $this->saveOrder($idTransaction, $paymentStatus, $this->paymentName, $product, $amount, $currency, $customer, $custom);
        $log .= $saveOrder['log'];
        $log .= 'Paysite-Cash class finished';
        $this->transactionLogService->saveLog($log);

        if (!$saveOrder['valid']) {
            return ['valid' => false, 'message' => $saveOrder['message']];
        }

        return ['valid' => true];
    }

    /********************************************************/
    /*********************** FUNCTIONS **********************/
    /********************************************************/

    /**
     * @return array|null
     */
    private function getIpList(): ?array
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::PSC_IP_URL);
        curl_setopt($ch, 15, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Billing Confirmation');
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $list = curl_exec($ch);
        if (empty($list)) {
            return null;
        }

        $list = explode('|', $list);
        return $list;
    }

    /**
     * @param string $ip
     * @param array $ipList
     * @return bool
     */
    public function checkPscIp(string $ip, array $ipList): bool
    {
        return in_array($ip, $ipList);
    }
}
