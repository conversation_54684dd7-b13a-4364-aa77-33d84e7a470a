<?php

namespace Learnybox\Services\Integration\Facebook;

use FacebookAds\Object\ServerSide\ActionSource;
use Learnybox\Entity\Facture\Facture;
use Learnybox\Repositories\TransactionsProduitsRepository;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Services\Integration\AbstractIntegrationEventService;

/**
 * Class FacebookEventsService
 * @package Learnybox\Services\Integration\Facebook
 */
class FacebookEventsService extends AbstractIntegrationEventService
{
    protected $events = ['addContact', 'addOrder'];

    private FacebookApiService $facebookApiService;
    private FacebookPixelsService $facebookPixelsService;
    private \DI\Container $container;

    /**
     * FacebookEventsService constructor.
     * @param FacebookApiService $facebookApiService
     * @param FacebookPixelsService $facebookPixelsService
     */
    public function __construct(
        FacebookApiService $facebookApiService,
        FacebookPixelsService $facebookPixelsService
    ) {
        $this->facebookApiService = $facebookApiService;
        $this->facebookPixelsService = $facebookPixelsService;
        $this->container = ContainerBuilderService::getInstance();
    }

    public function addContact(array $contact): array
    {
        $pixelId = $this->getPixelId($contact);
        if (!$pixelId) {
            return ['valid' => true];
        }

        $pixel = $this->facebookApiService->getFacebookPixelsRepository()->getById($pixelId, $_SESSION['id_client']);
        if (!$pixel || !$pixel['access_token']) {
            return ['valid' => true];
        }

        $eventId = uniqid('fb_', true);

        $content = [
            'email' => $contact['email'],
            'source_url' => ($contact['url_origin'] ?? ''),
            'ip' => $contact['ip'] ?? '',
        ];
        if (isset($contact['page_title'])) {
            $content['content_name'] = $contact['page_title'];
        } elseif (isset($contact['nom_formulaire'])) {
            $content['content_name'] = $contact['nom_formulaire'];
        }

        $sendContact = $this->facebookApiService->callFacebookApi($pixelId, FacebookPixelsService::EVENT_LEAD, $eventId, $content, ActionSource::WEBSITE);
        if (!$sendContact['valid']) {
            return ['valid' => false, 'message' => $sendContact['message']];
        }

        return ['valid' => true];
    }

    public function addOrder(array $order): array
    {
        //don't send invalid orders to Facebook
        if (!$order['valid']) {
            return ['valid' => true];
        }
        if ($order['fb_log']) {
            return ['valid' => true];
        }

        $pixelId = $this->getPixelId($order);
        if (!$pixelId) {
            return ['valid' => true];
        }

        $pixel = $this->facebookApiService->getFacebookPixelsRepository()->getById($pixelId, $_SESSION['id_client']);
        if (!$pixel || !$pixel['access_token']) {
            return ['valid' => true];
        }

        $eventId = uniqid('fb_', true);

        $content = [
            'email' => $order['email'],
            'value' => $order['montant'],
            'currency' => $order['devise'],
            'content_type' => 'product',
            'id_transaction' => $order['id_trans'],
            'order_id' => $order['id_trans'],
            'content_name' => $order['descriptor'],
        ];

        //get customer & customer data
        if ($order['id_customer']) {
            $customer = $this->container->get(\Shop_Customers::class)->getCustomerById($order['id_customer']);
            if ($customer && $customer['datas']) {
                $customerData = json_decode($customer['datas'], true);
                if (isset($customerData['fbp']) && $customerData['fbp']) {
                    $content['fbp'] = $customerData['fbp'];
                }
                if (isset($customerData['fbc']) && $customerData['fbc']) {
                    $content['fbc'] = $customerData['fbc'];
                }
            }
        }

        $transactionProduits = $this->container->get(TransactionsProduitsRepository::class)->getProduitsByTransaction($order['id'], $order['id_client']);
        if ($transactionProduits) {
            $content['content_ids'] = [];
            foreach ($transactionProduits as $transactionProduit) {
                if ($transactionProduit['nom'] === Facture::NAME_PAYMENT_FEES or $transactionProduit['nom'] === Facture::NAME_DISCOUNT or $transactionProduit['nom'] === Facture::NAME_TRIAL_PERIOD) {
                    continue;
                }
                $content['content_ids'][] = $transactionProduit['nom'];
            }
        }

        //convert amount to float
        //serialize_precision prevents json_encode to display float numbers with 14th precision
        ini_set('serialize_precision', -1);
        $content['value'] = (float)$content['value'];

        //log
        $this->facebookPixelsService->log($pixelId, $content);

        $sendOrder = $this->facebookApiService->callFacebookApi($pixelId, FacebookPixelsService::EVENT_PURCHASE, $eventId, $content, ActionSource::SYSTEM_GENERATED);
        if (!$sendOrder['valid']) {
            return ['valid' => false, 'message' => $sendOrder['message']];
        }

        return ['valid' => true];
    }

    public function getPixelId(array $data): int
    {
        if (isset($data['cancel_url']) and $data['cancel_url']) {
            if (str_contains($data['cancel_url'], '-')) {
                $explode = explode('-', $data['cancel_url']);
                $page = $this->container->get(\TunnelsPages::class)->getPageById($explode[1]);
            } else {
                $page = $this->container->get(\Pages::class)->adminGetPageById($data['cancel_url']);
            }

            if ($page) {
                $pixel = $this->facebookPixelsService->getFacebookPixelByPage($page);
                if ($pixel['facebook_pixel_id']) {
                    return $pixel['facebook_pixel_id'];
                }
            }
        }

        //default pixel
        $pixel = $this->container->get(\Reglages::class)->appGetParametreByName('facebook_pixel_id');
        if ($pixel and $pixel['value']) {
            return $pixel['value'];
        }

        return 0;
    }
}
