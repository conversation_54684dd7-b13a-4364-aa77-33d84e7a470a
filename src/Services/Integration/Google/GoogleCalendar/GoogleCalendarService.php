<?php

namespace Learnybox\Services\Integration\Google\GoogleCalendar;

/**
 * Class GoogleCalendarService
 * @package Learnybox\Services\Integration\Google\GoogleCalendar
 */
class GoogleCalendarService
{
    /**
     * @param string $text
     * @param \DateTime $dateStart
     * @param \DateTime $dateEnd
     * @param string|null $details
     * @param string|null $location
     * @param string|null $timezone
     * @param array|null $guests
     * @return string
     */
    public function getAddToCalendarLink(string $text, \DateTime $dateStart, \DateTime $dateEnd, string $details = null, string $location = null, string $timezone = null, array $guests = null): string
    {
        $url = 'https://calendar.google.com/calendar/r/eventedit?text=' . urlencode($text) . '&dates=' . $dateStart->format('Ymd\THis') . '/' . $dateEnd->format('Ymd\THis');

        if (null !== $details) {
            $url .= '&details=' . urlencode($details);
        }

        if (null !== $location) {
            $url .= '&location=' . urlencode($details);
        }

        if (null !== $timezone) {
            $url .= '&ctz=' . urlencode($timezone);
        }

        if (null !== $guests) {
            $url .= '&add=' . implode(',', $guests);
        }

        return $url;
    }
}
