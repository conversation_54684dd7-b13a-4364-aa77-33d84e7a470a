<?php

namespace Learnybox\Services\Integration\Shopify;

use Learnybox\Repositories\ConfigRepository;
use Learnybox\Services\Html\AlertHtmlService;
use Learnybox\Services\Tools\ArrayToolsService;
use Learnybox\Services\Transaction\TransactionService;
use PHPShopify;

class ShopifyService
{
    /**
     * @var ConfigRepository
     */
    private $configRepository;

    /**
     * @var TransactionService
     */
    private $transactionService;

    const SHOPIFY_URL = 'https://shopify.com/';
    const REDIRECT_URL = LB_HTTP . '/shopify_connect/';
    const SHOP_EXT = '.myshopify.com';
    const SCOPES = 'read_customers,read_products,write_merchant_managed_fulfillment_orders,write_third_party_fulfillment_orders';

    private $lb_shopify_api_key;
    private $lb_shopify_api_secret;
    private $shopify;
    private $shop;
    private $currency;
    private $cart = [];
    private $billingAddress = [];
    private $shippingAddress = [];
    private $transactions = [];

    private AlertHtmlService $alertHtmlService;

    /**
     * ShopifyService constructor.
     * @param ConfigRepository $configRepository
     * @param TransactionService $transactionService
     * @throws \Exception
     */
    public function __construct(ConfigRepository $configRepository, TransactionService $transactionService, AlertHtmlService $alertHtmlService)
    {
        if (!defined('LB_SHOPIFY_API_KEY') or !LB_SHOPIFY_API_KEY) {
            throw new \Exception(__('API key de Shopify non définie.'));
        }

        if (!defined('LB_SHOPIFY_API_SECRET') or !LB_SHOPIFY_API_SECRET) {
            throw new \Exception(__('API secret de Shopify non définie.'));
        }

        $this->lb_shopify_api_key = LB_SHOPIFY_API_KEY;
        $this->lb_shopify_api_secret = LB_SHOPIFY_API_SECRET;

        $this->configRepository = $configRepository;
        $this->transactionService = $transactionService;
        $this->currency = DEFAULT_CURRENCY;

        $this->alertHtmlService = $alertHtmlService;
    }

    /**
     * getShopFromUrl function.
     *
     * @access public
     * @param string $url
     * @return string
     */
    public function getShopFromUrl(string $url): string
    {
        return str_replace(self::SHOP_EXT, '', $url);
    }

    /**
     * getConfig function.
     *
     * @access private
     * @param string $shop
     * @return array
     */
    private function getConfig(string $shop): array
    {
        $config = [
            'ShopUrl' => $shop . self::SHOP_EXT,
            'ApiKey' => LB_SHOPIFY_API_KEY,
            'SharedSecret' => LB_SHOPIFY_API_SECRET
        ];

        return ['valid' => true, 'config' => $config];
    }

    /**
     * getConfigByIdClient function.
     *
     * @access public
     * @param int $idClient (default:null)
     * @return array
     */
    public function getConfigByIdClient(int $idClient = null): array
    {
        if (null === $idClient) {
            $idClient = $_SESSION['id_client'];
        }

        $shop = $this->configRepository->getByName('shopify_shop', $idClient);
        if (!$shop) {
            return ['valid' => false, 'message' => __('Boutique Shopify introuvable.')];
        }
        $this->shop = $shop['value'];

        $accessToken = $this->configRepository->getByName('shopify_access_token', $idClient);
        if (!$accessToken) {
            return ['valid' => false, 'message' => __('Token Shopify introuvable.')];
        }

        $shop = $this->getShopFromUrl($shop['value']);

        $response = $this->getConfig($shop);
        if (!$response) {
            return ['valid' => false, 'message' => $response['message']];
        }

        $config = $response['config'];
        $config['AccessToken'] = $accessToken['value'];

        return ['valid' => true, 'config' => $config];
    }

    /**
     * requestAccessToken function.
     *
     * @access public
     * @return array
     */
    public function requestAccessToken(): array
    {
        if (isset($_GET['shop'])) {
            $this->shop = filter_input(INPUT_GET, 'shop', FILTER_SANITIZE_SPECIAL_CHARS);
        }

        if (!$this->shop) {
            return ['valid' => false, 'message' => __('Erreur lors de la récupération de la boutique Shopify.')];
        }

        if (!isset($_SESSION['return_idclient'])) {
            return ['valid' => false, 'message' => __('Erreur lors de la récupération de la boutique Shopify.')];
        }

        $idClient = $_SESSION['return_idclient'];

        $shop = $this->getShopFromUrl($this->shop);

        $config = $this->getConfig($shop);
        if (false === $config['valid']) {
            return ['valid' => false, 'message' => __('Configuration Shopify non valide.')];
        }

        PHPShopify\ShopifySDK::config($config['config']);
        try {
            $accessToken = PHPShopify\AuthHelper::getAccessToken();
        } catch (PHPShopify\Exception\SdkException $e) {
            return ['valid' => false, 'message' => $e->getMessage()];
        }

        if (!$accessToken) {
            return ['valid' => false, 'message' => __('Access token non valide.')];
        }

        $saveAccessToken = $this->saveAccessToken($accessToken, $idClient);
        if (false === $saveAccessToken['valid']) {
            return ['valid' => false, 'message' => $saveAccessToken['message']];
        }

        $saveShopName = $this->saveShopName($shop, $idClient);
        if (false === $saveShopName['valid']) {
            return ['valid' => false, 'message' => $saveAccessToken['message']];
        }

        return ['valid' => true, 'shop' => $shop, 'access_token' => $accessToken];
    }

    /**
     * saveAccessToken function.
     *
     * @access public
     * @param string $accessToken
     * @param int $idClient
     * @return array
     */
    public function saveAccessToken(string $accessToken, int $idClient): array
    {
        $save = $this->configRepository->insertByName('shopify_access_token', $accessToken, $idClient);
        if (!$save) {
            return ['valid' => false, 'message' => __('Erreur lors de l\'enregistrement du token.')];
        }

        return ['valid' => true];
    }

    /**
     * saveShopName function.
     *
     * @access public
     * @param string $shop
     * @param int $idClient
     * @return array
     */
    public function saveShopName(string $shop, int $idClient): array
    {
        $save = $this->configRepository->insertByName('shopify_shop', $shop, $idClient);
        if (!$save) {
            return ['valid' => false, 'message' => __('Erreur lors de l\'enregistrement de la boutique.')];
        }

        return ['valid' => true];
    }

    /**
     * disconnect function.
     *
     * @access public
     * @return array
     */
    public function disconnect(): array
    {
        $names = ['shopify_shop', 'shopify_access_token'];

        foreach ($names as $name) {
            $delete = $this->configRepository->delete("name='$name'");
            if (false === $delete) {
                return ['valid' => false, 'message' => __('Erreur lors de la deconnexion de la boutique Shopify.')];
            }
        }

        return ['valid' => true];
    }

    /**
     * init function.
     *
     * @access public
     * @param string $shop (default:null)
     * @param int $idClient (default:null)
     * @return array
     */
    public function init(string $shop = null, int $idClient = null)
    {
        if (null === $shop) {
            $response = $this->getConfigByIdClient($idClient);
        } else {
            $response = $this->getConfig($shop);
        }

        if (false === $response['valid']) {
            return ['valid' => false, 'message' => $response['message']];
        }

        $this->shopify = PHPShopify\ShopifySDK::config($response['config']);

        return ['valid' => true];
    }

    /**
     * setup function.
     *
     * @access public
     * @param string $shop
     * @throws \Exception
     */
    public function setup(string $shop)
    {
        $config = $this->getConfig($shop);
        if (false === $config['valid']) {
            throw new \Exception(__('Configuration Shopify invalide.'));
        }

        PHPShopify\ShopifySDK::config($config['config']);
        try {
            PHPShopify\AuthHelper::createAuthRequest(self::SCOPES, urlencode(self::REDIRECT_URL));
        } catch (PHPShopify\Exception\SdkException $e) {
            throw new \Exception(__('Erreur d\'authentification Shopify'));
        }
    }

    /**
     * getProducts function.
     *
     * @access public
     * @return array
     */
    public function getProducts(): array
    {
        $shopify = $this->init();
        if (false === $shopify['valid']) {
            return ['valid' => false, 'message' => $shopify['message']];
        }

        // limit maximum of results for on request is 250
        $limit = 250;

        try {
            $count = $this->shopify->Product()->count();
            $products = $this->shopify->Product()->get(['limit' => $limit]);

            if ($count > $limit) {
                $pages = ceil($count / $limit) - 1;
                for ($i = 0; $i < $pages; $i++) {
                    $lastElementKey = ArrayToolsService::array_key_last($products);
                    $tempProducts = $this->shopify->Product()->get(['limit' => $limit, 'since_id' => $products[$lastElementKey]['id']]);
                    $products = array_merge($products, $tempProducts);
                }
            }
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Erreur lors de récupération des produits.')];
        }

        return ['valid' => true, 'products' => $products];
    }

    /**
     * getProduct function.
     *
     * @access public
     * @param int $idProduct
     * @return array
     */
    public function getProduct(int $idProduct): array
    {
        $shopify = $this->init();
        if (false === $shopify['valid']) {
            return ['valid' => false, 'message' => $shopify['message']];
        }

        try {
            $product = $this->shopify->Product($idProduct)->get();
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Erreur lors de récupération du produit.')];
        }

        return ['valid' => true, 'data' => $product];
    }

    /**
     * addItem function.
     *
     * @access public
     * @param array $data
     * @return void
     */
    public function addItem(array $data)
    {
        if (isset($data['id_shopify_product'])) {
            $item['variant_id'] = $data['id_shopify_product'];
        }

        if (isset($data['nom'])) {
            $item['title'] = $data['nom'];
        }

        if (isset($data['montant_ttc'])) {
            $item['price'] = $data['montant_ttc'];
        }

        if (isset($data['quantite'])) {
            $item['quantity'] = $data['quantite'];
        } else {
            $item['quantity'] = 1;
        }

        if (isset($data['montant_tva'])) {
            $item['total_taxes'] = $data['montant_tva'];
        } else {
            $item['total_taxes'] = 0;
        }

        if (isset($data['devise'])) {
            $item['currency'] = $data['devise'];
        } else {
            $item['currency'] = $this->currency;
        }

        if (isset($data['id_shopify_product']) && $data['id_shopify_product']) {
            $product = $this->getProduct($data['id_shopify_product']);
            if (true === $product['valid']) {
                $item['title'] = $product['data']['title'];
            }
        }

        $this->cart[] = $item;
    }

    /**
     * setBillingAddress function.
     *
     * @access public
     * @param array $address
     * @return void
     */
    public function setBillingAddress(array $address)
    {
        if (isset($address['nom'])) {
            $this->billingAddress['first_name'] = $address['nom'];
        }

        if (isset($address['prenom'])) {
            $this->billingAddress['last_name'] = $address['prenom'];
        }

        if (isset($address['adresse'])) {
            $this->billingAddress['address1'] = $address['adresse'];
        }

        if (isset($address['ville'])) {
            $this->billingAddress['city'] = $address['ville'];
        }

        if (isset($address['code_postal'])) {
            $this->billingAddress['zip'] = $address['code_postal'];
        }

        if (isset($address['pays'])) {
            $this->billingAddress['country'] = $address['pays'];
        }

        if (isset($address['telephone'])) {
            $this->billingAddress['phone'] = $address['telephone'];
        }

        if (!$this->shippingAddress) {
            $this->shippingAddress = $this->billingAddress;
        }
    }

    /**
     * setShippingAddress function.
     *
     * @access public
     * @param array $address
     * @return void
     */
    public function setShippingAddress(array $address)
    {
        if (isset($address['nom'])) {
            $this->shippingAddress['first_name'] = $address['nom'];
        }

        if (isset($address['prenom'])) {
            $this->shippingAddress['last_name'] = $address['prenom'];
        }

        if (isset($address['adresse'])) {
            $this->shippingAddress['address1'] = $address['adresse'];
        }

        if (isset($address['ville'])) {
            $this->shippingAddress['city'] = $address['ville'];
        }

        if (isset($address['code_postal'])) {
            $this->shippingAddress['zip'] = $address['code_postal'];
        }

        if (isset($address['pays'])) {
            $this->shippingAddress['country'] = $address['pays'];
        }

        if (isset($address['telephone'])) {
            $this->shippingAddress['phone'] = $address['telephone'];
        }
    }

    /**
     * addTransaction function.
     *
     * @access public
     * @param array $data
     * @return void
     */
    public function addTransaction(array $data)
    {
        $transaction = [];

        if (isset($data['amount'])) {
            $transaction['amount'] = $data['amount'];
        }

        if (isset($data['kind'])) {
            $transaction['kind'] = $data['kind'];
        }

        if (isset($data['status'])) {
            $transaction['status'] = $data['status'];
        }

        $this->transactions[] = $transaction;
    }

    /**
     * initializeOrderData function.
     *
     * @access public
     * @param void
     * @return void
     */
    public function initializeOrderData()
    {
        $this->cart = [];
        $this->transactions = [];
        $this->shippingAddress = [];
        $this->billingAddress = [];
    }

    /**
     * sendOrder function.
     *
     * @access public
     * @param string $idTransaction
     * @param string $idTrans
     * @param string $email
     * @param int $idClient
     * @return array
     */
    public function sendOrder(string $idTransaction, string $idTrans, string $email, int $idClient): array
    {
        if (!$this->cart) {
            return ['valid' => false, 'message' => __('Le panier est vide.')];
        }

        if (!$this->billingAddress) {
            return ['valid' => false, 'message' => __('L\'adresse de facturation est vide.')];
        }

        if (!$this->shippingAddress) {
            return ['valid' => false, 'message' => __('L\'adresse de livraison est vide.')];
        }

        $order = [
            'email' => $email,
            'line_items' => $this->cart,
            'billing_address' => $this->billingAddress,
            'shipping_address' => $this->shippingAddress,
            'transactions' => $this->transactions,
            'note' => 'LearnyBox #' . $idTrans
        ];

        $init = $this->init(null, $idClient);
        if (false === $init['valid']) {
            return ['valid' => false, 'message' => $init['message']];
        }

        try {
            $order = $this->shopify->Order->post($order);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => $e->getMessage()];
        }

        $shopifyOrderUrl = $this->getShopifyOrderAdminUrl($this->shop, $order['id']);
        $shopifyOrderLink = '<a href="' . $shopifyOrderUrl . '" target="_blank">' . $shopifyOrderUrl . '</a>';

        $this->transactionService->updateData($idTransaction, 'shopify', $shopifyOrderLink, $idClient);

        return ['valid' => true];
    }

    /**
     * getShopifyOrderAdminUrl function.
     *
     * @access public
     * @param int $shop
     * @param int $idOrder
     * @return string
     */
    public function getShopifyOrderAdminUrl($shop, $idOrder)
    {
        return 'https://' . $shop . '/admin/orders/' . $idOrder;
    }

    /**
     * generateProductsSelectOptions function.
     *
     * @access public
     * @param int $idSelected (default:null)
     * @return string
     */
    public function generateProductsSelectOptions(int $idSelected = null): string
    {
        $products = [];

        $getProducts = $this->getProducts();
        if (true === $getProducts['valid']) {
            $products = $getProducts['products'];
        }

        $select = \Eden_Template::i();
        return $select->set('products', $products)
            ->set('idSelected', $idSelected)
            ->parsePHP(VIEWS_PATH . '/app/shop/select_product_shopify.php');
    }

    public function hmacVerification(string $query, string $hmac): bool
    {
        parse_str($query, $params);
        if (isset($params['hmac'])) {
            unset($params['hmac']);
        }
        ksort($params);

        return hash_equals($hmac, hash_hmac('sha256', http_build_query($params), LB_SHOPIFY_API_SECRET));
    }

    public function getIntegrationAlert(): string
    {
        return $this->alertHtmlService->renderDangerAlert(
            __('L\'intégration Shopify n\'est plus fonctionnelle sur Learnybox. Si vous souhaitez continuer d\'utiliser Shopify avec Learnybox, nous vous invitons à le faire via Zapier.'),
            '',
            '<a target="_blank" href="' . ZAPIER_INVITE_URL . '">' . __('En savoir plus sur Zapier') . '</a>'
        );
    }
}
