<?php

namespace Learnybox\Services\Mangopay\Dispute;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Client\Client;
use Learnybox\Entity\Mangopay\Dispute\MangopayDispute;
use Learnybox\Entity\Mangopay\Dispute\MangopayDisputeTransfertError;
use Learnybox\Entity\Mangopay\Order\MangopayOrder;
use Learnybox\Entity\Mangopay\Transfert\MangopayTransfert;
use Learnybox\Enums\Mangopay\Dispute\MangopayDisputeTransfertErrorStatusEnum;
use Learnybox\Factories\Mangopay\Dispute\MangopayDisputeTransferErrorFactory;
use Learnybox\Services\Logger\LoggerService;
use Learnybox\Services\Mangopay\Api\MangopayApiService;
use Learnybox\Services\Monolog\Logger;
use MangoPay\Dispute;
use MangoPay\MangoPayApi;
use MangoPay\Money;
use MangoPay\SettlementTransfer;
use MangoPay\Transaction;
use MangoPay\Transfer;

class MangopayDisputeTransfertErrorService
{
    private EntityManager $em;
    private MangoPayApi $api;

    public function __construct(EntityManager $em, MangopayApiService $mangopayApiService)
    {
        $this->em = $em;
        $this->api = $mangopayApiService->getApi();
    }

    public function createOne(string $mpDisputeId, int $clientId, MangopayDisputeTransfertErrorStatusEnum $status): void
    {
        $client = $this->em->getRepository(Client::class)->find($clientId);
        $dispute = $this->em
            ->getRepository(MangopayDispute::class)
            ->findOneBy(['mangopayIdDispute' => $mpDisputeId, 'client' => $client]);

        $disputeTransfertError = MangopayDisputeTransferErrorFactory::createOne($dispute, $client, $status);

        $this->em->persist($disputeTransfertError);
        $this->em->flush();
    }

    public function handleDisputeTransfertErrors(): void
    {
        $disputesTransfertError = $this->em
            ->getRepository(MangopayDisputeTransfertError::class)
            ->adminFindRetryable();

        if (empty($disputesTransfertError)) {
            return;
        }

        /** @var MangopayDisputeTransfertError $disputeTransfertError */
        foreach ($disputesTransfertError as $disputeTransfertError) {
            $this->handleDisputeTransfertError($disputeTransfertError);
        }
    }

    public function handleDisputeTransfertError(MangopayDisputeTransfertError $disputeTransfertError)
    {
        /** @var MangopayDispute $dispute */
        $dispute = $disputeTransfertError->getDispute();

        $mpDispute = $this->api->Disputes->Get($dispute->getMangopayIdDispute());

        /** @var MangopayOrder $initialTransaction */
        $initialTransaction = $this->em->getRepository(MangopayOrder::class)->findOneBy([
            'mangopayIdPaiement' => $mpDispute->InitialTransactionId,
            'client' => $dispute->getClient()
        ]);

        /** @var MangopayTransfert $initialTransfert */
        $initialTransfert = $this->em->getRepository(MangopayTransfert::class)->findOneBy([
            'idOrder' => $initialTransaction->getIdOrder(),
            'etat' => 'SUCCEEDED',
            'client' => $dispute->getClient()
        ]);

        /** @var Transaction $transactions */
        $transactions = $this->api->Disputes->GetTransactions($dispute->getMangopayIdDispute());
        foreach ($transactions as $transaction) {
            if ($transaction->Nature !== 'REPUDIATION') {
                continue;
            }

            $repudiationId = $transaction->Id;

            if (
                $disputeTransfertError->getStatus() !== MangopayDisputeTransfertErrorStatusEnum::SETTLEMENT_FAILED
            ) {
                try {
                    $this->createTransfert($dispute, $mpDispute, $initialTransfert);
                } catch (\Exception $e) {
                    $this->update(
                        $disputeTransfertError,
                        $disputeTransfertError->getNbRetries() + 1,
                        MangopayDisputeTransfertErrorStatusEnum::TRANSFER_FAILED
                    );

                    LoggerService::log(Logger::ERROR, $e->getMessage());

                    continue;
                }
            }

            try {
                $this->createSettlementTransfert($transaction, $mpDispute, $repudiationId);
            } catch (\Exception $e) {
                $this->update(
                    $disputeTransfertError,
                    $disputeTransfertError->getNbRetries() + 1,
                    MangopayDisputeTransfertErrorStatusEnum::SETTLEMENT_FAILED
                );

                LoggerService::log(Logger::ERROR, $e->getMessage());

                continue;
            }

            $this->update(
                $disputeTransfertError,
                $disputeTransfertError->getNbRetries(),
                MangopayDisputeTransfertErrorStatusEnum::SUCCESS
            );
        }
    }

    private function update(
        MangopayDisputeTransfertError $disputeTransfertError,
        int $nbRetries,
        MangopayDisputeTransfertErrorStatusEnum $status
    ): void {
        $disputeTransfertError->setNbRetries($nbRetries);
        $disputeTransfertError->setStatus($status);

        $this->em->flush();
    }

    private function createTransfert(
        MangopayDispute $dispute,
        Dispute $mpDispute,
        MangopayTransfert $lbTransfert
    ): void {
        $transferId = $lbTransfert->getMangopayIdTransfert();

        $refund = new \MangoPay\Refund();
        $refund->AuthorId = $lbTransfert->getMangopayIdUser();
        $refund->DebitedFunds = new \MangoPay\Money();
        $refund->DebitedFunds->Amount = $mpDispute->DisputedFunds->Amount;
        $refund->DebitedFunds->Currency = $mpDispute->DisputedFunds->Currency;
        $refund->Fees = new \MangoPay\Money();
        $refund->Fees->Amount = 0;
        $refund->Fees->Currency = $mpDispute->DisputedFunds->Currency;

        try {
            $result = $this->api->Transfers->CreateRefund($transferId, $refund);
            if ($result->Status == 'FAILED') {
                throw new \Exception('Dispute ' . $dispute->getMangopayIdDispute() . ' transfert failed : ' . $result->ResultMessage);
            }
        } catch (\Exception $e) {
            LoggerService::log(\Monolog\Logger::ERROR, $e->getMessage());
            throw $e;
        }

        /*$transfert = new Transfer();
        $transfert->AuthorId = $dispute->getAccount()->getMangopayIdUser();
        $transfert->CreditedUserId = $dispute->getMangopayIdUser();
        $transfert->DebitedFunds = new Money();
        $transfert->DebitedFunds->Currency = $mpDispute->DisputedFunds->Currency;
        $transfert->DebitedFunds->Amount = $mpDispute->DisputedFunds->Amount;
        $transfert->Fees = new Money();
        $transfert->Fees->Currency = $mpDispute->DisputedFunds->Currency;
        $transfert->Fees->Amount = 0;
        $transfert->DebitedWalletId = $lbTransfert->getToWallet();
        $transfert->CreditedWalletId = $lbTransfert->getFromWallet();

        $result = $this->api->Transfers->Create($transfert);
        if ($result->Status == 'FAILED') {
            LoggerService::log(\Monolog\Logger::ERROR, json_encode($transfert));
            throw new \Exception('Dispute ' . $dispute->getMangopayIdDispute() . ' transfert failed : ' . $result->ResultMessage);
        }*/
    }

    private function createSettlementTransfert(
        Transaction $transaction,
        Dispute $mpDispute,
        string $repudiationId
    ): void {
        $settlementTransfer = new SettlementTransfer();
        $settlementTransfer->AuthorId = $transaction->AuthorId;
        $settlementTransfer->DebitedFunds = new Money();
        $settlementTransfer->DebitedFunds->Currency = $mpDispute->DisputedFunds->Currency;
        $settlementTransfer->DebitedFunds->Amount = $mpDispute->DisputedFunds->Amount;
        $settlementTransfer->Fees = new Money();
        $settlementTransfer->Fees->Currency = $mpDispute->DisputedFunds->Currency;
        $settlementTransfer->Fees->Amount = 0;

        $result = $this->api->Disputes->CreateSettlementTransfer($settlementTransfer, $repudiationId);
        if ($result->Status == 'FAILED') {
            throw new \Exception('Dispute ' . $mpDispute->Id . ' settlement transfert failed : ' . $result->ResultMessage);
        }
    }
}
