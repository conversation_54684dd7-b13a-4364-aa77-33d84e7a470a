<?php

namespace Learnybox\Services\FileGenerator;

abstract class AbstractFileGeneratorService
{
    public function generateFile(string $dirPath, string $fileName, string $extension, string $content): void
    {
        if (!is_dir($dirPath) && !mkdir($dirPath, 0777, true)) {
            throw new \Exception('Error while creating directory : ' . $dirPath);
        }

        $file = $dirPath . DIRECTORY_SEPARATOR . $fileName . '.' . $extension;
        if (file_exists($file)) {
            throw new \Exception('File ' . $fileName . ' already exist');
        } elseif (!file_put_contents($file, $content)) {
            throw new \Exception('Error while creating file : ' . $fileName);
        }
    }
}
