<?php

namespace Learnybox\Services\FileGenerator\Themes;

use Learnybox\Entity\Article\Article;
use Learnybox\Entity\Article\Col\ArticleCol;
use Learnybox\Entity\Article\Element\ArticleElement;
use Learnybox\Entity\Article\Line\ArticleLine;
use Learnybox\Entity\Formation\Page\FormationPage;
use Learnybox\Entity\Formation\Page\FormationPageCol;
use Learnybox\Entity\Formation\Page\FormationPageElement;
use Learnybox\Entity\Formation\Page\FormationPageLine;
use Learnybox\Entity\Mail\Col\MailCol;
use Learnybox\Entity\Mail\Element\MailElement;
use Learnybox\Entity\Mail\Line\MailLine;
use Learnybox\Entity\Mail\Mail;
use Learnybox\Entity\Page\Col\PageCol;
use Learnybox\Entity\Page\Element\PageElement;
use Learnybox\Entity\Page\Line\PageLine;
use Learnybox\Entity\Page\Page;
use Learnybox\Entity\Popup\Col\PopupCol;
use Learnybox\Entity\Popup\Element\PopupElement;
use Learnybox\Entity\Popup\Line\PopupLine;
use Learnybox\Entity\Popup\Popup;
use Learnybox\Entity\Tunnel\Page\TunnelPage;
use Learnybox\Entity\Tunnel\Page\TunnelPageCol;
use Learnybox\Entity\Tunnel\Page\TunnelPageElement;
use Learnybox\Entity\Tunnel\Page\TunnelPageLine;
use Learnybox\Services\FileGenerator\AbstractFileGeneratorService;

abstract class AbstractThemeFileGeneratorService extends AbstractFileGeneratorService
{
    public const TUNNEL_PAGE = 'tunnel_page';
    public const SITE_PAGE = 'site_page';
    public const ARTICLE = 'article';
    public const FORMATION_PAGE = 'formation_page';
    public const MAIL = 'mail';
    public const POPUP = 'popup';
    public const MASTERTHEME_PATH = CLASSES_PATH . DIRECTORY_SEPARATOR . 'builder' . DIRECTORY_SEPARATOR . 'themes' . DIRECTORY_SEPARATOR . 'mastertheme';
    public const MASTERTHEME_NAMESPACE = 'Learnybox\\Classes\\builder\\themes\\mastertheme';

    public function getObjectTypeMapping(string $objectType): array
    {
        $mapping = [
            self::TUNNEL_PAGE => [
                'object' => [
                    'class' => TunnelPage::class,
                    'primary' => 'idPage'
                ],
                'line' => [
                    'class' => TunnelPageLine::class,
                    'parentId' => 'tunnelPage'
                ],
                'col' => [
                    'class' => TunnelPageCol::class,
                    'parentId' => 'tunnelPageLine'
                ],
                'element' => [
                    'class' => TunnelPageElement::class,
                    'parentId' => 'idCol',
                    'rootParentId' => 'tunnelPage',
                ],
            ],
            self::SITE_PAGE => [
                'object' => [
                    'class' => Page::class,
                    'primary' => 'idPage'
                ],
                'line' => [
                    'class' => PageLine::class,
                    'parentId' => 'page'
                ],
                'col' => [
                    'class' => PageCol::class,
                    'parentId' => 'pageLine'
                ],
                'element' => [
                    'class' => PageElement::class,
                    'parentId' => 'idCol',
                    'rootParentId' => 'page',
                ],
            ],
            self::ARTICLE => [
                'object' => [
                    'class' => Article::class,
                    'primary' => 'idArticle'
                ],
                'line' => [
                    'class' => ArticleLine::class,
                    'parentId' => 'article'
                ],
                'col' => [
                    'class' => ArticleCol::class,
                    'parentId' => 'articleLine'
                ],
                'element' => [
                    'class' => ArticleElement::class,
                    'parentId' => 'idCol',
                    'rootParentId' => 'article',
                ],
            ],
            self::FORMATION_PAGE => [
                'object' => [
                    'class' => FormationPage::class,
                    'primary' => 'idPage'
                ],
                'line' => [
                    'class' => FormationPageLine::class,
                    'parentId' => 'formationPage'
                ],
                'col' => [
                    'class' => FormationPageCol::class,
                    'parentId' => 'formationPageLine'
                ],
                'element' => [
                    'class' => FormationPageElement::class,
                    'parentId' => 'idCol',
                    'rootParentId' => 'formationPage',
                ],
            ],
            self::MAIL => [
                'object' => [
                    'class' => Mail::class,
                    'primary' => 'idMail'
                ],
                'line' => [
                    'class' => MailLine::class,
                    'parentId' => 'mail'
                ],
                'col' => [
                    'class' => MailCol::class,
                    'parentId' => 'mailLine'
                ],
                'element' => [
                    'class' => MailElement::class,
                    'parentId' => 'idCol',
                    'rootParentId' => 'mail',
                ],
            ],
            self::POPUP => [
                'object' => [
                    'class' => Popup::class,
                    'primary' => 'idPopup'
                ],
                'line' => [
                    'class' => PopupLine::class,
                    'parentId' => 'popup'
                ],
                'col' => [
                    'class' => PopupCol::class,
                    'parentId' => 'popupLine'
                ],
                'element' => [
                    'class' => PopupElement::class,
                    'parentId' => 'idCol',
                    'rootParentId' => 'popup',
                ],
            ],
        ];

        return $mapping[$objectType] ?? [];
    }
}
