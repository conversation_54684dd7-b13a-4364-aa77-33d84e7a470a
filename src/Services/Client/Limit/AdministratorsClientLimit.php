<?php

namespace Learnybox\Services\Client\Limit;

use Learnybox\Repositories\UsersRolesUniversesRepository;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Traits\MigrationObjectFilter;

class AdministratorsClientLimit extends AbstractClientLimit
{
    use MigrationObjectFilter;

    public function getLimit(int $idClient): int
    {
        $client = $this->clientRepository->find($idClient);

        return $client->getMaxAdmins();
    }

    public function getCount(int $idClient, ?string $option = null): int
    {
        $usersRolesUniversesRepository = ContainerBuilderService::getInstance()->get(UsersRolesUniversesRepository::class);
        $count = max(
            $usersRolesUniversesRepository->getCountUsersByUniverse(UNIVERSE_APP_PRIVATE),
            $usersRolesUniversesRepository->getCountUsersByUniverse(UNIVERSE_APP_CONNEXION_CLIENTS)
        );

        if ($option !== null) {
            $count += (int) $option;
        }

        return $count;
    }

    public function getMessage(int $idClient): string
    {
        if ($this->isLimitNone($idClient)) {
            $message = __('Votre abonnement ne vous permet pas de créer d\'administrateurs.');
        } else {
            $message = n__(
                'Votre abonnement ne vous permet pas de créer plus de %d administrateur.',
                'Votre abonnement ne vous permet pas de créer plus de %d administrateurs.',
                $this->getLimit($idClient),
                $this->getLimit($idClient)
            );
        }
        $message .= '<br/>' . __('Vous devez passer à la formule supérieure.');

        return $message;
    }
}
