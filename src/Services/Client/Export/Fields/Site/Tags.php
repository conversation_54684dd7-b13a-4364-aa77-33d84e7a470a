<?php

namespace Learnybox\Services\Client\Export\Fields\Site;

use Learnybox\Entity\Tag\Tag;
use Learnybox\Services\Client\Export\Fields\AbstractFields;

/**
 * Class Tags
 * @package Learnybox\Services\Client\Export\Fields\Site
 */
class Tags extends AbstractFields
{
    public function __construct()
    {
        $this->entity = Tag::class;
        $this->fields = [
            'idTag',
            'nom',
            'date',
        ];
    }
}
