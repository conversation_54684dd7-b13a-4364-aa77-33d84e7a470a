<?php

namespace Learnybox\Services\Client\Export\Fields\Evaluations;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Query;
use Learnybox\Entity\Evaluation\Reponse\EvaluationReponse;
use Learnybox\Services\Client\Export\Fields\AbstractFields;

/**
 * Class Reponses
 * @package Learnybox\Services\Client\Export\Fields\Evaluations
 */
class Reponses extends AbstractFields
{
    /**
     * @var EntityManager
     */
    private $em;

    /**
     * Reponses constructor.
     * @param EntityManager $em
     */
    public function __construct(EntityManager $em)
    {
        $this->em = $em;

        $this->entity = EvaluationReponse::class;
        $this->fields = [
            'idReponse',
            'IDENTITY(evaluation) as idEvaluation',
            'IDENTITY(question) as idQuestion',
            'reponse',
            'explication',
            'correct',
            'position',
            'date',
        ];
    }

    /**
     * @param array $evaluations
     * @return array
     */
    public function getReponses(array $evaluations): array
    {
        $queryBuilder = $this->em->createQueryBuilder();

        foreach ($this->fields as $key => $field) {
            if (strpos($field, 'IDENTITY') !== false) {
                $this->fields[$key] = str_replace('IDENTITY(', 'IDENTITY(e.', $field);
            } else {
                $this->fields[$key] = 'e.' . $field;
            }
        }

        $evaluationsIds = [];
        foreach ($evaluations as $evaluation) {
            array_push($evaluationsIds, $evaluation['idEvaluation']);
        }

        $datas = $queryBuilder->select($this->fields)
            ->from($this->entity, 'e')
            ->where('e.evaluation IN (:evaluations)')
            ->setParameter('evaluations', $evaluationsIds)
            ->getQuery()
            ->getResult(Query::HYDRATE_ARRAY);

        return $datas;
    }
}
