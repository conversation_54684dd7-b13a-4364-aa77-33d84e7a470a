<?php

namespace Learnybox\Services\Client\Export\Fields\Tunnels;

use Learnybox\Entity\Tunnel\Comment\TunnelComment;
use Learnybox\Services\Client\Export\Fields\AbstractFields;

/**
 * Class PagesComments
 * @package Learnybox\Services\Client\Export\Fields\Tunnels
 */
class PagesComments extends AbstractFields
{
    public function __construct()
    {
        $this->entity = TunnelComment::class;
        $this->fields = [
            'idCommentaire',
            'IDENTITY(tunnel) as idTunnel',
            'IDENTITY(page) as idPage',
            'IDENTITY(user) as userId',
            'parent',
            'nom',
            'prenom',
            'email',
            'adresse',
            'telephone',
            'site',
            'commentaire',
            'etat',
            'ip',
            'date',
        ];
    }
}
