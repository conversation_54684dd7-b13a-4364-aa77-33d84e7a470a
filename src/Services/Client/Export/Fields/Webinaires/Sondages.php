<?php

namespace Learnybox\Services\Client\Export\Fields\Webinaires;

use Learnybox\Entity\Webinaire\Sondage\WebinaireSondage;
use Learnybox\Services\Client\Export\Fields\AbstractFields;

/**
 * Class Sondages
 * @package Learnybox\Services\Client\Export\Fields\Webinaires
 */
class Sondages extends AbstractFields
{
    public function __construct()
    {
        $this->entity = WebinaireSondage::class;
        $this->fields = [
            'idSondage',
            'IDENTITY(webinaire) as idWebinaire',
            'type',
            'question',
            'etat',
            'heurePublication',
            'heurePublicationFin',
            'date',
        ];
    }
}
