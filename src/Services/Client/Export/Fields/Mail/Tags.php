<?php

namespace Learnybox\Services\Client\Export\Fields\Mail;

use Learnybox\Entity\Mail\Tag\MailTag;
use Learnybox\Services\Client\Export\Fields\AbstractFields;

/**
 * Class Tags
 * @package Learnybox\Services\Client\Export\Fields\Mail
 */
class Tags extends AbstractFields
{
    public function __construct()
    {
        $this->entity = MailTag::class;
        $this->fields = [
            'idTag',
            'nom',
            'description',
            'date',
            'dateModification',
        ];
    }
}
