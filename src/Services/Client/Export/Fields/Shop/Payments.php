<?php

namespace Learnybox\Services\Client\Export\Fields\Shop;

use Learnybox\Entity\Shop\Paiement\ShopPaiement;
use Learnybox\Services\Client\Export\Fields\AbstractFields;

/**
 * Class Payments
 * @package Learnybox\Services\Client\Export\Fields\Shop
 */
class Payments extends AbstractFields
{
    public function __construct()
    {
        $this->entity = ShopPaiement::class;
        $this->fields = [
            'idPaiement',
            'IDENTITY(formulaire) as idFormulaire',
            'type',
            'position',
            'active',
            'byDefault',
            'date',
            'dateModification',
        ];
    }
}
