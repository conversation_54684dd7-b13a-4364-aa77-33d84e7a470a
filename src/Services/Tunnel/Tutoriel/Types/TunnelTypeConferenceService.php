<?php

namespace Learnybox\Services\Tunnel\Tutoriel\Types;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Mail\Sequence\MailSequenceThemeMail;
use Learnybox\Entity\Tunnel\Page\TunnelPageElement;
use Learnybox\Enums\Conference\ConferenceTypeEnum;
use Learnybox\Enums\Themes\TemplateEnum;
use Learnybox\Helpers\TextVarHelper;
use Learnybox\Services\Conference\ConferencesService;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Services\Integration\IntegrationsEventsService;
use Learnybox\Services\Tunnel\Tutoriel\TunnelTutorielService;

/**
 * Class TunnelTypeConferenceService
 * @package Learnybox\Services\Tunnel\Tutoriel\Types
 */
class TunnelTypeConferenceService extends AbstractTunnelTypeService
{
    protected TunnelTutorielService $tunnelTutorielService;

    private IntegrationsEventsService $integrationsEventsService;

    private \DI\Container $container;

    private \MySQL $database;

    private ConferencesService $conferenceService;

    public function __construct(
        TunnelTutorielService $tunnelTutorielService,
        IntegrationsEventsService $integrationsEventsService,
        ConferencesService $conferenceService
    ) {
        $this->tunnelTutorielService = $tunnelTutorielService;
        $this->integrationsEventsService = $integrationsEventsService;
        $this->conferenceService = $conferenceService;

        $this->type = 'conference';
        $this->datas = [
            'type' => 'conference',
            'title' => __('Webinaire live'),
            'description' => __('Augmentez vos revenus automatiques en offrant un webinaire live et une offre payante'),
            'steps' => [__('Capture'), __('Remerciement'), __('Conférence'), __('Vente')],
            'tags' => [__('Email'), __('Vente')],
            'prerequis' => __('1 produit à vendre'),
            'objectif' => __('Décupler vos revenus automatiques en quelques heures'),
            'long_description' => '<p>' . __('Offrir un webinaire live à vos prospects est une méthode très puissante pour leur montrer en live que vous pouvez régler leurs problèmes.') . '</p><p>' . _('C\'est surtout un moyen très rentable car vous pouvez naturellement proposer vos produits après avoir prouvé votre expertise.') . '</p><p>' . _('Le plus stratégique est de répondre en direct aux problèmes de votre cible ("3 conseils pour...", "Comment faire..."), puis de vendre une méthode pour y parvenir dans de meilleures conditions (plus rapidement, plus efficacement...)') . '</p>',
            'video' => 'rKgRc9ClAlM',
            'emails_themes' => [
                'inscription' => 'conference',
                'pre-conference' => 'pre-conference',
                'post-conference' => 'post-conference',
            ],
            'pages' => [
                'optin' => ['type' => 'optin', 'theme' => TemplateEnum::CAPTURE_1, 'default_thumbnail' => 'optin', 'thumbnail' => TemplateEnum::CAPTURE_1, 'name' => __('Capture'), 'arrow' => true],
                'content' => ['type' => 'content', 'theme' => TemplateEnum::CONFIRMATION_INSCRIPTION_1, 'default_thumbnail' => 'content', 'thumbnail' => TemplateEnum::CONFIRMATION_INSCRIPTION_1, 'name' => __('Confirmation'), 'arrow' => true],
                'sale' => ['type' => 'sale', 'theme' => TemplateEnum::VENTE_1, 'default_thumbnail' => 'vente', 'thumbnail' => TemplateEnum::VENTE_1, 'name' => __('Vente'), 'arrow' => true],
                'commande' => ['type' => 'commande', 'theme' => TemplateEnum::COMMANDE_1, 'default_thumbnail' => 'commande', 'thumbnail' => TemplateEnum::COMMANDE_1, 'name' => __('Commande'), 'arrow' => true],
                'confirmation-commande' => ['type' => 'confirmation-commande', 'theme' => TemplateEnum::CONFIRMATION_COMMANDE_1, 'default_thumbnail' => 'confirmation-commande', 'thumbnail' => TemplateEnum::CONFIRMATION_COMMANDE_1, 'name' => __('Confirmation de commande')],
            ],
        ];
        $this->datas['pages'] = $this->tunnelTutorielService->addPagesThemes($this->datas['pages']);

        $this->container = ContainerBuilderService::getInstance();
        $this->database = \MySQL::getInstance();
    }

    /**
     * @return array
     */
    public function makeTunnel(): array
    {
        $getTunnelDatas = $this->tunnelTutorielService->getTunnelDatas();
        if (!$getTunnelDatas['valid']) {
            return $getTunnelDatas;
        }

        $tunnelDatas = $getTunnelDatas['tunnelDatas'];
        $idConference = $tunnelDatas['id_conference'];
        $nomConference = $tunnelDatas['nom_conference'];
        $dateTimezone = $tunnelDatas['date_timezone'];
        $timezone = $tunnelDatas['timezone'];
        $theme = $tunnelDatas['theme'];
        $masterTheme = $tunnelDatas['masterTheme'];

        $createConference = $this->createConference($idConference, $nomConference, $dateTimezone, $timezone, $theme);
        if (!$createConference['valid']) {
            return $createConference;
        }

        $conference = $createConference['conference'];
        $idConference = $conference['id_conference'];

        $createTunnel = $this->tunnelTutorielService->createTunnel($idConference);
        if (!$createTunnel['valid']) {
            return ['valid' => false, 'message' => $createTunnel['message']];
        }

        $idTunnel = $createTunnel['id_tunnel'];
        $tunnel = $createTunnel['tunnel'];

        //création de la page d'inscription
        $position = 1;
        $pageTitle = __('Inscription') . ' ' . $nomConference;
        $datas = [
            'id_conference' => $idConference,
            'conf_type' => 'inscription',
        ];
        $vars = [
            'txt' => [
                'ACCROCHE' => __('Inscrivez-vous au webinaire') . '<br>' . $nomConference,
                'ACCROCHE_SMALL' => __('Inscrivez-vous au webinaire') . '<br>' . $nomConference,
                'SUB_ACCROCHE' => datetimefr($conference['date_timezone'], false),
                'DESCRIPTION' => $tunnelDatas['product_description'],
            ],
            'button_link' => [
                'BTN' => __('Je m\'inscris maintenant'),
            ],
        ];
        $insertPage = $this->tunnelTutorielService->createTunnelPage($pageTitle, permalink($pageTitle), 'optin', $this->datas['pages']['optin']['theme'], $position, $vars, $datas);
        if (!$insertPage['valid']) {
            return ['valid' => false, 'message' => $insertPage['message'], 'redirect' => $insertPage['redirect'] ?? null];
        }

        $idPageInscription = $insertPage['id_page'];
        $linkPageInscription = $insertPage['permalink'];

        ++$position;

        $elements = $this->container->get(EntityManager::class)->getRepository(TunnelPageElement::class)->findBy(['tunnelPage' => $idPageInscription, 'objet' => 'lbar_optin']);
        foreach ($elements as $element) {
            $contenu = [];
            $masterThemeExampleClassName = 'Learnybox\\Classes\\builder\\themes\\' . (isset($masterTheme['link']) ? $masterTheme['link'] . '\\' : '') . 'Examples\\ButtonlinkExample';
            if (class_exists($masterThemeExampleClassName) && method_exists($masterThemeExampleClassName, 'getExamples')) {
                $examples = $masterThemeExampleClassName::getExamples();
                if ($examples && isset($examples[0]) && $examples[0]) {
                    $contenu = array_merge(json_decode($element->getContenu(), true), json_decode($examples[0]['elements'][0]['update']['contenu'], true));
                }
            }
            $contenu['id_conference'] = $idConference;
            $contenu['align'] = 'center';
            $contenu['template'] = 'template6';

            try {
                $element->setObjet('conference_optin');
                $element->setContenu(json_encode($contenu));
                $element->setContenutexte(__('Valider'));
                $this->container->get(EntityManager::class)->flush();
            } catch (\Exception $e) {
                return ['valid' => false, 'message' => __("Erreur lors de l'intégration du formulaire de capture.")];
            }
        }

        $insertPopupOptin = $this->tunnelTutorielService->insertOptinPopup($idTunnel, $idPageInscription, $masterTheme['id_master_theme'], $theme['id_theme'], 0, $tunnelDatas['product_name'], 0, ['id_conference' => $idConference], false);
        if (!$insertPopupOptin['valid']) {
            return ['valid' => false, 'message' => $insertPopupOptin['message']];
        }


        //création de la page de confirmation d'inscription
        $dateConference = date_french('d F Y', strtotime($conference['date_timezone']));
        $heureConference = date('H\hi', strtotime($conference['date_timezone']));
        $datas = [
            'id_conference' => $idConference,
            'conf_type' => 'confirmation',
        ];
        $vars = [
            'txt' => [
                'ACCROCHE' => $nomConference,
                'NOM_CONFERENCE' => $nomConference,
                'DATE_CONFERENCE' => $dateConference,
                'DATE_CONFERENCE2' => $dateConference . ' à ' . $heureConference,
                'DESCRIPTION' => $tunnelDatas['product_description'],
            ],
        ];
        $insertPage = $this->tunnelTutorielService->createTunnelPage(_('Confirmation d\'inscription'), 'confirmation-inscription', 'content', $this->datas['pages']['content']['theme'], $position, $vars, $datas);
        if (!$insertPage['valid']) {
            return ['valid' => false, 'message' => $insertPage['message']];
        }

        $idPageConfirmation = $insertPage['id_page'];
        $linkPageConfirmation = $insertPage['permalink'];

        ++$position;


        //mise à jour de la conférence
        $arrayUpdate = [
            'id_tunnel' => $idTunnel,
            'id_page_inscription' => $idPageInscription,
            'id_page_confirmation' => $idPageConfirmation,
            'id_page_broadcast' => 0,
            'theme_inscription' => '',
            'theme_confirmation' => '',
            'theme_broadcast' => '',
        ];

        $themePage = $this->container->get(\Builder_ThemesPages::class)->getThemePageByName($this->datas['pages']['optin']['theme']);
        if ($themePage) {
            $arrayUpdate['theme_inscription'] = $theme['id_master_theme'] . '-' . $theme['id_theme'] . '-' . $themePage['id_theme_page'];
        }

        $themePage = $this->container->get(\Builder_ThemesPages::class)->getThemePageByName($this->datas['pages']['content']['theme']);
        if ($themePage) {
            $arrayUpdate['theme_confirmation'] = $theme['id_master_theme'] . '-' . $theme['id_theme'] . '-' . $themePage['id_theme_page'];
        }

        try {
            $this->database->updateRows('lw_conferences', $arrayUpdate, "id_conference='$idConference' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => _('Erreur lors de la mise à jour de la conférence.')];
        }


        if ($tunnelDatas['product_name']) {
            //création de page de commande avant page de vente
            //on crée d'abord la page de commande pour avoir le lien
            $pageCommandePosition = $position;
            ++$pageCommandePosition;
            $insertPage = $this->tunnelTutorielService->createTunnelPage(_('Commande'), 'commande-' . permalink($tunnelDatas['product_name']), 'commande', $this->datas['pages']['commande']['theme'], $pageCommandePosition);
            if (!$insertPage['valid']) {
                return ['valid' => false, 'message' => $insertPage['message']];
            }

            $idPageCommande = $insertPage['id_page'];
            $linkPageCommande = $insertPage['permalink'];

            //création de la page de vente
            $vars = [
                'txt' => [
                    'ACCROCHE' => $tunnelDatas['product_name'],
                    'DESCRIPTION' => $tunnelDatas['product_description'],
                    'TARIF' => ($tunnelDatas['montant'] ? $tunnelDatas['montant'] . \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol() : '49' . \Learnybox\Helpers\CurrencyHelper::getCurrencySymbol())
                ],
                'link' => $idTunnel . '-' . $idPageCommande,
            ];
            $insertPage = $this->tunnelTutorielService->createTunnelPage(_('Vente'), 'vente-' . permalink($tunnelDatas['product_name']), 'sale', $this->datas['pages']['sale']['theme'], $position, $vars);
            if (!$insertPage['valid']) {
                return ['valid' => false, 'message' => $insertPage['message']];
            }

            $idPageVente = $insertPage['id_page'];
            $linkPageVente = $insertPage['permalink'];

            //à cause de la page de commande on ajoute +2
            $position += 2;

            //création de la page de confirmation de commande
            $insertPage = $this->tunnelTutorielService->createTunnelPage(_('Confirmation de commande'), 'confirmation-de-commande', 'commande', $this->datas['pages']['confirmation-commande']['theme'], $position);
            if (!$insertPage['valid']) {
                return ['valid' => false, 'message' => $insertPage['message']];
            }

            $idPageConfirmationCommande = $insertPage['id_page'];
            $linkPageConfirmationCommande = $insertPage['permalink'];

            //page de commande : intégration du formulaire de paiement
            $insertPaymentForm = $this->tunnelTutorielService->createPaymentForm($idPageConfirmationCommande, $idPageCommande);
            if (!$insertPaymentForm['valid']) {
                return ['valid' => false, 'message' => $insertPaymentForm['message']];
            }
            $idPaymentForm = $insertPaymentForm['id_formulaire'];


            //emails séquence post-conférence
            //il faut modifier [[LIEN]] par le lien de la page de vente
            $conferenceMails = $this->container->get(\Webinar_Mails::class)->getMailsByConference($idConference);
            if ($conferenceMails) {
                $link = \Tools::getLink($_SESSION['default_id_domaine'], 'site', $linkPageVente);

                foreach ($conferenceMails as $conferenceMail) {
                    if ('post_end' != $conferenceMail['type']) {
                        continue;
                    }

                    $message = $conferenceMail['message'];
                    if (TextVarHelper::containsVar('LIEN', $message)) {
                        $message = TextVarHelper::replaceVar('LIEN', $link, $message);

                        try {
                            $this->database->updateRows('lw_conference_mail', ['message' => $message], "id_mail='" . $conferenceMail['id_mail'] . "' AND id_conference='$idConference' AND id_client='" . $_SESSION['id_client'] . "'");
                        } catch (\Eden_Error $e) {
                            return ['valid' => false, 'message' => _("Erreur lors de l'enregistrement du mail.")];
                        }
                    }
                }
            }
        }


        //séquence LearnyMail
        if (!$tunnelDatas['learnymail']) {
            return ['valid' => true, 'id_tunnel' => $idTunnel, 'tunnel' => $tunnel];
        }

        $createCategory = $this->tunnelTutorielService->createLearnyMailCategory($nomConference);
        if (!$createCategory['valid']) {
            return $createCategory;
        }
        $id_categorie = $createCategory['id_categorie'];

        //1. création de la séquence prospects
        $datas = [
            'product_name' => $nomConference,
            'nom_conference' => $nomConference,
            'date_conference' => $conference['date_timezone'],
            'url_inscription_conference' => \Tools::getLink($_SESSION['default_id_domaine'], 'site', $linkPageInscription),
        ];
        $createSequence = $this->tunnelTutorielService->createLearnyMailSequence($id_categorie, _('Prospects') . ' ' . $nomConference, $this->datas['emails_themes']['inscription'], $datas);
        if (!$createSequence['valid']) {
            return $createSequence;
        }

        $idSequenceProspects = $createSequence['id_sequence'];
        $this->tunnelTutorielService->createTunnelSequenceLink($idSequenceProspects);


        //2. création de la séquence abandons de panier
        $createSequence = $this->tunnelTutorielService->createLearnyMailSequence($id_categorie, _('Inscrits') . ' ' . $nomConference);
        if (!$createSequence['valid']) {
            return $createSequence;
        }

        $idSequenceInscrits = $createSequence['id_sequence'];
        $this->tunnelTutorielService->createTunnelSequenceLink($idSequenceInscrits);

        //insert first step
        $this->tunnelTutorielService->insertFirstStepClients($idSequenceInscrits, [$idSequenceProspects]);

        //Mise à jour autorépondeurs de la conférence
        $this->tunnelTutorielService->updateConferenceAutoresponder($idConference, $idSequenceInscrits);


        if ($tunnelDatas['product_name']) {
            //3. création de la séquence abandons de panier
            $createSequence = $this->tunnelTutorielService->createLearnyMailSequence($id_categorie, _('Abandons de panier') . ' ' . $tunnelDatas['product_name']);
            if (!$createSequence['valid']) {
                return $createSequence;
            }

            $idSequenceAbandons = $createSequence['id_sequence'];
            $this->tunnelTutorielService->createTunnelSequenceLink($idSequenceAbandons);

            //4. création de la séquence clients
            $createSequence = $this->tunnelTutorielService->createLearnyMailSequence($id_categorie, _('Clients') . ' ' . $tunnelDatas['product_name']);
            if (!$createSequence['valid']) {
                return $createSequence;
            }

            $idSequenceClients = $createSequence['id_sequence'];
            $this->tunnelTutorielService->createTunnelSequenceLink($idSequenceClients);

            //insert first step
            $this->tunnelTutorielService->insertFirstStepClients($idSequenceClients, [$idSequenceAbandons]);

            if (isset($tunnelDatas['formation']) and $tunnelDatas['formation']) {
                //Mise à jour autorépondeurs de la formation
                $this->tunnelTutorielService->updateFormationAutoresponder($idSequenceClients);
            }

            //Mise à jour autorépondeurs du formulaire de paiement
            $this->tunnelTutorielService->updatePaymentFormAutoresponder($idPaymentForm, $idSequenceAbandons);
            $this->tunnelTutorielService->updatePaymentFormAutoresponderPost($idPaymentForm, $idSequenceClients);
        }

        return ['valid' => true, 'id_tunnel' => $idTunnel, 'tunnel' => $tunnel];
    }

    /**
     * @param int $idConference
     * @param string $nomConference
     * @param string $dateTimezone
     * @param string $timezone
     * @param array $theme
     * @return array
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    private function createConference(int $idConference, string $nomConference, string $dateTimezone, string $timezone, array $theme): array
    {
        if ($idConference) {
            $conference = $this->container->get(\Webinar_Conferences::class)->getConferenceById($idConference);
            if (!$conference) {
                return ['valid' => false, 'message' => _('Erreur lors de la récupération du webinaire live')];
            }

            //select conférence
            $this->container->get(\Webinar_Conferences::class)->change_conference(['id_conference' => $idConference]);
            return ['valid' => true, 'conference' => $conference];
        }

        try {
            $randomId = $this->conferenceService->getValidPermalink($nomConference);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la génération du lien unique.')];
        }

        //verification de la date
        $minute = date('i', strtotime($dateTimezone));
        $minute = str_pad($minute - ($minute % 15), 2, '0', STR_PAD_LEFT);
        $dateTimezone = date('Y-m-d H', strtotime($dateTimezone)) . ':' . $minute . ':00';

        //calcul de la date
        $dateTimeZone = new \DateTimeZone($timezone);
        $dateTime = new \DateTime($dateTimezone, $dateTimeZone);

        $dateTime->setTimeZone(new \DateTimeZone(SERVER_TIMEZONE));
        $date = $dateTime->format('Y-m-d H:i:s');

        //insertion de la conférence
        $conference = [
            'id_client' => $_SESSION['id_client'],
            'nom' => $nomConference,
            'type_conf' => ConferenceTypeEnum::TOKBOX,
            'nb_coaches' => 0,
            'nb_auditeurs' => null,
            'is_coache_payant' => 0,
            'coache_productid' => 0,
            'is_auditeur_payant' => 0,
            'auditeur_productid' => 0,
            'afficher_participants' => 0,
            'delta_participants' => 0,
            'type' => 'privee',
            'type_youtube' => 'privee',
            'acces_conference' => 'inscription',
            'type_inscription' => 'automatique',
            'email_inscription' => 0,
            'page_confirmation' => 'defaut',
            'email_rappel' => 0,
            'chat' => 'question',
            'replay_auto' => 0,
            'replay_desactivation' => 0,
            'date' => $date,
            'date_timezone' => $dateTimezone,
            'timezone' => $timezone,
            'etat' => 'enattente',
            'random_id' => $randomId,
        ];

        try {
            $this->database->insertRow('lw_conferences', $conference);
        } catch (\Eden_Error $e) {
            return ['valid' => false, 'message' => _("Erreur lors de l'enregistrement du webinaire live.")];
        }

        $idConference = $this->database->getLastInsertedId();
        if (!$idConference) {
            return ['valid' => false, 'message' => _("Erreur lors de l'enregistrement du webinaire live")];
        }

        $event = [
            'eventName' => 'Conference Created',
            'Name' => $nomConference,
            'Date' => $date,
            'ConferenceThemeID' => $this->datas['pages']['optin']['theme'],
            'ChatType' => 'chat',
        ];
        $this->integrationsEventsService->sendEvent('track', $event);

        $this->container->get(\Webinar_Conferences::class)->sendNbConferencesToSegment();


        $conference['id_conference'] = $idConference;

        //select conférence
        $this->container->get(\Webinar_Conferences::class)->change_conference(['id_conference' => $idConference]);

        //insertion des présentateurs
        $presentateur = $this->container->get(\Webinar_Presentateurs::class)->getPresentateurByEmail($_SESSION['email']);
        if ($presentateur) {
            try {
                $this->database->insertRow('lw_conference_presentateurs', ['id_conference' => $idConference, 'id_presentateur' => $presentateur['id_presentateur'], 'id_client' => $_SESSION['id_client']]);
            } catch (\Eden_Error $e) {
                return ['valid' => false, 'message' => _("Erreur lors de l'enregistrement des présentateurs du webinaire live.")];
            }
        }


        //enregistrement des 2 séquences pré-inscription et post-inscription
        $emailsTypes = [
            'pre-conference' => 'pre',
            'post-conference' => 'post_end',
        ];
        foreach ($emailsTypes as $emailType => $type) {
            $sequenceTheme = $this->container->get(\Lbar_SequencesThemes::class)->getThemeByNameAndSpecificTheme($this->datas['emails_themes'][$emailType], $theme['id_specific_theme']);
            if (!$sequenceTheme) {
                $sequenceTheme = $this->container->get(\Lbar_SequencesThemes::class)->getThemeByName($this->datas['emails_themes'][$emailType]);
            }
            if (!$sequenceTheme) {
                return ['valid' => false, 'message' => _('Erreur lors de la création des emails.')];
            }

            //mise à jour du nombre d'utilisations
            $this->container->get(\Lbar_SequencesThemes::class)->update_utilisations($sequenceTheme['id_theme']);

            //création des emails
            $emails = $this->container->get(\Lbar_SequencesThemes::class)->getEmailsByTheme($sequenceTheme['id_theme']);
            if (!$emails) {
                return ['valid' => false, 'message' => _('Erreur lors de la création des emails.')];
            }

            foreach ($emails as $email) {
                $mailDatas = [
                    'nom_conference' => $nomConference,
                ];

                $sujet = stripslashes(MailSequenceThemeMail::getI18nLibelles($email['sujet']));
                $sujet = TextVarHelper::replaceVar('HEURE_CONFERENCE', date('H', strtotime($conference['date_timezone'])), $sujet);
                $sujet = $this->tunnelTutorielService->replaceFields($sujet, $mailDatas);

                $message = stripslashes(MailSequenceThemeMail::getI18nLibelles($email['message']));
                $message = TextVarHelper::replaceVars([
                    'TITRE' => '[[CONF_TITRE]]',
                    'DATE_CONFERENCE' => datetimefr($conference['date_timezone'], false),
                    'HEURE_CONFERENCE' => date('H', strtotime($conference['date_timezone'])),
                ], $message);

                $message = $this->tunnelTutorielService->replaceFields($message, $mailDatas);

                $day = $email['day'];
                $hour = $email['hour'];
                $minute = $email['minute'];

                $typeMail = $type;
                if ($type == 'pre' and !$day and !$hour and !$minute) {
                    $typeMail = 'inscription';
                }

                $arrayInsert = [
                    'id_client' => $_SESSION['id_client'],
                    'id_conference' => $idConference,
                    'id_mail_predefini' => 0,
                    'subject' => $sujet,
                    'message' => $message,
                    'type' => $typeMail,
                    'day' => $day,
                    'hour' => $hour,
                    'minute' => $minute,
                    'senders' => 'inscrits',
                    'date' => date('Y-m-d H:i:s'),
                ];

                try {
                    $this->database->insertRow('lw_conference_mail', $arrayInsert);
                } catch (\Eden_Error $e) {
                    return ['valid' => false, 'message' => _("Erreur lors de l'enregistrement du mail.")];
                }
            }
        }

        return ['valid' => true, 'conference' => $conference];
    }
}
