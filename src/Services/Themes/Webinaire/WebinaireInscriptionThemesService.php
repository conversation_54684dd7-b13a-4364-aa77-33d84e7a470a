<?php

namespace Learnybox\Services\Themes\Webinaire;

use Learnybox\Helpers\Assets;

class WebinaireInscriptionThemesService extends AbstractWebinaireThemesService
{
    public function __construct()
    {
        parent::__construct();
        $this->sectionTitle = __('Page d\'inscription');
        $this->popupTitle = __('Sélectionnez votre page d\'inscription');
        $this->defaultPage = [
            'thumbnail' => Assets::getImageUrl('templates/webinars/inscription-small.jpg'),
            'preview' => Assets::getImageUrl('templates/webinars/inscription.jpg')
        ];
        $this->themeSelectModalTitle = __('Sélectionnez le modèle de votre page d\'inscription');
    }

    /**
     * @param array $webinaire
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function init(array $webinaire)
    {
        $this->customizeDefaultUrl = \Tools::makeLink('app', 'page_editor', 'everwebinar/inscription/' . $webinaire['id_webinaire']);
        parent::webinaireInit($webinaire, $webinaire['id_page_inscription'], $webinaire['theme_inscription'], 'everwebinaire_inscription', 'optin');
        return;
    }
}
