<?php

namespace Learnybox\Services\Themes\Webinaire;

use Learnybox\Helpers\Assets;

class WebinaireConfirmationThemesService extends AbstractWebinaireThemesService
{
    public function __construct()
    {
        parent::__construct();
        $this->sectionTitle = __('Page de confirmation d\'inscription');
        $this->popupTitle = __('Sélectionnez votre page de confirmation d\'inscription');

        $this->defaultPage = [
            'thumbnail' => Assets::getImageUrl('templates/webinars/confirmation-small.jpg'),
            'preview' => Assets::getImageUrl('templates/webinars/confirmation.jpg')
        ];
        $this->themeSelectModalTitle = __('Sélectionnez le modèle de votre page de confirmation d\'inscription');
        $this->previewThemePage = 'confirmation-inscription-conference-1';
    }

    /**
     * @param array $webinaire
     * @throws \DI\DependencyException
     * @throws \DI\NotFoundException
     */
    public function init(array $webinaire)
    {
        $this->customizeDefaultUrl = \Tools::makeLink('app', 'page_editor', 'everwebinar/confirmation/' . $webinaire['id_webinaire']);
        parent::webinaireInit($webinaire, $webinaire['id_page_confirmation'], $webinaire['theme_confirmation'], 'everwebinaire_confirmation', 'content', ['contenu-confirmation-conference']);
        return;
    }
}
