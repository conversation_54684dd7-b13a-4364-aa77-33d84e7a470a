<?php

namespace Learnybox\Services\Sendgrid;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Sendgrid\Domain\SendgridDomain;
use Learnybox\Entity\Sendgrid\Sender\SendgridSender;

/**
 * Class SendgridSendersService
 * @package Learnybox\Services\Sendgrid
 */
class SendgridSendersService extends SendgridService
{
    /**
     * SendgridSendersService constructor.
     * @param EntityManager $entityManager
     */
    public function __construct(EntityManager $entityManager)
    {
        parent::__construct();
        $this->em = $entityManager;
        $this->repository = $this->em->getRepository(SendgridSender::class);
    }

    /**
     * @param string   $fromName
     * @param string   $fromEmail
     * @param string   $replytoName
     * @param string   $replytoEmail
     * @param int|null $idClient
     * @return string|null
     */
    public function getSenderId(string $fromName, string $fromEmail, string $replytoName = '', string $replytoEmail = '', int $idClient = null): ?string
    {
        if ($idClient === null) {
            $idClient = $_SESSION['id_client'];
        }
        if (!$replytoName) {
            $replytoName = $fromName;
        }
        if (!$replytoEmail) {
            $replytoEmail = $fromEmail;
        }

        $params = [
            'client' => $idClient,
            'fromName' => $fromName,
            'fromEmail' => $fromEmail,
            'replytoName' => $replytoName,
            'replytoEmail' => $replytoEmail,
        ];
        $sender = $this->repository->findOneBy($params);
        if ($sender) {
            return $sender->getSenderId();
        }
        return null;
    }

    /**
     * @param string $senderId
     * @return array
     */
    public function getSendgridSender(string $senderId): array
    {
        try {
            $result = $this->sendgridApi->client->marketing()->senders()->_($senderId)->get();
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => $e->getMessage()];
        }

        return $this->getResponse($result);
    }

    /**
     * @param SendgridDomain $domain
     * @param string $fromName
     * @param string $fromEmail
     * @param string $replyToName
     * @param string $replyToEmail
     * @param int|null $idClient
     * @return array
     */
    public function createSenderId(SendgridDomain $domain, string $fromName, string $fromEmail, string $replyToName = '', string $replyToEmail = '', ?int $idClient = null): array
    {
        if (!$replyToName) {
            $replyToName = $fromName;
        }
        if (!$replyToEmail) {
            $replyToEmail = $fromEmail;
        }
        if ($idClient === null) {
            $idClient = $_SESSION['id_client'];
        }

        $request = [
            'nickname' => permalink('client-' . $idClient . '-' . md5($fromEmail . '-' . $replyToEmail)),
            'from' => [
                'email' => $fromEmail,
                'name' => $fromName,
            ],
            'reply_to' => [
                'email' => $replyToEmail,
                'name' => $replyToName,
            ],
            'address' => $domain->getAddress(),
            'address_2' => $domain->getAddress2(),
            'city' => $domain->getCity(),
            'country' => $domain->getCountry(),
            'state' => $domain->getState(),
            'zip' => $domain->getZip(),
        ];

        try {
            $result = $this->sendgridApi->client->marketing()->senders()->post($request);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => $e->getMessage()];
        }

        $response = $this->getResponse($result, ['id']);
        if (!$response['valid']) {
            return $response;
        }

        $senderId = $response['id'];
        $sender = new SendgridSender();
        $client = $this->clientService->getRepository()->find($idClient);
        $sender->setClient($client);
        $sender->setSenderId($senderId);
        $sender->setFromEmail($fromEmail);
        $sender->setFromName($fromName);
        $sender->setReplytoEmail($replyToEmail);
        $sender->setReplytoName($replyToName);
        $this->persistAndFlush($sender);

        return ['valid' => true, 'id' => $senderId];
    }
}
