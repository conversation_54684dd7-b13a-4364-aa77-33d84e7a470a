<?php

namespace Learnybox\Services\Mail;

use Learnybox\Services\Integration\Facebook\FacebookBusinessService;
use Learnybox\Repositories\Mail\MailSequencesFacebookLeadFormsRepository;

/**
 * Class MailFacebookLeadFormsService
 * @package Learnybox\Services\Mail
 */
class MailFacebookLeadFormsService
{
    /**
     * @var array
     */
    public $formQuestionsAll;

    /**
     * @var array
     */
    public $formQuestionsAllowed = [
        'EMAIL', 'FIRST_NAME', 'LAST_NAME', 'STREET_ADDRESS', 'ZIP', 'CITY', 'COUNTRY', 'PHONE'
    ];

    /**
     * @var FacebookBusinessService
     */
    private $facebookBusinessService;

    /**
     * @var MailSequencesFacebookLeadFormsRepository
     */
    private $mailSequencesFacebookLeadFormsRepository;

    /**
     * LbarFacebookLeadFormsService constructor.
     * @param FacebookBusinessService $facebookBusinessService
     * @param MailSequencesFacebookLeadFormsRepository $mailSequencesFacebookLeadFormsRepository
     */
    public function __construct(
        FacebookBusinessService $facebookBusinessService,
        MailSequencesFacebookLeadFormsRepository $mailSequencesFacebookLeadFormsRepository
    ) {
        $this->facebookBusinessService = $facebookBusinessService;
        $this->mailSequencesFacebookLeadFormsRepository = $mailSequencesFacebookLeadFormsRepository;

        $this->formQuestionsAll = [
            'CUSTOM' => __('Champs personnalisé'),
            'CITY' => __('Ville'),
            'COMPANY_NAME' => __('Nom entreprise'),
            'COUNTRY' => __('Pays'),
            'DOB' => __('Date d\'anniversaire'),
            'EMAIL' => __('Adresse email'),
            'GENDER' => __('Sexe'),
            'FIRST_NAME' => __('Prénom'),
            'FULL_NAME' => __('Nom et prénom'),
            'JOB_TITLE' => __('Profession'),
            'LAST_NAME' => __('Nom'),
            'MARITIAL_STATUS' => __('Etat civil'),
            'PHONE' => __('Téléphone'),
            'POST_CODE' => __('Code postal'),
            'PROVINCE' => __('Province'),
            'RELATIONSHIP_STATUS' => __('Etat civil'),
            'STATE' => __('Etat'),
            'STREET_ADDRESS' => __('Adresse'),
            'ZIP' => __('Code postal'),
            'WORK_EMAIL' => __('Adresse email professionnelle'),
            'MILITARY_STATUS' => __('Statut militaire'),
            'WORK_PHONE_NUMBER' => __('Téléphone professionnel'),
            'STORE_LOOKUP' => __('Localisation du magasin'),
            'STORE_LOOKUP_WITH_TYPEAHEAD' => __('Localisation du magasin'),
            'DATE_TIME' => __('Date'),
            'ID_CPF' => __('Carte d\'identité (Brésil)'),
            'ID_AR_DNI' => __('Carte d\'identité (Argentine)'),
            'ID_CL_RUT' => __('Carte d\'identité (Chili)'),
            'ID_CO_CC' => __('Carte d\'identité (Colombie)'),
            'ID_EC_CI' => __('Carte d\'identité (Equateur)'),
            'ID_PE_DNI' => __('Carte d\'identité (Pérou)'),
        ];
    }

    /**
     * @return array
     */
    public function getFormQuestions(): array
    {
        $questions = [];
        foreach ($this->formQuestionsAllowed as $formQuestion) {
            if (isset($this->formQuestionsAll[$formQuestion])) {
                $questions[$formQuestion] = $this->formQuestionsAll[$formQuestion];
            }
        }

        return $questions;
    }

    /**
     * @param array $postArray
     * @return array
     */
    public function validatePost(array $postArray): array
    {
        $validPost = true;
        $errorString = '';

        $name = filter_var($postArray['name'], FILTER_SANITIZE_SPECIAL_CHARS);
        if (!$name) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez entrer le nom du formulaire') . '</li>';
        }

        $idPage = filter_var($postArray['id_page'], FILTER_VALIDATE_INT);
        if (!$idPage) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez sélectionner une page Facebook') . '</li>';
        }

        $followUpActionUrl = filter_var($postArray['follow_up_action_url'], FILTER_VALIDATE_URL);
        if (!$followUpActionUrl) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez entrer le lien') . '</li>';
        }

        $privacyPolicyUrl = filter_var($postArray['privacy_policy_url'], FILTER_VALIDATE_URL);
        if (!$privacyPolicyUrl) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez entrer le lien vers la politique de confidentialité') . '</li>';
        }

        $privacyPolicyText = filter_var($postArray['privacy_policy_link_text'], FILTER_SANITIZE_SPECIAL_CHARS);
        if (!$privacyPolicyText) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez entrer le texte du lien vers la politique de confidentialité') . '</li>';
        }

        if (!isset($postArray['questions']) or !filter_var_array($postArray['questions'], FILTER_SANITIZE_SPECIAL_CHARS)) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez sélectionner des questions') . '</li>';
        }

        if (!isset($postArray['sequences']) or !$postArray['sequences']) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez sélectionner une séquence') . '</li>';
        }

        return ["valid" => $validPost, 'message' => $errorString];
    }

    /**
     * @param array $cleanedPost
     * @return array
     */
    public function save(array $cleanedPost): array
    {
        $idClient = $_SESSION['id_client'];

        $accessToken = $this->facebookBusinessService->getAccessToken($idClient);
        if (!$accessToken) {
            return ["valid" => false, 'message' => __('Votre compte LearnyBox n\'est pas connecté à Facebook Leads.')];
        }

        $this->facebookBusinessService->getInstanceApi($accessToken);

        $leadFormQuestions = [];
        foreach ($cleanedPost['questions'] as $question) {
            $leadFormQuestions[] = ['type' => $question];
        }

        $facebookPageId = filter_var($cleanedPost['id_page'], FILTER_VALIDATE_INT);

        $pageAccessToken = '';
        $getPageAccessToken = $this->facebookBusinessService->getPageAccessTokenByIdPage($facebookPageId);
        if (isset($getPageAccessToken['page_access_token']) and $getPageAccessToken['page_access_token']) {
            $pageAccessToken = $getPageAccessToken['page_access_token'];
        }

        if (!$pageAccessToken) {
            return ["valid" => false, 'message' => __('Une erreur est survenue lors de la récupération du token d\'accès de la page Facebook.')];
        }

        $params = [
            'name' => $cleanedPost['name'],
            'follow_up_action_url' => $cleanedPost['follow_up_action_url'],
            'privacy_policy_url' => $cleanedPost['privacy_policy_url'],
            'privacy_policy_link_text' => $cleanedPost['privacy_policy_link_text'],
            'questions' => $leadFormQuestions,
        ];

        $createLeadForm = $this->facebookBusinessService->createLeadForm($facebookPageId, $pageAccessToken, $params);
        if (false === $createLeadForm['valid']) {
            return ["valid" => false, 'message' => $createLeadForm['message']];
        }

        $facebookFormId = $createLeadForm['id_form'];

        if (isset($cleanedPost['sequences']) and $cleanedPost['sequences'] and is_array($cleanedPost['sequences'])) {
            $this->addLeadFormToSequences($cleanedPost['sequences'], $facebookFormId);
        }

        return ["valid" => true];
    }

    /**
     * @param array $cleanedPost
     * @return array
     */
    public function update(array $cleanedPost): array
    {
        $idClient = $_SESSION['id_client'];

        $accessToken = $this->facebookBusinessService->getAccessToken($idClient);
        if (!$accessToken) {
            return ["valid" => false, 'message' => __('Votre compte LearnyBox n\'est pas connecté à Facebook Leads.')];
        }

        $facebookFormId = filter_var($cleanedPost['facebookFormId'], FILTER_VALIDATE_INT);
        if (!$facebookFormId) {
            return ["valid" => false, 'message' => __('Ce formlaire Facebook n\'existe pas.')];
        }

        $sequences = [];
        if (isset($cleanedPost['sequences']) and $cleanedPost['sequences'] and is_array($cleanedPost['sequences'])) {
            $sequences = filter_var($cleanedPost['sequences'], FILTER_VALIDATE_INT, FILTER_REQUIRE_ARRAY);
        }

        if (!$sequences) {
            return ["valid" => false, 'message' => __('Veuillez sélectionner une séquence.')];
        }

        //delete form sequences
        $this->mailSequencesFacebookLeadFormsRepository->deleteByLeadFormId($facebookFormId);

        //add new sequences
        $this->addLeadFormToSequences($sequences, $facebookFormId);

        return ["valid" => true];
    }

    /**
     * @param array $sequences
     * @param int $facebookLeadFormId
     * @return array
     */
    public function addLeadFormToSequences(array $sequences, int $facebookLeadFormId): array
    {
        $valid = true;
        $message = '';

        foreach ($sequences as $idSequence) {
            $addLeadFormToSequence = $this->addLeadFormToSequence($idSequence, $facebookLeadFormId);
            if (false === $addLeadFormToSequence['valid']) {
                $valid = false;
                $message .= $addLeadFormToSequence['message'] . PHP_EOL;
            }
        }

        return ['valid' => $valid, 'message' => $message];
    }

    /**
     * @param int $idSequence
     * @param int $facebookLeadFormId
     * @return array
     */
    public function addLeadFormToSequence(int $idSequence, int $facebookLeadFormId): array
    {
        $idClient = $_SESSION['id_client'];

        $sequence = $this->mailSequencesFacebookLeadFormsRepository->getBySequenceAndLeadFormId($idSequence, $facebookLeadFormId);
        if ($sequence) {
            return ['valid' => true];
        }

        $arrayInsert = [
            'id_client' => $idClient,
            'id_sequence' => $idSequence,
            'facebook_lead_form_id' => $facebookLeadFormId,
            'date' => date('Y-m-d H:i:s')
        ];

        if (!$this->mailSequencesFacebookLeadFormsRepository->insertOne($arrayInsert)) {
            return ['valid' => false, 'message' => __('Erreur lors de la création du formulaire sur Facebook.')];
        }

        return ['valid' => true];
    }

    /**
     * @param int $facebookFormId
     * @param int $idPage
     * @return array
     */
    public function getFacebookForm(int $facebookFormId, int $idPage): array
    {
        $idClient = $_SESSION['id_client'];

        $accessToken = $this->facebookBusinessService->getAccessToken($idClient);
        if (!$accessToken) {
            return ["valid" => false, 'message' => __('Votre compte LearnyBox n\'est pas connecté à Facebook Leads.')];
        }

        $facebookForm = $this->facebookBusinessService->getLeadFormByPage($idPage, $facebookFormId);
        if (!$facebookForm['valid']) {
            return $facebookForm;
        }

        return ["valid" => true, 'facebookForm' => $facebookForm['facebookForm']];
    }
}
