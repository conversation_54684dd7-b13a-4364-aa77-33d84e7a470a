<?php

namespace Learnybox\Services\Mail;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Client\Client;
use Learnybox\Entity\Mail\User\MailUserAction;
use Learnybox\Factories\Mail\MailUserActionFatory;
use Learnybox\Services\BaseEntityService;
use Learnybox\Services\Logger\LoggerService;

class MailUserActionService extends BaseEntityService
{
    public function __construct(EntityManager $entityManager)
    {
        $this->em = $entityManager;
        $this->repository = $this->em->getRepository(MailUserAction::class);
    }

    public function insertAction(
        string $type,
        array $usersIds,
        array $ids = [],
        string $action = '',
        ?bool $dontStartSequence = null,
        bool $createUser = false,
        string $provenance = ''
    ): MailUserAction {
        $actions = [];
        if ($action) {
            $actions[] = [
                'action' => $action,
                'param' => $ids
            ];

            if (null !== $dontStartSequence) {
                $actions[0]['dont_start_sequence'] = $dontStartSequence;
            }

            if ($createUser) {
                $actions[0]['create_user'] = true;
            }

            if ($provenance) {
                $actions[0]['provenance'] = $provenance;
            }
        }

        $etat = 'waiting';
        if ($type === 'send_email') {
            $etat = 'not_ready';
        }

        try {
            $client = $this->em->getRepository(Client::class)->find($_SESSION['id_client']);

            $mailUserAction = MailUserActionFatory::createOne($client, $type, $actions, $usersIds, $etat);
            $this->em->persist($mailUserAction);
            $this->em->flush();
        } catch (\Exception $e) {
            LoggerService::log(\Monolog\Logger::ERROR, "Erreur lors de la création de MailUserAction : " . $e->getMessage());
            throw new \Exception(__('Une erreur est survenue, veuillez nous en excuser.'));
        }

        return $mailUserAction;
    }
}
