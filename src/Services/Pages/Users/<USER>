<?php

namespace Learnybox\Services\Pages\Users;

use Learnybox\Helpers\Colors;

class UsersConnexionService extends AbstractPagesUsersService
{
    /**
     * UsersConnexionService constructor.
     */
    public function __construct()
    {
        $this->type = parent::TYPE_CONNEXION;
        parent::__construct();
    }

    /**
     * @param string $defaultTitle
     * @return string
     */
    public function render(string $defaultTitle = ''): string
    {
        $this->setTitle(__('Connexion à votre compte'));
        $this->setContent($this->getContent($defaultTitle));
        return $this->renderPage();
    }

    /**
     * @return string
     */
    public function renderEditor(): string
    {
        $this->editorService->setReturnLink(\Tools::makeLink('app', 'reglages_site', '', 'id_domaine=' . $this->getIdDomaine()));
        $this->editorService->setService($this);
        return $this->editorService->renderPageEditor();
    }

    /**
     * @param string $defaultTitle
     * @return string
     */
    private function getContent(string $defaultTitle = ''): string
    {
        $connectionTitle = __('Espace Formation');
        if ($this->inEditor) {
            $connectionTitle = __('Nom de la formation');
        } elseif (isset($_SESSION['formation']) and $_SESSION['formation']) {
            $connectionTitle = $_SESSION['formation']['nomformation'];
        }

        $design = $this->getEditorDesign();
        $bgColor = ((isset($design['main_color']) and $design['main_color']) ? $design['main_color'] : '#4A90E2');
        $darkColor = false;
        $txtColor = Colors::getTextColorForBackground($bgColor);
        if ($txtColor == '#FFFFFF') {
            $darkColor = true;
        }

        $connectionImage = '';
        if (!$this->inEditor and isset($_SESSION['formation']) and $_SESSION['formation']) {
            $connectionImage = $_SESSION['formation']['vignette'];
        }

        $inputErrors = [];
        if (isset($_SESSION['input_errors']) and $_SESSION['input_errors']) {
            $inputErrors = $_SESSION['input_errors'];
            unset($_SESSION['input_errors']);
        }

        $resetLink = \Tools::getLink($_SESSION['actual_domaine_id'], 'site', 'reset');
        return $this->parser->set('resetLink', $resetLink)
            ->set('connectionTitle', $connectionTitle)
            ->set('inEditor', $this->inEditor)
            ->set('design', $design)
            ->set('darkColor', $darkColor)
            ->set('inputErrors', $inputErrors)
            ->set('connectionImage', $connectionImage)
            ->parsePhp(VIEWS_PATH . '/pages/users/connexion.php');
    }
}
