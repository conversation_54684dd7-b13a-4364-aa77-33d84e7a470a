<?php

namespace Learnybox\Services\Pages\Everwebinars;

class EverwebinarSondageConfirmationService extends AbstractPagesEverwebinarService
{
    /**
     * EverwebinarSondageConfirmationService constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @param array $webinar
     * @param array $user
     * @return string
     */
    public function render(array $webinar, array $user): string
    {
        $this->initateWebinar($webinar, $user);
        $this->setContent($this->getContent($webinar));
        return $this->renderPage();
    }

    /**
     * @param array $webinar
     * @return string
     */
    private function getContent(array $webinar): string
    {
        return $this->parser->set('webinar', $webinar)
            ->parsePhp(VIEWS_PATH . '/pages/everwebinars/sondage-confirmation.php');
    }
}
