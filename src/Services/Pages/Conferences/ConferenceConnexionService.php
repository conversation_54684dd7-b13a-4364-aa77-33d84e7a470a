<?php

namespace Learnybox\Services\Pages\Conferences;

use Learnybox\Helpers\Assets;
use Learnybox\Helpers\RouterHelper;

class ConferenceConnexionService extends AbstractPagesConferenceService
{
    /**
     * ConferenceConnexionService constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @param array $conference
     * @param array $user
     * @return string
     */
    public function render(array $conference, ?array $user = []): string
    {
        if (is_null($user)) {
            $user = [];
        }
        $this->initateConference($conference);
        $this->setContent($this->getContent($conference, $user));
        return $this->renderPage();
    }

    /**
     * @param array $conference
     * @return string
     */
    public function renderEditor(array $conference): string
    {
        $this->editorService->setReturnLink(RouterHelper::generate('app_conference_pages', [
            'conferenceId' => $conference['id_conference'],
        ]));
        $this->editorService->setService($this);
        return $this->editorService->renderPageEditor([$conference]);
    }

    /**
     * @param array $conference
     * @param array $user
     * @return string
     */
    private function getContent(array $conference, array $user = []): string
    {
        $prenom = $email = '';
        if ($user) {
            $prenom = $user['fname'];
        } elseif (isset($_GET['prenom'])) {
            $prenom = filter_input(INPUT_GET, 'prenom', FILTER_SANITIZE_SPECIAL_CHARS);
        }
        if ($user) {
            $email = $user['email'];
        } elseif (isset($_GET['email'])) {
            $email = filter_input(INPUT_GET, 'email', FILTER_VALIDATE_EMAIL);
        }

        if (!$prenom and isset($_COOKIE[$_SESSION['client_uniqid'] . '_lw_prenom'])) {
            $prenom = filter_var($_COOKIE[$_SESSION['client_uniqid'] . '_lw_prenom'], FILTER_SANITIZE_SPECIAL_CHARS);
        }
        if (!$email and isset($_COOKIE[$_SESSION['client_uniqid'] . '_lw_email'])) {
            $email = filter_var($_COOKIE[$_SESSION['client_uniqid'] . '_lw_email'], FILTER_VALIDATE_EMAIL);
        }

        if ('enattente' == $conference['etat']) {
            $this->addCountdown($conference);
        }

        $conferenceHeader = $this->getConferenceHeader($conference);
        $displayPresenters = $this->getConferencePresenters($conference);

        //conference inscription
        if ($conference['id_domaine']) {
            $urlInscription = \Tools::getLink($conference['id_domaine'], 'conference', 'inscription', $conference['random_id']);
        } elseif (defined('CUSTOM_DOMAINE') and CUSTOM_DOMAINE) {
            $urlInscription = CUSTOM_DOMAINE . 'conference/inscription/' . $conference['random_id'] . '/';
        } else {
            $urlInscription = \Tools::makeLink('conference', 'inscription', $conference['random_id']);
        }
        if ($conference['id_tunnel'] and $conference['id_page_inscription']) {
            $pageInscription = eden()->TunnelsPages()->getPageById($conference['id_page_inscription'], $conference['id_tunnel']);
            if ($pageInscription and 'encours' == $pageInscription['publication']) {
                $urlInscription = \Tools::getLink($pageInscription['id_domaine'], 'site', $pageInscription['permalink']);
            }
        }

        return $this->parser->set('conference', $conference)
            ->set('prenom', $prenom)
            ->set('email', $email)
            ->set('conferenceHeader', $conferenceHeader)
            ->set('displayPresenters', $displayPresenters)
            ->set('urlInscription', $urlInscription)
            ->parsePhp(VIEWS_PATH . '/pages/conferences/connexion.php');
    }

    private function addCountdown(array $conference)
    {
        Assets::addCssV5(Assets::CSS_TYPE_COMPONENTS, '_jquery.countdown.css');
        Assets::addCss('themes/t/css/countdown.css', null);
        Assets::addJs('conference/get_conf_status.js');
        Assets::addJs('common/jquery/jquery.plugin.js');
        Assets::addJs('common/jquery/jquery.countdown.min.js');

        if (false === \Learnybox\Helpers\I18nHelper::isEnglish()) {
            $language = substr(\Learnybox\Helpers\I18nHelper::getLang(), 0, 2);
            Assets::addJs('common/jquery/jquery.countdown-' . $language . '.js');
        }

        $time = strtotime($conference['date']);
        $dtz = new \DateTimeZone(SERVER_TIMEZONE);
        $datetime = new \DateTime('now', $dtz);
        $offset = round($dtz->getOffset($datetime) / 3600, 0);

        $inlineJs = "
            <script type='text/javascript'>
            $(function(){
                $('#countdown').countdown({
                    until: new Date(" . date('Y', $time) . ", " . (date('m', $time) - 1) . ", " . date('d', $time) . ", " . date('H', $time) . ", " . date('i', $time) . ", " . date('s', $time) . "),
                    timezone: +" . $offset . ",
                    alwaysExpire: true
                });
            });

            $(document).ready(function() {
                UpdateConf('" . $conference['random_id'] . "');
            });
            </script>";
        Assets::addInlineJs($inlineJs);
    }
}
