<?php

namespace Learnybox\Services\PaymentMethod;

class ClickbankPaymentMethodService extends AbstractPaymentMethodService implements PaymentMethodServiceInterface
{
    public function __construct()
    {
        parent::__construct();
        $this->currencies = [
            'EUR' => ['value' => 'EUR', 'label' => 'EUR'],
            'MXN' => ['value' => 'MXN', 'label' => 'MXN'],
        ];
    }

    /**
     * @param array $data
     * @return string
     */
    public function getConfigPanelFormHtml(array $data): string
    {
        $help = '<a href="' . \Tools::makeLink('app', 'help', 'clickbank') . '" target="_blank"><i class="fa fa-external-link"></i> ' . __('Aide sur la configuration de ClickBank') . '</a>';
        $html = $this->builderPanelService->renderAlert($help, 'info');

        $clickbankAccount = '';
        if (!isset($data['clickbank_account']) || (isset($data['clickbank_account']) && $data['clickbank_account'] === '')) {
            $getClickbankAccount = $this->container->get(\Reglages::class)->appGetParametreByName('clickbank_account');
            if ($getClickbankAccount) {
                $clickbankAccount = $getClickbankAccount['value'];
            }
        } else {
            $clickbankAccount = $data['clickbank_account'];
        }

        $clickbankSecretKey = '';
        if (!isset($data['clickbank_secret_key']) || (isset($data['clickbank_secret_key']) && $data['clickbank_secret_key'] === '')) {
            $getClickbankSecretKey = $this->container->get(\Reglages::class)->appGetParametreByName('clickbank_secret_key');
            if ($getClickbankSecretKey) {
                $clickbankSecretKey = $getClickbankSecretKey['value'];
            }
        } else {
            $clickbankSecretKey = $data['clickbank_secret_key'];
        }

        $clickbankParams = $data['clickbank_params'];
        $clickbankIdProduct = $data['clickbank_id_product'];

        $html .= $this->builderPanelFormService->renderInput('clickbank_account', __('Votre nom d\'utilisateur ClickBank'), $clickbankAccount);
        $html .= $this->builderPanelFormService->renderInput('clickbank_secret_key', __('Clé secrète de votre compte ClickBank'), $clickbankSecretKey);
        $html .= $this->builderPanelFormService->renderInput('clickbank_id_product', __('Numéro de votre produit'), $clickbankIdProduct);
        $help = __('Pour les experts. Permet par exemple d\'utiliser un bon de commande personnalisé avec cbskin=1234&cbfid=xxx, etc.');
        $html .= $this->builderPanelFormService->renderInput('clickbank_params', __('Paramètres additionnels'), $clickbankParams, null, 'text', null, $help);

        return $this->getConfigPanelHtml('clickbankConfiguration', $data['display'], $html);
    }
}
