<?php

namespace Learnybox\Services\Formations;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Formation\Academy\FormationAcademyFormation;
use Learnybox\Helpers\TemplateHelper;
use Learnybox\Services\BaseEntityService;
use Learnybox\Services\DI\ContainerBuilderService;

class FormationAcademyFormationService extends BaseEntityService
{
    protected EntityManager $em;

    public function __construct(EntityManager $em)
    {
        $this->em = $em;
        $this->repository = $this->em->getRepository(FormationAcademyFormation::class);
    }

    public function getModalFormation(): string
    {
        $container = ContainerBuilderService::getInstance();
        $selectPage = $container->get(\Pages::class)->generateSelectAllPages('', '', false);

        return TemplateHelper::render('formations/academy/modal-formation.html.twig', [
            'selectPage' => $selectPage,
        ]);
    }

    public function sortFormations(array $formations, array $academyFormations): array
    {
        $positions = [];
        foreach ($academyFormations as $academyFormation) {
            $positions[$academyFormation->getFormation()->getIdFormation()] = $academyFormation->getPosition();
        }

        usort($formations, function ($a, $b) use ($positions) {
            if (!isset($positions[$a->getIdFormation()]) || !isset($positions[$b->getIdFormation()])) {
                return 0;
            }
            return $positions[$a->getIdFormation()] <=> $positions[$b->getIdFormation()];
        });

        return $formations;
    }

    public function sortMemberFormations(array $memberFormations, array $academyFormations): array
    {
        $positions = [];
        foreach ($academyFormations as $academyFormation) {
            $positions[$academyFormation->getFormation()->getIdFormation()] = $academyFormation->getPosition();
        }

        usort($memberFormations, function ($a, $b) use ($positions) {
            if (!isset($positions[$a->getFormation()->getIdFormation()]) || !isset($positions[$b->getFormation()->getIdFormation()])) {
                return 0;
            }
            return $positions[$a->getFormation()->getIdFormation()] <=> $positions[$b->getFormation()->getIdFormation()];
        });

        return $memberFormations;
    }

    public function updatePositions(array $formationsIds): void
    {
        $position = 0;
        foreach ($formationsIds as $formationId) {
            if (!is_numeric($formationId)) {
                $formationId = str_replace('formation', '', $formationId);
            }
            $academyFormation = $this->repository->findOneBy(['formation' => $formationId]);
            if ($academyFormation) {
                $academyFormation->setPosition($position);
                $this->persistAndFlush($academyFormation);
                $position++;
            }
        }
    }
}
