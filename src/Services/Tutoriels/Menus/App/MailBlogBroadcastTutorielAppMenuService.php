<?php

namespace Learnybox\Services\Tutoriels\Menus\App;

use Learnybox\Helpers\Assets;

/**
 * Class MailBlogBroadcastTutorielAppMenuService
 * @package Learnybox\Services\Tutoriels\Menus\App
 */
class MailBlogBroadcastTutorielAppMenuService extends AbstractAppTutorielMenuService
{
    /**
     * @var array
     */
    protected $steps;

    /**
     * @var string
     */
    protected $title;

    /**
     * @var string
     */
    protected $actionLink;

    /**
     * MailBlogBroadcastTutorielAppMenuService constructor.
     */
    public function __construct()
    {
        $this->steps = [];
        $this->steps[1] = [
            'title' => __('Email'),
            'icon' => 'fa-envelope',
            'main-title' => __('Sélection de votre email'),
            'description' => __('La première étape consiste à créer votre email, pour l\'envoyer à un ou plusieurs destinataires.') . '<br>' . __('Si vous avez déjà créé votre email, cliquez sur "Sélectionner un email" et choisissez-le dans la liste déroulante.'),
        ];
        $this->steps[2] = [
            'title' => __('Construction'),
            'icon' => 'fa-wrench',
            'width' => 'extra-large',
        ];
        $this->steps[3] = [
            'title' => __('Destinataires'),
            'icon' => 'fa-group',
            'main-title' => __('Sélection des destinataires'),
            'description' => __('Sur cette page, sélectionnez les différents séquences et tags auxquels vous voulez envoyer cet email. Si vous voulez envoyer votre email aux contacts inscrits dans une séquence, cochez-la dans la case "Inclure".') . '<br><br>' . __('Notez que vous pouvez aussi sélectionner un ou plusieurs tags : dans ce cas, nous sélectionnerons les contacts inclus dans les séquences sélectionnées, et qui possèdent aussi les tags sélectionnés.')
        ];
        $this->steps[4] = [
            'title' => __('Date'),
            'icon' => 'fa-calendar',
            'main-title' => __('Programmation de l\'email'),
            'description' => __('La dernière étape consiste à donner un nom à cette newsletter et à spécifier l\'adresse de votre flux RSS.'),
        ];
        $this->steps[5] = [
            'title' => __('Résumé'),
            'icon' => 'fa-check',
            'width' => 'large',
        ];

        $this->title = __('Création d\'une newsletter');
        $this->actionLink = 'autorepondeur/blog_broadcast_tutoriel';
        $this->returnLink = \Tools::makeLink('app', 'autorepondeur', 'blog_broadcasts');

        parent::__construct();
    }

    /**
     * @param int $idStep
     * @return void
     * @throws \Exception
     */
    public function prepare(int $idStep = 1)
    {
        Assets::addJs('app/lbar_mail_apercu2.js');
        Assets::addJs('app/lbar_envoitutoriel.js');

        if (1 == $idStep) {
            Assets::addCssV5(Assets::CSS_TYPE_COMPONENTS, '_bootstrap-image-gallery.css');
            Assets::addInlineJs(\Tools::getJsGallery());
        }

        if (2 == $idStep) {
            $this->setAction('<a class="btn btn-primary btn-timeline-next" onclick="return submitForm();"> ' . __('Suivant') . '</a>');
        }

        if (3 == $idStep) {
            Assets::addJs('app/tables-v2/lbar_envois_sequences.js');
            Assets::addJs('app/tables-v2/lbar_envois_tags.js');
        }

        if (5 == $idStep) {
            $idMail = 0;
            if (isset($_SESSION['lbar_blogbroadcasttutoriel']['id_mail'])) {
                $idMail = $_SESSION['lbar_blogbroadcasttutoriel']['id_mail'];
            }

            if ($idMail) {
                Assets::addJs('app/lbar_mail_apercu2.js');
                Assets::addInlineJs('<script type="text/javascript">$(document).ready(function() { AfficherApercuMail(' . $idMail . '); });</script>');
            }

            $this->setAction('<a class="btn btn-primary btn-timeline-next" onclick="$(this).html(\'<i class=\\\'fa fa-circle-o-notch fa-spin\\\'></i> Programmer\'); $(this).addClass(\'disabled\'); $(\'#form-timeline\').submit(); return false;"> ' . __('Enregistrer') . '</a>');
        }

        parent::prepare($idStep);
    }

    protected function setLastStep()
    {
        $this->lastStep = count($this->steps) + 1;
    }
}
