<?php

namespace Learnybox\Services\Quiz;

use Learnybox\Repositories\Support\QuizReponseRepository;

/**
 * Class QuizReponseService
 * @package Learnybox\Services\Support
 */
class QuizReponseService
{
    /**
     * @var QuizReponseRepository
     */
    private $quizReponseRepository;

    /**
     * QuizReponseService constructor.
     * @param QuizReponseRepository $quizReponseRepository
     */
    public function __construct(QuizReponseRepository $quizReponseRepository)
    {
        $this->quizReponseRepository = $quizReponseRepository;
    }
}
