<?php

namespace Learnybox\Services\Conference;

use Doctrine\ORM\EntityManager;
use Learnybox\Entity\Conference\Conference;
use Learnybox\Entity\Conference\Renseignement\ConferenceConferenceRenseignement;
use Learnybox\Entity\Conference\Renseignement\ConferenceRenseignement;
use Learnybox\Services\BaseEntityService;
use Learnybox\Services\ClientService;

class ConferenceConferenceRenseignementService extends BaseEntityService
{
    private ClientService $clientService;

    public function __construct(EntityManager $em, ClientService $clientService)
    {
        $this->em = $em;
        $this->repository = $this->em->getRepository(ConferenceConferenceRenseignement::class);
        $this->clientService = $clientService;
    }

    public function createWithConferenceRenseignement(Conference $conference, ?ConferenceRenseignement $conferenceRenseignement, ?int $idRens)
    {
        if (!$conferenceRenseignement) {
            $conferenceRenseignement = $this->em->getRepository(ConferenceRenseignement::class)->find($idRens);
        }
        $ccrName = $this->repository->findOneBy(['conference' => $conference, 'idRens' => $conferenceRenseignement->getIdRens()]);
        if (!$ccrName) {
            $ccrName = new ConferenceConferenceRenseignement();
            $ccrName->setConference($conference);
            $ccrName->setPosition(1);
            $ccrName->setIdRens($conferenceRenseignement->getIdRens());
            $ccrName->setClient($this->clientService->getRepository()->find($_SESSION['id_client']));
        }
        $ccrName->setObligatoire(true);
        $this->persistAndFlush($ccrName);
    }

    public function updateMandatoryFieldToFalse(Conference $conference, ?ConferenceRenseignement $conferenceRenseignement, ?int $idRens)
    {
        if (!$conferenceRenseignement) {
            $conferenceRenseignement = $this->em->getRepository(ConferenceRenseignement::class)->find($idRens);
        }
        $ccrName = $this->repository->findOneBy(['conference' => $conference, 'idRens' => $conferenceRenseignement->getIdRens()]);
        $ccrName->setObligatoire(false);
        $this->persistAndFlush($ccrName);
    }
}
