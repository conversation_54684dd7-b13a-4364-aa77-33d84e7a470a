<?php

namespace Learnybox\Services;

use Learnybox\Helpers\I18nHelper;

/**
 * Class I18nService
 *
 * @package Learnybox\Services
 */
class I18nService
{
    public $languages = [];
    public $language;
    public $folder;
    public $domain;
    public $encoding;

    public function __construct()
    {
        $this->languages = I18nHelper::getArrayLangs();
        $this->language = SERVER_LOCALE;
        $this->folder = I18N_PATH;
        $this->domain = 'all';
        $this->encoding = 'UTF-8';

        //$this->setLanguage();
        //$this->starti18n();
    }

    /**
     * setLanguage function.
     */
    public function setLanguage($language = '')
    {
        if ($language) {
            $this->language = $language;
        }

        if (!$language and defined('APP_LANGUAGE') and APP_LANGUAGE) {
            $this->language = APP_LANGUAGE;
        }

        if (!in_array($this->language, $this->languages)) {
            $this->language = SERVER_LOCALE;
        }

        if (isset($_SESSION['force_lng']) and $_SESSION['force_lng']) {
            $this->language = $_SESSION['force_lng'];
        }

        //start
        $this->starti18n();
    }

    /**
     * starti18n function.
     */
    public function starti18n()
    {
        $_SESSION['language'] = $this->language;

        putenv('LANG=' . $this->language);
        putenv('LANGUAGE=' . $this->language);
        setlocale(LC_ALL, $this->language);

        bindtextdomain($this->domain, $this->folder);
        bind_textdomain_codeset($this->domain, $this->encoding);

        textdomain($this->domain);
    }
}
