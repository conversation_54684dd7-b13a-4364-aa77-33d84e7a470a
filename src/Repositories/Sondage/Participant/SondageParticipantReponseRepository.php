<?php

namespace Learnybox\Repositories\Sondage\Participant;

use Learnybox\Repositories\AbstractRepository;

class SondageParticipantReponseRepository extends AbstractRepository
{
    public function __construct()
    {
        parent::__construct();
        $this->table = 'sdg_participants_reponses';
        $this->primaryKey = 'ID';
        $this->hasClientId = false;
    }

    public function getResultatsByQuestion(int $idSondage, int $idQuestion): ?array
    {
        return $this->database
            ->search($this->table)
            ->addFilter("id_sondage='$idSondage' AND id_question='$idQuestion'")
            ->getRows();
    }

    public function getResultatsByQuestionAndParticipant(int $idSondage, int $idQuestion, int $idParticipant): ?array
    {
        return $this->database
            ->search($this->table)
            ->addFilter("id_sondage='$idSondage' AND id_question='$idQuestion' AND id_participant='$idParticipant'")
            ->getRow();
    }

    public function getNbResultatsBySondage(int $idSondage): int
    {
        $result = $this->database
            ->search($this->table)
            ->setColumns('COUNT(DISTINCT(id_participant)) as nb')
            ->addFilter("id_sondage='$idSondage'")
            ->getRow();
        return (int) $result['nb'];
    }
}
