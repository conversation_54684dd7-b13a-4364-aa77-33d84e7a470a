<?php

namespace Learnybox\Repositories;

/**
 * EverwebinarUsers Repository
 */
class EverwebinarUsersRepository extends AbstractRepository
{
    public function __construct()
    {
        parent::__construct();
        $this->table = 'ew_webinaire_users';
        $this->primaryKey = 'id_user';
    }

    /**
     * getWebinarsSessionsByWebnaireId function.
     *
     * @param int $idWebinaire
     * @param string $idClient (default: null)
     * @return array
     */
    public function getWebinarsSessionsByWebnaireId(int $idWebinaire, $idClient = null)
    {
        if (!$idClient) {
            $idClient = $_SESSION['id_client'];
        }

        return $this->database
            ->search($this->table . ' ewu')
            ->leftJoinOn('ew_webinaire_inscrits ewi', 'ewu.id_user=ewi.id_user')
            ->addFilter("ewi.id_webinaire='" . $idWebinaire . "' AND ewi.id_client='" . $idClient . "'")
            ->setGroup('ewu.date_webinaire')
            ->addSort('ewu.date_webinaire', 'DESC')
            ->getRows();
    }
}
