<?php

namespace Learnybox\Repositories\Faq;

use Learnybox\Repositories\AbstractRepository;

/**
 * Class FaqCategorieRepository
 * @package Learnybox\Repositories
 */
class FaqCategorieRepository extends AbstractRepository
{
    public function __construct()
    {
        parent::__construct();
        $this->table = DB_PREFIX . 'faq_categories';
        $this->primaryKey = 'id_categorie';
        $this->hasClientId = true;
    }

    /**
     * @param $idCategorie
     * @param null $idClient
     * @return mixed
     */
    public function getCategorieById($idCategorie, $idClient = null)
    {
        if ($idClient === null) {
            $idClient = $_SESSION['id_client'];
        }

        $result = $this->database
            ->search($this->table)
            ->addFilter("id_categorie='$idCategorie' AND id_client='$idClient'")
            ->getRow();
        return $result;
    }

    /**
     * @return mixed
     */
    public function getAllCategories()
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter("id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('position', 'ASC')
            ->getRows();
        return $result;
    }

    /**
     * @return mixed
     */
    public function getCountCategories()
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter("id_client='" . $_SESSION['id_client'] . "'")
            ->getTotal();
        return $result;
    }
}
