<?php

namespace Learnybox\Repositories;

class CitationsRepository extends AbstractRepository
{
    public function __construct()
    {
        parent::__construct();
        $this->table = DB_PREFIX . 'citations';
        $this->primaryKey = 'id_citation';
        $this->hasClientId = false;
    }

    /**
     * @param int $limit
     * @return array|null
     */
    public function getRandomCitations(int $limit = 1): ?array
    {
        $citations = parent::getAll();
        shuffle($citations);
        return array_slice($citations, 0, $limit);
    }
}
