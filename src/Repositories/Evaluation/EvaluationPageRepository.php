<?php

namespace Learnybox\Repositories\Evaluation;

use Learnybox\Repositories\AbstractRepository;

class EvaluationPageRepository extends AbstractRepository
{
    public function __construct()
    {
        parent::__construct();
        $this->table = 'ev_pages';
        $this->primaryKey = 'id_page';
        $this->hasClientId = true;
    }

    public function getPageById(int $idEvaluation, int $idPage): ?array
    {
        return $this->database
            ->search($this->table)
            ->addFilter("id_page='$idPage' AND id_evaluation='$idEvaluation' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getRow();
    }

    public function getPages(int $idEvaluation)
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter("id_evaluation='$idEvaluation' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('position', 'ASC')
            ->getRows();

        if (!$result) {
            return false;
        }

        $return = [];
        foreach ($result as $_result) {
            $return[$_result['id_page']] = $_result;
        }

        return $return;
    }

    public function searchPageId(int $idEvaluation, int $idPage)
    {
        $pages = $this->database
            ->search($this->table)
            ->addFilter("id_evaluation='$idEvaluation' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('position', 'ASC')
            ->getRows();

        if (!$pages) {
            return false;
        }

        $idPage--;
        if (isset($pages[$idPage])) {
            return $pages[$idPage];
        }

        return false;
    }

    public function searchPageIndex(int $idEvaluation, int $idPage)
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter("id_evaluation='$idEvaluation' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('position', 'ASC')
            ->getRows();

        if (!$result) {
            return false;
        }

        $index = null;
        foreach ($result as $index => $_result) {
            if ($_result['id_page'] == $idPage) {
                break;
            }
        }

        if ($index !== null) {
            $index++;
        }

        return $index;
    }

    public function getNbPages(int $idEvaluation): int
    {
        return $this->database
            ->search($this->table)
            ->addFilter("id_evaluation='$idEvaluation' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getTotal();
    }

    public function getNextPage(int $idEvaluation, int $position): ?array
    {
        return $this->database
            ->search($this->table)
            ->addFilter("id_evaluation='$idEvaluation' AND position > '$position' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('position', 'ASC')
            ->getRow();
    }

    public function getLastPage(int $idEvaluation): ?array
    {
        return $this->database
            ->search($this->table)
            ->addFilter("id_evaluation='$idEvaluation' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('position', 'DESC')
            ->getRow();
    }
}
