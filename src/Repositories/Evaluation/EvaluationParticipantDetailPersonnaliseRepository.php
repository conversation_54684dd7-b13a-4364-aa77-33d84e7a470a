<?php

namespace Learnybox\Repositories\Evaluation;

use Learnybox\Repositories\AbstractRepository;

class EvaluationParticipantDetailPersonnaliseRepository extends AbstractRepository
{
    public function __construct()
    {
        parent::__construct();
        $this->table = 'ev_participants_details_personnalises';
        $this->primaryKey = 'ID';
        $this->hasClientId = false;
    }

    public function getRenseignementsPersonnalisesReponsesByEvaluation(int $idEvaluation, string $idRensP): ?array
    {
        return $this->database
            ->search($this->table . ' pdp')
            ->leftJoinOn('ev_participants p', 'p.id_participant = pdp.id_participant')
            ->addFilter("pdp.id_evaluation='$idEvaluation' AND pdp.id_rens_p='$idRensP' AND p.etat !='0'")
            ->addSort('id_rens_p', 'ASC')
            ->getRows();
    }

    public function getRenseignementPersonnaliseReponseByEvaluationAndParticipant(int $idEvaluation, string $idRensP, int $idParticipant): ?array
    {
        return $this->database
            ->search($this->table)
            ->addFilter("id_evaluation='$idEvaluation' AND id_rens_p='$idRensP' AND id_participant='$idParticipant'")
            ->getRow();
    }
}
