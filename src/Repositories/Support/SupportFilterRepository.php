<?php

namespace Learnybox\Repositories\Support;

use Learnybox\Repositories\AbstractRepository;

/**
 * Class SupportFilterRepository
 * @package Learnybox\Repositories
 */
class SupportFilterRepository extends AbstractRepository
{
    public function __construct()
    {
        parent::__construct();
        $this->table = 'hdk_filters';
        $this->primaryKey = 'id_filter';
        $this->hasClientId = true;
    }

    /**
     * @param int $idFilter
     * @return bool
     */
    public function getFilterById(int $idFilter)
    {
        if (!$idFilter) {
            return false;
        }
        $result = $this->database
            ->search($this->table)
            ->addFilter("id_filter='$idFilter' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('id_filter', 'ASC')
            ->getRow();
        return $result;
    }

    /**
     * @param null $idClient
     * @return mixed
     */
    public function getAllFilters($idClient = null)
    {
        if ($idClient === null) {
            $idClient = $_SESSION['id_client'];
        }
        $result = $this->database
            ->search($this->table)
            ->addFilter("id_client='$idClient'")
            ->addSort('id_filter', 'ASC')
            ->getRows();
        return $result;
    }

    /**
     * @return mixed
     */
    public function getCountFilters()
    {
        $result = $this->database
            ->search($this->table)
            ->addFilter("id_client='" . $_SESSION['id_client'] . "'")
            ->getTotal();
        return $result;
    }
}
