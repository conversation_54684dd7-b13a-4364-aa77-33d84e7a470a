<?php

namespace Learnybox\Enums\Mail\Brevo;

enum BrevoCodeEnum
{
    private const BREVO_CODE_GOOD_REPUTATION_SERVER = BREVO_CODE_GOOD_REPUTATION_SERVER;
    private const BREVO_CODE_AVERAGE_REPUTATION_SERVER = BR<PERSON>VO_CODE_AVERAGE_REPUTATION_SERVER;
    private const BREVO_CODE_TRANSACTIONAL_SERVER = BREVO_CODE_TRANSACTIONAL_SERVER;

    public static function getBrevoCodes(): array
    {
        return [
            self::BREVO_CODE_GOOD_REPUTATION_SERVER,
            self::BREVO_CODE_AVERAGE_REPUTATION_SERVER,
            self::BR<PERSON>VO_CODE_TRANSACTIONAL_SERVER
        ];
    }
}
