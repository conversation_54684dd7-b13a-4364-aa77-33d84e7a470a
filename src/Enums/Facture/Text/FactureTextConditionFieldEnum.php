<?php

namespace Learnybox\Enums\Facture\Text;

abstract class FactureTextConditionFieldEnum
{
    const COUNTRY = 'country';
    const SOCIETE = 'societe';
    const TVA_INTRACOM = 'tva_intracom';

    /**
     * @return array
     */
    public static function getI18nChoices(): array
    {
        return [
            self::COUNTRY => __('Si pays'),
            self::SOCIETE => __('Si société'),
            self::TVA_INTRACOM => __('Si numéro de TVA Intracommunautaire'),
        ];
    }
}
