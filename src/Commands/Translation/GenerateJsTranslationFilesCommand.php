<?php

namespace Learnybox\Commands\Translation;

use Gettext\Merge;
use Gettext\Translations;
use Learnybox\Commands\AbstractCommand;
use Learnybox\Helpers\I18nHelper;
use Learnybox\Services\TranslationsService;
use Learnybox\Attributes\Description;

/**
 * Class GenerateJsTranslationFilesCommand
 * @package Learnybox\Commands\Translation
 */
#[Description('Generate .json files for JS translations')]
class GenerateJsTranslationFilesCommand extends AbstractCommand
{
    /**
     * @var TranslationsService
     */
    protected $translationsService;

    /**
     * GeneratePotTranslationFileCommand constructor.
     * @param TranslationsService $translationsService
     */
    public function __construct(TranslationsService $translationsService)
    {
        $this->translationsService = $translationsService;
    }

    /**
     * @return mixed|void
     */
    public function execute()
    {
        // Fix PHP error Allowed memory size exhausted
        ini_set('memory_limit', '1024M');

        foreach (I18nHelper::getArrayLangs() as $language) {
            if ($language === I18nHelper::LANG_FRENCH_FR) {
                continue;
            }

            $allPoFilePath = $this->translationsService->getPoFilePath($language);
            if (!is_file($allPoFilePath)) {
                echo ".json file " . $language . " \e[31mKO : .po file " . $allPoFilePath . " not found.\e[0m\n";
                continue;
            }

            $jsPoFilePath = $this->translationsService->getPoFilePath($language, 'js');
            if (!is_file($jsPoFilePath)) {
                echo ".json file " . $language . " \e[31mKO : .po file " . $jsPoFilePath . " not found.\e[0m\n";
                continue;
            }

            try {
                $allTranslations = Translations::fromPoFile($allPoFilePath);
                $jsTranslations = Translations::fromPoFile($jsPoFilePath);

                $allTranslations->mergeWith($jsTranslations);
                $allTranslations->toPoFile($jsPoFilePath);

                $allTranslations->toJsonFile($this->translationsService->getJsonFilePath($language));
            } catch (\Exception $e) {
                echo ".json file " . $language . " \e[31mKO : " . $e->getMessage() . "\e[0m\n";
                continue;
            }

            echo ".json file " . $language . " \e[32mOK\e[0m\n";
        }

        $this->translationsService->generateJsScripts();
    }
}
