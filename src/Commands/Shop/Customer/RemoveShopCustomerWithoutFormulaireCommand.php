<?php

namespace Learnybox\Commands\Shop\Customer;

use Doctrine\ORM\EntityManager;
use Learnybox\Commands\AbstractCommand;
use Learnybox\Entity\Shop\Customer\ShopCustomer;
use Learnybox\Helpers\CommandHelper;
use Learnybox\Attributes\Description;

/**
 * Class RemoveShopCustomerWithoutFormulaireCommand
 * @package Learnybox\Commands\Shop\Formulaire
 */
#[Description('Remove shop customer without shop formulaire')]
class RemoveShopCustomerWithoutFormulaireCommand extends AbstractCommand
{
    private EntityManager $em;

    public function __construct(EntityManager $em)
    {
        $this->em = $em;
    }

    public function execute(): void
    {
        $save = $this->param === 'save';

        @ini_set('memory_limit', '4096M');

        CommandHelper::displayMessage('RemoveShopCustomerWithoutFormulaireCommand command started');

        $shopCustomers = $this->em->getRepository(ShopCustomer::class)->createQueryBuilder('sc')
            ->leftJoin('sc.formulaire', 'f')
            ->where('f.idFormulaire IS NULL')
            ->getQuery()
            ->getResult();
        $nbShopCustomers = count($shopCustomers);
        CommandHelper::displayMessage("$nbShopCustomers shop customers found", true);

        if ($save) {
            foreach ($shopCustomers as $shopCustomer) {
                $this->em->remove($shopCustomer);
            }
            $this->em->flush();
        }

        CommandHelper::displayEndMessage();
    }
}
