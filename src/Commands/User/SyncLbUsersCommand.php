<?php

namespace Learnybox\Commands\User;

use League\CLImate\CLImate;
use Learnybox\Commands\AbstractCommand;
use Learnybox\Entity\User\Role\UserRole;
use Learnybox\Helpers\CommandHelper;
use Learnybox\Services\Logger\LoggerService;
use Monolog\Logger;
use MySQL;
use Learnybox\Attributes\Description;

/**
 * bin/console -a sync_lb_users --param=save
 * Class SyncLbUsersCommand
 * @package Learnybox\Commands\User
 */
#[Description('Synchronize lb_users with other users types')]
class SyncLbUsersCommand extends AbstractCommand
{
    private const TABLES_INFO = [
        'all' => [

        ],
        'lw_conference_users' => [
            'columns' => [
                'lb_users' => [
                    'id_client' => 'id_client',
                    'prenom' => 'fname',
                    'email' => 'email',
                    'newsletter' => 'newsletter',
                ],
                'lb_users_config' => [
                    'telephone' => 'tel',
                    'pays' => 'pays',
                    'region' => 'region'
                ]
            ],
            'date_column_name' => 'date_inscr',
            'primary_key' => 'id_user',
            'migration_table' => 'lw_conference_users_migration_history'
        ],
        'ew_webinaire_users' => [
            'columns' => [
                'lb_users' => [
                    'id_client' => 'id_client',
                    'prenom' => 'fname',
                    'email' => 'email',
                    'newsletter' => 'newsletter',
                ],
                'lb_users_config' => [
                    'telephone' => 'tel',
                    'pays' => 'pays',
                    'region' => 'region'
                ]
            ],
            'date_column_name' => 'date_inscr',
            'primary_key' => 'id_user',
            'migration_table' => 'ew_webinaire_users_migration_history'
        ],
        'lb_contact' => [
            'columns' => [
                'lb_users' => [
                    'id_client' => 'id_client',
                    'nom' => 'lname',
                    'prenom' => 'fname',
                    'email' => 'email',
                ],
            ],
            'date_column_name' => 'date',
            'primary_key' => 'ID',
            'migration_table' => 'lb_contact_migration_history'
        ],
        'ps_customers' => [
            'columns' => [
                'lb_users' => [
                    'id_client' => 'id_client',
                    'nom' => 'lname',
                    'prenom' => 'fname',
                    'email' => 'email',
                ],
                'lb_users_config' => [
                    'pays' => 'pays',
                ]
            ],
            'date_column_name' => 'date',
            'primary_key' => 'id_customer',
            'migration_table' => 'ps_customers_migration_history'
        ],
        'ev_utilisateurs' => [
            'columns' => [
                'lb_users' => [
                    'id_client' => 'id_client',
                    'nom' => 'lname',
                    'prenom' => 'fname',
                    'email' => 'email',
                ],
            ],
            'date_column_name' => 'date',
            'primary_key' => 'id_utilisateur',
            'migration_table' => 'ev_utilisateurs_migration_history'
        ],
        'hdk_tickets' => [
            'columns' => [
                'lb_users' => [
                    'id_client' => 'id_client',
                    'nom' => 'lname',
                    'prenom' => 'fname',
                    'email' => 'email',
                ],
                'lb_users_config' => [
                    'tel' => 'tel',
                    'city' => 'ville',
                    'pays' => 'pays',
                ]
            ],
            'date_column_name' => 'date_creation',
            'primary_key' => 'id',
            'migration_table' => 'hdk_tickets_migration_history'
        ],
        'aff_affilies' => [
            'columns' => [
                'lb_users' => [
                    'id_client' => 'id_client',
                    'nom' => 'lname',
                    'prenom' => 'fname',
                    'email' => 'email',
                ]
            ],
            'date_column_name' => 'date',
            'primary_key' => 'id_affilie',
            'migration_table' => 'aff_affilies_migration_history'
        ],
        'bxi_ideas' => [
            'columns' => [
                'lb_users' => [
                    'id_client' => 'id_client',
                    'nom' => 'lname',
                    'prenom' => 'fname',
                    'email' => 'email',
                ],
                'lb_users_config' => [
                    'pays' => 'pays',
                ]
            ],
            'date_column_name' => 'date_creation',
            'primary_key' => 'id',
            'migration_table' => 'bxi_ideas_migration_history'
        ],
        'lbar_users' => [
            'columns' => [
                'lb_users' => [
                    'id_client' => 'id_client',
                    'nom' => 'lname',
                    'prenom' => 'fname',
                    'email' => 'email',
                ],
                'lb_users_config' => [
                    'adresse' => 'adresse',
                    'cp' => 'code_postal',
                    'ville' => 'ville',
                    'pays' => 'pays',
                    'region' => 'region',
                    'tel' => 'tel',
                    'mobile' => 'mobile',
                ]
            ],
            'date_column_name' => 'date_inscription',
            'primary_key' => 'user_id',
            'migration_table' => 'lbar_users_migration_history'
        ]
    ];

    private CLImate $climate;
    private MySQL $database;
    private string $tableName = '';
    private bool $save = false;
    private bool $doMigrationHistory = false;
    private ?int $currentUserId = null;
    private ?string $currentUserEmail = null;
    private ?string $operationType = null;

    public function __construct()
    {
        $this->climate = new CLImate();
        $this->database = MySQL::getInstance();
    }

    private function initValues(): void
    {
        $this->doMigrationHistory = false;
        $this->currentUserId = $this->currentUserEmail = $this->operationType = null;
    }

    public function execute(): void
    {
        ini_set('memory_limit', '2048M');

        $this->save = $this->param === 'save';

        CommandHelper::displayMessage('<bold><underline>List of available tables</underline></bold>');
        $this->climate->json(array_keys(self::TABLES_INFO));

        $tablesNames = $this->getTablesNames();
        $clientId = CommandHelper::askForIntInput('Client ID ? (if empty, get all clients)', -1);
        $offset = CommandHelper::askForIntInput('Offset ? (per table)');
        $limit = CommandHelper::askForIntInput('Limit ? (per table)');

        $totalUsers = 0;
        $processedUsers = 0;

        foreach ($tablesNames as $tableName) {
            CommandHelper::displayMessage('-----------------------', true);
            CommandHelper::displayMessage('<magenta>Table ' . $tableName . '</magenta>');

            $this->tableName = $tableName;

            $query = "SELECT * FROM $this->tableName";

            if (-1 !== $clientId) {
                $query .= " WHERE id_client = $clientId";
            } else {
                $query .= " WHERE 1=1";
            }

            $query .= " AND (user_id NOT IN (SELECT user_id FROM " . $this->getTableInfo('migration_table') . ") OR user_id IS NULL) LIMIT $limit OFFSET $offset";

            $tableData = $this->database->query($query);

            $totalUsers += count($tableData);

            CommandHelper::displayMessage('<magenta>' . $totalUsers . ' users found to process</magenta>');

            foreach ($tableData as $data) {
                $this->initValues();

                $this->handleUser($data);

                if ($this->doMigrationHistory) {
                    try {
                        $res = $this->database->query("SELECT COUNT(user_id) as total FROM " . $this->getTableInfo('migration_table') . " WHERE user_id = $this->currentUserId");
                        $res = array_shift($res);

                        if (0 === intval($res['total'])) {
                            $this->createUserMigrationHistory();
                            CommandHelper::displayMessage('<green>Create ' . $this->getTableInfo('migration_table') . '</green> for user #' . $this->currentUserId);
                            ++$processedUsers;
                        }
                    } catch (\Exception $e) {
                        CommandHelper::displayMessage($e->getMessage());
                        $this->saveError($e->getMessage());
                    }
                }
            }
        }

        CommandHelper::displayMessage('-----------------------');
        CommandHelper::displayMessage('<bold>Report: ' . $processedUsers . ' users processed / ' . $totalUsers . ' to process</bold>');

        CommandHelper::displayMessage('END');
    }

    private function handleUser(array $data): void
    {
        CommandHelper::displayMessage('-----------------------');
        CommandHelper::displayMessage('<bold>User ' . $data['email'] . '</bold> (client #' . $data['id_client'] . ')');
        $this->currentUserEmail = $data['email'];
        $clientId = $data['id_client'];
        $date = new \DateTime($data[$this->getTableInfo('date_column_name')]);

        $user = $this->database->query("SELECT * FROM lb_users WHERE id_client = '" . $clientId . "' AND email = '" . $this->currentUserEmail . "'");
        $columns = $this->getTableInfo('columns');

        if (isset($user[0]) && $user[0]) {
            $user = $user[0];
            $this->currentUserId = intval($user['user_id']);
            $userDate = new \DateTime($user['timestamp']);

            if (!$data['user_id']) {
                try {
                    $this->updateTargetTable($data);
                    CommandHelper::displayMessage('<yellow>Update ' . $this->tableName . '</yellow> for user #' . $this->currentUserId);

                    $this->doMigrationHistory = true;
                } catch (\Exception $e) {
                    CommandHelper::displayErrorMessage($e->getMessage());
                    $this->saveError($e->getMessage());
                }
            }

            if ($date > $userDate) {
                try {
                    $this->updateLbUsers($columns['lb_users'], $data);
                    CommandHelper::displayMessage('<yellow>Update lb_users</yellow> for user #' . $this->currentUserId);

                    if (isset($columns['lb_users_config'])) {
                        $userConfig = $this->database->query("SELECT * FROM lb_users_config WHERE user_id = '" . $this->currentUserId . "'");
                        if ($userConfig) {
                            $targetUserConfigValues = array_filter($userConfig, fn($item) => in_array($item['name'], $columns['lb_users_config']));
                            if ($targetUserConfigValues) {
                                try {
                                    $this->updateLbUsersConfig($columns['lb_users_config'], $data, $targetUserConfigValues);
                                    CommandHelper::displayMessage('<yellow>Update lb_users_config</yellow> for user ' . $this->currentUserId);
                                } catch (\Exception $e) {
                                    CommandHelper::displayErrorMessage($e->getMessage());
                                    $this->saveError($e->getMessage());
                                }
                            }
                        } else {
                            try {
                                $totalInserted = $this->createLbUsersConfig($data, $columns['lb_users_config']);
                                CommandHelper::displayMessage('<green>Create lb_users_config</green> for user #' . $this->currentUserId . ' (' . $totalInserted . ' rows inserted)');
                            } catch (\Exception $e) {
                                CommandHelper::displayErrorMessage($e->getMessage());
                                $this->saveError($e->getMessage());
                            }
                        }
                    }

                    $this->doMigrationHistory = true;
                } catch (\Exception $e) {
                    CommandHelper::displayErrorMessage($e->getMessage());
                    $this->saveError($e->getMessage());
                }
            } else {
                $this->doMigrationHistory = true;
            }
        } else {
            try {
                $this->currentUserId = $this->createLbUsers($data, $columns['lb_users']);
                CommandHelper::displayMessage('<green>Create lb_users</green> #' . $this->currentUserId);

                $this->updateTargetTable($data);
                CommandHelper::displayMessage('<yellow>Update ' . $this->tableName . '</yellow> for user #' . $this->currentUserId);

                if (isset($columns['lb_users_config'])) {
                    $totalInserted = $this->createLbUsersConfig($data, $columns['lb_users_config']);
                    CommandHelper::displayMessage('<green>Create lb_users_config</green> for user #' . $this->currentUserId . ' (' . $data['email'] . ') (' . $totalInserted . ' rows inserted)');
                }

                $this->doMigrationHistory = true;
            } catch (\Exception $e) {
                CommandHelper::displayErrorMessage($e->getMessage());
                $this->saveError($e->getMessage());
            }
        }
    }

    private function createLbUsers(array $data, array $targetColumns): int
    {
        $clientId = $data['id_client'];
        $role = $this->database->query("SELECT user_role_id FROM lb_users_roles WHERE role_id = 1 AND id_client = $clientId");

        $extraValues = [
            'password' => md5(strtotime('now') . uniqid()),
            'random_id' => createRandomID(),
            'md5_email' => md5($data['email']),
            'user_role_id' => isset($role[0]) ? $role[0]['user_role_id'] : null
        ];

        $mappedData = array_merge($this->getMappedData($data, $targetColumns, false), $extraValues);

        try {
            $res = $this->executeQuery('lb_users', $mappedData);

            $this->operationType = 'create';
        } catch (\Exception $e) {
            throw new \Exception('[ERROR][INSERT][LB_USERS]' . $e->getMessage());
        }

        return $res ? $res->getLastInsertedId() : 0;
    }

    private function createLbUsersConfig(array $data, array $targetColumns): int
    {
        $mapping = $this->getMappedData($data, $targetColumns);

        $totalInserted = 0;
        foreach ($mapping as $key => $value) {
            try {
                $this->executeQuery(
                    'lb_users_config',
                    ['user_id' => $this->currentUserId, 'name' => $key, 'value' => $value],
                );

                ++$totalInserted;
            } catch (\Exception $e) {
                throw new \Exception('[ERROR][INSERT][LB_USERS_CONFIG][' . $this->currentUserId . '] ' . $e->getMessage());
            }
        }

        return $totalInserted;
    }

    private function updateLbUsers(array $columns, array $data): void
    {
        $extraValues = [
//            'timestamp' => $data[$this->getTableInfo('date_column_name')] // We do not want to update timestamp anymore
        ];

        try {
            $this->executeQuery(
                'lb_users',
                array_merge($this->getMappedData($data, $columns), $extraValues),
                [['user_id=%d', $this->currentUserId]]
            );

            $this->operationType = 'update';
        } catch (\Exception $e) {
            throw new \Exception('[ERROR][UPDATE][LB_USERS][' . $this->currentUserId . '] ' . $e->getMessage());
        }
    }

    private function updateLbUsersConfig(array $columns, array $data, array $values): void
    {
        $mapping = $this->getMappedData($data, $columns);

        foreach ($values as $value) {
            foreach ($value as $val) {
                if (in_array($val, array_keys($mapping))) {
                    $configUserId = $value['id'];

                    try {
                        $this->executeQuery(
                            'lb_users_config',
                            ['value' => $mapping[$val]],
                            [['id=%d AND user_id=%d', intval($configUserId), $this->currentUserId]]
                        );
                    } catch (\Exception $e) {
                        throw new \Exception('[ERROR][UPDATE][LB_USERS_CONFIG][' . $this->currentUserId . '][' . $configUserId . '] ' . $e->getMessage());
                    }
                }
            }
        }
    }

    private function updateTargetTable(array $data): void
    {
        $primaryColumnName = $this->getTableInfo('primary_key');

        try {
            $this->executeQuery(
                $this->tableName,
                ['user_id' => $this->currentUserId],
                [[$primaryColumnName . '=%d', intval($data[$primaryColumnName])]]
            );
        } catch (\Exception $e) {
            throw new \Exception('[ERROR][UPDATE][' . $this->tableName . '][' . $this->currentUserId . ']' . $e->getMessage());
        }
    }

    private function createUserMigrationHistory(): void
    {
        try {
            $this->executeQuery(
                $this->getTableInfo('migration_table'),
                ['user_id' => $this->currentUserId, 'type' => $this->operationType],
            );
        } catch (\Exception $e) {
            throw new \Exception('[ERROR][INSERT][HISTORY][' . $this->currentUserId . '][' . $this->getTableInfo('migration_table') . ']' . $e->getMessage());
        }
    }

    private function executeQuery($table, array $data, ?array $conditions = []): ?MySQL
    {
        if ($this->save) {
            if ($conditions) {
                return $this->database->updateRows($table, $data, $conditions);
            }

            return $this->database->insertRow($table, $data);
        }

        return null;
    }

    private function getMappedData(array $data, array $columns, $isUpdate = true): array
    {
        $res = array_filter($data, fn($item) => in_array($item, array_keys($columns)), ARRAY_FILTER_USE_KEY);

        foreach ($res as $targetColumn => $value) {
            if ('' === $value) {
                unset($res[$targetColumn]);
                continue;
            }
            foreach ($columns as $matchingColumn => $validColumn) {
                if ($targetColumn === $matchingColumn) {
                    if ($isUpdate && 'id_client' === $validColumn) {
                        unset($res[$validColumn]);

                        continue;
                    }

                    unset($res[$targetColumn]);
                    $res[$validColumn] = $value;
                }
            }
        }

        return $res;
    }

    private function getTablesNames(): array
    {
        CommandHelper::displayMessage('Table name ?');
        $tableName = CommandHelper::waitUserInput();

        if (!in_array($tableName, array_keys(self::TABLES_INFO))) {
            return $this->getTablesNames();
        }

        $tables = self::TABLES_INFO;
        unset($tables['all']);

        return 'all' === $tableName ? array_keys($tables) : [$tableName];
    }

    private function getTableInfo(string $key)
    {
        return self::TABLES_INFO[$this->tableName][$key];
    }

    private function saveError(string $message): void
    {
        $this->doMigrationHistory = false;

        LoggerService::log(Logger::ERROR, '[SYNC_USER]' . $message);
    }
}
