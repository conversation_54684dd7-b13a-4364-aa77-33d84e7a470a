<?php

namespace Learnybox\Commands\Mail\Spam;

use Learnybox\Commands\AbstractCommand;
use Learnybox\Helpers\CommandHelper;
use Learnybox\Services\SpamService;

/**
 * Class SpamCheckerMailTest5Command
 * @package Learnybox\Commands\Mail
 */
class SpamCheckerMailTest5Command extends AbstractCommand
{
    public function execute(): void
    {
        CommandHelper::displayMessage('SpamCheckerMailTest5Command command started');

        $html = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>[[SUBJECT]]</title>
    <style type="text/css">
        @import url(\'https://fonts.googleapis.com/css?family=Rubik:300,400,500,700\');
        #outlook a {padding:0;}
        body{font-family: \'Rubik\', serif; width:100% !important; -webkit-text-size-adjust:100%; -ms-text-size-adjust:100%; margin:0; padding:0;}
        .ExternalClass {width:100%;}
        .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {line-height: 100%;}
        #backgroundTable {margin:0; padding:0; width:100% !important; line-height: 100% !important;}
        img {outline:none; text-decoration:none; -ms-interpolation-mode: bicubic;}
        a img {border:none;}
        .image_fix {display:block;}
        p {margin:0 0 1em 0; font-size:16px; line-height:24px;}
        p.unubscribe {font-size:11px; line-height:18px;}
        ul li {font-size:16px;}
        h1, h2, h3, h4, h5, h6 {color: black !important;}
        h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {color: blue !important;}
        h1 a:active, h2 a:active,  h3 a:active, h4 a:active, h5 a:active, h6 a:active {
        color: red !important;
        }
        h1 a:visited, h2 a:visited,  h3 a:visited, h4 a:visited, h5 a:visited, h6 a:visited {
        color: purple !important;
        }
        table td {border-collapse: collapse;}
        table { border-collapse:collapse; mso-table-lspace:0pt; mso-table-rspace:0pt; }
        a {color: #337AB7;}
        a[href] {color: #337AB7;}
        .ii a[href] {color: #337AB7;}
        a.link { color: #34364B;}
            a.link-unsubscribe { color: #90959A;}
                .im { color: #90959A; }
                    @media only screen and (max-device-width: 480px) {
                        a[href^="tel"], a[href^="sms"] {
                        text-decoration: none;
                color: black;
                pointer-events: none;
                cursor: default;
            }
            .mobile_link a[href^="tel"], .mobile_link a[href^="sms"] {
                        text-decoration: initial;
                color: orange !important;
                pointer-events: auto;
                cursor: default;
            }
        }
        @media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
                        a[href^="tel"], a[href^="sms"] {
                        text-decoration: none;
                color: blue;
                pointer-events: none;
                cursor: default;
            }
            .mobile_link a[href^="tel"], .mobile_link a[href^="sms"] {
                        text-decoration: initial;
                color: orange !important;
                pointer-events: auto;
                cursor: default;
            }
        }
        @media only screen and (max-device-width: 720px) {
                        .container-header-padding p {
                            font-size: 24px !important;
            }
            .container-padding { padding: 20px !important; }
        }
        @media only screen and (-webkit-min-device-pixel-ratio: 2) {
                        /* Put your iPhone 4g styles in here */
                    }
        @media only screen and (-webkit-device-pixel-ratio:.75){
                        /* Put CSS for low density (ldpi) Android layouts in here */
                    }
        @media only screen and (-webkit-device-pixel-ratio:1){
                        /* Put CSS for medium density (mdpi) Android layouts in here */
                    }
        @media only screen and (-webkit-device-pixel-ratio:1.5){
                        /* Put CSS for high density (hdpi) Android layouts in here */
                    }
    </style>
    <!--[if IEMobile 7]>
    <style type="text/css">
        /* Targeting Windows Mobile */
    </style>
    <![endif]-->
    <!--[if gte mso 9]>
    <style>
        /* Target Outlook 2007 and 2010 */
    </style>
    <![endif]-->
</head>
<body >
                    [[URL_TRACKING]]    <table cellpadding="20" cellspacing="0" border="0" width="100%" height="100%" style="margin-top: 30px; margin-bottom: 30px;">
        <tr>
            <td class="main-td" align="center" valign="top">
                <!--[if (gte mso 9)|(IE)]>
                <table align="center" border="0" cellspacing="0" cellpadding="0" width="720">
                    <tr>
                        <td align="center" valign="top" width="720">
                <![endif]-->
                <table border="0" width="100%" cellpadding="0" cellspacing="0" style="border-collapse: collapse; max-width: 720px;">
                                        <tr>
                        <td style="text-align: center; padding-top: 44px; padding-bottom: 46px;">
                            <img src="[[SITE_LOGO]]" alt="Logo" style="height: 120px;" />
                        </td>
                    </tr>
                                        <tr>
                        <td class="container-header-padding content" style="padding-top: 0px; padding-bottom: 40px; text-align: center; font-weight: 300">
                            <p style="font-size: 36px; line-height: 40px; color: #273139; margin-top: 10px; margin-bottom: 0px">Let op: controleer uw betaling – Ref: 41004539202364</p>
                        </td>
                    </tr>
                </table>
                <table border="0" width="100%" cellpadding="0" cellspacing="0" style="border-collapse: collapse; max-width: 720px;">
                    <tr style="border-radius: 6px 6px 6px 6px;">
                        <td class="content" style="padding: 0px; line-height: 24px; color:#34364B;">
                            <div class="container-padding" style="background: #FFFFFF; border: 1px solid #D2D3E0; border-top: 4px solid #3068A3; border-radius: 6px; padding: 48px;">
                                <div style="color:#34364B;">
                                    <h2><input alt="" src="https://da32ev14kd4yl.cloudfront.net/versioned/addfengshui/telenet-logo-png_seeklogo-342210.png" style="width: 150px; height: 150px;" type="image" /></h2>

<h2>Opmerking bij recente betaling</h2>

<p>Beste klant,</p>

<div style="background-color:#f8f8f8; border:1px solid #d6d4d4; padding:7px">
<p>Bij de verwerking van uw recente betaling is opgemerkt dat er mogelijk een dubbele transactie is uitgevoerd. Dit kan per ongeluk zijn gebeurd, en we willen u graag ondersteunen bij een eventuele correctie.</p>

<p>U kunt dit eenvoudig nakijken en indien nodig een verzoek tot terugbetaling indienen via uw beveiligde klantenzone:</p>

<ul>
	<li><strong>Log in via de knop hieronder.</strong></li>
	<li><strong>Ga naar het onderdeel &quot;Facturen &amp; Betalingen&quot;.</strong></li>
	<li><strong>Controleer uw recente transacties en onderneem actie indien nodig.</strong></li>
</ul>
</div>

<p><br />
<a href="https://carlosaguilar.com.ar//LabCliente/model/extra/APP/">https://www.telenet.be/klantenzone</a></p>

<p>Zorg ervoor dat onze berichten uw inbox bereiken door dit bericht naar het Primair tabblad te verplaatsen indien nodig.</p>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
                <table border="0" width="100%" cellpadding="0" cellspacing="0" style="border-collapse: collapse; max-width: 720px;">
                                        <tr>
                        <td style="text-align: center; font-size: 11px; padding-top: 62px; padding-bottom: 8px; color:#90959A; font-weight: 300;">
                        Propulsé par<br/>
                            <a href="https://learnybox.com"><img src="https://da32ev14kd4yl.cloudfront.net/assets/images/site/logo/v6/small/LearnyBox-Logo.png?v=1212" style="padding: 6px; width: 90px"></a>
                        </td>
                    </tr>
                                        <tr>
                        <td style="text-align: center; color: #90959A; font-size: 11px; line-height: 18px; padding-top: 16px; font-weight: 300;">
                            <div style="max-width: 499px; margin: 0 auto;">
                                <div align="center">
    <p class="site-email-address" style="text-align:center; font-size: 11px;">
                        [[SITE_EMAIL_ADDRESS]]    </p>
</div>
                                <div align="center">
    <p class="unubscribe" style="text-align:center; font-size: 11px;">
                        Si vous désirez mettre à jour votre email ou vous désabonner, cliquez sur le lien ci-dessous.        <br>
        <a class="link-unsubscribe" style="color: #90959A;" href="[[UNSUBSCRIBE_HREF]]" target="_blank">
                        Mettre à jour vos informations ou vous désabonner.        </a>
        <br>
            </p>
</div>
                                <br/>

                                <div align="center" style="text-align:center;">
                        [[EMAIL_NOTIFICATION_PREFERENCES_LINK]]                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="text-align: center; opacity: 0.5; padding-bottom: 30px; font-weight: 300; text-decoration: none; color: #34364B; font-size: 11px;">
                        [[LINK_EMAIL_TEXT]]                        </td>
                    </tr>
                </table>
                <!--[if (gte mso 9)|(IE)]>
                        </td>
                    </tr>
                </table>
                <![endif]-->
            </td>
        </tr>
    </table>
</body>
</html>';

        $isSpam = SpamService::isSpam($html, null, 1, 2);
        if ($isSpam['success'] === false) {
            CommandHelper::displayErrorMessage('Mail is spam');
        } else {
            CommandHelper::displaySuccessMessage('Mail is not spam');
        }

        CommandHelper::displayEndMessage();
    }
}
