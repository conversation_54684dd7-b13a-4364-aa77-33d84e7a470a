<?php

namespace Learnybox\Helpers;

class ViewHelper
{
    public static function getPath(string $path, string $filename, string $extension = 'php'): string
    {
        return VIEWS_PATH . $path . $filename . '.' . $extension;
    }

    public static function renderTemplate(string $file, array $paramToParse = []): string
    {
        $template = \Eden_Template::i();
        foreach ($paramToParse as $source => $param) {
            $template->set($source, $param);
        }
        return $template->parsePhp($file);
    }

    public static function checkFileAndRenderTemplate(string $file, array $paramToParse): string
    {
        if (!file_exists($file)) {
            return '';
        }

        return self::renderTemplate($file, $paramToParse);
    }
}
