<?php

namespace Learnybox\EventListener\Subscriber\HumanResources\Collaborator;

use Doctrine\ORM\Event\LifecycleEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Learnybox\Entity\HumanResources\Collaborator\CollaboratorVacation;
use Learnybox\Event\EntityEvent;
use Learnybox\Event\EntityEvents;
use Learnybox\Event\EntityUpdatedEvent;
use Learnybox\Services\DI\ContainerBuilderService;
use Learnybox\Services\HumanResources\Collaborator\CollaboratorVacationCreditRecoveryService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Class CollaboratorVacationSubscriber
 * @package Learnybox\EventListener\Subscriber\HumanResources\Collaborator
 */
class CollaboratorVacationSubscriber implements EventSubscriberInterface
{
    /**
     * @return array
     */
    public static function getSubscribedEvents()
    {
        return [
            EntityEvents::HR_COLLABORATOR_VACATION_CREATED => 'onCollaboratorVacationCreated',
            EntityEvents::HR_COLLABORATOR_VACATION_UPDATED => 'onCollaboratorVacationUpdated',
        ];
    }

    /**
     * @param EntityEvent $entityEvent
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function onCollaboratorVacationCreated(EntityEvent $entityEvent)
    {
        $collaboratorVacation = $entityEvent->getEntity();
        if ($collaboratorVacation->getType() === CollaboratorVacation::TYPE_EXTERNAL_ABSENCE) {
            return;
        }

        // Create recovery if needed
        $container = ContainerBuilderService::getInstance();
        $container->get(CollaboratorVacationCreditRecoveryService::class)->adaptFromCollaboratorVacation($collaboratorVacation);
    }

    /**
     * @param EntityUpdatedEvent $entityUpdatedEvent
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function onCollaboratorVacationUpdated(EntityUpdatedEvent $entityUpdatedEvent)
    {
        $collaboratorVacation = $entityUpdatedEvent->getEntity();

        if ($collaboratorVacation->getType() === CollaboratorVacation::TYPE_EXTERNAL_ABSENCE) {
            return;
        }

        // Modify recovery if needed
        $container = ContainerBuilderService::getInstance();
        $container->get(CollaboratorVacationCreditRecoveryService::class)->adaptFromCollaboratorVacation($collaboratorVacation);
    }
}
