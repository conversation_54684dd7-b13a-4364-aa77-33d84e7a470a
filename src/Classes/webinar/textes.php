<?php

use Learnybox\Helpers\NumberHelper;
use Learnybox\Helpers\RouterHelper;
use Learnybox\Services\Integration\IntegrationsEventsService;

/**
 * Webinar_Textes class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Webinar_Textes extends Eden_Class
{
    private $database;

    /**
     * @var IntegrationsEventsService
     */
    private $integrationsEventsService;

    /**
     * Webinar_Textes constructor.
     * @param IntegrationsEventsService $integrationsEventsService
     */
    public function __construct(IntegrationsEventsService $integrationsEventsService)
    {
        $this->database = MySQL::getInstance();
        $this->integrationsEventsService = $integrationsEventsService;
    }

    /********************************************************/
    /********************** TEXTES **************************/
    /********************************************************/

    public function getTextes($id_conference)
    {
        $result = $this->database
            ->search('lw_conference_texte')
            ->addFilter("id_conference='$id_conference' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('id_texte', 'ASC')
            ->getRows();

        return $result;
    }

    public function getCountTextes($id_conference)
    {
        $result = $this->database
            ->search('lw_conference_texte')
            ->addFilter("id_conference='$id_conference' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('id_texte', 'ASC')
            ->getTotal();

        return $result;
    }

    public function getTexteEnCours($id_conference)
    {
        $result = $this->database
            ->search('lw_conference_texte')
            ->addFilter("id_conference='$id_conference' AND visible='1' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getRow();

        return $result;
    }

    public function getTextesPublies($id_conference)
    {
        $result = $this->database
            ->search('lw_conference_texte')
            ->addFilter("id_conference='$id_conference' AND (date_publication != '0000-00-00 00:00:00' OR visible='1') AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('date_publication', 'ASC')
            ->getRows();

        return $result;
    }

    public function getTexte($id_texte, $id_conference)
    {
        $result = $this->database
            ->search('lw_conference_texte')
            ->addFilter("id_conference='$id_conference' AND id_texte='$id_texte' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('date_publication', 'ASC')
            ->getRow();

        return $result;
    }

    public function getCountTextesGroupByConference()
    {
        $result = $this->database
            ->search('lw_conference_texte')
            ->setColumns('id_conference, COUNT(*) as nb_textes')
            ->addFilter("id_client='" . $_SESSION['id_client'] . "'")
            ->setGroup('id_conference')
            ->getRows();

        return $result;
    }

    /********************************************************/
    /********************* AFFICHAGE ************************/
    /********************************************************/

    public function afficher_texte_byId($id_texte, $id_conference)
    {
        $output = '';

        $recup_texte = $this->getTexte($id_texte, $id_conference);
        if (!$recup_texte) {
            return '<div class="alert alert-danger">' . __('Erreur : cette notification n\'existe pas') . '</div>';
        }

        $output .= '<h2 class="texte-title">' . $recup_texte['titre'] . '</h2>';
        $output .= '<hr class="texte-hr">';

        $content = htmlspecialchars_decode(stripslashes($recup_texte['texte']));
        if (false !== strpos($content, '[[BOUTON')) {
            preg_match_all("/\[\[BOUTON(.*?)\]\]/", $content, $result);

            if ($result and $result[0] and isset($result[1]) and $result[1]) {
                foreach ($result[1] as $id_bouton) {
                    $id_bouton = trim($id_bouton);
                    $id_bouton = str_replace('ID=', '', $id_bouton);

                    if (is_numeric($id_bouton)) {
                        $form = eden()->Boutons()->getBoutonForm($id_bouton, false, 0, true);
                        $content = str_replace('[[BOUTON ID=' . $id_bouton . ']]', $form, $content);
                    }
                }
            }
        }

        $output .= $content;

        return $output;
    }

    public function afficher_textes_inconf($id_conference, $last_texteid)
    {
        $output = '';

        if ('undefined' == $last_texteid) {
            //last_texteid = undefined : aucun texte n'est affiché pour l'instant --> on recherche une publication de texte
            $texte_encours = $this->getTexteEnCours($id_conference);
            if (!$texte_encours) {
                return;
            }

            $content = htmlspecialchars_decode(stripslashes($texte_encours['texte']));

            if (false !== strpos($content, '[[BOUTON')) {
                preg_match_all("/\[\[BOUTON(.*?)\]\]/", $content, $result);

                if ($result and $result[0] and isset($result[1]) and $result[1]) {
                    //récupération de la page en cours
                    $id_page = 0;
                    $conference = eden()->Webinar_Conferences()->getConferenceById($id_conference);
                    if ($conference['id_page_inscription']) {
                        $id_page = $conference['id_page_inscription'];
                    }

                    foreach ($result[1] as $id_bouton) {
                        $id_bouton = trim($id_bouton);
                        $id_bouton = str_replace('ID=', '', $id_bouton);

                        if (is_numeric($id_bouton)) {
                            $form = eden()->Boutons()->getBoutonForm($id_bouton, true, $id_page, true);
                            $content = str_replace('[[BOUTON ID=' . $id_bouton . ']]', $form, $content);
                        }
                    }
                }
            }

            if (\Learnybox\Helpers\TextVarHelper::containsVar('LINK_SONDAGE', $content)) {
                $link_sondage = '';
                if (isset($_SESSION['user_random_id'])) {
                    $conference = eden()->Webinar_Conferences()->getConferenceById($id_conference);
                    $link_sondage = '<a href="' . Tools::getLink($conference['id_domaine'], 'conference', 'sondage', $conference['random_id'] . '/' . $_SESSION['user_random_id']) . '" class="btn btn-primary">' . __('Donner son avis') . '</a>';
                }
                $content = \Learnybox\Helpers\TextVarHelper::replaceVar('LINK_SONDAGE', $link_sondage, $content);
            }

            $output .= '<div class="conf_texte" id="' . $texte_encours['id_texte'] . '">' . $content . '</div>';
        } else {
            //un texte est en cours de publication --> on recherche si ce texte est désactivé
            $texte_encours = $this->getTexte($last_texteid, $id_conference);
            if (!$texte_encours) {
                $output .= 'Erreur';
            } elseif (!$texte_encours['visible']) {
                $output .= 'termine';
            }
        }

        return $output;
    }

    public function afficher_textes_inconf2($id_conference, $id_texte)
    {
        $output = '';

        $texte_encours = $this->getTexte($id_texte, $id_conference);
        if (!$texte_encours) {
            return;
        }
        if (!$texte_encours['visible']) {
            return;
        }

        $content = htmlspecialchars_decode(stripslashes($texte_encours['texte']));

        if (false !== strpos($content, '[[BOUTON')) {
            preg_match_all("/\[\[BOUTON(.*?)\]\]/", $content, $result);

            if ($result and $result[0] and isset($result[1]) and $result[1]) {
                //récupération de la page en cours
                $id_page = 0;
                $conference = eden()->Webinar_Conferences()->getConferenceById($id_conference);
                if ($conference['id_page_inscription']) {
                    $id_page = $conference['id_page_inscription'];
                }

                foreach ($result[1] as $id_bouton) {
                    $id_bouton = trim($id_bouton);
                    $id_bouton = str_replace('ID=', '', $id_bouton);

                    if (is_numeric($id_bouton)) {
                        $link = Tools::getLink($_SESSION['actual_domaine_id'], 'conference', 'bouton', $conference['random_id'] . '/' . $id_bouton);
                        $button = eden()->Boutons()->getBoutonForm($id_bouton, true, $id_page, true, $link);
                        $content = str_replace('[[BOUTON ID=' . $id_bouton . ']]', $button, $content);
                    }
                }
            }
        }

        if (\Learnybox\Helpers\TextVarHelper::containsVar('LINK_SONDAGE', $content)) {
            $conference = eden()->Webinar_Conferences()->getConferenceById($id_conference);
            $link = Tools::getLink($conference['id_domaine'], 'conference', 'sondage', $conference['random_id']);
            $linkSondage = '<a href="' . $link . '" class="btn btn-primary">' . __('Donner son avis') . '</a>';
            $content = \Learnybox\Helpers\TextVarHelper::replaceVar('LINK_SONDAGE', $linkSondage, $content);
        }

        $output .= '<div class="conf_texte" id="' . $texte_encours['id_texte'] . '">' . $content . '</div>';

        return $output;
    }

    public function admin_afficher_textes_inconf($id_conference, $in_conf = false)
    {
        $output = '';

        $textes = $this->getTextes($id_conference);
        if (!$textes) {
            if (!$in_conf) {
                return false;
            }

            return '<div class="alert alert-info">' . __('Aucune notification pour l\'instant') . '</div>';
        }

        $output .= '
			<table class="table table-striped table-condensed bootstrap-datatable">
			  <thead>
				  <tr>
					  <th>' . __('Texte') . '</th>
					  <th>' . __('Etat') . '</th>
					  <th class="number-position">' . __('Clics') . '</th>
					  <th></th>
				  </tr>
			  </thead>
			  <tbody>';

        foreach ($textes as $id => $_texte) {
            $output .= '<tr>';

            $output .= '<td><a href="#" onclick="AfficherTexte(' . $_texte['id_texte'] . ',' . $id_conference . '); return false;">' . $_texte['titre'] . '</a></td>';

            if (!$_texte['visible']) {
                $output .= '<td><span class="label label-status label-info">' . __('Désactivé') . '</span></td>';
            } else {
                $output .= '<td><span class="label label-status label-success">' . __('En cours') . '</span></td>';
            }

            //liens
            $liens = $this->getLiensByTexte($_texte['id_texte']);
            $clics = 0;
            if ($liens) {
                foreach ($liens as $id => $_lien) {
                    $clics += $_lien['clics'];
                }
            }
            $output .= '<td class="number-position">' . NumberHelper::formatNumber($clics) . '</td>';

            $output .= '<td id="texte_' . $_texte['id_texte'] . '">';

            $output .= '
            <div class="btn-group action">
                <a type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="dropdown-toggle ">
                    <i class="dropdown-icon fa-solid fa-ellipsis-vertical"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-right">
                <li><a href="#" onclick="AfficherTexte(' . $_texte['id_texte'] . ',' . $id_conference . '); return false;"><i class="fa-regular fa-eye"></i>' . __('Voir') . '</a></li>
                ';

            if (!$in_conf) {
                $output .= '<li><a href="' . RouterHelper::generate('app_conference_texte_edit', [
                        'conferenceId' => $id_conference,
                        'texteId' => $_texte['id_texte']
                    ]) . '"><i class="fa fa-edit"></i> ' . __('Modifier') . '</a></li>';
            }

            if (!$_texte['visible']) {
                $output .= '<li><a href="#" onclick="$(\'#activer' . $_texte['id_texte'] . '\').submit(); return false;"><i class="fa fa-power-off"></i> ' . __('Activer') . '</a></li>';
            } elseif ($_texte['visible']) {
                $output .= '<li><a href="#" onclick="$(\'#desactiver' . $_texte['id_texte'] . '\').submit(); return false;"><i class="fa fa-power-off"></i> ' . __('Désactiver') . '</a></li>';
            }

            if (!$_texte['visible']) {
                $output .= '<li><a href="#" class="js-modal-handler" data-method="createModal" data-title="' . __('Confirmation') . '" data-content="' . __('Êtes-vous sûr de vouloir supprimer cette notification ?') . '" data-action="$(\'#suppr' . $_texte['id_texte'] . '\').submit(); return false;" data-button="' . __('Supprimer cette notification') . '" data-type="error" data-blocker="' . __('Oui, je confirme la suppression.') . '"><i class="fa fa-trash-o"></i> ' . __('Supprimer') . '</a></li>';
            }

            $output .= '
                </ul>
            </div>';

            $output .= '</td>';
            $output .= '</tr>';

            $output .= '
            <form method="post" action="" id="activer' . $_texte['id_texte'] . '" style="display:none">
                [[CSRF]]
                <input type="hidden" name="form_action" value="lw_texte_activer" />
                <input type="hidden" name="id_conference" id="id_conference" value="' . $id_conference . '" />
                <input type="hidden" name="id_texte" id="id_texte" value="' . $_texte['id_texte'] . '" />
            </form>
    
            <form method="post" action="" id="desactiver' . $_texte['id_texte'] . '" style="display:none">
                [[CSRF]]
                <input type="hidden" name="form_action" value="lw_texte_desactiver" />
                <input type="hidden" name="id_conference" id="id_conference" value="' . $id_conference . '" />
                <input type="hidden" name="id_texte" id="id_texte" value="' . $_texte['id_texte'] . '" />
            </form>
    
            <form method="post" action="" id="suppr' . $_texte['id_texte'] . '" style="display:none">
                [[CSRF]]
                <input type="hidden" name="form_action" value="lw_texte_suppr" />
                <input type="hidden" name="id_conference" id="id_conference" value="' . $id_conference . '" />
                <input type="hidden" name="id_texte" id="id_texte" value="' . $_texte['id_texte'] . '" />
            </form>
            ';
        }

        $output .= '
		        </tbody>
		    </table>';

        return $output;
    }

    public function displayTextesInConf($idConference)
    {
        $return = [
            'status' => true,
            'textes' => '',
            'id_texte_encours' => 0
        ];

        $textes = $this->getTextes($idConference);
        if (!$textes) {
            $return['textes'] = '
            <div class="box-no-content">
                <div class="icon"><i class="nc-icon-coloured nc-icon-notification"></i></div>
                <div class="title"><h3>' . __('Pas encore de notifications ?') . '</h3></div>
                <div class="message">' . __('Créer une notification pour pouvoir l\'envoyer à vos auditeurs.') . '</div>
            </div>';
            return $return;
        }

        $output = '';
        foreach ($textes as $texte) {
            $label = 'label-default';
            $etat = __('En attente de diffusion');
            if ($texte['visible']) {
                $label = 'label-success';
                $etat = __('En cours de diffusion');
            }

            $output .= '
            <div class="texte" id="texte' . $texte['id_texte'] . '">
                <div class="texte-header">
                    <span class="label label-status ' . $label . '">' . $etat . '</span>
                    <div class="texte-action pull-right">
                        <button type="button" class="btn btn-secondary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="fa fa-ellipsis-h"></i></button>
                        <ul class="dropdown-menu dropdown-menu-right">';

            if (!$texte['visible']) {
                $output .= '<li><a href="#" onclick="publishTexte(' . $texte['id_texte'] . ',' . $idConference . '); return false;"><i class="nc-icon nc-icon-toggle-1"></i> ' . __('Publier cette notification') . '</a></li>';
                $output .= '<li><a href="#" onclick="deleteTexte(' . $texte['id_texte'] . ',' . $idConference . '); return false;"><i class="nc-icon nc-icon-trash"></i> ' . __('Supprimer cette notification') . '</a></li>';
            } else {
                $output .= '<li><a href="#" onclick="termineTexte(' . $texte['id_texte'] . ',' . $idConference . '); return false;"><i class="nc-icon nc-icon-toggle-1 nc-icon-flip-y"></i> ' . __('Désactiver cette notification') . '</a></li>';
            }

            $output .= '
                        </ul>
                    </div>
                </div>
                <div class="texte-title">
                    <h3>' . $texte['titre'] . '</h3>
                </div>
                <div class="texte-apercu">
                    ' . $this->afficher_texte_byId($texte['id_texte'], $idConference) . '
                </div>';

            //liens
            $nbClics = $this->getNbClics($texte['id_texte']);
            if ($nbClics !== false) {
                if ($texte['visible']) {
                    $return['id_texte_encours'] = $texte['id_texte'];
                }
                $output .= '<div class="texte-nb-clics">' . n__('%d clic sur le lien de cette notification', '%d clics sur le lien de cette notification', $nbClics, $nbClics) . '</div>';
            }

            $output .= '<hr>';
            $output .= '</div>';
        }

        $return['textes'] = $output;
        return $return;
    }

    /**
     * @param int $idConference
     * @return string
     */
    public function displayTextesInChatActions(int $idConference): string
    {
        $textes = $this->getTextes($idConference);
        if (!$textes) {
            return '';
        }

        $output = '';
        foreach ($textes as $texte) {
            $action = 'onclick="publishTexte(' . $texte['id_texte'] . ',' . $idConference . ');"';
            $checked = '';
            if ($texte['visible']) {
                $action = 'onclick="termineTexte(' . $texte['id_texte'] . ',' . $idConference . ');"';
                $checked = 'checked';
            }

            $output .= '
            <div class="chat-item chat-texte">
                <div class="chat-item-content" onclick="LoadTab(\'textes\');">
                    <p class="chat-item-title">' . $texte['titre'] . '</p>
                    <div class="chat-item-infos">
                        <p>' . __('Notification') . '</p>
                    </div>
                </div>
                <div class="chat-item-actions">
                    <div class="switch ' . ($texte['visible'] ? 'visible' : '') . '">
                        <input type="checkbox" id="active-chat-texte-' . $texte['id_texte'] . '" class="ios-toggle ios-toggle-round ios-toggle-red" ' . $checked . ' data-no-uniform="true" ' . $action . '>
                        <label for="active-chat-texte-' . $texte['id_texte'] . '" class="' . $checked . '"></label>
                    </div>
                </div>
            </div>';
        }

        return $output;
    }

    public function getNbClics($idTexte)
    {
        //liens
        $links = $this->getLiensByTexte($idTexte);
        if (!$links) {
            return false;
        }

        $nbClics = 0;
        foreach ($links as $link) {
            $nbClics += $link['clics'];
        }

        return $nbClics;
    }

    public function validatepost_texte_add($postarray)
    {
        //initialisation
        $validPost = true;
        $error = [];

        $id_conference = filter_var($postarray['id_conference'], FILTER_VALIDATE_INT);
        if (!$id_conference) {
            $validPost = false;
            $error[] = '<li>' . __("Erreur : le numéro de la conférence n'a pas été reçu") . '</li>';
        }

        if (isset($postarray['titre'])) {
            $titre = filter_var($postarray['titre'], FILTER_SANITIZE_SPECIAL_CHARS);
            if (!$titre) {
                $validPost = false;
                $error[] = '<li>' . __('Veuillez entrer un titre') . '</li>';
            }
        } else {
            $validPost = false;
            $error[] = '<li>' . __('Veuillez entrer un titre') . '</li>';
        }

        if (isset($_POST['texte'])) {
            $texte = filter_var($_POST['texte'], FILTER_UNSAFE_RAW);
            if (!$texte) {
                $validPost = false;
                $error[] = '<li>' . __('Veuillez entrer un texte') . '</li>';
            }
        } else {
            $validPost = false;
            $error[] = '<li>' . __('Veuillez entrer un texte') . '</li>';
        }

        return ['valid' => $validPost, 'message' => $error];
    }

    public function validatepost_texte_edit($postarray)
    {
        //initialisation
        $validPost = true;
        $error = [];

        $id_texte = filter_var($postarray['id_texte'], FILTER_VALIDATE_INT);
        if (!$id_texte) {
            $validPost = false;
            $error[] = '<li>' . __("Erreur : le numéro de la notification n'a pas été reçu") . '</li>';
        }

        $id_conference = filter_var($postarray['id_conference'], FILTER_VALIDATE_INT);
        if (!$id_conference) {
            $validPost = false;
            $error[] = '<li>' . __("Erreur : le numéro de la conférence n'a pas été reçu") . '</li>';
        }

        if (isset($postarray['titre'])) {
            $titre = filter_var($postarray['titre'], FILTER_SANITIZE_SPECIAL_CHARS);
            if (!$titre) {
                $validPost = false;
                $error[] = '<li>' . __('Veuillez entrer un titre') . '</li>';
            }
        } else {
            $validPost = false;
            $error[] = '<li>' . __('Veuillez entrer un titre') . '</li>';
        }

        if (isset($_POST['texte'])) {
            $texte = filter_var($_POST['texte'], FILTER_UNSAFE_RAW);
            if (!$texte) {
                $validPost = false;
                $error[] = '<li>' . __('Veuillez entrer un texte') . '</li>';
            }
        } else {
            $validPost = false;
            $error[] = '<li>' . __('Veuillez entrer un texte') . '</li>';
        }

        return ['valid' => $validPost, 'message' => $error];
    }

    public function insert_texte($cleaned_post)
    {
        $id_conference = filter_var($cleaned_post['id_conference'], FILTER_VALIDATE_INT);
        $titre = filter_var($cleaned_post['titre'], FILTER_SANITIZE_SPECIAL_CHARS);

        if (isset($_POST['texte'])) {
            $texte = filter_var($_POST['texte'], FILTER_UNSAFE_RAW);
        } else {
            $texte = filter_var($cleaned_post['texte'], FILTER_UNSAFE_RAW);
        }

        if (false !== strpos($texte, '<?')) {
            $texte = preg_replace('/\<(\?|\%)(.*)(\?|\%)\>/s', '', $texte);
        }

        $visible = 0;
        if (isset($cleaned_post['visible']) and $cleaned_post['visible']) {
            $visible = 1;
        }

        //insertion
        $array_insert = [
            'id_client' => $_SESSION['id_client'],
            'id_conference' => $id_conference,
            'titre' => addslashes($titre),
            'texte' => addslashes($texte),
            'visible' => $visible,
            'date' => date('Y-m-d H:i:s'),
        ];
        try {
            $this->database->insertRow('lw_conference_texte', $array_insert);
        } catch (Eden_Error $e) {
            \Learnybox\Services\Logger\LoggerService::log(\Monolog\Logger::ERROR, 'Erreur lors de l\'enregistrement de la notification : ' . $e->getMessage());
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de la notification")];
        }

        $idTexte = $this->database->getLastInsertedId();
        if (!$idTexte) {
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de la notification")];
        }

        //extraction des liens
        $extract_liens = $this->extract_liens($id_conference, $idTexte, $texte);
        if (!$extract_liens['valid']) {
            return ['valid' => false, 'message' => $extract_liens['message']];
        }

        //mise à jour du texte
        try {
            $this->database->updateRows('lw_conference_texte', ['texte' => addslashes($extract_liens['texte'])], "id_texte='$idTexte' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la mise à jour de la notification.')];
        }

        $event = ['eventName' => 'Conference Notification Created'];
        $this->integrationsEventsService->sendEvent('track', $event);

        if ($visible) {
            $this->publish_texte($idTexte, $id_conference);
        } else {
            $data['channel'] = 'conference-admin-' . $id_conference;
            $data['event'] = 'textes_updated';
            eden()->PusherEvents()->event($data);
        }

        return ['valid' => true];
    }

    public function update_texte($cleaned_post)
    {
        $id_texte = filter_var($cleaned_post['id_texte'], FILTER_VALIDATE_INT);
        $titre = filter_var($cleaned_post['titre'], FILTER_SANITIZE_SPECIAL_CHARS);
        $texte = filter_var($_POST['texte'], FILTER_UNSAFE_RAW);

        if (false !== strpos($texte, '<?')) {
            $texte = preg_replace('/\<(\?|\%)(.*)(\?|\%)\>/s', '', $texte);
        }

        //mise à jour
        $array_update = [
            'titre' => $titre,
            'texte' => addslashes($texte),
        ];

        try {
            $this->database->updateRows('lw_conference_texte', $array_update, "id_texte='$id_texte' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la mise à jour de la notification.')];
        }

        return ['valid' => true];
    }

    public function desactive_textes($id_conference, $id_client = false)
    {
        if (!$id_client) {
            $id_client = $_SESSION['id_client'];
        }

        try {
            $this->database->updateRows('lw_conference_texte', ['visible' => 0, 'date_publication_fin' => date('Y-m-d H:i:s')], "id_conference='$id_conference' AND visible='1' AND id_client='$id_client'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la désactivation de toutes les notifications.')];
        }

        return ['valid' => true];
    }

    public function desactive_texte_byId($id_texte, $id_conference)
    {
        try {
            $this->database->updateRows('lw_conference_texte', ['visible' => 0, 'date_publication_fin' => date('Y-m-d H:i:s')], "id_conference='$id_conference' AND id_texte='$id_texte' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la désactivation de toutes les notifications.')];
        }

        $data['channel'] = 'conference-' . $id_conference;
        $data['event'] = 'terminerTexte';
        eden()->PusherEvents()->event($data);

        $data['channel'] = 'conference-admin-' . $id_conference;
        $data['event'] = 'textes_updated';
        eden()->PusherEvents()->event($data);

        return ['valid' => true];
    }

    public function publish_texte($id_texte, $id_conference)
    {
        $texte_encours = $this->getTexteEnCours($id_conference);
        if ($texte_encours) {
            $desactive_texte = $this->desactive_texte_byId($texte_encours['id_texte'], $id_conference);
            if (!$desactive_texte['valid']) {
                return ['valid' => false, 'message' => $desactive_texte['message']];
            }
        }

        $texte = $this->getTexte($id_texte, $id_conference);
        if (!$texte) {
            return ['valid' => false, 'message' => __('Cette notification n\'existe pas.')];
        }

        $array_update = [
            'visible' => 1,
            'date_publication' => date('Y-m-d H:i:s'),
        ];

        try {
            $this->database->updateRows('lw_conference_texte', $array_update, "id_conference='$id_conference' AND id_texte='$id_texte' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la publication de la notification.')];
        }

        $output = $this->afficher_textes_inconf2($id_conference, $id_texte);
        $data['channel'] = 'conference-' . $id_conference;
        $data['event'] = 'displayTexte';
        $data['html'] = $output;
        eden()->PusherEvents()->event($data);

        $data['channel'] = 'conference-admin-' . $id_conference;
        $data['event'] = 'textes_updated';
        eden()->PusherEvents()->event($data);

        $html = '<div class="event"><hr><div class="content">' . __('Publication d\'une notification') . '</div><hr></div>';
        $htmlAdmin = '<div class="event-item"><fieldset><legend>' . __('Publication d\'une notification') . '</legend><div class="content">' . $texte['titre'] . '</div></fieldset></div>';

        $data['channel'] = 'conference-' . $id_conference;
        $data['messageAdmin'] = $htmlAdmin;
        $data['messageParticipant'] = $html;
        $data['user_id'] = 0;
        $data['event'] = 'insert_comment';
        eden()->PusherEvents()->event($data);

        return ['valid' => true];
    }

    public function delete_texte($id_texte, $id_conference)
    {
        $filter = [];
        $filter[] = ['id_texte=%s', $id_texte];
        $filter[] = ['id_conference=%s', $id_conference];
        $filter[] = ['id_client=%s', $_SESSION['id_client']];

        try {
            $this->database->deleteRows('lw_conference_texte', $filter);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la suppression de la notification.')];
        }

        $data['channel'] = 'conference-admin-' . $id_conference;
        $data['event'] = 'textes_updated';
        eden()->PusherEvents()->event($data);

        return ['valid' => true];
    }

    /********************************************************/
    /*********************** LIENS **************************/
    /********************************************************/

    public function getLiens($id_conference)
    {
        $result = $this->database
            ->search('lw_conference_liens')
            ->addFilter("id_conference='$id_conference' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('id_lien', 'ASC')
            ->getRows();

        return $result;
    }

    public function getLien($id_lien, $id_conference)
    {
        $result = $this->database
            ->search('lw_conference_liens')
            ->addFilter("id_conference='$id_conference' AND id_lien='$id_lien' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getRow();

        return $result;
    }

    public function getLienBanniereByConf($id_conference)
    {
        $result = $this->database
            ->search('lw_conference_liens')
            ->addFilter("id_conference='$id_conference' AND type='banniere' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getRow();

        return $result;
    }

    public function getLienByRandomId($random_id, $id_client = null)
    {
        if (null === $id_client) {
            $id_client = $_SESSION['id_client'];
        }

        $result = $this->database
            ->search('lw_conference_liens')
            ->addFilter("random_id='$random_id' AND id_client='$id_client'")
            ->getRow();

        return $result;
    }

    public function getLienByRandomIdWoClient($random_id)
    {
        $result = $this->database
            ->search('lw_conference_liens')
            ->addFilter("random_id='$random_id'")
            ->getRow();

        return $result;
    }

    public function getLiensByTexte($id_texte)
    {
        $result = $this->database
            ->search('lw_conference_liens')
            ->addFilter("id_texte='$id_texte' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getRows();

        return $result;
    }

    public function update_lien($id_conference, $type, $lien_random_id = false, $id_client = false)
    {
        if (!$id_client) {
            $id_client = $_SESSION['id_client'];
        }

        if ('banniere' == $type) {
            $link = $this->getLienBanniereByConf($id_conference);
            if (!$link) {
                return ['valid' => false, 'message' => __("Erreur : ce lien n'existe pas")];
            }

            $clics = $link['clics'];
            ++$clics;

            try {
                $this->database->updateRows('lw_conference_liens', ['clics' => $clics], "id_conference='$id_conference' AND type='banniere' AND id_client='$id_client'");
            } catch (Eden_Error $e) {
                return ['valid' => false, 'message' => __('Erreur lors de la mise à jour du lien.')];
            }
        } else {
            //récupération du lien
            $link = $this->getLienByRandomId($lien_random_id, $id_client);
            if (!$link) {
                return ['valid' => false, 'message' => __("Erreur : ce lien n'existe pas")];
            }

            $clics = $link['clics'];
            ++$clics;

            try {
                $this->database->updateRows('lw_conference_liens', ['clics' => $clics], "random_id='$lien_random_id' AND id_client='$id_client'");
            } catch (Eden_Error $e) {
                return ['valid' => false, 'message' => __('Erreur lors de la mise à jour du lien.')];
            }
        }

        return ['valid' => true];
    }

    public function extract_liens($id_conference, $id_texte, $texte)
    {
        //récupération de la conférence
        $conference = eden()->Webinar_Conferences()->getConferenceById($id_conference);

        //suppression de tous les liens du texte dans un premier temps
        $filter = [];
        $filter[] = ['id_conference=%s', $id_conference];
        $filter[] = ['id_texte=%s', $id_texte];
        $filter[] = ['id_client=%s', $_SESSION['id_client']];

        try {
            $this->database->deleteRows('lw_conference_liens', $filter);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la suppression des liens.')];
        }

        $texte = stripslashes($texte);
        preg_match_all('#<a(.*?)href="(.*?)"(.*?)>(.*?)</a>#', stripslashes($texte), $resultats, PREG_PATTERN_ORDER);

        if ($resultats and isset($resultats[0]) and $resultats[0]) {
            $nb_resultats = count($resultats[0]);
            $array_resultats = [];

            for ($i = 0; $i < $nb_resultats; ++$i) {
                $str = $resultats[0][$i];
                $link = $resultats[2][$i];
                $texte_link = $resultats[4][$i];

                $array_resultats[$i] = ['string' => $str, 'lien' => $link, 'texte' => $texte_link];
            }

            //vérification du tableau
            foreach ($array_resultats as $id => $_lien) {
                $pos = strpos($_lien['lien'], '/conference/link/');
                if (false !== $pos) {
                    unset($array_resultats[$id]);
                }
            }

            //virer les occurences en doubles
            $array_resultats_unique = array_map('unserialize', array_unique(array_map('serialize', $array_resultats)));

            foreach ($array_resultats_unique as $id => $_lien) {
                //random_id
                $valid_random_id = false;
                while (!$valid_random_id) {
                    $random_id_lien = createRandomID();
                    $verif = $this->getLienByRandomId($random_id_lien);
                    if (!$verif) {
                        $valid_random_id = true;
                    }
                }

                //enregistrement en base de données
                $array_insert = [
                    'id_client' => $_SESSION['id_client'],
                    'id_conference' => $id_conference,
                    'id_texte' => $id_texte,
                    'type' => 'texte',
                    'lien' => $_lien['lien'],
                    'clics' => '0',
                    'random_id' => $random_id_lien,
                ];

                try {
                    $this->database->insertRow('lw_conference_liens', $array_insert);
                } catch (Eden_Error $e) {
                    return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement du lien")];
                }

                $insert = $this->database->getLastInsertedId();
                if (!$insert) {
                    return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement du lien")];
                }

                $array_resultats_unique[$id]['id'] = $insert;
                $array_resultats_unique[$id]['random_id'] = $random_id_lien;
            }

            //préparation du texte
            $new_texte = $texte;

            foreach ($array_resultats as $id => $_lien) {
                //recherche de l'id du lien dans le tableau $array_unique
                foreach ($array_resultats_unique as $unique_id => $unique_lien) {
                    if ($unique_lien['lien'] == $_lien['lien']) {
                        $search_id = $unique_lien['random_id'];
                    }
                }
                $new_lien = '<a href="' . Tools::getLink($conference['id_domaine'], 'conference', 'link', $conference['random_id'] . '/' . $search_id) . '" target="_blank">' . $_lien['texte'] . '</a>';
                $new_texte = str_replace($_lien['string'], $new_lien, $new_texte);
            }

            return ['valid' => true, 'texte' => $new_texte];
        }

        return ['valid' => true, 'texte' => $texte];
    }

    public function delete_lien($id_lien, $id_conference)
    {
        $filter = [];
        $filter[] = ['id_texte=%s', $id_texte];
        $filter[] = ['id_conference=%s', $id_conference];
        $filter[] = ['id_client=%s', $_SESSION['id_client']];

        try {
            $this->database->deleteRows('lw_conference_liens', $filter);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la suppression du lien.')];
        }

        return ['valid' => true];
    }

    /********************************************************/
    /******************* TEXTES PRÉDÉFINIS ******************/
    /********************************************************/

    public function getTextePredefiniById($id_texte_predefini)
    {
        $result = $this->database
            ->search('lw_texte_predefini')
            ->addFilter("id_texte='$id_texte_predefini' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getRow();

        return $result;
    }

    public function getTextesPredefinis()
    {
        $result = $this->database
            ->search('lw_texte_predefini')
            ->addFilter("id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('id_texte', 'DESC')
            ->getRows();

        return $result;
    }

    public function validatepost_texte_predefini_add($postarray)
    {
        //initialisation
        $validPost = true;
        $errorString = '';

        $titre = filter_var($postarray['titre'], FILTER_SANITIZE_SPECIAL_CHARS);
        $texte = filter_var($_POST['texte'], FILTER_UNSAFE_RAW);

        $error = '';

        if (!$titre) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez entrer un titre') . '</li>';
        }

        if (!$texte) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez entrer un texte') . '</li>';
        }

        return ['valid' => $validPost, 'message' => $errorString];
    }

    public function validatepost_texte_predefini_edit($postarray)
    {
        //initialisation
        $validPost = true;
        $errorString = '';

        $id_texte_predefini = filter_var($postarray['id_texte_predefini'], FILTER_VALIDATE_INT);
        $titre = filter_var($postarray['titre'], FILTER_SANITIZE_SPECIAL_CHARS);
        $texte = filter_var($_POST['texte'], FILTER_UNSAFE_RAW);

        $error = '';

        if (!$id_texte_predefini) {
            $validPost = false;
            $errorString .= '<li>' . __("Erreur : le numéro de la notification n'a pas été reçu") . '</li>';
        } else {
            //vérification que ce club existe
            $recup_texte_predefini = $this->getTextePredefiniById($id_texte_predefini);
            if (!$recup_texte_predefini) {
                $validPost = false;
                $errorString .= '<li>' . __("Erreur : cette notification prédéfinie n'existe pas") . '</li>';
            }
        }

        if (!$titre) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez entrer un titre') . '</li>';
        }
        if (!$texte) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez entrer un texte') . '</li>';
        }

        return ['valid' => $validPost, 'message' => $errorString];
    }

    public function insert_texte_predefini($cleaned_post)
    {
        $titre = filter_var($cleaned_post['titre'], FILTER_SANITIZE_SPECIAL_CHARS);
        $texte = filter_var($_POST['texte'], FILTER_UNSAFE_RAW);

        if (false !== strpos($texte, '<?')) {
            $texte = preg_replace('/\<(\?|\%)(.*)(\?|\%)\>/s', '', $texte);
        }

        //insertion
        $array_insert = [
            'id_client' => $_SESSION['id_client'],
            'titre' => $titre,
            'texte' => addslashes($texte),
        ];
        try {
            $this->database->insertRow('lw_texte_predefini', $array_insert);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de la notification prédéfinie")];
        }

        return ['valid' => true];
    }

    public function update_texte_predefini($cleaned_post)
    {
        $titre = filter_var($cleaned_post['titre'], FILTER_SANITIZE_SPECIAL_CHARS);
        $texte = filter_var($_POST['texte'], FILTER_UNSAFE_RAW);

        if (false !== strpos($texte, '<?')) {
            $texte = preg_replace('/\<(\?|\%)(.*)(\?|\%)\>/s', '', $texte);
        }

        //mise à jour
        $array_update = [
            'titre' => $titre,
            'texte' => addslashes($texte),
        ];

        try {
            $this->database->updateRows('lw_texte_predefini', $array_update, "id_texte='" . $cleaned_post['id_texte_predefini'] . "' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la mise à jour de la notification prédéfinie.')];
        }

        return ['valid' => true];
    }

    public function delete_texte_predefini($id_texte_predefini)
    {
        $filter = [];
        $filter[] = ['id_texte=%s', $id_texte_predefini];
        $filter[] = ['id_client=%s', $_SESSION['id_client']];

        try {
            $this->database->deleteRows('lw_texte_predefini', $filter);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la suppression de la notification prédéfinie.')];
        }

        return ['valid' => true];
    }

    public function afficher_texte_predefini_byId($id_texte_predefini)
    {
        $texte = $this->getTextePredefiniById($id_texte_predefini);
        if (!$texte) {
            return '<div class="alert alert-danger">' . __('Erreur : cette notification n\'existe pas') . '</div>';
        }

        $content = $texte['texte'];
        if (false !== strpos($content, '[[BOUTON')) {
            preg_match_all("/\[\[BOUTON(.*?)\]\]/", $content, $result);

            if ($result and $result[0] and isset($result[1]) and $result[1]) {
                foreach ($result[1] as $id_bouton) {
                    $id_bouton = trim($id_bouton);
                    $id_bouton = str_replace('ID=', '', $id_bouton);

                    if (is_numeric($id_bouton)) {
                        $form = eden()->Boutons()->getBoutonForm($id_bouton);
                        $content = str_replace('[[BOUTON ID=' . $id_bouton . ']]', $form, $content);
                    }
                }
            }
        }

        $output = '
		    <h2>' . $texte['titre'] . '</h2>
		    <hr />' .
            $content;

        return $output;
    }

    public function afficher_textes_predefinis_inconf($id_conference)
    {
        $output = '';

        $textes_predefinis = $this->getTextesPredefinis();
        if (!$textes_predefinis) {
            return '<div class="alert alert-info">' . __('Aucune notification prédéfinie') . '</div>';
        }

        $output .= '
			<table class="table table-striped table-condensed bootstrap-datatable">
    			<tbody>';

        foreach ($textes_predefinis as $id => $_texte_predefini) {
            $output .= '
			    <tr>
			        <td>' . $_texte_predefini['titre'] . '</td>
                    <td id="texte_predefini_' . $_texte_predefini['id_texte'] . '">
                        <div class="btn-group">
                            <button class="btn btn-secondary btn-small" onclick="transformTextePredefini(' . $_texte_predefini['id_texte'] . ',' . $id_conference . ', false); return false;">' . __('Importer') . '</button>
                            <button type="button" class="btn btn-secondary btn-small dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                <li><a onclick="transformTextePredefini(' . $_texte_predefini['id_texte'] . ',' . $id_conference . ', true); return false;"><i class="fa fa-share"></i> ' . __('Publier') . '</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>';
        }

        $output .= '
			    </tbody>
            </table>';

        return $output;
    }

    public function transform_texte_predefini($id_texte_predefini, $id_conference, $publish = false)
    {
        $conference = eden()->Webinar_Conferences()->getConferenceById($id_conference);
        if (!$conference) {
            return ['valid' => false, 'message' => __('Erreur lors de la récupération de la conférence.')];
        }

        $texte_predefini = $this->getTextePredefiniById($id_texte_predefini);
        if (!$texte_predefini) {
            return ['valid' => false, 'message' => __("Erreur : cette notification prédéfinie n'existe pas.")];
        }

        $visible = 0;
        if ($publish) {
            //desactivation des textes en cours
            $visible = 1;
            $desactive_textes = $this->desactive_textes($id_conference);
        }

        $array_insert = [
            'id_client' => $_SESSION['id_client'],
            'id_conference' => $id_conference,
            'titre' => $texte_predefini['titre'],
            'texte' => addslashes($texte_predefini['texte']),
            'visible' => $visible,
            'date' => date('Y-m-d H:i:s'),
        ];

        try {
            $this->database->insertRow('lw_conference_texte', $array_insert);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de la notification")];
        }

        $lastId = $this->database->getLastInsertedId();
        if (!$lastId) {
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de la notification")];
        }

        //extraction des liens
        $extract_liens = $this->extract_liens($id_conference, $lastId, $texte_predefini['texte']);
        if (!$extract_liens['valid']) {
            return ['valid' => false, 'message' => $extract_liens['message']];
        }

        //mise à jour du texte
        try {
            $this->database->updateRows('lw_conference_texte', ['texte' => addslashes($extract_liens['texte'])], "id_texte='$lastId' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la mise à jour de la notification.')];
        }

        return ['valid' => true, 'id_texte' => $lastId];
    }
}
