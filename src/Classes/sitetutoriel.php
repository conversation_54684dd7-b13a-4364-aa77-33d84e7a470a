<?php

use Learnybox\Services\Integration\IntegrationsEventsService;

class SiteT<PERSON>riel extends Eden_Class
{
    private $database;

    /**
     * @var array
     */
    private $str_pages;

    /**
     * @var array
     */
    private $str_widgets;

    /**
     * @var IntegrationsEventsService
     */
    private $integrationsEventsService;

    public function __construct(IntegrationsEventsService $integrationsEventsService)
    {
        $this->database = MySQL::getInstance();
        $this->integrationsEventsService = $integrationsEventsService;

        $this->str_pages = [
            'contact' => __('Contact'),
            'articles' => __('Blog'),
            'formation' => __('Plateforme de formation'),
        ];

        $this->str_widgets = [];
        $this->str_widgets['social'] = ['titre' => __('Réseaux Sociaux'), 'icone' => 'fa-facebook'];
        $this->str_widgets['search'] = ['titre' => __('Barre de recherche')];
        $this->str_widgets['last_articles'] = ['titre' => __('Derniers Articles'), 'displayTitle' => 1, 'box' => true];
        $this->str_widgets['top_articles'] = ['titre' => __('Articles les plus consultés'), 'displayTitle' => 1, 'box' => true];
        $this->str_widgets['last_comments'] = ['titre' => __('Derniers commentaires'), 'displayTitle' => 1, 'box' => true];
        $this->str_widgets['categories'] = ['titre' => __('Catégories'), 'displayTitle' => 1, 'box' => true];
        $this->str_widgets['tags'] = ['titre' => __('Tags'), 'displayTitle' => 1, 'box' => true];
    }

    /********************************************************/
    /********************** TUTORIEL ************************/
    /********************************************************/

    public function insert_step($cleaned_post)
    {
        if (!isset($_SESSION['site_tutoriel'])) {
            $_SESSION['site_tutoriel'] = [];
        }

        $step = filter_var($cleaned_post['step'], FILTER_VALIDATE_INT);
        if (!$step) {
            return ['valid' => false, 'message' => __('Une erreur est survenue')];
        }

        $insert_step = $this->{'insert_step_' . $step}($cleaned_post);
        if (!$insert_step['valid']) {
            return ['valid' => false, 'message' => $insert_step['message']];
        }

        return ['valid' => true];
    }

    public function insert_step_1($cleaned_post)
    {
        $specificTheme = '';
        if (isset($cleaned_post['specific_theme'])) {
            $specificTheme = filter_var($cleaned_post['specific_theme'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        $masterTheme = '';
        if (isset($cleaned_post['master_theme'])) {
            $masterTheme = filter_var($cleaned_post['master_theme'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        if (!$specificTheme || !$masterTheme) {
            return ['valid' => false, 'message' => __('Veuillez sélectionner un thème')];
        }

        $_SESSION['site_tutoriel']['specific_theme'] = $specificTheme;
        $_SESSION['site_tutoriel']['master_theme'] = $masterTheme;

        $theme = eden()->Builder_Themes()->getThemeByMasterTheme($masterTheme, $specificTheme);
        if (!$theme) {
            return ['valid' => false, 'message' => __('Erreur : ce thème n\'existe pas')];
        }

        $themeDatas = [
            'theme' => 'focusweb',
            'id_theme' => $theme['id_theme'],
        ];

        $update = eden()->Themes()->app_theme_active($themeDatas);
        if (!$update['valid']) {
            return ['valid' => false, 'message' => $update['message']];
        }

        return ['valid' => true];
    }

    public function insert_step_2($cleaned_post)
    {
        $attachments = [];
        $logo = '';

        if (isset($cleaned_post['attachments'])) {
            $attachments = filter_var($cleaned_post['attachments'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
            if ($attachments) {
                $attachment = array_shift($attachments);

                //copie du fichier
                $s3Service = new \Learnybox\Services\Aws\S3\MediasService();
                $getFile = $s3Service->getFile('upload/' . $attachment);
                if (!$getFile['valid']) {
                    return ['valid' => false, 'message' => __('Une erreur est survenue lors de la récupération de votre fichier.')];
                }

                $uploadFile = $s3Service->uploadFileContents($getFile['file'], $_SESSION['client_uniqid'] . '/' . $attachment, ['ContentType' => $getFile['content-type']]);
                if (!$uploadFile['valid']) {
                    return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement du fichier, l'administrateur a été prévenu.")];
                }

                $logo = $uploadFile['url'];
            }
        }

        if (!$logo and isset($cleaned_post['logo']) and $cleaned_post['logo']) {
            $logo = filter_var($cleaned_post['logo'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        if ($logo) {
            $update = eden()->Reglages()->updateParametreByName('site_logo', $logo);
            if (!$update['valid']) {
                return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement des paramètres") . ' : ' . $update['message']];
            }
        }

        return ['valid' => true];
    }

    public function insert_step_3($cleaned_post)
    {
        $menu = [];
        if (isset($cleaned_post['elements'])) {
            $menu = filter_var($cleaned_post['elements'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
        }

        if ($menu) {
            foreach ($menu as $_menu) {
                if ('accueil' == $_menu) {
                    $id_page_accueil = 0;

                    $page = eden()->Reglages()->appGetParametreByName('site_index');
                    if ($page) {
                        $id_page_accueil = $page['value'];
                    }
                    if (!$id_page_accueil) {
                        $page = eden()->Pages()->getPageByPermalink('index');
                        if ($page) {
                            $id_page_accueil = $page['id_page'];
                        }
                    }

                    if ($id_page_accueil) {
                        $element = eden()->Menu()->getMenuElementByIdPage($id_page_accueil);
                        if (!$element) {
                            $array_insert = [
                                'displayName' => __('Accueil'),
                                'type' => 'page',
                                'icone' => 'fa-home',
                                'active' => 1,
                                'parent' => 0,
                                'element' => $id_page_accueil,
                                'param' => '',
                            ];

                            $insert = eden()->Menu()->insert_menu($array_insert);
                            if (!$insert['valid']) {
                                return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement des paramètres") . ' : ' . $insert['message']];
                            }
                        }
                    }
                } else {
                    $element = eden()->Menu()->getMenuElementByIdPage($_menu);
                    if (!$element) {
                        $array_insert = [
                            'displayName' => $this->str_pages[$_menu],
                            'type' => 'page',
                            'icone' => '',
                            'active' => 1,
                            'parent' => 0,
                            'element' => $_menu,
                            'param' => '',
                        ];

                        $insert = eden()->Menu()->insert_menu($array_insert);
                        if (!$insert['valid']) {
                            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement des paramètres") . ' : ' . $insert['message']];
                        }
                    }
                }
            }
        }

        return ['valid' => true];
    }

    public function insert_step_4($cleaned_post)
    {
        $nb_articles = 0;

        if (isset($cleaned_post['nb_articles'])) {
            $nb_articles = filter_var($cleaned_post['nb_articles'], FILTER_VALIDATE_INT);
        }

        if ($nb_articles) {
            if ($nb_articles > 10) {
                $nb_articles = 10;
            }

            for ($i = 1; $i <= $nb_articles; ++$i) {
                $array_insert = [
                    'title' => __('Mon article') . ' ' . $i,
                    'theme' => "8-91-61"
                ];

                $insert = eden()->Builder_Articles()->insert($array_insert);
                if (!$insert['valid']) {
                    return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de l'article") . ' : ' . $insert['message']];
                }
            }
        }

        return ['valid' => true];
    }

    public function insert_step_5($cleaned_post)
    {
        $widgets = [];
        if (isset($cleaned_post['elements'])) {
            $widgets = filter_var($cleaned_post['elements'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
        }

        if ($widgets) {
            foreach ($widgets as $widget) {
                $element = eden()->Sidebar()->getSidebarElementsByType($widget);
                if (!$element) {
                    $array_insert = [
                        'displayName' => $this->str_widgets[$widget]['titre'],
                        'type' => $widget,
                        'active' => 1,
                    ];

                    if (isset($this->str_widgets['displayTitle'])) {
                        $array_insert['displayTitle'] = 1;
                    }
                    if (isset($this->str_widgets['box'])) {
                        $array_insert['box'] = 1;
                    }
                    if (isset($this->str_widgets['icone'])) {
                        $array_insert['icone'] = $this->str_widgets['icone'];
                    }

                    $insert = eden()->Sidebar()->insert_sidebar($array_insert);
                    if (!$insert['valid']) {
                        return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement des paramètres") . ' : ' . $insert['message']];
                    }
                }
            }
        }

        return ['valid' => true];
    }

    public function insert_step_6($cleaned_post)
    {
        //footer
        $footer = eden()->Reglages()->appGetParametreByName('footer');
        if (!$footer) {
            $footerValue = ['affiliation_site' => 0, 'affiliation_formation' => 0];
        } else {
            $footerValue = json_decode($footer['value'], true);
        }

        $footerValue['nb_columns'] = filter_var($cleaned_post['footer_nbcolumns'], FILTER_VALIDATE_INT);
        $footerValue['column1'] = filter_input(INPUT_POST, 'footer_column1', FILTER_UNSAFE_RAW);
        $footerValue['column2'] = filter_input(INPUT_POST, 'footer_column2', FILTER_UNSAFE_RAW);
        $footerValue['column3'] = filter_input(INPUT_POST, 'footer_column3', FILTER_UNSAFE_RAW);

        $newFooterValue = json_encode($footerValue);

        $update = eden()->Reglages()->updateParametreByName('footer', $newFooterValue);
        if (!$update['valid']) {
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement des paramètres") . ' : ' . $update['message']];
        }

        //call segment.io
        $theme = (defined('THEME') and THEME) ? THEME : '';
        $id_theme = (defined('ID_THEME') and ID_THEME) ? ID_THEME : 0;
        if ($theme and $theme == 'focusweb' and $id_theme) {
            $active_theme = eden()->Builder_Themes()->getThemeById($id_theme);
            if ($active_theme) {
                $master_theme = eden()->Builder_MasterThemes()->getMasterThemeById($active_theme['id_master_theme']);
                if ($master_theme) {
                    $theme = $master_theme['link'] . '-' . $active_theme['link'];
                }
            }
        }

        $articles = eden()->Articles()->getAllArticles();
        $sidebar = eden()->Sidebar()->getSidebar();
        if ($sidebar) {
            $sidebar = array_change_key($sidebar, 'type');
        }

        $event = [
            'eventName' => 'Site Launched',
            'SiteThemeID' => $theme,
            'Blog' => ($articles ? true : false),
            'Widgets' => ($sidebar ? implode(',', array_keys($sidebar)) : ''),
        ];
        $this->integrationsEventsService->sendEvent('track', $event);

        return ['valid' => true];
    }
}
