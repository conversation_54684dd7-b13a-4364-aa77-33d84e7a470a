<?php

use Learnybox\Entity\User\Rgpd\UserRgpd;

/**
 * Tabs class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Tabs extends Eden_Class
{
    private $tabs = [];

    public function __construct()
    {
        $this->tabs = [
            'videoplayer' => [
                ['id' => 'videoplayer', 'icone' => 'video-camera', 'nom' => __('Vidéo')],
                ['id' => 'chapters', 'icone' => 'book', 'nom' => __('Chapitres')],
            ],
            'mp3player' => [
                ['id' => 'mp3player', 'icone' => 'volume-up', 'nom' => __('Fichier audio')],
            ],
            'txt' => [
                ['id' => 'txt', 'icone' => 'file-text', 'nom' => __('Texte')],
            ],
            'optin' => [
                ['id' => 'html', 'icone' => 'user-plus', 'nom' => __('Formulaire')],
                ['id' => 'champs', 'icone' => 'list-alt', 'nom' => __('Champs')],
                ['id' => 'button', 'icone' => 'square', 'nom' => __('Bouton')],
                ['id' => 'theme', 'icone' => 'picture-o', 'nom' => __('Thème')],
                ['id' => 'redirection', 'icone' => 'sign-out', 'nom' => __('Redirection')],
                ['id' => 'champs_additionnels', 'icone' => 'list-alt', 'nom' => __('Champs additionnels')],
            ],
            'optin_field' => [
                ['id' => 'champs', 'icone' => 'square-o', 'nom' => __('Champs')],
                ['id' => 'theme', 'icone' => 'picture-o', 'nom' => __('Thème')],
            ],
            'optin_custom_field' => [
                ['id' => 'champs', 'icone' => 'square-o', 'nom' => __('Champs')],
                ['id' => 'theme', 'icone' => 'picture-o', 'nom' => __('Thème')],
            ],
            'html' => [
                ['id' => 'html', 'icone' => 'code', 'nom' => __('HTML')],
            ],
            'tip' => [
                ['id' => 'tip', 'icone' => 'file-text', 'nom' => __('Texte')],
            ],
            'image' => [
                ['id' => 'image', 'icone' => 'picture-o', 'nom' => __('Image')],
            ],
            'image-lightbox' => [
                ['id' => 'image', 'icone' => 'picture-o', 'nom' => __('Image')],
            ],
            'box' => [
                ['id' => 'box', 'icone' => 'inbox', 'nom' => __('Apparence')],
                ['id' => 'popup', 'icone' => 'square', 'nom' => __('Popup')],
            ],
            'downloads' => [
                ['id' => 'downloads', 'icone' => 'download', 'nom' => __('Téléchargement')],
            ],
            'link' => [
                ['id' => 'link', 'icone' => 'download', 'nom' => __('Lien')],
            ],
            'button_link' => [
                ['id' => 'button_link', 'icone' => 'download', 'nom' => __('Lien')],
                ['id' => 'button', 'icone' => 'square-o', 'nom' => __('Bouton')],
                ['id' => 'button_actions', 'icone' => 'mouse-pointer', 'nom' => __('Actions au clic')],
            ],
            'button_link_rgpd' => [
                ['id' => 'button_link', 'icone' => 'download', 'nom' => __('Lien')],
                ['id' => 'button', 'icone' => 'square-o', 'nom' => __('Bouton')],
            ],
            'button_checkout' => [
                ['id' => 'button_checkout', 'icone' => 'money', 'nom' => __('Paiement')],
                ['id' => 'button', 'icone' => 'square', 'nom' => __('Bouton')],
                ['id' => 'button_predefini', 'icone' => 'square-o', 'nom' => __('Boutons Prédéfinis')],
            ],
            'button_popup_version' => [
                ['id' => 'button_version', 'icone' => 'exchange', 'nom' => __('Version')],
                ['id' => 'button', 'icone' => 'square-o', 'nom' => __('Bouton')],
            ],
            'formulaire_paiement' => [
                ['id' => 'formulaire_paiement', 'icone' => 'list-alt', 'nom' => __('Formulaire')],
            ],
            'social_share' => [
                ['id' => 'social_apparence', 'icone' => 'picture-o', 'nom' => __('Apparence')],
                ['id' => 'facebook', 'icone' => 'facebook-square', 'nom' => __('Facebook')],
                ['id' => 'twitter', 'icone' => 'twitter-square', 'nom' => __('Twitter')],
                ['id' => 'googleplus', 'icone' => 'google-plus-square', 'nom' => __('GooglePlus')],
                ['id' => 'linkedin', 'icone' => 'linkedin-square', 'nom' => __('LinkedIn')],
            ],
            'lbar_social_share' => [
                ['id' => 'social_apparence', 'icone' => 'picture-o', 'nom' => __('Apparence')],
                ['id' => 'facebook', 'icone' => 'facebook-square', 'nom' => __('Facebook')],
                ['id' => 'twitter', 'icone' => 'twitter-square', 'nom' => __('Twitter')],
                ['id' => 'googleplus', 'icone' => 'google-plus-square', 'nom' => __('GooglePlus')],
                ['id' => 'linkedin', 'icone' => 'linkedin-square', 'nom' => __('LinkedIn')],
                ['id' => 'pinterest', 'icone' => 'pinterest-square', 'nom' => __('Pinterest')],
            ],
            'lbar_share_email' => [
                ['id' => 'social_apparence', 'icone' => 'picture-o', 'nom' => __('Apparence')],
                ['id' => 'facebook', 'icone' => 'facebook-square', 'nom' => __('Facebook')],
                ['id' => 'twitter', 'icone' => 'twitter-square', 'nom' => __('Twitter')],
                ['id' => 'googleplus', 'icone' => 'google-plus-square', 'nom' => __('GooglePlus')],
                ['id' => 'linkedin', 'icone' => 'linkedin-square', 'nom' => __('LinkedIn')],
                ['id' => 'pinterest', 'icone' => 'pinterest-square', 'nom' => __('Pinterest')],
            ],
            'funnel_menu' => [
                ['id' => 'funnel_menu', 'icone' => 'picture-o', 'nom' => __('Apparence')],
                ['id' => 'funnel_pages', 'icone' => 'file-text', 'nom' => __('Pages')],
            ],
            'fa-icon' => [
                ['id' => 'icon', 'icone' => 'flag', 'nom' => __('Icône')],
            ],
            'bullets' => [
                ['id' => 'bullets', 'icone' => 'check', 'nom' => __('Liste')],
                ['id' => 'font', 'icone' => 'file-text', 'nom' => __('Police')],
            ],
            'faq' => [
                ['id' => 'faq', 'icone' => 'question-circle', 'nom' => __('FAQ')],
                ['id' => 'font', 'icone' => 'file-text', 'nom' => __('Police')],
            ],
            'progressbar' => [
                ['id' => 'progressbar', 'icone' => 'bar', 'nom' => __('Barre de progression')],
            ],
            'formation_form' => [
                ['id' => 'formation_form', 'icone' => 'user-plus', 'nom' => __('Formation')],
                ['id' => 'button', 'icone' => 'square', 'nom' => __('Bouton')],
            ],
            'conference_optin' => [
                ['id' => 'conference_optin', 'icone' => 'user-plus', 'nom' => __('Webinaire live')],
                ['id' => 'button', 'icone' => 'square', 'nom' => __('Bouton')],
                ['id' => 'theme', 'icone' => 'picture-o', 'nom' => __('Thème')],
            ],
            'conference_liste' => [
                ['id' => 'conflist', 'icone' => 'cogs', 'nom' => __('Réglages')],
                ['id' => 'conflist_conferences', 'icone' => 'video-camera', 'nom' => __('Webinaire live')],
                ['id' => 'button', 'icone' => 'square', 'nom' => __('Bouton')],
            ],
            'conference_calendar' => [
                ['id' => 'conference_calendar', 'icone' => 'calendar', 'nom' => __('Webinaire live')],
            ],
            'formationshop' => [
                ['id' => 'fshop', 'icone' => 'cogs', 'nom' => __('Réglages')],
                ['id' => 'fshop_formations', 'icone' => 'book', 'nom' => __('Formations')],
                ['id' => 'button', 'icone' => 'square', 'nom' => __('Bouton')],
            ],
            'sendtoafriend' => [
                ['id' => 'sendtoafriend', 'icone' => 'share', 'nom' => __('Email')],
                ['id' => 'button', 'icone' => 'square', 'nom' => __('Bouton')],
            ],
            'temoignages' => [
                ['id' => 'temoignage', 'icone' => 'user', 'nom' => __('Témoignage')],
                ['id' => 'theme', 'icone' => 'picture-o', 'nom' => __('Thème')],
            ],
            'module' => [
                ['id' => 'module', 'icone' => 'cog', 'nom' => __('Module')],
            ],
            'contact_form' => [
                ['id' => 'contact', 'icone' => 'file-text', 'nom' => __('Sujets')],
                ['id' => 'reponse', 'icone' => 'paper-plane', 'nom' => __('Réponse automatique')],
                ['id' => 'button', 'icone' => 'square', 'nom' => __('Bouton')],
                ['id' => 'rgpd', 'icone' => 'user-plus', 'nom' => UserRgpd::getI18nLibelles('user_rgpd.builder.contact_form.title')],
            ],
            'articles' => [
                ['id' => 'articles', 'icone' => 'file-text', 'nom' => __('Articles')],
                ['id' => 'button', 'icone' => 'square', 'nom' => __('Bouton')],
            ],
            'article_rss' => [
                ['id' => 'theme', 'icone' => 'picture-o', 'nom' => __('Thème')],
                ['id' => 'title', 'icone' => 'file-text', 'nom' => __('Titre')],
                ['id' => 'description', 'icone' => 'list-alt', 'nom' => __('Description')],
                ['id' => 'image', 'icone' => 'picture-o', 'nom' => __('Image')],
                ['id' => 'button', 'icone' => 'square', 'nom' => __('Bouton')],
            ],
            'everwebinar_optin' => [
                ['id' => 'webinaire_optin', 'icone' => 'user-plus', 'nom' => __('Webinaire')],
                ['id' => 'button', 'icone' => 'square', 'nom' => __('Bouton')],
                ['id' => 'theme', 'icone' => 'picture-o', 'nom' => __('Thème')],
            ],
            'blogsearch' => [
                ['id' => 'button', 'icone' => 'square-o', 'nom' => __('Bouton')],
            ],
            'connexion_form' => [
                ['id' => 'connexion_form', 'icone' => 'user-plus', 'nom' => __('Formulaire de connexion')],
                ['id' => 'button', 'icone' => 'square', 'nom' => __('Bouton')],
            ],
        ];
    }

    /**
     * getTabs function.
     *
     * @return array
     */
    public function getAllTabs()
    {
        return $this->tabs;
    }

    /**
     * getTabs function.
     *
     * @param string $element
     *
     * @return array
     */
    public function getTabs($element = '')
    {
        if (isset($this->tabs[$element])) {
            return $this->tabs[$element];
        }

        $element = str_replace('TunnelsPagesElements_', '', $element);
        $element = str_replace('PagesElements_', '', $element);
        $element = strtolower($element);

        return isset($this->tabs[$element]) ? $this->tabs[$element] : null;
    }

    /**
     * displayTabs function.
     *
     * @param string $element
     *
     * @return array
     */
    public function displayTabs($element = '')
    {
        if (!isset($this->tabs[$element]) or !$this->tabs[$element]) {
            return;
        }

        $tabs = $this->tabs[$element];
        $output = '';
        $i = 0;
        foreach ($tabs as $tab) {
            $output .= '<li' . (0 == $i ? ' class="active"' : '') . '><a href="#tab_' . $tab['id'] . '"><i class="fa fa-' . $tab['icone'] . '"></i> ' . $tab['nom'] . '</a></li>';
            ++$i;
        }

        return $output;
    }

    /**
     * displayBuilderTabs function.
     *
     * @param string $element
     *
     * @return array
     */
    public function displayBuilderTabs($element = '')
    {
        if (!isset($this->tabs[$element]) or !$this->tabs[$element]) {
            return;
        }

        $tabs = $this->tabs[$element];
        if (1 == count($tabs)) {
            return;
        }

        $output = '';
        $i = 0;
        foreach ($tabs as $tab) {
            $output .= '<li' . (0 == $i ? ' class="active"' : '') . '><a href="#tab_' . $tab['id'] . '"><i class="fa fa-' . $tab['icone'] . '"></i> ' . $tab['nom'] . '</a></li>';
            ++$i;
        }

        return $output;
    }
}
