<?php

namespace Learnybox\Classes\builder\themes\mastertheme8\Examples;

class FunnelmenuExample
{
    public static function getExamples(): array
    {
        $examples = [];

        $examples[0]['elements'][0]['update']['contenutexte'] = '';

        $examples[0]['elements'][0]['update']['contenu'] = json_encode([
            'template' => 'template1',
            'display_thumbnails' => true,
            'pages' => [
                952644 => [
                    'active' => '0',
                    'title' => 'Paramètres',
                    'title_unavailable' => 'Bientôt disponible',
                    'image' => '',
                    'image-unavailable' => '',
                ],
            ],
            'padding_top' => 16,
            'padding_bottom' => 16,
            'padding_left' => 16,
            'padding_right' => 16,
            'font' => 'Montserrat',
            'font_size' => 18,
            'font_color' => '#5A655C',
            'align' => 'center',
            'bgcolor1' => '#ffffff',
            'bgcolor2' => '',
            'element_bg_gradient_angle' => 180,
            'bg_color' => '',
            'bg_opacity' => '1',
            'bg_opacity2' => '1',
            'bg_border' => 0,
            'bg_border_color' => '#000000',
            'bg_border_color_opacity' => '0',
            'bg_border_style' => 'none',
            'bg_border_radius_top_left' => 24,
            'bg_border_radius_top_right' => 24,
            'bg_border_radius_bottom_left' => 24,
            'bg_border_radius_bottom_right' => 24,
            'margin_top' => 10,
            'margin_bottom' => 10,
            'margin_left' => 0,
            'margin_right' => 0,
            'box_shadow' => 1,
            'box_shadow_x' => 0,
            'box_shadow_y' => 10,
            'box_shadow_blur' => 40,
            'box_shadow_color' => '#354463',
            'box_shadow_opacity' => 0.1,
            'absolute_position' => 0,
            'prevent_global_animation' => false,
        ]);

        $examples[0]['elements'][0]['update']['contenu_mobile'] = '';

        return $examples;
    }
}
