<?php

/**
 * Builder_Themes_Mastertheme1_popup3 class.
 *
 * @extends  Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Themes_Mastertheme1_popup3 extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        $theme_datas = [];
        $theme_datas['height'] = 570;

        $theme_datas['design'] = [
            'bgcolor1' => '',
            'bgcolor2' => '',
            'bgimage' => '',
            'padding_top' => '0',
            'padding_bottom' => '0',
            'margin_top' => '0',
            'margin_bottom' => '0',
            'animation' => 'fadeInDown',
            'animation_disappear' => 'fadeOutUp',
        ];

        $theme_datas['lines'][] = [
            'id_version' => 1,
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '[[FULLWIDTH]]',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'cover',
                'padding_top' => '100',
                'padding_bottom' => '150',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'line_theme' => 'line_theme4',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'cols' => [
                [
                    'span' => 12,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"h2","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<h2 style="text-align: center;"><span style=\"color: rgb(255, 255, 255);\">' . __('Inscrivez-vous à la newsletter<br />et recevez de nombreux cadeaux !') . '</span></h2>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"h3","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<h3 style=\"text-align: center;\"><span style=\"font-size: 20px; color: rgb(224, 224, 224);\">' . __('Faites votre choix ci-dessous !') . '</span></h3>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                        ],
                    ],
                ],
                [
                    'span' => 6,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 2,
                    'elements' => [
                        [
                            'objet' => 'button_popup_version',
                            'bloc_alignement' => 'auto',
                            'span' => 'span6',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"icone":"fa-check","align":"center","bg_color1":"#7ccf00","size_text":22,"size_textHover":22,"size_textClicked":22,"size":"btn-xlarge","sizeHover":"btn-xlarge","sizeClicked":"btn-xlarge","height":"btn-h-vb","color_text":"#ffffff","border":0,"border_color":"#cccccc","border_radius":4,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => __('Oui je veux m\'inscrire<br />à la newsletter !'),
                            'type' => '2',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                        ],
                    ],
                ],
                [
                    'span' => 6,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 2,
                    'elements' => [
                        [
                            'objet' => 'button_popup_close',
                            'bloc_alignement' => 'auto',
                            'span' => 'span6',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"icone":"fa-remove","align":"center","bg_color1":"#ff0000","size_text":22,"size_textHover":22,"size_textClicked":22,"size":"btn-xlarge","sizeHover":"btn-xlarge","sizeClicked":"btn-xlarge","height":"btn-h-vb","color_text":"#ffffff","border":0,"border_color":"#cccccc","border_radius":4,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => __('Non merci ça ne<br />m\'intéresse pas !'),
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'id_version' => 2,
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '[[FULLWIDTH]]',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'cover',
                'padding_top' => '20',
                'padding_bottom' => '50',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'line_theme' => 'line_theme4',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'cols' => [
                [
                    'span' => 12,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"h2","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<h2 style="text-align: center;"><span style=\"color: rgb(255, 255, 255);\">' . __('Vous avez fait le bon choix !') . '<br />' . __('A vous les nombreux cadeaux !') . '</span></h2>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"h3","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":20,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<h3 style=\"text-align: center;\"><span style=\"font-size: 20px; color: rgb(224, 224, 224);\">' . __('Remplissez le formulaire ci-dessous pour vous inscrire !') . '</span></h3>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                        ],
                    ],
                ],
                [
                    'span' => 2,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 2,
                    'elements' => [
                    ],
                ],
                [
                    'span' => 8,
                    'design' => [
                        'bgcolor1' => '#ffffff',
                        'bgcolor2' => '',
                        'gradient_angle' => '0',
                        'bgimage' => '',
                        'bgimage_position' => '',
                        'bgimage_size' => 'auto',
                        'bg_color_overlay' => '',
                        'bg_color_overlay_opacity' => '0',
                        'padding_top' => 40,
                        'padding_bottom' => 40,
                        'padding_left' => 40,
                        'padding_right' => 40,
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_width' => '0',
                        'border_color' => '',
                        'border_color_opacity' => '1',
                        'border_style' => 'none',
                        'border_radius_top_left' => '8',
                        'border_radius_top_right' => '8',
                        'border_radius_bottom_left' => '8',
                        'border_radius_bottom_right' => '8',
                        'box_shadow_x' => '0',
                        'box_shadow_y' => '0',
                        'box_shadow_blur' => '0',
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => '1',
                        'delay' => '0',
                        'delay_disappear' => '0',
                        'full_width' => '0',
                        'box_shadow' => '0',
                        'bg_side' => '0',
                        'bg_side_position' => '',
                        'bg_side_width' => '100',
                        'bg_side_height' => '100',
                    ],
                    'line_position' => 2,
                    'elements' => [
                        [
                            'objet' => 'optin_field',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"field":{"name":"prenom","displayName":"","value":"","placeholder":"' . __('Votre prénom') . '","get":"","active":true,"obligatoire":false},"template":"template7","font":"","font_size":18,"font_color":"","champs_bgcolor":"","champs_bgcolor_transparent":false,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                        ],
                        [
                            'objet' => 'optin_field',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"field":{"name":"email","displayName":"","value":"","placeholder":"' . __('Votre adresse email') . '","get":"","active":true,"obligatoire":true},"template":"template7","font":"","font_size":18,"font_color":"","champs_bgcolor":"","champs_bgcolor_transparent":false,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                        ],
                        [
                            'objet' => 'button_submit',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"icone":"fa-check","size_text":22,"size_textHover":22,"size_textClicked":22,"size":"btn-xlarge","sizeHover":"btn-xlarge","sizeClicked":"btn-xlarge","height":"btn-h-vb","color_text":"#ffffff","align":"center","open_window":0,"color":"#7ccf00","border":0,"border_color":"#cccccc","border_radius":4,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => __('Je m\'inscris !'),
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                        ],
                    ],
                ],
            ],
        ];

        return $theme_datas;
    }
}
