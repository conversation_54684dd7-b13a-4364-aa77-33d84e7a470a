<?php

/**
 * Builder_Themes_Mastertheme1_confirmationinscription1 class.
 *
 * @extends  Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Themes_Mastertheme1_confirmationinscription1 extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        $theme_datas = [];
        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'cover',
                'padding_top' => '70',
                'padding_bottom' => '140',
                'margin_top' => '0',
                'margin_bottom' => '0',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
                'line_theme' => 'line_theme3',
                'gradient_angle' => '0',
                'bg_color_overlay' => '',
                'bg_color_overlay_opacity' => '0',
                'padding_left' => '0',
                'padding_right' => '0',
                'margin_left' => '0',
                'margin_right' => '0',
                'border_width' => '0',
                'border_color' => '',
                'border_color_opacity' => '1',
                'border_radius_top_left' => '0',
                'border_radius_top_right' => '0',
                'border_radius_bottom_left' => '0',
                'border_radius_bottom_right' => '0',
                'border_style' => 'none',
                'box_shadow_x' => '0',
                'box_shadow_y' => '0',
                'box_shadow_blur' => '0',
                'box_shadow_color' => '',
                'box_shadow_opacity' => '1',
                'box_shadow' => '0',
                'bg_color_overlay1' => '',
                'bg_color_overlay2' => '',
                'bg_overlay_gradient_angle' => '240',
            ],
            'cols' => [
                [
                    'span' => '12',
                    'position' => '0',
                    'line_position' => '2',
                    'elements' => [
                        [
                            'objet' => 'image',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '',
                            'largeur' => '180',
                            'eval' => '',
                            'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":10,"alt":""}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => 'center',
                            'time' => '0',
                            'name' => '',
                            'urls' => '[[LOGO]]',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
                [
                    'span' => '12',
                    'position' => '1',
                    'line_position' => '3',
                    'design' => [
                        'bgcolor1' => '#ffffff',
                        'bgcolor2' => '',
                        'gradient_angle' => '0',
                        'bgimage' => '',
                        'bgimage_position' => '',
                        'bgimage_size' => 'auto',
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => '0',
                        'bg_overlay_gradient_angle' => '0',
                        'padding_top' => '0',
                        'padding_bottom' => '0',
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_top' => '65',
                        'margin_bottom' => '0',
                        'margin_left' => '100',
                        'margin_right' => '100',
                        'border_width' => '0',
                        'border_color' => '',
                        'border_color_opacity' => '1',
                        'border_style' => 'none',
                        'border_radius_top_left' => '8',
                        'border_radius_top_right' => '8',
                        'border_radius_bottom_left' => '8',
                        'border_radius_bottom_right' => '8',
                        'box_shadow_x' => '5',
                        'box_shadow_y' => '5',
                        'box_shadow_blur' => '26',
                        'box_shadow_color' => '#000000',
                        'box_shadow_opacity' => '0.17',
                        'delay' => '0',
                        'delay_disappear' => '0',
                        'full_width' => '0',
                        'box_shadow' => '1',
                        'bg_side' => '0',
                        'bg_side_position' => '',
                        'bg_side_width' => '100',
                        'bg_side_height' => '100',
                    ],
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"Rubik","font_theme":"h1","padding_top":45,"padding_bottom":0,"padding_left":10,"padding_right":10,"margin_top":0,"margin_bottom":0}',
                            'contenutexte' => '<h1 style=\"text-align: center;\"><span style="color: #526173\">' . __('Merci pour votre<br>inscription !') . '</span></h1>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"Rubik","font_theme":"h3","padding_top":5,"padding_bottom":0,"padding_left":10,"padding_right":10,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":"1"}',
                            'contenutexte' => '<h3 style=\"text-align: center;\"><span style=\"color: rgb(168, 176, 185);\">' . __('Votre lien de téléchargement est disponible ci-dessous :') . '</span></h3>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'button_link',
                            'bloc_alignement' => '',
                            'span' => 'span6',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"contenu":"' . mb_strtoupper(_('Téléchargez votre guide d\'apprentissage rapide')) . '","icone":"fa-angle-right","color_icone":"","align":"center","color":"#13bd3b","color_text":"#ffffff","size":"btn-large","size_text":18,"border":0,"border_color":"#a5ba6f","border_radius":100,"button_box_shadow_x":0,"button_box_shadow_y":3,"button_box_shadow_blur":0,"button_box_shadow_color":"#a5ba6f","contenuHover":"","iconeHover":"","color_iconeHover":"#ffffff","alignHover":"left","colorHover":"","color_textHover":"#ffffff","sizeHover":"btn-xlarge","size_textHover":18,"borderHover":0,"border_colorHover":"#ccc","border_radiusHover":100,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":10,"button_box_shadow_colorHover":"#BBBBBB","contenuClicked":"","iconeClicked":"","color_iconeClicked":"","alignClicked":"left","colorClicked":"","color_textClicked":"#ffffff","sizeClicked":"btn-xlarge","size_textClicked":18,"borderClicked":0,"border_colorClicked":"#ccc","border_radiusClicked":100,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":10,"button_box_shadow_colorClicked":"#BBBBBB","default_color_icone":"#ffffff","size_icone":18,"position_icone":"left","decalage_icone":26,"bg_color1":"#b4cf70","bg_color2":"","bg_gradient_angle":"","font_text":"","font_styles":[],"btn_padding_top":15,"btn_padding_bottom":15,"btn_padding_left":30,"btn_padding_right":30,"size_iconeHover":18,"position_iconeHover":"left","decalage_iconeHover":26,"bg_color1Hover":"#a0ba62","bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"","font_stylesHover":[],"btn_padding_topHover":15,"btn_padding_bottomHover":15,"btn_padding_leftHover":30,"btn_padding_rightHover":30,"size_iconeClicked":18,"position_iconeClicked":"left","decalage_iconeClicked":26,"bg_color1Clicked":"#337ab7","bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"","font_stylesClicked":[],"btn_padding_topClicked":15,"btn_padding_bottomClicked":15,"btn_padding_leftClicked":30,"btn_padding_rightClicked":30,"action":"","param":"","margin_top":32,"margin_bottom":0,"height":"btn-h-n"}',
                            'contenutexte' => mb_strtoupper(_('Téléchargez votre guide d\'apprentissage rapide')),
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"Rubik","font_theme":"h3","padding_top":5,"padding_bottom":50,"padding_left":94,"padding_right":88,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":10,"margin_bottom":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":"1"}',
                            'contenutexte' => '<h3 style=\"text-align: center;\"><span style=\"font-size: 18px;\">' . __('Ce guide va vous apprendre à apprendre et à développer les capacités de votre cerveau<br>En 5 minutes par jour,<br>Grâce aux dernières découvertes des neurosciences !') . '</span></h3>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"Rubik","font_theme":"h3","padding_top":10,"padding_bottom":10,"padding_left":10,"padding_right":10,"bg_color":"#F3F6F9","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":"1","absolute_position":false}',
                            'contenutexte' => '<h3 style=\"text-align: center;\"><span style=\"color: rgb(162, 171, 182);\">' . __('Partagez le guide avec vos amis') . '</span></h3>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'social_share',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"template":"template7","padding_top":0,"padding_bottom":27,"padding_left":0,"padding_right":0,"margin_bottom":0,"id_page":"","url":"","facebook":{"active":"oui","title":"' . __('Partager') . '"},"twitter":{"active":"oui","texte":"","title":"Tweet"},"googleplus":{"title":"' . __('Partager') . '","active":"non"},"linkedin":{"active":"oui","title":"' . __('Partager') . '"},"bg_color":"#F3F6F9","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":"1","absolute_position":false}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        return $theme_datas;
    }
}
