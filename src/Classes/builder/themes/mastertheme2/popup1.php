<?php

/**
 * Builder_Themes_Mastertheme2_popup class.
 *
 * @extends  Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Themes_Mastertheme2_popup1 extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        $theme_datas = [];

        $theme_datas['height'] = 600;

        $theme_datas['design'] = [
            'bgcolor1' => '#ffffff',
            'bgcolor2' => '',
            'bgimage' => '',
            'padding_top' => '50',
            'padding_bottom' => '50',
            'margin_top' => '0',
            'margin_bottom' => '0',
            'animation' => 'fadeInDown',
            'animation_disappear' => 'fadeOutUp',
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor1_opacity' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'auto',
                'padding_top' => '0',
                'padding_bottom' => '0',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'cols' => [
                [
                    'span' => 12,
                    'design' => [
                        'padding_top' => 40,
                        'padding_bottom' => 40,
                        'padding_left' => 40,
                        'padding_right' => 40,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"h2","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<h2 style="text-align: center;">' . __('Ce texte utilise quelques mots<br />de longueur variable.') . '</h2>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'save_appear' => '0',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"h3","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":20,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<h3 style=\"text-align: center;\"><span style=\"font-size: 24px;\">' . __('Expert en utilisabilité des sites web et des logiciels, Jakob Nielsen souligne que l\'une des limites de l\'utilisation du faux-texte.') . '</span></h3>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'save_appear' => '0',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'optin_field',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"field":{"name":"fname","displayName":"","value":"","placeholder":"' . __('Votre prénom') . '","get":"","obligatoire":false},"template":"template7","font":"","font_size":18,"font_color":"","displayName_font":"","displayName_font_size":false,"displayName_font_color":"","champs_bgcolor":"","champs_bgcolor_transparent":false,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'save_appear' => '0',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'optin_field',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"field":{"name":"email","displayName":"","value":"","placeholder":"' . __('Votre adresse email') . '","get":"","obligatoire":true},"template":"template7","font":"","font_size":18,"font_color":"","displayName_font":"","displayName_font_size":false,"displayName_font_color":"","champs_bgcolor":"","champs_bgcolor_transparent":false,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'save_appear' => '0',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'optin_rgpd',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'save_appear' => '0',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'button_submit',
                            'bloc_alignement' => 'center',
                            'span' => 'span6',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"icone":"","color_icone":"","align":"center","color":"#B8CC7F","color_text":"#ffffff","size":"btn-large","size_text":24,"border":0,"border_color":"#ccc","border_radius":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"#404d4d","contenuHover":"","iconeHover":"","color_iconeHover":"","alignHover":"center","colorHover":"","color_textHover":"#ffffff","sizeHover":"btn-large","size_textHover":24,"borderHover":0,"border_colorHover":"","border_radiusHover":0,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":10,"button_box_shadow_colorHover":"#404d4d","contenuClicked":"","iconeClicked":"","color_iconeClicked":"","alignClicked":"center","colorClicked":"","color_textClicked":"#ffffff","sizeClicked":"btn-large","size_textClicked":24,"borderClicked":0,"border_colorClicked":"","border_radiusClicked":0,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":10,"button_box_shadow_colorClicked":"#404d4d","action":"","param":"","position_icone":"left","decalage_icone":20,"position_iconeHover":"left","decalage_iconeHover":20,"position_iconeClicked":"left","decalage_iconeClicked":20,"size_icone":26,"size_iconeHover":26,"size_iconeClicked":26,"default_color_icone":"","bg_color1":"main_color0","bg_color2":"","bg_gradient_angle":"","font_text":"","font_styles":["bold"],"btn_padding_top":25,"btn_padding_bottom":25,"btn_padding_left":60,"btn_padding_right":60,"bg_color1Hover":"#101111","bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"","font_stylesHover":[],"btn_padding_topHover":25,"btn_padding_bottomHover":25,"btn_padding_leftHover":60,"btn_padding_rightHover":60,"bg_color1Clicked":"#060707","bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"","font_stylesClicked":[],"btn_padding_topClicked":25,"btn_padding_bottomClicked":25,"btn_padding_leftClicked":60,"btn_padding_rightClicked":60,"margin_top":24,"margin_bottom":0,"height":"btn-h-vb","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"open_window":0}',
                            'contenutexte' => __('Je m\'inscris maintenant'),
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'save_appear' => '0',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"p","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<p style="text-align: center;"><span style=\"color: rgba(82, 97, 115, 0.5);\"><i class="fa fa-lock">&nbsp;</i>&nbsp;' . __('Vos informations sont confidentielles.') . '</span></p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'save_appear' => '0',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        return $theme_datas;
    }
}
