<?php

/**
 * Builder_Themes_Mastertheme2_mail2 class.
 *
 * @extends  Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Themes_Mastertheme2_mail2 extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        $theme_datas = [];

        $theme_datas['design'] = [
            'bgcolor1' => '#eaeced',
            'bgcolor2' => '',
            'bgimage' => '',
            'padding_top' => '20',
            'padding_bottom' => '20',
            'margin_top' => '0',
            'margin_bottom' => '0',
        ];

        if (defined('SITE_LOGO') and SITE_LOGO) {
            $logo = SITE_LOGO;
        } else {
            $logo = \Learnybox\Helpers\Assets::getImageUrl('tunnels/logo-exemple.png');
        }

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '',
                'padding_bottom' => '',
                'margin_top' => '0',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
            ],
            'cols' => [
                [
                    'span' => 12,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'image',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '0',
                            'contenu' => '{"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => 'center',
                            'time' => '0',
                            'name' => '',
                            'urls' => $logo,
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '',
                'padding_bottom' => '',
                'margin_top' => '0',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
            ],
            'cols' => [
                [
                    'span' => 12,
                    'design' => [
                        'padding_top' => 0,
                        'padding_bottom' => 0,
                        'padding_left' => 0,
                        'padding_right' => 0,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'image',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '0',
                            'contenu' => '{"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => 'center',
                            'time' => '0',
                            'name' => '',
                            'urls' => \Learnybox\Helpers\Assets::getImageUrl('mails/source/blog_post.jpg'),
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '0',
                'padding_bottom' => '0',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'line_theme' => 'line_theme1',
            ],
            'cols' => [
                [
                    'span' => 12,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"h2","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<h2 style="text-align: center;">News</h2>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"p","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse id mollis ante. Proin auctor ligula et convallis volutpat. Curabitur in imperdiet urna. Duis et leo velit. Nam laoreet imperdiet libero, eu pretium nisl porta eget. Quisque laoreet est vitae ante luctus gravida.</p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'hide' => '0',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '20',
                'padding_bottom' => '0',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'line_theme' => 'line_theme2',
            ],
            'cols' => [
                [
                    'span' => 6,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'image',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '0',
                            'contenu' => '{"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '{"img_border":0,"img_border_color":"","img_border_radius":0}',
                            'type' => '',
                            'fleche' => 'center',
                            'time' => '0',
                            'name' => '',
                            'urls' => \Learnybox\Helpers\Assets::getImageUrl('mails/blog_post-657x345.png'),
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'hide' => '0',
                        ],
                    ],
                ],
                [
                    'span' => 6,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span6',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"h4","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<h4>Lorem ipsum dolor</h4>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span6',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"p","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse id mollis ante. Proin auctor ligula et convallis volutpat. Curabitur in imperdiet urna. Duis et leo velit. Nam laoreet imperdiet libero, eu pretium nisl porta eget. Quisque laoreet est vitae ante luctus gravida.</p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'hide' => '0',
                        ],
                    ],
                ],
                [
                    'span' => 12,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 2,
                    'elements' => [
                        [
                            'objet' => 'button_link',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"contenu":"Lire l&#39;article","icone":"","color_icone":"","align":"center","color":"#B8CC7F","color_text":"#ffffff","size":"btn-large","size_text":20,"border":0,"border_color":"#ccc","border_radius":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"#404d4d","contenuHover":"","iconeHover":"","color_iconeHover":"","alignHover":"center","colorHover":"","color_textHover":"#ffffff","sizeHover":"btn-large","size_textHover":20,"borderHover":0,"border_colorHover":"","border_radiusHover":0,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":10,"button_box_shadow_colorHover":"#404d4d","contenuClicked":"","iconeClicked":"","color_iconeClicked":"","alignClicked":"center","colorClicked":"","color_textClicked":"#ffffff","sizeClicked":"btn-large","size_textClicked":20,"borderClicked":0,"border_colorClicked":"","border_radiusClicked":0,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":10,"button_box_shadow_colorClicked":"#404d4d","action":"","param":"","position_icone":"left","decalage_icone":20,"position_iconeHover":"left","decalage_iconeHover":20,"position_iconeClicked":"left","decalage_iconeClicked":20,"size_icone":26,"size_iconeHover":26,"size_iconeClicked":26,"default_color_icone":"","bg_color1":"main_color0","bg_color2":"","bg_gradient_angle":"","font_text":"","font_styles":["bold"],"btn_padding_top":14,"btn_padding_bottom":14,"btn_padding_left":20,"btn_padding_right":20,"bg_color1Hover":"#101111","bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"","font_stylesHover":[],"btn_padding_topHover":14,"btn_padding_bottomHover":14,"btn_padding_leftHover":20,"btn_padding_rightHover":20,"bg_color1Clicked":"#060707","bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"","font_stylesClicked":[],"btn_padding_topClicked":14,"btn_padding_bottomClicked":14,"btn_padding_leftClicked":20,"btn_padding_rightClicked":20,"margin_top":0,"margin_bottom":0,"height":"btn-h-b","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"open_window":0}',
                            'contenutexte' => 'Lire l\'article',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '20',
                'padding_bottom' => '0',
                'margin_top' => '',
                'margin_bottom' => '20',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'line_theme' => 'line_theme1',
            ],
            'cols' => [
                [
                    'span' => 6,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'image',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '0',
                            'contenu' => '{"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '{"img_border":0,"img_border_color":"","img_border_radius":0}',
                            'type' => '',
                            'fleche' => 'center',
                            'time' => '0',
                            'name' => '',
                            'urls' => \Learnybox\Helpers\Assets::getImageUrl('mails/blog_post-657x345.png'),
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'hide' => '0',
                        ],
                    ],
                ],
                [
                    'span' => 6,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span6',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"h4","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<h4>Lorem ipsum dolor</h4>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span6',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"p","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse id mollis ante. Proin auctor ligula et convallis volutpat. Curabitur in imperdiet urna. Duis et leo velit. Nam laoreet imperdiet libero, eu pretium nisl porta eget. Quisque laoreet est vitae ante luctus gravida.</p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'hide' => '0',
                        ],
                    ],
                ],
                [
                    'span' => 12,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 2,
                    'elements' => [
                        [
                            'objet' => 'button_link',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"contenu":"Lire l&#39;article","icone":"","color_icone":"","align":"center","color":"#B8CC7F","color_text":"#ffffff","size":"btn-large","size_text":20,"border":0,"border_color":"#ccc","border_radius":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"#404d4d","contenuHover":"","iconeHover":"","color_iconeHover":"","alignHover":"center","colorHover":"","color_textHover":"#ffffff","sizeHover":"btn-large","size_textHover":20,"borderHover":0,"border_colorHover":"","border_radiusHover":0,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":10,"button_box_shadow_colorHover":"#404d4d","contenuClicked":"","iconeClicked":"","color_iconeClicked":"","alignClicked":"center","colorClicked":"","color_textClicked":"#ffffff","sizeClicked":"btn-large","size_textClicked":20,"borderClicked":0,"border_colorClicked":"","border_radiusClicked":0,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":10,"button_box_shadow_colorClicked":"#404d4d","action":"","param":"","position_icone":"left","decalage_icone":20,"position_iconeHover":"left","decalage_iconeHover":20,"position_iconeClicked":"left","decalage_iconeClicked":20,"size_icone":26,"size_iconeHover":26,"size_iconeClicked":26,"default_color_icone":"","bg_color1":"main_color0","bg_color2":"","bg_gradient_angle":"","font_text":"","font_styles":["bold"],"btn_padding_top":14,"btn_padding_bottom":14,"btn_padding_left":20,"btn_padding_right":20,"bg_color1Hover":"#101111","bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"","font_stylesHover":[],"btn_padding_topHover":14,"btn_padding_bottomHover":14,"btn_padding_leftHover":20,"btn_padding_rightHover":20,"bg_color1Clicked":"#060707","bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"","font_stylesClicked":[],"btn_padding_topClicked":14,"btn_padding_bottomClicked":14,"btn_padding_leftClicked":20,"btn_padding_rightClicked":20,"margin_top":0,"margin_bottom":0,"height":"btn-h-b","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"open_window":0}',
                            'contenutexte' => 'Lire l\'article',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        return $theme_datas;
    }
}
