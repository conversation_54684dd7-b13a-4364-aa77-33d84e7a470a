<?php

class Builder_Themes_Mastertheme6_mail1 extends Eden_Class
{
    public function getThemeData(): array
    {
        return [
            'lines' => [
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/background3.png',
                        'padding_top' => '24',
                        'padding_bottom' => '24',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'line_theme' => 'line_theme3',
                        'gradient_angle' => 180,
                        'padding_left' => '24',
                        'padding_right' => '24',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bgimage_position' => 'scroll',
                        'bgimage_size' => 'cover',
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'bg_color_page' => false,
                        'delay' => 0,
                        'delay_disappear' => 0,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 12,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 20,
                                'padding_bottom' => 20,
                                'padding_left' => 20,
                                'padding_right' => 20,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => 'auto',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 41,
                                    'largeur' => 200,
                                    'eval' => '0',
                                    'contenu' => '{"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":0,"img_border_radius_top_right":0,"img_border_radius_bottom_left":0,"img_border_radius_bottom_right":0,"img_border_radius_circle":0,"img_size":1,"button_box_shadow":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"brightness":"100","opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => 'left',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/versioned/yann-merkel/images/templates-bertrand/logos/logo-1.png',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => 'fadeIn',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => 'fadeOut',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '#FFFFFF',
                        'bgcolor2' => '',
                        'bgimage' => '',
                        'padding_top' => '0',
                        'padding_bottom' => '0',
                        'margin_top' => '',
                        'margin_bottom' => '',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => '1',
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 12,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 20,
                                'padding_bottom' => 20,
                                'padding_left' => 20,
                                'padding_right' => 20,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => 'auto',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h2","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_radius":0,"margin_top":0,"margin_bottom":20,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2 style=\"text-align:center\">Vous voulez d&eacute;couvrir les cl&eacute;s d&#39;une vie &eacute;quilibr&eacute;e et &eacute;panouie ?</h2>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => 'fadeIn',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => 'fadeOut',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => 'auto',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_radius":0,"margin_top":0,"margin_bottom":20,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p style=\"text-align:center\"><strong>D&eacute;couvrez des conseils pratiques, des exercices de relaxation, et des strat&eacute;gies pour g&eacute;rer le stress et augmenter votre bonheur.</strong></p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => 'fadeIn',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => 'fadeOut',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => 'auto',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                                    'contenutexte' => "<html><body><p>Et quoniam mirari posse quosdam peregrinos existimo haec lecturos forsitan, si contigerit, quamobrem cum oratio ad ea monstranda deflexerit quae Romae gererentur, nihil praeter seditiones narratur et tabernas et vilitates harum similis alias, summatim causas perstringam nusquam a veritate sponte propria digressurus.<br><br>\nExsistit autem hoc loco quaedam quaestio subdifficilis, num quando amici novi, digni amicitia, veteribus sint anteponendi, ut equis vetulis teneros anteponere solemus. Indigna homine dubitatio! Non enim debent esse amicitiarum sicut aliarum rerum satietates; veterrima quaeque, ut ea vina, quae vetustatem ferunt, esse debet suavissima; verumque illud est, quod dicitur, multos modios salis simul edendos esse, ut amicitiae munus expletum sit.<br><br>\nQuid enim tam absurdum quam delectari multis inanimis rebus, ut honore, ut gloria, ut aedificio, ut vestitu cultuque corporis, animante virtute praedito, eo qui vel amare vel, ut ita dicam, redamare possit, non admodum delectari? Nihil est enim remuneratione benevolentiae, nihil vicissitudine studiorum officiorumque iucundius.</p></body></html>",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => 'fadeIn',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => 'fadeOut',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 3,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => '',
                        'padding_top' => '0',
                        'padding_bottom' => '0',
                        'margin_top' => '',
                        'margin_bottom' => '',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => '1',
                        'line_theme' => 'line_theme3',
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 12,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 20,
                                'padding_bottom' => 20,
                                'padding_left' => 20,
                                'padding_right' => 20,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => 'auto',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                                    'contenutexte' => '<p><span style="color: #fefefe;">team learnybox</span></p>',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => 'fadeIn',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => 'fadeOut',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }
}
