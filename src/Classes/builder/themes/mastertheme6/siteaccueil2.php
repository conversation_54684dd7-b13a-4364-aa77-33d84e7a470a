<?php

class Builder_Themes_Mastertheme6_siteaccueil2 extends Eden_Class
{
    public function getThemeData(): array
    {
        return [
            'lines' => [
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/background-graphic-1.png',
                        'bgimage_position' => 'scroll',
                        'bgimage_size' => 'cover',
                        'padding_top' => '120',
                        'padding_bottom' => '80',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'line_theme' => 'line_theme2',
                        'gradient_angle' => 180,
                        'bg_color_overlay' => '',
                        'bg_color_overlay_opacity' => 0.06,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '#000000',
                        'bg_color_overlay2' => '',
                        'bg_overlay_gradient_angle' => 0,
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 7,
                            'position' => 1,
                            'line_position' => 0,
                            'design' => [
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => '0',
                                'bgimage' => '',
                                'bgimage_position' => '',
                                'bgimage_size' => 'auto',
                                'bg_color_overlay' => '',
                                'bg_color_overlay_opacity' => '0',
                                'padding_top' => '0',
                                'padding_bottom' => '0',
                                'padding_left' => '10',
                                'padding_right' => '10',
                                'margin_top' => '0',
                                'margin_bottom' => '0',
                                'border_width' => '0',
                                'border_color' => '',
                                'border_color_opacity' => '1',
                                'border_style' => 'none',
                                'border_radius_top_left' => '8',
                                'border_radius_top_right' => '8',
                                'border_radius_bottom_left' => '8',
                                'border_radius_bottom_right' => '8',
                                'box_shadow_x' => '5',
                                'box_shadow_y' => '5',
                                'box_shadow_blur' => '26',
                                'box_shadow_color' => '#000000',
                                'box_shadow_opacity' => '0.17',
                                'delay' => '0',
                                'delay_disappear' => '0',
                                'full_width' => '0',
                                'box_shadow' => '0',
                                'bg_side' => '0',
                                'bg_side_position' => '',
                                'bg_side_width' => '100',
                                'bg_side_height' => '100',
                                'margin_left' => '0',
                                'margin_right' => '0',
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h1>Vous voulez acc&eacute;l&eacute;rer votre succ&egrave;s en tant qu&#39;entrepreneur, rapidement et sans limite ?</h1>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h3","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_top":18,"margin_bottom":0,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h3>&Ecirc;tes-vous pr&ecirc;t &agrave; propulser votre activit&eacute; dans une nouvelle dimension ?</h3>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_top":18,"margin_bottom":32,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p>Nos formations en ligne sont sp&eacute;cialement &eacute;labor&eacute;es pour vous accompagner vers le succ&egrave;s de votre entreprise.</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                                [
                                    'objet' => 'button_link',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bg_color1":"#2F2F2F","bg_color1Hover":"#6078C0","bg_color1Clicked":"#000000","color_text":"#ffffff","color_textHover":"#ffffff","color_textClicked":"#ffffff","size_text":16,"size_textHover":16,"size_textClicked":16,"border_radius":6,"border_radiusHover":6,"border_radiusClicked":6,"size":"btn-large","height":"btn-h-b","border_color":"#000000","border_colorHover":"#6078C0","border_colorClicked":"#000000","color_icone":"#ffffff","color_iconeHover":"#ffffff","color_iconeClicked":"#ffffff","open_window":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"align":"left","icone":"","default_color_icone":"","action":"page","param":"","actions":[],"bg_color2":"","border":2,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"contenu":"D\u00c9COUVRIR NOS FORMATIONS","size_icone":14,"position_icone":"left","decalage_icone":0,"bg_gradient_angle":"","font_text":"Open Sans","font_styles":["bold"],"btn_padding_top":16,"btn_padding_bottom":16,"btn_padding_left":32,"btn_padding_right":32,"contenuHover":"","size_iconeHover":14,"position_iconeHover":"","decalage_iconeHover":0,"bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"Open Sans","font_stylesHover":["bold"],"borderHover":2,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":0,"button_box_shadow_colorHover":"","button_box_shadow_opacityHover":1,"btn_padding_topHover":16,"btn_padding_bottomHover":16,"btn_padding_leftHover":32,"btn_padding_rightHover":32,"contenuClicked":"","size_iconeClicked":14,"position_iconeClicked":"","decalage_iconeClicked":0,"bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"Open Sans","font_stylesClicked":["bold"],"borderClicked":2,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":0,"button_box_shadow_colorClicked":"","button_box_shadow_opacityClicked":1,"btn_padding_topClicked":16,"btn_padding_bottomClicked":16,"btn_padding_leftClicked":32,"btn_padding_rightClicked":32,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'DÉCOUVRIR NOS FORMATIONS',
                                    'contenu_mobile' => json_encode([
                                        'contenu' => json_encode([
                                            "size_text" => 14,
                                            "size_textHover" => 14,
                                            "size_textClicked" => 14,
                                            "height" => "btn-h-s",
                                            "btn_padding_top" => 6,
                                            "btn_padding_right" => 12,
                                            "btn_padding_bottom" => 6,
                                            "btn_padding_left" => 12,
                                            "btn_padding_topHover" => 6,
                                            "btn_padding_rightHover" => 12,
                                            "btn_padding_bottomHover" => 6,
                                            "btn_padding_leftHover" => 12,
                                            "btn_padding_topClicked" => 6,
                                            "btn_padding_rightClicked" => 12,
                                            "btn_padding_bottomClicked" => 6,
                                            "btn_padding_leftClicked" => 12,
                                            "padding_top" => 10,
                                            "padding_bottom" => 10,
                                            "padding_left" => 10,
                                            "padding_right" => 10,
                                            "margin_top" => 0,
                                            "margin_bottom" => 0,
                                            "margin_left" => 0,
                                            "margin_right" => 0,
                                        ])
                                    ]),
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                                [
                                    'objet' => 'button_link',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bg_color1":"","bg_color1Hover":"#6078C0","bg_color1Clicked":"","color_text":"#2F2F2F","color_textHover":"#ffffff","color_textClicked":"#2F2F2F","size_text":16,"size_textHover":16,"size_textClicked":16,"border_radius":6,"border_radiusHover":6,"border_radiusClicked":6,"size":"btn-large","height":"btn-h-b","border_color":"#000000","border_colorHover":"#6078C0","border_colorClicked":"#000000","color_icone":"#ffffff","color_iconeHover":"#ffffff","color_iconeClicked":"#ffffff","open_window":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"align":"left","icone":"","default_color_icone":"","action":"page","param":"","actions":[],"bg_color2":"","border":2,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"contenu":"NOUS CONTACTER","size_icone":14,"position_icone":"left","decalage_icone":0,"bg_gradient_angle":180,"font_text":"Open Sans","font_styles":["bold"],"btn_padding_top":16,"btn_padding_bottom":16,"btn_padding_left":32,"btn_padding_right":32,"contenuHover":"","size_iconeHover":14,"position_iconeHover":"","decalage_iconeHover":0,"bg_color2Hover":"","bg_gradient_angleHover":180,"font_textHover":"Open Sans","font_stylesHover":["bold"],"borderHover":2,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":0,"button_box_shadow_colorHover":"","button_box_shadow_opacityHover":1,"btn_padding_topHover":16,"btn_padding_bottomHover":16,"btn_padding_leftHover":32,"btn_padding_rightHover":32,"contenuClicked":"","size_iconeClicked":14,"position_iconeClicked":"","decalage_iconeClicked":0,"bg_color2Clicked":"","bg_gradient_angleClicked":180,"font_textClicked":"Open Sans","font_stylesClicked":["bold"],"borderClicked":2,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":0,"button_box_shadow_colorClicked":"","button_box_shadow_opacityClicked":1,"btn_padding_topClicked":16,"btn_padding_bottomClicked":16,"btn_padding_leftClicked":32,"btn_padding_rightClicked":32,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'NOUS CONTACTER',
                                    'contenu_mobile' => json_encode([
                                        'contenu' => json_encode([
                                            "size_text" => 14,
                                            "size_textHover" => 14,
                                            "size_textClicked" => 14,
                                            "height" => "btn-h-s",
                                            "btn_padding_top" => 6,
                                            "btn_padding_right" => 12,
                                            "btn_padding_bottom" => 6,
                                            "btn_padding_left" => 12,
                                            "btn_padding_topHover" => 6,
                                            "btn_padding_rightHover" => 12,
                                            "btn_padding_bottomHover" => 6,
                                            "btn_padding_leftHover" => 12,
                                            "btn_padding_topClicked" => 6,
                                            "btn_padding_rightClicked" => 12,
                                            "btn_padding_bottomClicked" => 6,
                                            "btn_padding_leftClicked" => 12,
                                            "padding_top" => 10,
                                            "padding_bottom" => 10,
                                            "padding_left" => 10,
                                            "padding_right" => 10,
                                            "margin_top" => 0,
                                            "margin_bottom" => 0,
                                            "margin_left" => 0,
                                            "margin_right" => 0,
                                        ])
                                    ]),
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                            ],
                        ],
                        [
                            'span' => 5,
                            'position' => 2,
                            'line_position' => 0,
                            'design' => [
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => '0',
                                'bgimage' => '',
                                'bgimage_position' => '',
                                'bgimage_size' => 'auto',
                                'bg_color_overlay' => '',
                                'bg_color_overlay_opacity' => '0',
                                'padding_top' => '0',
                                'padding_bottom' => '0',
                                'padding_left' => '10',
                                'padding_right' => '10',
                                'margin_top' => '0',
                                'margin_bottom' => '0',
                                'border_width' => '0',
                                'border_color' => '',
                                'border_color_opacity' => '1',
                                'border_style' => 'none',
                                'border_radius_top_left' => '8',
                                'border_radius_top_right' => '8',
                                'border_radius_bottom_left' => '8',
                                'border_radius_bottom_right' => '8',
                                'box_shadow_x' => '5',
                                'box_shadow_y' => '5',
                                'box_shadow_blur' => '26',
                                'box_shadow_color' => '#000000',
                                'box_shadow_opacity' => '0.17',
                                'delay' => '0',
                                'delay_disappear' => '0',
                                'full_width' => '0',
                                'box_shadow' => '0',
                                'bg_side' => '0',
                                'bg_side_position' => '',
                                'bg_side_width' => '100',
                                'bg_side_height' => '100',
                                'margin_left' => '0',
                                'margin_right' => '0',
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 441,
                                    'largeur' => 390,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":0,"img_border_radius_top_right":0,"img_border_radius_bottom_left":0,"img_border_radius_bottom_right":0,"img_border_radius_circle":0,"img_size":0,"button_box_shadow":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/image3.png',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/background-graphic-1.png',
                        'bgimage_position' => 'scroll',
                        'bgimage_size' => 'cover',
                        'padding_top' => '0',
                        'padding_bottom' => '64',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '#F1F1F1',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0.5,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => 'line_theme2',
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 2,
                            'position' => 0,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'col_height' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [],
                        ],
                        [
                            'span' => 8,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 64,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":16,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2 style=\"text-align:center\"><span style=\"color:#6078c0\">Notre vision</span></h2>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 3,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\" style=\"text-align:center\">Notre aspiration profonde est de fa&ccedil;onner une communaut&eacute; florissante d&#39;entrepreneurs &eacute;panouis, porteurs d&#39;une r&eacute;ussite remarquable et d&#39;un impact positif &eacute;tendu. Nous croyons en la puissance transformative de chaque entrepreneur pour cr&eacute;er un changement positif dans le monde.</p>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 6,
                                ],
                            ],
                        ],
                        [
                            'span' => 4,
                            'position' => 1,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 32,
                                'padding_bottom' => 48,
                                'padding_left' => 48,
                                'padding_right' => 24,
                                'bgcolor1' => '#6078C0',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 4,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 64,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 1,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '{"design":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_top\":0,\"margin_bottom\":0,\"margin_left\":0,\"margin_right\":0,\"border_radius_top_left\":0,\"border_radius_bottom_left\":0}"}',
                            'elements' => [
                                [
                                    'objet' => 'fa-icon',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"icone":"fa-smile-o","align":"center","size":"52","color":"#ffffff","padding_top":0,"padding_bottom":16,"padding_left":0,"padding_right":0,"margin_bottom":0,"open_window":0,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 9,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h4 style=\"text-align:center\"><span style=\"color:#ffffff\">Strat&eacute;gies &Eacute;prouv&eacute;es</span></h4>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 10,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\" style=\"text-align:center\"><span style=\"color:#ffffff\">B&eacute;n&eacute;ficiez d&#39;un acc&egrave;s privil&eacute;gi&eacute; &agrave; des m&eacute;thodes efficaces pour stimuler la croissance de votre entreprise.</span></p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 11,
                                ],
                            ],
                        ],
                        [
                            'span' => 4,
                            'position' => 2,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 32,
                                'padding_bottom' => 48,
                                'padding_left' => 24,
                                'padding_right' => 24,
                                'bgcolor1' => '#6078C0',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 1,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '{"design":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_top\":0,\"margin_bottom\":0,\"margin_left\":0,\"margin_right\":0}"}',
                            'elements' => [
                                [
                                    'objet' => 'fa-icon',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"icone":"fa-smile-o","align":"center","size":"52","color":"#ffffff","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":16,"open_window":0,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 9,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h4 style=\"text-align:center\"><span style=\"color:#ffffff\">Flexibilit&eacute; Totale</span></h4>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 10,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\" style=\"text-align:center\"><span style=\"color:#ffffff\">Profitez de vos formations 100% en ligne adapt&eacute;es &agrave; votre emploi du temps et &agrave; vos contraintes.</span></p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 11,
                                ],
                            ],
                        ],
                        [
                            'span' => 4,
                            'position' => 3,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 32,
                                'padding_bottom' => 48,
                                'padding_left' => 24,
                                'padding_right' => 48,
                                'bgcolor1' => '#6078C0',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 4,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 64,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 1,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '{"design":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_top\":0,\"margin_bottom\":0,\"margin_left\":0,\"margin_right\":0,\"border_radius_top_right\":0,\"border_radius_bottom_right\":0}"}',
                            'elements' => [
                                [
                                    'objet' => 'fa-icon',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"icone":"fa-smile-o","align":"center","size":"52","color":"#ffffff","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":16,"open_window":0,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 9,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h4 style=\"text-align:center\"><span style=\"color:#ffffff\">Suivi Personnalis&eacute;</span></h4>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 10,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\" style=\"text-align:center\"><span style=\"color:#ffffff\">B&eacute;n&eacute;ficiez de conseils personnalis&eacute;s pour appliquer les enseignements &agrave; votre entreprise</span></p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 11,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/background-graphic-2.png',
                        'bgimage_position' => 'scroll',
                        'bgimage_size' => 'auto',
                        'padding_top' => '64',
                        'padding_bottom' => '120',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => '',
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '{"design":"{\"padding_top\":\"10\",\"padding_bottom\":\"10\",\"margin_top\":\"0\",\"margin_bottom\":\"40\",\"padding_left\":\"10\",\"padding_right\":\"10\",\"margin_left\":\"0\",\"margin_right\":\"0\"}"}',
                    'cols' => [
                        [
                            'span' => 6,
                            'position' => 0,
                            'line_position' => 2,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 24,
                                'margin_bottom' => 120,
                                'margin_left' => 0,
                                'margin_right' => 48,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '{"design":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_top\":0,\"margin_bottom\":24,\"margin_left\":0,\"margin_right\":0}"}',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h5><strong><span style=\"color:#6078c0\">FORMATION</span></strong></h5>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":0,\"padding_bottom\":0,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":16,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2>Leadership Impactant</h2>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":0,\"padding_bottom\":0,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 4,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":32,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\">&Ecirc;tre un leader efficace est essentiel pour r&eacute;ussir en entrepreneuriat. Cette formation vous aidera &agrave; d&eacute;velopper vos comp&eacute;tences en leadership, &agrave; inspirer et &agrave; motiver votre &eacute;quipe, et &agrave; cr&eacute;er une culture d&#39;entreprise qui favorise l&#39;innovation et la performance exceptionnelle.</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 5,
                                ],
                                [
                                    'objet' => 'button_link',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bg_color1":"#2F2F2F","bg_color1Hover":"#6078C0","bg_color1Clicked":"#2F2F2F","color_text":"#ffffff","color_textHover":"#ffffff","color_textClicked":"#ffffff","size_text":13,"size_textHover":13,"size_textClicked":13,"border_radius":6,"border_radiusHover":6,"border_radiusClicked":6,"size":"btn-large","height":"btn-h-n","border_color":"","border_colorHover":"","border_colorClicked":"","color_icone":"#ffffff","color_iconeHover":"#ffffff","color_iconeClicked":"#ffffff","open_window":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"align":"left","icone":"","default_color_icone":"","action":"page","param":"","actions":[],"bg_color2":"","border":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"contenu":"D\u00c9COUVRIR NOS FORMATIONS","size_icone":14,"position_icone":"left","decalage_icone":0,"bg_gradient_angle":"","font_text":"Open Sans","font_styles":["bold"],"btn_padding_top":12,"btn_padding_bottom":12,"btn_padding_left":24,"btn_padding_right":24,"contenuHover":"","size_iconeHover":14,"position_iconeHover":"","decalage_iconeHover":0,"bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"Open Sans","font_stylesHover":["bold"],"borderHover":0,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":0,"button_box_shadow_colorHover":"","button_box_shadow_opacityHover":1,"btn_padding_topHover":12,"btn_padding_bottomHover":12,"btn_padding_leftHover":24,"btn_padding_rightHover":24,"contenuClicked":"","size_iconeClicked":14,"position_iconeClicked":"","decalage_iconeClicked":0,"bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"Open Sans","font_stylesClicked":["bold"],"borderClicked":0,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":0,"button_box_shadow_colorClicked":"","button_box_shadow_opacityClicked":1,"btn_padding_topClicked":12,"btn_padding_bottomClicked":12,"btn_padding_leftClicked":24,"btn_padding_rightClicked":24,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'DÉCOUVRIR NOS FORMATIONS',
                                    'contenu_mobile' => json_encode([
                                        'contenu' => json_encode([
                                            "size_text" => 14,
                                            "size_textHover" => 14,
                                            "size_textClicked" => 14,
                                            "height" => "btn-h-s",
                                            "btn_padding_top" => 6,
                                            "btn_padding_right" => 12,
                                            "btn_padding_bottom" => 6,
                                            "btn_padding_left" => 12,
                                            "btn_padding_topHover" => 6,
                                            "btn_padding_rightHover" => 12,
                                            "btn_padding_bottomHover" => 6,
                                            "btn_padding_leftHover" => 12,
                                            "btn_padding_topClicked" => 6,
                                            "btn_padding_rightClicked" => 12,
                                            "btn_padding_bottomClicked" => 6,
                                            "btn_padding_leftClicked" => 12,
                                        ])
                                    ]),
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 8,
                                ],
                            ],
                        ],
                        [
                            'span' => 6,
                            'position' => 0,
                            'line_position' => 3,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'col_height' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 374,
                                    'largeur' => 561,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":12,"img_border_radius_top_right":12,"img_border_radius_bottom_left":12,"img_border_radius_bottom_right":12,"img_border_radius_circle":0,"img_size":0,"button_box_shadow":1,"button_box_shadow_x":-24,"button_box_shadow_y":-24,"button_box_shadow_blur":0,"button_box_shadow_color":"#6078C0","button_box_shadow_opacity":1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://images.unsplash.com/photo-1524508762098-fd966ffb6ef9?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=Mnw5ODc5NHwwfDF8c2VhcmNofDZ8fHBlcnNvbiUyMG9uJTIwY29tcHV0ZXJ8ZW58MHwwfHx8MTY3MTUzMDE3OA&ixlib=rb-4.0.3&q=80&w=1080',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                            ],
                        ],
                        [
                            'span' => 6,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'col_height' => 0,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 120,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 374,
                                    'largeur' => 561,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":12,"img_border_radius_top_right":12,"img_border_radius_bottom_left":12,"img_border_radius_bottom_right":12,"img_border_radius_circle":0,"img_size":0,"button_box_shadow":1,"button_box_shadow_x":-24,"button_box_shadow_y":-24,"button_box_shadow_blur":0,"button_box_shadow_color":"#6078C0","button_box_shadow_opacity":1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://images.unsplash.com/photo-1524508762098-fd966ffb6ef9?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=Mnw5ODc5NHwwfDF8c2VhcmNofDZ8fHBlcnNvbiUyMG9uJTIwY29tcHV0ZXJ8ZW58MHwwfHx8MTY3MTUzMDE3OA&ixlib=rb-4.0.3&q=80&w=1080',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                            ],
                        ],
                        [
                            'span' => 6,
                            'position' => 1,
                            'line_position' => 2,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'col_height' => 0,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 120,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '{"design":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_top\":0,\"margin_bottom\":64,\"margin_left\":0,\"margin_right\":0}"}',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 374,
                                    'largeur' => 561,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":12,"img_border_radius_top_right":12,"img_border_radius_bottom_left":12,"img_border_radius_bottom_right":12,"img_border_radius_circle":0,"img_size":0,"button_box_shadow":1,"button_box_shadow_x":-24,"button_box_shadow_y":-24,"button_box_shadow_blur":0,"button_box_shadow_color":"#6078C0","button_box_shadow_opacity":1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://images.unsplash.com/photo-1524508762098-fd966ffb6ef9?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=Mnw5ODc5NHwwfDF8c2VhcmNofDZ8fHBlcnNvbiUyMG9uJTIwY29tcHV0ZXJ8ZW58MHwwfHx8MTY3MTUzMDE3OA&ixlib=rb-4.0.3&q=80&w=1080',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                            ],
                        ],
                        [
                            'span' => 6,
                            'position' => 1,
                            'line_position' => 3,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 24,
                                'margin_bottom' => 0,
                                'margin_left' => 48,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h5><strong><span style=\"color:#6078c0\">FORMATION</span></strong></h5>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":0,\"padding_bottom\":0,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":16,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2>Marketing Strat&eacute;gique et Branding</h2>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":0,\"padding_bottom\":0,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":32,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\">Le marketing est au c&oelig;ur de toute entreprise prosp&egrave;re. Cette formation vous enseignera les principes du marketing strat&eacute;gique, y compris la cr&eacute;ation d&#39;une image de marque forte, la d&eacute;finition de votre public cible et la mise en &oelig;uvre de campagnes de marketing efficaces pour augmenter la visibilit&eacute; et la croissance de votre entreprise.</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                                [
                                    'objet' => 'button_link',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bg_color1":"#2F2F2F","bg_color1Hover":"#6078C0","bg_color1Clicked":"#2F2F2F","color_text":"#ffffff","color_textHover":"#ffffff","color_textClicked":"#ffffff","size_text":13,"size_textHover":13,"size_textClicked":13,"border_radius":6,"border_radiusHover":6,"border_radiusClicked":6,"size":"btn-large","height":"btn-h-n","border_color":"","border_colorHover":"","border_colorClicked":"","color_icone":"#ffffff","color_iconeHover":"#ffffff","color_iconeClicked":"#ffffff","open_window":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"align":"left","icone":"","default_color_icone":"","action":"page","param":"","actions":[],"bg_color2":"","border":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"contenu":"D\u00c9COUVRIR NOS FORMATIONS","size_icone":14,"position_icone":"left","decalage_icone":0,"bg_gradient_angle":"","font_text":"Open Sans","font_styles":["bold"],"btn_padding_top":12,"btn_padding_bottom":12,"btn_padding_left":24,"btn_padding_right":24,"contenuHover":"","size_iconeHover":14,"position_iconeHover":"","decalage_iconeHover":0,"bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"Open Sans","font_stylesHover":["bold"],"borderHover":0,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":0,"button_box_shadow_colorHover":"","button_box_shadow_opacityHover":1,"btn_padding_topHover":12,"btn_padding_bottomHover":12,"btn_padding_leftHover":24,"btn_padding_rightHover":24,"contenuClicked":"","size_iconeClicked":14,"position_iconeClicked":"","decalage_iconeClicked":0,"bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"Open Sans","font_stylesClicked":["bold"],"borderClicked":0,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":0,"button_box_shadow_colorClicked":"","button_box_shadow_opacityClicked":1,"btn_padding_topClicked":12,"btn_padding_bottomClicked":12,"btn_padding_leftClicked":24,"btn_padding_rightClicked":24,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'DÉCOUVRIR NOS FORMATIONS',
                                    'contenu_mobile' => json_encode([
                                        'contenu' => json_encode([
                                            "size_text" => 14,
                                            "size_textHover" => 14,
                                            "size_textClicked" => 14,
                                            "height" => "btn-h-s",
                                            "btn_padding_top" => 6,
                                            "btn_padding_right" => 12,
                                            "btn_padding_bottom" => 6,
                                            "btn_padding_left" => 12,
                                            "btn_padding_topHover" => 6,
                                            "btn_padding_rightHover" => 12,
                                            "btn_padding_bottomHover" => 6,
                                            "btn_padding_leftHover" => 12,
                                            "btn_padding_topClicked" => 6,
                                            "btn_padding_rightClicked" => 12,
                                            "btn_padding_bottomClicked" => 6,
                                            "btn_padding_leftClicked" => 12,
                                        ])
                                    ]),
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 3,
                                ],
                            ],
                        ],
                        [
                            'span' => 2,
                            'position' => 1,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 80,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [],
                        ],
                        [
                            'span' => 6,
                            'position' => 2,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 24,
                                'margin_bottom' => 120,
                                'margin_left' => 48,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '{"design":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_top\":0,\"margin_bottom\":40,\"margin_left\":0,\"margin_right\":0}"}',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h5><strong><span style=\"color:#6078c0\">FORMATION</span></strong></h5>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":0,\"padding_bottom\":0,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":16,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2>Strat&eacute;gies de Croissance &Eacute;conomique</h2>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":0,\"padding_bottom\":0,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":32,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\">Cette formation approfondie vous guidera &agrave; travers les strat&eacute;gies &eacute;prouv&eacute;es pour stimuler la croissance &eacute;conomique de votre entreprise. Apprenez &agrave; identifier les opportunit&eacute;s de march&eacute;, &agrave; d&eacute;velopper des mod&egrave;les commerciaux solides et &agrave; mettre en &oelig;uvre des plans de croissance durables.</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                                [
                                    'objet' => 'button_link',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bg_color1":"#2F2F2F","bg_color1Hover":"#6078C0","bg_color1Clicked":"#2F2F2F","color_text":"#ffffff","color_textHover":"#ffffff","color_textClicked":"#ffffff","size_text":13,"size_textHover":13,"size_textClicked":13,"border_radius":6,"border_radiusHover":6,"border_radiusClicked":6,"size":"btn-large","height":"btn-h-n","border_color":"","border_colorHover":"","border_colorClicked":"","color_icone":"#ffffff","color_iconeHover":"#ffffff","color_iconeClicked":"#ffffff","open_window":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"align":"left","icone":"","default_color_icone":"","action":"page","param":"","actions":[],"bg_color2":"","border":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"contenu":"D\u00c9COUVRIR NOS FORMATIONS","size_icone":14,"position_icone":"left","decalage_icone":0,"bg_gradient_angle":"","font_text":"Open Sans","font_styles":["bold"],"btn_padding_top":12,"btn_padding_bottom":12,"btn_padding_left":24,"btn_padding_right":24,"contenuHover":"","size_iconeHover":14,"position_iconeHover":"","decalage_iconeHover":0,"bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"Open Sans","font_stylesHover":["bold"],"borderHover":0,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":0,"button_box_shadow_colorHover":"","button_box_shadow_opacityHover":1,"btn_padding_topHover":12,"btn_padding_bottomHover":12,"btn_padding_leftHover":24,"btn_padding_rightHover":24,"contenuClicked":"","size_iconeClicked":14,"position_iconeClicked":"","decalage_iconeClicked":0,"bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"Open Sans","font_stylesClicked":["bold"],"borderClicked":0,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":0,"button_box_shadow_colorClicked":"","button_box_shadow_opacityClicked":1,"btn_padding_topClicked":12,"btn_padding_bottomClicked":12,"btn_padding_leftClicked":24,"btn_padding_rightClicked":24,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'DÉCOUVRIR NOS FORMATIONS',
                                    'contenu_mobile' => json_encode([
                                        'contenu' => json_encode([
                                            "size_text" => 14,
                                            "size_textHover" => 14,
                                            "size_textClicked" => 14,
                                            "height" => "btn-h-s",
                                            "btn_padding_top" => 6,
                                            "btn_padding_right" => 12,
                                            "btn_padding_bottom" => 6,
                                            "btn_padding_left" => 12,
                                            "btn_padding_topHover" => 6,
                                            "btn_padding_rightHover" => 12,
                                            "btn_padding_bottomHover" => 6,
                                            "btn_padding_leftHover" => 12,
                                            "btn_padding_topClicked" => 6,
                                            "btn_padding_rightClicked" => 12,
                                            "btn_padding_bottomClicked" => 6,
                                            "btn_padding_leftClicked" => 12,
                                        ])
                                    ]),
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 4,
                                ],
                            ],
                        ],
                        [
                            'span' => 8,
                            'position' => 2,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'col_height' => 0,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 64,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":16,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2 style=\"text-align:center\"><span style=\"color:#6078c0\">Nos formations</span></h2>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":0,\"padding_bottom\":0,\"padding_left\":0,\"padding_right\":0,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\" style=\"text-align:center\">Atteignez les sommets de la r&eacute;ussite entrepreneuriale avec nos formations personnalis&eacute;es. Quel que soit votre objectif, d&eacute;marrer, cro&icirc;tre ou innover, nos programmes vous fournissent des comp&eacute;tences concr&egrave;tes et des strat&eacute;gies test&eacute;es. Optez pour la formation qui r&eacute;pond &agrave; vos besoins d&egrave;s maintenant !</p>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":40,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => 'https://images.unsplash.com/photo-1572021335469-31706a17aaef?crop=entropy&cs=tinysrgb&fm=jpg&ixid=Mnw5ODc5NHwwfDF8c2VhcmNofDY3fHxjb3Jwb3JhdGUlMjBoYXBweSUyMGxhcHRvcCUyMHxlbnwwfDB8fHwxNjcxNTMzNzA2&ixlib=rb-4.0.3&q=80',
                        'bgimage_position' => 'fixed',
                        'bgimage_size' => 'cover',
                        'padding_top' => '80',
                        'padding_bottom' => '80',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'line_theme' => 'line_theme3',
                        'gradient_angle' => 180,
                        'bg_color_overlay' => '',
                        'bg_color_overlay_opacity' => 0.11,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '#6078C0',
                        'bg_color_overlay2' => '',
                        'bg_overlay_gradient_angle' => 0,
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 4,
                            'position' => 1,
                            'line_position' => 0,
                            'design' => [
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay' => '',
                                'bg_color_overlay_opacity' => 0,
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_style' => 'none',
                                'border_radius_top_left' => 8,
                                'border_radius_top_right' => 8,
                                'border_radius_bottom_left' => 8,
                                'border_radius_bottom_right' => 8,
                                'box_shadow_x' => 5,
                                'box_shadow_y' => 5,
                                'box_shadow_blur' => 26,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 0.17,
                                'delay' => 0,
                                'delay_disappear' => 0,
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'top',
                                'bg_side_width' => 100,
                                'bg_side_height' => 100,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'col_height' => 0,
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_overlay_gradient_angle' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [],
                        ],
                        [
                            'span' => 8,
                            'position' => 2,
                            'line_position' => 0,
                            'design' => [
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay' => '',
                                'bg_color_overlay_opacity' => 0.85,
                                'padding_top' => 64,
                                'padding_bottom' => 64,
                                'padding_left' => 64,
                                'padding_right' => 64,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_style' => 'none',
                                'border_radius_top_left' => 8,
                                'border_radius_top_right' => 8,
                                'border_radius_bottom_left' => 8,
                                'border_radius_bottom_right' => 8,
                                'box_shadow_x' => 5,
                                'box_shadow_y' => 5,
                                'box_shadow_blur' => 26,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 0.17,
                                'delay' => 0,
                                'delay_disappear' => 0,
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'top',
                                'bg_side_width' => 100,
                                'bg_side_height' => 100,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'col_height' => 0,
                                'bg_color_overlay1' => '#2F2F2F',
                                'bg_color_overlay2' => '',
                                'bg_overlay_gradient_angle' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h1><span style=\"color:#ffffff\">C&#39;est le moment d&#39;acc&eacute;l&eacute;rer votre succ&egrave;s, rapidement et sans limite.</span></h1>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h3","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_top":0,"margin_bottom":0,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h3><span style=\"color:#ffffff\"><span>Obtenez des conseils strat&eacute;giques personnalis&eacute;s</span></span></h3>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_top":0,"margin_bottom":24,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p><span style=\"color:#ffffff\"><span style=\"font-size:20px\">Inscrivez-vous d&egrave;s maintenant et ouvrez la porte vers un avenir prosp&egrave;re. Votre r&eacute;ussite commence ici !</span></span></p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                                [
                                    'objet' => 'button_link',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bg_color1":"#6078C0","bg_color1Hover":"#ffffff","bg_color1Clicked":"#6078C0","color_text":"#ffffff","color_textHover":"#2F2F2F","color_textClicked":"#ffffff","size_text":16,"size_textHover":16,"size_textClicked":16,"border_radius":6,"border_radiusHover":6,"border_radiusClicked":6,"size":"btn-large","height":"btn-h-b","border_color":"#6078C0","border_colorHover":"#ffffff","border_colorClicked":"#6078C0","color_icone":"#ffffff","color_iconeHover":"#ffffff","color_iconeClicked":"#ffffff","open_window":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"align":"left","icone":"","default_color_icone":"","action":"page","param":"","actions":[],"bg_color2":"","border":2,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"contenu":"D\u00c9COUVRIR NOS FORMATIONS","size_icone":14,"position_icone":"left","decalage_icone":0,"bg_gradient_angle":"","font_text":"Open Sans","font_styles":["bold"],"btn_padding_top":16,"btn_padding_bottom":16,"btn_padding_left":32,"btn_padding_right":32,"contenuHover":"","size_iconeHover":14,"position_iconeHover":"","decalage_iconeHover":0,"bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"Open Sans","font_stylesHover":["bold"],"borderHover":2,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":0,"button_box_shadow_colorHover":"","button_box_shadow_opacityHover":1,"btn_padding_topHover":16,"btn_padding_bottomHover":16,"btn_padding_leftHover":32,"btn_padding_rightHover":32,"contenuClicked":"","size_iconeClicked":14,"position_iconeClicked":"","decalage_iconeClicked":0,"bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"Open Sans","font_stylesClicked":["bold"],"borderClicked":2,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":0,"button_box_shadow_colorClicked":"","button_box_shadow_opacityClicked":1,"btn_padding_topClicked":16,"btn_padding_bottomClicked":16,"btn_padding_leftClicked":32,"btn_padding_rightClicked":32,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'DÉCOUVRIR NOS FORMATIONS',
                                    'contenu_mobile' => json_encode([
                                        'contenu' => json_encode([
                                            "size_text" => 14,
                                            "size_textHover" => 14,
                                            "size_textClicked" => 14,
                                            "height" => "btn-h-s",
                                            "btn_padding_top" => 6,
                                            "btn_padding_right" => 12,
                                            "btn_padding_bottom" => 6,
                                            "btn_padding_left" => 12,
                                            "btn_padding_topHover" => 6,
                                            "btn_padding_rightHover" => 12,
                                            "btn_padding_bottomHover" => 6,
                                            "btn_padding_leftHover" => 12,
                                            "btn_padding_topClicked" => 6,
                                            "btn_padding_rightClicked" => 12,
                                            "btn_padding_bottomClicked" => 6,
                                            "btn_padding_leftClicked" => 12,
                                        ])
                                    ]),
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 5,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'padding_top' => '120',
                        'padding_bottom' => '120',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => '',
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 6,
                            'position' => 0,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 48,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2>Atteignez des r&eacute;sultats inattendus en quelques semaines !</h2>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                                [
                                    'objet' => 'bullets',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"Open Sans","font_size":16,"font_color":"#2F2F2F","decalage_left":16,"decalage_bottom":4,"icone":"fa-check-square","icone_size":24,"icone_color":"#6078C0","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","decalage_top":0,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '["<p>Augmentez vos ventes et vos revenus gr&acirc;ce aux strat&eacute;gies enseign&eacute;es pour optimiser votre processus de vente et attirer efficacement vos clients.<\/p>\r\n","<p>Gr&acirc;ce &agrave; des m&eacute;thodes marketing &eacute;prouv&eacute;es, &eacute;largissez votre base de clients et attirez de nouveaux prospects int&eacute;ress&eacute;s par votre produit \/ service.<\/p>\r\n","<p>Renforcez votre confiance en tant qu&#39;entrepreneur, surmontez les obstacles et les incertitudes gr&acirc;ce aux pr&eacute;cieux conseils personnalis&eacute;s de nos experts !<\/p>\r\n"]',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '0',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 3,
                                ],
                            ],
                        ],
                        [
                            'span' => 6,
                            'position' => 1,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'col_height' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 374,
                                    'largeur' => 561,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":12,"img_border_radius_top_right":12,"img_border_radius_bottom_left":12,"img_border_radius_bottom_right":12,"img_border_radius_circle":0,"img_size":0,"button_box_shadow":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://images.unsplash.com/photo-1524508762098-fd966ffb6ef9?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=Mnw5ODc5NHwwfDF8c2VhcmNofDZ8fHBlcnNvbiUyMG9uJTIwY29tcHV0ZXJ8ZW58MHwwfHx8MTY3MTUzMDE3OA&ixlib=rb-4.0.3&q=80&w=1080',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=Mnw5ODc5NHwwfDF8c2VhcmNofDExN3x8cGVyc29uJTIwb24lMjBpbnRlcnZpZXd8ZW58MHwwfHx8MTY3MTUyNjI5MQ&ixlib=rb-4.0.3&q=80&w=1080',
                        'bgimage_position' => 'fixed',
                        'bgimage_size' => 'cover',
                        'padding_top' => '120',
                        'padding_bottom' => '120',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'line_theme' => 'line_theme4',
                        'gradient_angle' => 180,
                        'bg_color_overlay' => '',
                        'bg_color_overlay_opacity' => 0.6,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_overlay_gradient_angle' => 0,
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 2,
                            'position' => 1,
                            'line_position' => 0,
                            'design' => [
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => '0',
                                'bgimage' => '',
                                'bgimage_position' => '',
                                'bgimage_size' => 'auto',
                                'bg_color_overlay' => '',
                                'bg_color_overlay_opacity' => '0',
                                'padding_top' => '0',
                                'padding_bottom' => '0',
                                'padding_left' => '10',
                                'padding_right' => '10',
                                'margin_top' => '0',
                                'margin_bottom' => '0',
                                'border_width' => '0',
                                'border_color' => '',
                                'border_color_opacity' => '1',
                                'border_style' => 'none',
                                'border_radius_top_left' => '8',
                                'border_radius_top_right' => '8',
                                'border_radius_bottom_left' => '8',
                                'border_radius_bottom_right' => '8',
                                'box_shadow_x' => '5',
                                'box_shadow_y' => '5',
                                'box_shadow_blur' => '26',
                                'box_shadow_color' => '#000000',
                                'box_shadow_opacity' => '0.17',
                                'delay' => '0',
                                'delay_disappear' => '0',
                                'full_width' => '0',
                                'box_shadow' => '0',
                                'bg_side' => '0',
                                'bg_side_position' => '',
                                'bg_side_width' => '100',
                                'bg_side_height' => '100',
                                'margin_left' => '0',
                                'margin_right' => '0',
                                'col_height' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [],
                        ],
                        [
                            'span' => 8,
                            'position' => 2,
                            'line_position' => 0,
                            'design' => [
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay' => '',
                                'bg_color_overlay_opacity' => 0,
                                'padding_top' => 0,
                                'padding_bottom' => 0,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_style' => 'none',
                                'border_radius_top_left' => 8,
                                'border_radius_top_right' => 8,
                                'border_radius_bottom_left' => 8,
                                'border_radius_bottom_right' => 8,
                                'box_shadow_x' => 5,
                                'box_shadow_y' => 5,
                                'box_shadow_blur' => 26,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 0.17,
                                'delay' => 0,
                                'delay_disappear' => 0,
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'top',
                                'bg_side_width' => 100,
                                'bg_side_height' => 100,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'col_height' => 0,
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_overlay_gradient_angle' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2 style=\"text-align:center\"><span style=\"color:#ffffff\">Vous souhaitez passer &agrave; la vitesse sup&eacute;rieure et atteindre le succ&egrave;s ?</span></h2>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_top":18,"margin_bottom":32,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p style=\"text-align:center\"><span style=\"color:#ffffff\"><span style=\"font-size:20px\">Ne laissez pas cette opportunit&eacute; vous &eacute;chapper, inscrivez-vous d&egrave;s maintenant et transformez votre parcours entrepreneurial !</span></span></p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                                [
                                    'objet' => 'button_link',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bg_color1":"#000000","bg_color1Hover":"#ffffff","bg_color1Clicked":"#000000","color_text":"#ffffff","color_textHover":"#000000","color_textClicked":"#ffffff","size_text":15,"size_textHover":15,"size_textClicked":15,"border_radius":6,"border_radiusHover":6,"border_radiusClicked":6,"size":"btn-large","height":"btn-h-b","border_color":"#000000","border_colorHover":"#ffffff","border_colorClicked":"#000000","color_icone":"","color_iconeHover":"","color_iconeClicked":"","open_window":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"align":"center","icone":"","default_color_icone":"","action":"page","param":"","actions":[],"bg_color2":"","border":3,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"contenu":"D\u00c9COUVRIR NOS FORMATIONS","size_icone":14,"position_icone":"left","decalage_icone":0,"bg_gradient_angle":180,"font_text":"Open Sans","font_styles":["bold"],"btn_padding_top":14,"btn_padding_bottom":14,"btn_padding_left":32,"btn_padding_right":32,"contenuHover":"","size_iconeHover":14,"position_iconeHover":"","decalage_iconeHover":0,"bg_color2Hover":"","bg_gradient_angleHover":180,"font_textHover":"Open Sans","font_stylesHover":["bold"],"borderHover":3,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":0,"button_box_shadow_colorHover":"","button_box_shadow_opacityHover":1,"btn_padding_topHover":14,"btn_padding_bottomHover":14,"btn_padding_leftHover":32,"btn_padding_rightHover":32,"contenuClicked":"","size_iconeClicked":14,"position_iconeClicked":"","decalage_iconeClicked":0,"bg_color2Clicked":"","bg_gradient_angleClicked":180,"font_textClicked":"Open Sans","font_stylesClicked":["bold"],"borderClicked":3,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":0,"button_box_shadow_colorClicked":"","button_box_shadow_opacityClicked":1,"btn_padding_topClicked":14,"btn_padding_bottomClicked":14,"btn_padding_leftClicked":32,"btn_padding_rightClicked":32,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'DÉCOUVRIR NOS FORMATIONS',
                                    'contenu_mobile' => json_encode([
                                        'contenu' => json_encode([
                                            "size_text" => 14,
                                            "size_textHover" => 14,
                                            "size_textClicked" => 14,
                                            "height" => "btn-h-s",
                                            "btn_padding_top" => 6,
                                            "btn_padding_right" => 12,
                                            "btn_padding_bottom" => 6,
                                            "btn_padding_left" => 12,
                                            "btn_padding_topHover" => 6,
                                            "btn_padding_rightHover" => 12,
                                            "btn_padding_bottomHover" => 6,
                                            "btn_padding_leftHover" => 12,
                                            "btn_padding_topClicked" => 6,
                                            "btn_padding_rightClicked" => 12,
                                            "btn_padding_bottomClicked" => 6,
                                            "btn_padding_leftClicked" => 12,
                                        ])
                                    ]),
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 3,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'padding_top' => '80',
                        'padding_bottom' => '80',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => '',
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 12,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'col_height' => 0,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'articles',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"nb_articles":8,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"nb_articles_per_col":3,"template":"template2","margin_top":0,"margin_left":0,"margin_right":0,"contenu":"Lire la suite","size_icone":14,"color_icone":"","position_icone":"left","decalage_icone":0,"bg_color1":"#6078C0","bg_color2":"","bg_gradient_angle":"","font_text":"default","color_text":"#ffffff","size_text":12,"font_styles":[],"border":0,"border_color":"","border_radius":6,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"#a7be69","btn_padding_top":6,"btn_padding_bottom":6,"btn_padding_left":20,"btn_padding_right":20,"contenuHover":"","size_iconeHover":14,"color_iconeHover":"","position_iconeHover":"","decalage_iconeHover":0,"bg_color1Hover":"#2F2F2F","bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"default","color_textHover":"#ffffff","size_textHover":12,"font_stylesHover":[],"borderHover":0,"border_colorHover":"","border_radiusHover":6,"button_box_shadow_xHover":6,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":0,"button_box_shadow_colorHover":"","btn_padding_topHover":6,"btn_padding_bottomHover":6,"btn_padding_leftHover":20,"btn_padding_rightHover":20,"contenuClicked":"","size_iconeClicked":14,"color_iconeClicked":"","position_iconeClicked":"","decalage_iconeClicked":0,"bg_color1Clicked":"#6078C0","bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"default","color_textClicked":"#ffffff","size_textClicked":12,"font_stylesClicked":[],"borderClicked":0,"border_colorClicked":"","border_radiusClicked":6,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":0,"button_box_shadow_colorClicked":"#a7be69","btn_padding_topClicked":6,"btn_padding_bottomClicked":6,"btn_padding_leftClicked":20,"btn_padding_rightClicked":20,"align":"left","size":"btn-large","height":"btn-h-s","font":"default","font_size":16,"font_color":"#2F2F2F","title_font":"default","title_font_size":22,"title_font_color":"#2F2F2F","article_bg_color":"#ffffff","article_border":"0","article_border_color":"","article_border_radius":8,"article_box_shadow_x":0,"article_box_shadow_y":2,"article_box_shadow_blur":18,"article_box_shadow_color":"#2F2F2F","article_box_shadow_opacity":0.12,"icone":"","default_color_icone":"","id_categorie":0,"button_box_shadow_opacity":1,"button_box_shadow_opacityHover":1,"button_box_shadow_opacityClicked":1,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"box_shadow":0,"box_shadow_x":6,"box_shadow_y":6,"box_shadow_blur":18,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'Lire la suite',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 3,
                                ],
                            ],
                        ],
                        [
                            'span' => 1,
                            'position' => 1,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [],
                        ],
                        [
                            'span' => 10,
                            'position' => 2,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2 style=\"text-align:center\"><span style=\"color:#6078c0\">Articles de blog</span></h2>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h5 style=\"text-align:center\"><strong>Plongez dans notre riche biblioth&egrave;que d&#39;articles de blog con&ccedil;us pour &eacute;clairer et guider les entrepreneurs ambitieux comme vous.</strong></h5>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":32,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\" style=\"text-align:center\">Explorez nos sujets vari&eacute;s, des techniques de croissance aux astuces de gestion du temps, en passant par les histoires inspirantes d&#39;entrepreneurs &agrave; succ&egrave;s. Que vous soyez en train de d&eacute;marrer, de d&eacute;velopper ou d&#39;optimiser votre entreprise, nos articles sont l&agrave; pour vous accompagner &agrave; chaque &eacute;tape de votre voyage entrepreneurial.</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }
}
