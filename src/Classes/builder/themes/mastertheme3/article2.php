<?php

/**
 * Builder_Themes_Mastertheme3_article2 class.
 *
 * @extends  Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see      http://thieumlabs.com
 */
class Builder_Themes_Mastertheme3_article2 extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        $theme_datas = [];
        $theme_datas['lines'][] = [
            'design' => [
                'margin_top' => 0,
                'margin_bottom' => 0,
                'margin_left' => 0,
                'margin_right' => 0,
                'padding_top' => 0,
                'padding_bottom' => 0,
                'padding_left' => 0,
                'padding_right' => 0,
            ],
            'cols' => [
                [
                    'span' => '12',
                    'position' => '1',
                    'line_position' => '0',
                    'contenu_mobile' => '',
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"h4"}',
                            'contenutexte' => '<h4>Lommodo ligula eget dolor. Aenean massa. Cum sociis que penatibus et magnis dis parturient montes lorem, nascetur ridiculus mus. Donec quam felis, ultricies nec, pellentesque eu, pretium quis, sem. Nulla onsequat massa quis enim. Donec pede justo fringilla vel aliquet nec vulputate eget. Lorem ispum dolore siamet ipsum dolor.</h4>',
                            'contenu_mobile' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'margin_top' => 0,
                'margin_bottom' => 0,
                'margin_left' => 0,
                'margin_right' => 0,
                'padding_top' => 0,
                'padding_bottom' => 0,
                'padding_left' => 0,
                'padding_right' => 0,
            ],
            'cols' => [
                [
                    'span' => '12',
                    'position' => '1',
                    'line_position' => '0',
                    'contenu_mobile' => '',
                    'elements' => [
                        [
                            'objet' => 'image',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => '',
                            'contenu_mobile' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '[[FULLWIDTH]]',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'margin_top' => 0,
                'margin_bottom' => 0,
                'margin_left' => 0,
                'margin_right' => 0,
                'padding_top' => 0,
                'padding_bottom' => 0,
                'padding_left' => 0,
                'padding_right' => 0,
            ],
            'cols' => [
                [
                    'span' => '12',
                    'position' => '1',
                    'line_position' => '0',
                    'contenu_mobile' => '',
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"p"}',
                            'contenutexte' => '<p>Morbi vitae purus dictum, ultrices tellus in, gravida lectus. Vestibulum dignissim maximus massa et faucibus. Nullam sodales luctus arcu varius rhoncus. Nam sagittis, lectus sed sodales maximus, turpis ante placerat diam, vitae pellentesque orci odio ut sem. Proin fringilla finibus purus. Aliquam tincidunt nisl ut condimentum maximus. Proin sit amet orci at urna dignissim auctor a vitae tortor. Etiam viverra nibh et posuere eleifend. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aliquam eu mollis ante. Nunc eu lacus fermentum enim volutpat vestibulum.</p>',
                            'contenu_mobile' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'margin_top' => 0,
                'margin_bottom' => 20,
                'margin_left' => 0,
                'margin_right' => 0,
                'padding_top' => 50,
                'padding_bottom' => 50,
                'padding_left' => 50,
                'padding_right' => 50,
                'line_theme' => 'line_theme4',
                'fullwidth' => '0',
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '[[FULLWIDTH]]',
                'bgimage_position' => 'scroll',
                'bgimage_size' => 'cover',
            ],
            'cols' => [
                [
                    'span' => '12',
                    'position' => '1',
                    'line_position' => '0',
                    'contenu_mobile' => '',
                    'design' => [
                        'bgcolor1' => '#ffffff',
                        'padding_top' => '40',
                        'padding_bottom' => '40',
                        'padding_left' => '40',
                        'padding_right' => '40',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_radius_top_left' => '0',
                        'border_radius_top_right' => '0',
                        'border_radius_bottom_left' => '0',
                        'border_radius_bottom_right' => '0',
                        'box_shadow_x' => '0',
                        'box_shadow_y' => '22',
                        'box_shadow_blur' => '54',
                        'box_shadow_color' => '#000000',
                        'box_shadow_opacity' => '0.50',
                        'delay' => '0',
                        'delay_disappear' => '0',
                        'full_width' => '0',
                        'box_shadow' => '1',
                        'bg_side' => '',
                        'bg_side_position' => '',
                        'bg_side_width' => '100',
                        'bg_side_height' => '100',
                    ],
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"h4"}',
                            'contenutexte' => '<h4 style="text-align: center">' . _('Envie de recevoir des infos chaque semaine ?') . '<br>' . _('Inscrivez-vous gratuitement à la newsletter !') . '</h4>',
                            'contenu_mobile' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'lbar_optin',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0}',
                            'contenutexte' => '',
                            'contenu_mobile' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        return $theme_datas;
    }
}
