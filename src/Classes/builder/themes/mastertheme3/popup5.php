<?php

/**
 * Builder_Themes_Mastertheme3_popupbgimage class.
 *
 * @extends  Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Themes_Mastertheme3_popup5 extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        $theme_datas = [];

        $theme_datas['height'] = 550;
        $theme_datas['design'] = [
            'bgcolor1' => '#ebebeb',
            'bgcolor2' => '',
            'bgimage' => '',
            'padding_top' => '20',
            'padding_bottom' => '20',
            'margin_top' => '0',
            'margin_bottom' => '0',
            'animation' => 'fadeInDown',
            'animation_disappear' => 'fadeOutUp',
        ];

        $theme_datas['lines'][] = [
            'id_version' => 1,
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'auto',
                'padding_top' => '',
                'padding_bottom' => '0',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'cols' => [
                [
                    'span' => 12,
                    'design' => [
                        'padding_top' => 0,
                        'padding_bottom' => 0,
                        'padding_left' => 0,
                        'padding_right' => 0,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"h2","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<h2 style=\"text-align: center;\">' . mb_strtoupper(_('Inscrivez-vous à la newsletter !')) . '</h2>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'id_version' => 1,
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'auto',
                'padding_top' => '',
                'padding_bottom' => '10',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'cols' => [
                [
                    'span' => 4,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'image',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '0',
                            'contenu' => '{"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => 'left',
                            'time' => '0',
                            'name' => '',
                            'urls' => '[[EBOOK]]',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                        ],
                    ],
                ],
                [
                    'span' => 8,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 20,
                        'padding_right' => 20,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"p","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<p>Batnae municipium in Anthemusia conditum Macedonum manu priscorum ab Euphrate flumine brevi spatio disparatur, refertum mercatoribus opulentis, ubi annua sollemnitate prope Septembris initium mensis ad nundinas magna promiscuae fortunae convenit multitudo ad commercanda quae Indi mittunt et Seres aliaque plurima vehi terra marique consueta.</p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'bullets',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_size":14,"font_color":"","decalage_left":0,"decalage_bottom":0,"icone":"fa-check","icone_size":20,"icone_color":"#ff0000","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '["Lorem ipsum dolor sit amet, consectetuer adipiscing","Lorem ipsum dolor sit amet","Lorem ipsum dolor sit amet, consectetuer adipiscing","Lorem ipsum dolor sit amet, consectetuer"]',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '0',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'id_version' => 1,
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'auto',
                'padding_top' => '',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'delay' => '0',
                'delay_disappear' => '0',
                'line_theme' => 'line_theme3',
            ],
            'cols' => [
                [
                    'span' => 4,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 10,
                        'padding_left' => 0,
                        'padding_right' => 0,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'optin_field',
                            'bloc_alignement' => 'auto',
                            'span' => 'span4',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"field":{"name":"prenom","displayName":"","value":"","placeholder":"' . __('Votre prénom') . '","get":"","active":true,"obligatoire":false},"template":"template7","font":"","font_size":18,"font_color":"","champs_bgcolor":"","champs_bgcolor_transparent":false,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                        ],
                    ],
                ],
                [
                    'span' => 4,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 10,
                        'padding_left' => 0,
                        'padding_right' => 0,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'optin_field',
                            'bloc_alignement' => 'auto',
                            'span' => 'span4',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"field":{"name":"email","displayName":"","value":"","placeholder":"' . __('Votre adresse email') . '","get":"","active":true,"obligatoire":true},"template":"template7","font":"","font_size":18,"font_color":"","champs_bgcolor":"","champs_bgcolor_transparent":false,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                        ],
                    ],
                ],
                [
                    'span' => 4,
                    'design' => [
                        'padding_top' => 20,
                        'padding_bottom' => 20,
                        'padding_left' => 0,
                        'padding_right' => 0,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'button_submit',
                            'bloc_alignement' => 'auto',
                            'span' => 'span4',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '1',
                            'contenu' => '{"icone":"","align":"center","open_window":0,"color":"#2D2D2D","color_text":"#ffffff","size":"btn-xlarge","sizeHover":"btn-xlarge","sizeClicked":"btn-xlarge","height":"btn-h-n","size_text":20,"size_textHover":20,"size_textClicked":20,"border":0,"border_color":"#cccccc","border_radius":0,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => __('Je m\'inscris'),
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'id_version' => 1,
            'cols' => [
                [
                    'span' => 12,
                    'design' => [
                        'padding_top' => 0,
                        'padding_bottom' => 0,
                        'padding_left' => 0,
                        'padding_right' => 0,
                    ],
                    'line_position' => 1,
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"default","font_theme":"p","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => '<p style=\"text-align: center;\"><i class=\"fa fa-lock\">&nbsp;</i>&nbsp;' . __('Vos informations sont confidentielles.') . '</p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => 'fadeIn',
                            'delay_disappear' => '0',
                            'animation_disappear' => 'fadeOut',
                            'hide' => '0',
                        ],
                    ],
                ],
            ],
        ];

        return $theme_datas;
    }
}
