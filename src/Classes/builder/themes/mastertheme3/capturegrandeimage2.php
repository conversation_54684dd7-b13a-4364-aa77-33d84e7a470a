<?php

/**
 * Builder_Themes_Mastertheme3_capturegrandeimage2 class.
 *
 * @extends  Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Themes_Mastertheme3_capturegrandeimage2 extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        $theme_datas = [];
        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '[[FULLWIDTH]]',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'cover',
                'padding_top' => '100',
                'padding_bottom' => '360',
                'margin_top' => '0',
                'margin_bottom' => '0',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
                'line_theme' => 'line_theme4',
                'gradient_angle' => '0',
                'bg_color_overlay' => '',
                'bg_color_overlay_opacity' => '0',
                'padding_left' => '100',
                'padding_right' => '100',
                'margin_left' => '0',
                'margin_right' => '0',
                'border_width' => '0',
                'border_color' => '',
                'border_color_opacity' => '1',
                'border_radius_top_left' => '0',
                'border_radius_top_right' => '0',
                'border_radius_bottom_left' => '0',
                'border_radius_bottom_right' => '0',
                'border_style' => 'none',
                'box_shadow_x' => '0',
                'box_shadow_y' => '0',
                'box_shadow_blur' => '0',
                'box_shadow_color' => '',
                'box_shadow_opacity' => '1',
                'box_shadow' => '0',
                'bg_color_overlay1' => '',
                'bg_color_overlay2' => '',
                'bg_overlay_gradient_angle' => '135',
            ],
            'cols' => [
                [
                    'span' => '12',
                    'position' => '0',
                    'line_position' => '1',
                    'design' => [
                        'padding_top' => '10',
                        'padding_bottom' => '10',
                        'padding_left' => '10',
                        'padding_right' => '10',
                    ],
                    'elements' => [
                        [
                            'objet' => 'image',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '',
                            'largeur' => '180',
                            'eval' => '',
                            'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":10,"alt":""}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => 'center',
                            'time' => '0',
                            'name' => '',
                            'urls' => '[[LOGO]]',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
                [
                    'span' => '12',
                    'position' => '0',
                    'line_position' => '2',
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'gradient_angle' => '0',
                        'bgimage' => '',
                        'bgimage_position' => '',
                        'bgimage_size' => 'auto',
                        'bg_color_overlay' => '',
                        'bg_color_overlay_opacity' => '0',
                        'padding_top' => '36',
                        'padding_bottom' => '62',
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_top' => '64',
                        'margin_bottom' => '63',
                        'border_width' => '0',
                        'border_color' => '',
                        'border_color_opacity' => '1',
                        'border_style' => 'none',
                        'border_radius_top_left' => '0',
                        'border_radius_top_right' => '0',
                        'border_radius_bottom_left' => '0',
                        'border_radius_bottom_right' => '0',
                        'box_shadow_x' => '0',
                        'box_shadow_y' => '0',
                        'box_shadow_blur' => '0',
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => '1',
                        'delay' => '0',
                        'delay_disappear' => '0',
                        'full_width' => '0',
                        'box_shadow' => '0',
                        'bg_side' => '',
                        'bg_side_position' => '',
                        'bg_side_width' => '100',
                        'bg_side_height' => '100',
                        'margin_left' => '0',
                        'margin_right' => '0',
                    ],
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":23,"padding_right":23}',
                            'contenutexte' => '<h1 style=\"text-align: center; color: #FFFFFF;\">[[ACCROCHE]]</h1>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"","font_theme":"h3"}',
                            'contenutexte' => '<h3 style=\"text-align: center;\"><span style=\"font-size: 24px; color: rgb(255, 255, 255);\">[[SUB_ACCROCHE]]</span></h3>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'button_link',
                            'bloc_alignement' => '',
                            'span' => 'span6',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '1',
                            'contenu' => '{"contenu":"[[BTN]]","icone":"","color_icone":"","align":"center","color":"#B8CC7F","color_text":"#ffffff","size":"btn-large","size_text":24,"border":0,"border_color":"#ccc","border_radius":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"#a2b96f","contenuHover":"","iconeHover":"","color_iconeHover":"","alignHover":"center","colorHover":"","color_textHover":"#ffffff","sizeHover":"btn-large","size_textHover":24,"borderHover":0,"border_colorHover":"","border_radiusHover":0,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":10,"button_box_shadow_colorHover":"#BBBBBB","contenuClicked":"","iconeClicked":"","color_iconeClicked":"","alignClicked":"center","colorClicked":"","color_textClicked":"#ffffff","sizeClicked":"btn-large","size_textClicked":24,"borderClicked":0,"border_colorClicked":"","border_radiusClicked":0,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":10,"button_box_shadow_colorClicked":"#BBBBBB","action":"","param":"","position_icone":"left","decalage_icone":20,"position_iconeHover":"left","decalage_iconeHover":20,"position_iconeClicked":"left","decalage_iconeClicked":20,"size_icone":26,"size_iconeHover":26,"size_iconeClicked":26,"default_color_icone":"","bg_color1":"#FFFFFF","force_color":true,"bg_color2":"","bg_gradient_angle":"","font_text":"","font_styles":["bold"],"btn_padding_top":25,"btn_padding_bottom":25,"btn_padding_left":60,"btn_padding_right":60,"bg_color1Hover":"#9cb860","bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"","font_stylesHover":[],"btn_padding_topHover":25,"btn_padding_bottomHover":25,"btn_padding_leftHover":60,"btn_padding_rightHover":60,"bg_color1Clicked":"#859e52","bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"","font_stylesClicked":[],"btn_padding_topClicked":25,"btn_padding_bottomClicked":25,"btn_padding_leftClicked":60,"btn_padding_rightClicked":60,"margin_top":33,"margin_bottom":0,"height":"btn-h-vb"}',
                            'contenutexte' => '[[BTN]]',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        return $theme_datas;
    }
}
