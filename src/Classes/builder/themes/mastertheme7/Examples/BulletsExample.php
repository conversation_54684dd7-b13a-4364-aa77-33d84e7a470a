<?php

namespace Learnybox\Classes\builder\themes\mastertheme7\Examples;

class BulletsExample
{
    public static function getExamples(): array
    {
        $examples = [];

        $examples[0]['elements'][0]['update']['contenutexte'] = '["<p open=\"\" sans=\"\" style=\"color:#2f2f2f\">Lorem ipsum dolor sit amet, consectetur adipiscing elit.<\/p>\r\n","<p open=\"\" sans=\"\" style=\"color:#2f2f2f\">Lorem ipsum dolor sit amet, consectetur adipiscing elit.<\/p>\r\n","<p open=\"\" sans=\"\" style=\"color:#2f2f2f\">Lorem ipsum dolor sit amet, consectetur adipiscing elit.<\/p>\r\n"]';

        $examples[0]['elements'][0]['update']['contenu'] = json_encode([
            'font' => 'Open Sans',
            'font_size' => 16,
            'font_color' => '#354463',
            'decalage_left' => 12,
            'decalage_bottom' => 8,
            'icone' => 'fa-solid fa-square-check',
            'icone_size' => 24,
            'icone_color' => '#E6AD89',
            'padding_top' => 0,
            'padding_bottom' => 0,
            'padding_left' => 0,
            'padding_right' => 0,
            'word_break' => 'by_default',
            'decalage_top' => 1,
            'bgcolor1' => '',
            'bgcolor2' => '',
            'element_bg_gradient_angle' => 180,
            'bg_color' => '',
            'bg_opacity' => '1',
            'bg_opacity2' => '1',
            'bg_border' => 0,
            'bg_border_color' => '#000000',
            'bg_border_color_opacity' => '0',
            'bg_border_style' => 'none',
            'bg_border_radius_top_left' => 0,
            'bg_border_radius_top_right' => 0,
            'bg_border_radius_bottom_left' => 0,
            'bg_border_radius_bottom_right' => 0,
            'margin_top' => 10,
            'margin_bottom' => 10,
            'margin_left' => 0,
            'margin_right' => 0,
            'box_shadow' => 0,
            'box_shadow_x' => 0,
            'box_shadow_y' => 0,
            'box_shadow_blur' => 0,
            'box_shadow_color' => '',
            'box_shadow_opacity' => 1,
            'absolute_position' => 0,
            'prevent_global_animation' => false,
        ]);

        $examples[0]['elements'][0]['update']['contenu_mobile'] = '';

        return $examples;
    }
}
