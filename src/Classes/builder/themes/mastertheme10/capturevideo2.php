<?php

class Builder_Themes_Mastertheme10_capturevideo2 extends Eden_Class
{
    public function getThemeData(): array
    {
        return [
            'lines' => [
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'padding_top' => '40',
                        'padding_bottom' => '0',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'line_theme' => 'line_theme1',
                        'gradient_angle' => 180,
                        'bg_color_overlay' => '',
                        'bg_color_overlay_opacity' => 0,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_overlay_gradient_angle' => 135,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 6,
                            'position' => 0,
                            'line_position' => 3,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 1,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 0.7,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'videoplayer',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '0',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"chapters":[{"hour_start":"0","minute_start":"0","second_start":"0","nom":"Nom du chapitre"}],"subtitles":{"1":{"url":"","language":"fr"},"2":{"url":"","language":"fr"},"3":{"url":"","language":"fr"}},"affix":{"active":false,"position":"top_right","width":"300","hide":"0"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":1,"box_shadow_x":0,"box_shadow_y":10,"box_shadow_blur":40,"box_shadow_color":"#000000","box_shadow_opacity":0.1,"absolute_position":1,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '0',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/assets/images/tunnels/optin_video/eveningclouds.mp4?v=849',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                            ],
                        ],
                        [
                            'span' => 6,
                            'position' => 1,
                            'line_position' => 3,
                            'design' => [
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay' => '',
                                'bg_color_overlay_opacity' => 0,
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 64,
                                'padding_right' => 10,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_style' => 'none',
                                'border_radius_top_left' => 6,
                                'border_radius_top_right' => 6,
                                'border_radius_bottom_left' => 6,
                                'border_radius_bottom_right' => 6,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 10,
                                'box_shadow_blur' => 40,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 0.08,
                                'delay' => 0,
                                'delay_disappear' => 0,
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'top',
                                'bg_side_width' => 100,
                                'bg_side_height' => 100,
                                'col_height' => 1,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_overlay_gradient_angle' => 0,
                            ],
                            'contenu_mobile' => '{"design":"{\"padding_top\":10,\"padding_bottom\":0,\"padding_left\":10,\"padding_right\":10,\"margin_top\":0,\"margin_bottom\":0,\"margin_left\":0,\"margin_right\":0}"}',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h4>D&eacute;couvrez les cl&eacute;s pour atteindre vos objectifs les plus audacieux</h4>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 5,
                                ],
                            ],
                        ],
                        [
                            'span' => 12,
                            'position' => 2,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 71,
                                    'largeur' => 60,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":40,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":0,"img_border_radius_top_right":0,"img_border_radius_bottom_left":0,"img_border_radius_bottom_right":0,"img_border_radius_circle":0,"img_size":1,"button_box_shadow":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '{"largeur":40,"hauteur":47,"contenu":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"action\":\"page\",\"param\":\"\",\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme05/graphiques/learnybox-theme-05-logo-04.png',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h4 style=\"text-align:center\"><strong><span style=\"color:#849bb0\">CONTENU GRATUIT</span></strong></h4>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":40,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2 style=\"text-align:center\"><strong>Vous voulez savoir comment d&eacute;passer vos limites<br />\net atteindre vos objectifs ?</strong></h2>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 3,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'padding_top' => '24',
                        'padding_bottom' => '120',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => 'shape12',
                                'color' => '#FFFFFF',
                                'height' => '80',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => 'line_theme2',
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 6,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'col_height' => 0,
                            ],
                            'contenu_mobile' => '{"design":"{\"padding_top\":0,\"padding_bottom\":0,\"padding_left\":0,\"padding_right\":0,\"bgcolor1\":\"\",\"bgcolor2\":\"\",\"gradient_angle\":180,\"bgimage\":false,\"margin_top\":0,\"margin_bottom\":0,\"margin_left\":0,\"margin_right\":0,\"border_width\":0,\"border_color\":\"#000000\",\"border_color_opacity\":\"0\",\"border_radius_top_left\":0,\"border_radius_top_right\":0,\"border_radius_bottom_left\":0,\"border_radius_bottom_right\":0,\"box_shadow_x\":0,\"box_shadow_y\":0,\"box_shadow_blur\":0,\"box_shadow_color\":\"\",\"box_shadow_opacity\":1,\"border_style\":\"none\",\"full_width\":0,\"box_shadow\":0,\"bg_side\":0,\"bg_side_position\":\"left\",\"bg_side_width\":100,\"bg_side_height\":false,\"bgimage_position\":\"\",\"bgimage_size\":\"cover\",\"bg_color_overlay1\":\"\",\"bg_color_overlay2\":\"\",\"bg_color_overlay_opacity\":1,\"bg_overlay_gradient_angle\":0,\"delay\":0,\"delay_disappear\":0}"}',
                            'elements' => [],
                        ],
                        [
                            'span' => 6,
                            'position' => 2,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 64,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\">Visionnez notre vid&eacute;o exclusive qui vous r&eacute;v&eacute;lera des strat&eacute;gies puissantes pour d&eacute;passer vos limites et atteindre de nouveaux sommets dans votre vie.</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'countdown',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '6',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"align":"left","number_font":"Montserrat","number_font_size":32,"number_font_color":"#849BB0","text_font":"Montserrat","text_font_size":12,"text_font_color":"#354463","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '2023-01-26 12:15:04',
                                    'contenu_mobile' => '',
                                    'type' => 'date_fixe',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                                [
                                    'objet' => 'button_link',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bg_color1":"main_color0","bg_color1Hover":"#5D87B9","bg_color1Clicked":"main_color0","color_text":"#ffffff","color_textHover":"#ffffff","color_textClicked":"#ffffff","size_text":18,"size_textHover":18,"size_textClicked":18,"border_radius":100,"border_radiusHover":100,"border_radiusClicked":100,"size":"btn-large","height":"btn-h-vb","border_color":"","border_colorHover":"","border_colorClicked":"","color_icone":"#ffffff","color_iconeHover":"#ffffff","color_iconeClicked":"#ffffff","open_window":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"align":"left","icone":"","default_color_icone":"","action":"page","param":"","actions":[],"bg_color2":"","border":0,"button_box_shadow_x":0,"button_box_shadow_y":5,"button_box_shadow_blur":10,"button_box_shadow_color":"#000000","button_box_shadow_opacity":0.1,"contenu":"ACC\u00c9DER \u00c0 LA VID\u00c9O","size_icone":16,"position_icone":"left","decalage_icone":0,"bg_gradient_angle":180,"font_text":"Poppins","font_styles":[],"btn_padding_top":20,"btn_padding_bottom":20,"btn_padding_left":30,"btn_padding_right":30,"contenuHover":"","size_iconeHover":16,"position_iconeHover":"","decalage_iconeHover":0,"bg_color2Hover":"","bg_gradient_angleHover":180,"font_textHover":"Poppins","font_stylesHover":[],"borderHover":0,"button_box_shadow_xHover":0,"button_box_shadow_yHover":5,"button_box_shadow_blurHover":10,"button_box_shadow_colorHover":"#000000","button_box_shadow_opacityHover":0.1,"btn_padding_topHover":20,"btn_padding_bottomHover":20,"btn_padding_leftHover":30,"btn_padding_rightHover":30,"contenuClicked":"","size_iconeClicked":16,"position_iconeClicked":"","decalage_iconeClicked":0,"bg_color2Clicked":"","bg_gradient_angleClicked":180,"font_textClicked":"Poppins","font_stylesClicked":[],"borderClicked":0,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":5,"button_box_shadow_blurClicked":10,"button_box_shadow_colorClicked":"#000000","button_box_shadow_opacityClicked":0.1,"btn_padding_topClicked":20,"btn_padding_bottomClicked":20,"btn_padding_leftClicked":30,"btn_padding_rightClicked":30,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'ACCÉDER À LA VIDÉO',
                                    'contenu_mobile' => json_encode([
                                        'contenu' => json_encode([
                                            "size_text" => 14,
                                            "size_textHover" => 14,
                                            "size_textClicked" => 14,
                                            "height" => "btn-h-s",
                                            "btn_padding_top" => 6,
                                            "btn_padding_right" => 12,
                                            "btn_padding_bottom" => 6,
                                            "btn_padding_left" => 12,
                                            "btn_padding_topHover" => 6,
                                            "btn_padding_rightHover" => 12,
                                            "btn_padding_bottomHover" => 6,
                                            "btn_padding_leftHover" => 12,
                                            "btn_padding_topClicked" => 6,
                                            "btn_padding_rightClicked" => 12,
                                            "btn_padding_bottomClicked" => 6,
                                            "btn_padding_leftClicked" => 12,
                                        ])
                                    ]),
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'padding_top' => '40',
                        'padding_bottom' => '40',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => 'line_theme1',
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '{"design":"{\"padding_top\":\"10\",\"padding_bottom\":\"40\",\"margin_top\":\"0\",\"margin_bottom\":\"0\",\"padding_left\":\"10\",\"padding_right\":\"10\",\"margin_left\":\"0\",\"margin_right\":\"0\"}"}',
                    'cols' => [
                        [
                            'span' => 3,
                            'position' => 0,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '{"design":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_top\":0,\"margin_bottom\":0,\"margin_left\":0,\"margin_right\":0}"}',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 296,
                                    'largeur' => 261,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":40,"margin_bottom":0,"alt":"","action":"page","param":"","open_window":0,"img_border":16,"img_border_color":"#ffffff","img_border_radius_top_left":0,"img_border_radius_top_right":0,"img_border_radius_bottom_left":0,"img_border_radius_bottom_right":0,"img_border_radius_circle":0,"img_size":0,"button_box_shadow":1,"button_box_shadow_x":0,"button_box_shadow_y":5,"button_box_shadow_blur":40,"button_box_shadow_color":"#000000","button_box_shadow_opacity":0.1,"img_border_color_opacity":"1","img_border_style":"solid","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme05/images/learnybox-theme-05-image-18.jpg',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => 'featured-image',
                                    'position' => null,
                                ],
                            ],
                        ],
                        [
                            'span' => 9,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'col_height' => 0,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 40,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h5><span style=\"color:#849bb0\"><strong>D&eacute;couvrez les techniques pour d&eacute;passer vos limites !</strong></span></h5>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":20,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 3,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":40,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\">D&eacute;veloppez un mental fort et r&eacute;silient, vous permettant de surmonter les obstacles et les peurs qui entravent votre progression.</p>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":24,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 6,
                                ],
                            ],
                        ],
                        [
                            'span' => 12,
                            'position' => 1,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h5><strong>CONTENU GRATUIT</strong></h5>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":10,\"padding_bottom\":0,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":10,"padding_left":40,"padding_right":40,"margin_bottom":40,"word_break":"by_default","bgcolor1":"#5D87B9","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":-40,"margin_right":-40,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h3><span style=\"color:#ffffff\"><strong>Gr&acirc;ce &agrave; cette vid&eacute;o exclusive, vous allez apprendre :</strong></span></h3>\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":0,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'padding_top' => '0',
                        'padding_bottom' => '40',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => 'line_theme1',
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '{"design":"{\"padding_top\":\"10\",\"padding_bottom\":\"40\",\"margin_top\":\"0\",\"margin_bottom\":\"0\",\"padding_left\":\"10\",\"padding_right\":\"10\",\"margin_left\":\"0\",\"margin_right\":\"0\"}"}',
                    'cols' => [
                        [
                            'span' => 3,
                            'position' => 0,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 214,
                                    'largeur' => 189,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":40,"margin_bottom":0,"alt":"","action":"page","param":"","open_window":0,"img_border":16,"img_border_color":"#ffffff","img_border_radius_top_left":0,"img_border_radius_top_right":0,"img_border_radius_bottom_left":0,"img_border_radius_bottom_right":0,"img_border_radius_circle":0,"img_size":0,"button_box_shadow":1,"button_box_shadow_x":0,"button_box_shadow_y":5,"button_box_shadow_blur":40,"button_box_shadow_color":"#000000","button_box_shadow_opacity":0.1,"img_border_color_opacity":"1","img_border_style":"solid","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme05/images/learnybox-theme-05-image-19.jpg',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => 'featured-image',
                                    'position' => null,
                                ],
                            ],
                        ],
                        [
                            'span' => 9,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'col_height' => 0,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 40,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h5><span style=\"color:#849bb0\"><strong>Les cl&eacute;s avoir une confiance absolue.</strong></span></h5>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":40,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\">Renforcez votre confiance en vous et votre estime personnelle, vous donnant le courage d&#39;explorer de nouveaux horizons et de relever des d&eacute;fis audacieux.</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'padding_top' => '0',
                        'padding_bottom' => '80',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => 'line_theme1',
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '{"design":"{\"padding_top\":\"10\",\"padding_bottom\":\"40\",\"margin_top\":\"0\",\"margin_bottom\":\"0\",\"padding_left\":\"10\",\"padding_right\":\"10\",\"margin_left\":\"0\",\"margin_right\":\"0\"}"}',
                    'cols' => [
                        [
                            'span' => 3,
                            'position' => 0,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 214,
                                    'largeur' => 189,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":40,"margin_bottom":0,"alt":"","action":"page","param":"","open_window":0,"img_border":16,"img_border_color":"#ffffff","img_border_radius_top_left":0,"img_border_radius_top_right":0,"img_border_radius_bottom_left":0,"img_border_radius_bottom_right":0,"img_border_radius_circle":0,"img_size":0,"button_box_shadow":1,"button_box_shadow_x":0,"button_box_shadow_y":5,"button_box_shadow_blur":40,"button_box_shadow_color":"#000000","button_box_shadow_opacity":0.1,"img_border_color_opacity":"1","img_border_style":"solid","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme05/images/learnybox-theme-05-image-20.jpg',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => 'featured-image',
                                    'position' => null,
                                ],
                            ],
                        ],
                        [
                            'span' => 9,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'col_height' => 0,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 40,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h5><span style=\"color:#849bb0\"><strong>Des techniques pour vous lib&eacute;rer d&eacute;finitivement.</strong></span></h5>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":40,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\">Identifiez et d&eacute;passez vos limites actuelles, vous permettant de lib&eacute;rer tout votre potentiel et d&#39;atteindre des objectifs plus ambitieux que jamais.</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '#5D87B9',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'padding_top' => '80',
                        'padding_bottom' => '120',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => 'shape12',
                                'color' => '#FFFFFF',
                                'height' => '80',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => '',
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 12,
                            'position' => 0,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2><span style=\"color:#ffffff\"><strong>Ils ont suivi le programme et sont devenus addicts !</strong></span></h2>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                            ],
                        ],
                        [
                            'span' => 6,
                            'position' => 1,
                            'line_position' => 2,
                            'design' => [
                                'padding_top' => 16,
                                'padding_bottom' => 16,
                                'padding_left' => 16,
                                'padding_right' => 16,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 20,
                                'margin_bottom' => 20,
                                'margin_left' => 0,
                                'margin_right' => 20,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'temoignages',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"nom":"<h4>Julie R.<\/h4>\r\n","description":"<p>&Eacute;tudiante<\/p>\r\n","theme":"theme1","bg_color":"","description_color":"#707070","bg_opacity":"1","padding_top":32,"padding_bottom":32,"padding_left":40,"padding_right":40,"display_icone":"on","icone":"fa-quote-left","size_icone":"40","color_icone":"#707070","content_color":"#707070","profil_color":"#000000","word_break":"by_default","margin_bottom":0,"bg_content_color":"","content_font":"Open Sans","bg_profil_color":"","profil_font":"Open Sans","description_font":"Open Sans","display_hr":"off","color_hr":"#DDD","size_hr":"50","bgcolor1":"#ffffff","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":1,"box_shadow_x":0,"box_shadow_y":10,"box_shadow_blur":40,"box_shadow_color":"#000000","box_shadow_opacity":0.06,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p style=\"color:#5a655c\">J&#39;ai &eacute;t&eacute; profond&eacute;ment inspir&eacute;e par cette vid&eacute;o ! Elle m&#39;a aid&eacute;e &agrave; changer ma perspective sur mes propres limites. Maintenant, je me sens plus confiante et pr&ecirc;te &agrave; relever des d&eacute;fis plus grands. Merci pour cette pr&eacute;cieuse ressource !</p>\r\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":24,\"padding_bottom\":24,\"padding_left\":24,\"padding_right\":24,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://images.unsplash.com/photo-1499887142886-791eca5918cd?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w5ODc5NHwwfDF8c2VhcmNofDJ8fGdpcmwlMjBzdHVkZW50fGVufDB8Mnx8fDE2OTA5NzA5MDZ8MA&ixlib=rb-4.0.3&q=80&w=400',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                            ],
                        ],
                        [
                            'span' => 12,
                            'position' => 1,
                            'line_position' => 3,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 40,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'button_link',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bg_color1":"main_color0","bg_color1Hover":"#ffffff","bg_color1Clicked":"main_color0","color_text":"#ffffff","color_textHover":"#354463","color_textClicked":"#ffffff","size_text":18,"size_textHover":18,"size_textClicked":18,"border_radius":100,"border_radiusHover":100,"border_radiusClicked":100,"size":"btn-large","height":"btn-h-vb","border_color":"","border_colorHover":"","border_colorClicked":"","color_icone":"#ffffff","color_iconeHover":"#ffffff","color_iconeClicked":"#ffffff","open_window":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"align":"center","icone":"","default_color_icone":"","action":"page","param":"","actions":[],"bg_color2":"","border":0,"button_box_shadow_x":0,"button_box_shadow_y":5,"button_box_shadow_blur":10,"button_box_shadow_color":"#000000","button_box_shadow_opacity":0.1,"contenu":"JE M\u2019INSCRIS MAINTENANT","size_icone":16,"position_icone":"left","decalage_icone":0,"bg_gradient_angle":180,"font_text":"Poppins","font_styles":[],"btn_padding_top":20,"btn_padding_bottom":20,"btn_padding_left":30,"btn_padding_right":30,"contenuHover":"","size_iconeHover":16,"position_iconeHover":"","decalage_iconeHover":0,"bg_color2Hover":"","bg_gradient_angleHover":180,"font_textHover":"Poppins","font_stylesHover":[],"borderHover":0,"button_box_shadow_xHover":0,"button_box_shadow_yHover":5,"button_box_shadow_blurHover":10,"button_box_shadow_colorHover":"#000000","button_box_shadow_opacityHover":0.1,"btn_padding_topHover":20,"btn_padding_bottomHover":20,"btn_padding_leftHover":30,"btn_padding_rightHover":30,"contenuClicked":"","size_iconeClicked":16,"position_iconeClicked":"","decalage_iconeClicked":0,"bg_color2Clicked":"","bg_gradient_angleClicked":180,"font_textClicked":"Poppins","font_stylesClicked":[],"borderClicked":0,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":5,"button_box_shadow_blurClicked":10,"button_box_shadow_colorClicked":"#000000","button_box_shadow_opacityClicked":0.1,"btn_padding_topClicked":20,"btn_padding_bottomClicked":20,"btn_padding_leftClicked":30,"btn_padding_rightClicked":30,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'JE M’INSCRIS MAINTENANT',
                                    'contenu_mobile' => json_encode([
                                        'contenu' => json_encode([
                                            "size_text" => 14,
                                            "size_textHover" => 14,
                                            "size_textClicked" => 14,
                                            "height" => "btn-h-s",
                                            "btn_padding_top" => 6,
                                            "btn_padding_right" => 12,
                                            "btn_padding_bottom" => 6,
                                            "btn_padding_left" => 12,
                                            "btn_padding_topHover" => 6,
                                            "btn_padding_rightHover" => 12,
                                            "btn_padding_bottomHover" => 6,
                                            "btn_padding_leftHover" => 12,
                                            "btn_padding_topClicked" => 6,
                                            "btn_padding_rightClicked" => 12,
                                            "btn_padding_bottomClicked" => 6,
                                            "btn_padding_leftClicked" => 12,
                                        ])
                                    ]),
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                            ],
                        ],
                        [
                            'span' => 6,
                            'position' => 2,
                            'line_position' => 2,
                            'design' => [
                                'padding_top' => 16,
                                'padding_bottom' => 16,
                                'padding_left' => 16,
                                'padding_right' => 16,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 20,
                                'margin_bottom' => 20,
                                'margin_left' => 20,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'temoignages',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"nom":"<h4>Nicolas L.<\/h4>\r\n","description":"<p>Coach sportif<\/p>\r\n","theme":"theme1","bg_color":"","description_color":"#707070","bg_opacity":"1","padding_top":32,"padding_bottom":32,"padding_left":40,"padding_right":40,"display_icone":"on","icone":"fa-quote-left","size_icone":"40","color_icone":"#707070","content_color":"#707070","profil_color":"#000000","word_break":"by_default","margin_bottom":0,"bg_content_color":"","content_font":"Open Sans","bg_profil_color":"","profil_font":"Open Sans","description_font":"Open Sans","display_hr":"off","color_hr":"#DDD","size_hr":"50","bgcolor1":"#ffffff","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":1,"box_shadow_x":0,"box_shadow_y":10,"box_shadow_blur":40,"box_shadow_color":"#000000","box_shadow_opacity":0.06,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p style=\"color:#5a655c\">Cette vid&eacute;o est un vrai bijou pour quiconque cherche &agrave; d&eacute;passer ses limites physiques et mentales. Les conseils pratiques et les exemples concrets ont &eacute;t&eacute; d&#39;une grande aide pour moi et mes clients. Je la recommande vivement !</p>\r\n",
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":24,\"padding_bottom\":24,\"padding_left\":24,\"padding_right\":24,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://images.unsplash.com/photo-1562124638-724e13052daf?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w5ODc5NHwwfDF8c2VhcmNofDY0fHxtYW4lMjBwb3J0cmFpdHxlbnwwfDJ8fHwxNjkwOTcwNTczfDA&ixlib=rb-4.0.3&q=80&w=400',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'padding_top' => '80',
                        'padding_bottom' => '120',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => 'shape12',
                                'color' => '#F7F9FC',
                                'height' => '80',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0.5,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => 'line_theme1',
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '{"design":"{\"padding_top\":\"40\",\"padding_bottom\":\"80\",\"margin_top\":\"0\",\"margin_bottom\":\"0\",\"padding_left\":\"10\",\"padding_right\":\"10\",\"margin_left\":\"0\",\"margin_right\":\"0\"}"}',
                    'cols' => [
                        [
                            'span' => 4,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '#ffffff',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 40,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 1,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '{"design":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_top\":10,\"margin_bottom\":0,\"margin_left\":10,\"margin_right\":10,\"bgimage_position\":\"\"}"}',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 628,
                                    'largeur' => 483,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":0,"img_border_radius_top_right":0,"img_border_radius_bottom_left":0,"img_border_radius_bottom_right":0,"img_border_radius_circle":0,"img_size":0,"button_box_shadow":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":0,\"padding_bottom\":0,\"padding_left\":0,\"padding_right\":0,\"margin_bottom\":0,\"action\":\"page\",\"param\":\"\",\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme05/images/learnybox-theme-05-image-21.jpg',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                            ],
                        ],
                        [
                            'span' => 8,
                            'position' => 2,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '#ffffff',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 1,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":40,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2><span style=\"color:#849bb0\"><strong>Qui suis-je ?</strong></span></h2>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":40,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p>Je suis Dominic, votre coach de vie d&eacute;vou&eacute;. Lib&eacute;rez votre potentiel et gagnez en confiance pour affronter tous les d&eacute;fis avec assurance ! Pr&ecirc;t &agrave; commencer cette aventure ?</p>\n\n<p><br />\nEnsemble, nous parcourrons ce chemin vers l&#39;&eacute;panouissement personnel, et vous d&eacute;couvrirez une version de vous-m&ecirc;me que vous n&#39;auriez jamais imagin&eacute;e possible !</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 3,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h5><strong>Dominic Dufour</strong></h5>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 4,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'padding_top' => '64',
                        'padding_bottom' => '80',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'line_theme' => 'line_theme2',
                        'gradient_angle' => 180,
                        'bg_color_overlay' => '',
                        'bg_color_overlay_opacity' => 0,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_overlay_gradient_angle' => 135,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 5,
                            'position' => 0,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 64,
                                'col_height' => 0,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 543,
                                    'largeur' => 456,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":0,"img_border_radius_top_right":0,"img_border_radius_bottom_left":0,"img_border_radius_bottom_right":0,"img_border_radius_circle":0,"img_size":0,"button_box_shadow":1,"button_box_shadow_x":0,"button_box_shadow_y":5,"button_box_shadow_blur":40,"button_box_shadow_color":"#000000","button_box_shadow_opacity":0.1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme05/images/learnybox-theme-05-image-22.jpg',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                            ],
                        ],
                        [
                            'span' => 6,
                            'position' => 1,
                            'line_position' => 0,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":32,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h2><span style=\"display:none\">&nbsp;</span><strong>N&#39;attendez plus...<br />\nCommencez maintenant !</strong><span style=\"display:none\">&nbsp;</span></h2>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":32,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\">Ne manquez pas cette opportunit&eacute; unique d&#39;acc&eacute;der &agrave; des conseils pratiques et inspirants pour d&eacute;passer vos limites et atteindre de nouveaux sommets !</p>\n\n<p class=\"normal\">&nbsp;</p>\n\n<p class=\"normal\">Inscrivez-vous d&egrave;s maintenant pour d&eacute;couvrir les secrets qui changeront votre vie !</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                                [
                                    'objet' => 'countdown',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '6',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":40,"align":"left","number_font":"Francois One","number_font_size":32,"number_font_color":"#849BB0","text_font":"Open Sans","text_font_size":12,"text_font_color":"#354463","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '2023-01-26 12:15:04',
                                    'contenu_mobile' => '{"contenu":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"align\":\"center\",\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => 'date_fixe',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                                [
                                    'objet' => 'button_link',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bg_color1":"main_color0","bg_color1Hover":"#5D87B9","bg_color1Clicked":"main_color0","color_text":"#ffffff","color_textHover":"#ffffff","color_textClicked":"#ffffff","size_text":18,"size_textHover":18,"size_textClicked":18,"border_radius":100,"border_radiusHover":100,"border_radiusClicked":100,"size":"btn-large","height":"btn-h-vb","border_color":"","border_colorHover":"","border_colorClicked":"","color_icone":"#ffffff","color_iconeHover":"#ffffff","color_iconeClicked":"#ffffff","open_window":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"align":"left","icone":"","default_color_icone":"","action":"page","param":"","actions":[],"bg_color2":"","border":0,"button_box_shadow_x":0,"button_box_shadow_y":5,"button_box_shadow_blur":10,"button_box_shadow_color":"#000000","button_box_shadow_opacity":0.1,"contenu":"JE M\u2019INSCRIS MAINTENANT","size_icone":16,"position_icone":"left","decalage_icone":0,"bg_gradient_angle":180,"font_text":"Poppins","font_styles":[],"btn_padding_top":20,"btn_padding_bottom":20,"btn_padding_left":30,"btn_padding_right":30,"contenuHover":"","size_iconeHover":16,"position_iconeHover":"","decalage_iconeHover":0,"bg_color2Hover":"","bg_gradient_angleHover":180,"font_textHover":"Poppins","font_stylesHover":[],"borderHover":0,"button_box_shadow_xHover":0,"button_box_shadow_yHover":5,"button_box_shadow_blurHover":10,"button_box_shadow_colorHover":"#000000","button_box_shadow_opacityHover":0.1,"btn_padding_topHover":20,"btn_padding_bottomHover":20,"btn_padding_leftHover":30,"btn_padding_rightHover":30,"contenuClicked":"","size_iconeClicked":16,"position_iconeClicked":"","decalage_iconeClicked":0,"bg_color2Clicked":"","bg_gradient_angleClicked":180,"font_textClicked":"Poppins","font_stylesClicked":[],"borderClicked":0,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":5,"button_box_shadow_blurClicked":10,"button_box_shadow_colorClicked":"#000000","button_box_shadow_opacityClicked":0.1,"btn_padding_topClicked":20,"btn_padding_bottomClicked":20,"btn_padding_leftClicked":30,"btn_padding_rightClicked":30,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'JE M’INSCRIS MAINTENANT',
                                    'contenu_mobile' => json_encode([
                                        'contenu' => json_encode([
                                            "size_text" => 14,
                                            "size_textHover" => 14,
                                            "size_textClicked" => 14,
                                            "height" => "btn-h-s",
                                            "btn_padding_top" => 6,
                                            "btn_padding_right" => 12,
                                            "btn_padding_bottom" => 6,
                                            "btn_padding_left" => 12,
                                            "btn_padding_topHover" => 6,
                                            "btn_padding_rightHover" => 12,
                                            "btn_padding_bottomHover" => 6,
                                            "btn_padding_leftHover" => 12,
                                            "btn_padding_topClicked" => 6,
                                            "btn_padding_rightClicked" => 12,
                                            "btn_padding_bottomClicked" => 6,
                                            "btn_padding_leftClicked" => 12,
                                            "padding_top" => 10,
                                            "padding_bottom" => 10,
                                            "padding_left" => 10,
                                            "padding_right" => 10,
                                            "margin_top" => 0,
                                            "margin_bottom" => 0,
                                            "margin_left" => 0,
                                            "margin_right" => 0,
                                            "align" => "center"
                                        ])
                                    ]),
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 3,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'padding_top' => '32',
                        'padding_bottom' => '32',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => 'line_theme1',
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 3,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 47,
                                    'largeur' => 40,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":0,"img_border_radius_top_right":0,"img_border_radius_bottom_left":0,"img_border_radius_bottom_right":0,"img_border_radius_circle":0,"img_size":1,"button_box_shadow":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '{"largeur":30,"hauteur":35,"fleche":"center","contenu":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"action\":\"page\",\"param\":\"\",\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => 'left',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme05/graphiques/learnybox-theme-05-logo-01.png',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                            ],
                        ],
                        [
                            'span' => 9,
                            'position' => 2,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\" style=\"text-align:right\">Copyright 2023 Learnybox. Tous droits r&eacute;serv&eacute;s.<br />\nCGV - Mentions l&eacute;gales</p>\n",
                                    'contenu_mobile' => '{"contenutexte":"<p class=\"normal\" style=\"text-align:center\">Copyright 2023 Learnybox. Tous droits r&eacute;serv&eacute;s.<br \/>\nCGV - Mentions l&eacute;gales<\/p>\n","contenu":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }
}
