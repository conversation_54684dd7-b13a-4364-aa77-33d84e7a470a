<?php

class Builder_Themes_Mastertheme9_confirmationinscriptionconference1 extends Eden_Class
{
    public function getThemeData(): array
    {
        return [
            'lines' => [
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme04/fonds/learnybox-theme-04-fond-03.jpg',
                        'bgimage_position' => 'scroll',
                        'bgimage_size' => 'cover',
                        'padding_top' => '120',
                        'padding_bottom' => '120',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'line_theme' => 'line_theme1',
                        'gradient_angle' => 180,
                        'bg_color_overlay' => '',
                        'bg_color_overlay_opacity' => 0.3,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'bg_color_overlay1' => '#F8F6F2',
                        'bg_color_overlay2' => '',
                        'bg_overlay_gradient_angle' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '{"design":"{\"padding_top\":\"10\",\"padding_bottom\":\"10\",\"margin_top\":\"0\",\"margin_bottom\":\"0\",\"padding_left\":\"10\",\"padding_right\":\"10\",\"margin_left\":\"0\",\"margin_right\":\"0\"}"}',
                    'cols' => [
                        [
                            'span' => 12,
                            'position' => 0,
                            'line_position' => 2,
                            'design' => [
                                'padding_top' => 80,
                                'padding_bottom' => 80,
                                'padding_left' => 80,
                                'padding_right' => 80,
                                'col_height' => 1,
                                'bgcolor1' => '#ffffff',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'fa-icon',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"icone":"fa-check-circle-o","align":"center","size":"52","color":"#707070","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"open_window":0,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h1 style=\"text-align:center\"><span style=\"color:#000000\"><strong>Merci pour votre inscription !</strong></span></h1>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h4 class=\"normal\" style=\"text-align:center\">D&eacute;cuplez votre potentiel entrepreneurial</h4>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h4 class=\"normal\" style=\"text-align:center\">12 janvier 2024 &agrave; 21h00</h4>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 3,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\" style=\"text-align:center\">Un email vous a &eacute;t&eacute; envoy&eacute;. Vous recevrez un lien le 12 janvier<br />\npour assister en direct &agrave; la conf&eacute;rence en ligne.</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 4,
                                ],
                                [
                                    'objet' => 'separator',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 2,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bgcolor1":"#F8F6F2","bgcolor2":"","element_bg_gradient_angle":0,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":40,"margin_bottom":40,"margin_left":40,"margin_right":40,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '{"contenu":"{\"margin_top\":24,\"margin_bottom\":24,\"margin_left\":24,\"margin_right\":24,\"padding_top\":0,\"padding_bottom\":0,\"padding_left\":0,\"padding_right\":0}"}',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 6,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":16,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\" style=\"text-align:center\">Partagez la conf&eacute;rence avec vos amis</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 7,
                                ],
                                [
                                    'objet' => 'social_share_link',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"template":"template7","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"facebook":{"active":"oui","url":"#"},"twitter":{"active":"oui","url":"#"},"youtube":{"active":"oui","url":"#"},"linkedin":{"active":"oui","url":"#"},"pinterest":{"active":"non","url":""},"instagram":{"active":"non","url":"#"},"tumblr":{"active":"non","url":""},"tiktok":{"active":"oui","url":"#"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 8,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'padding_top' => '32',
                        'padding_bottom' => '32',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => 'line_theme2',
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 12,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 10,
                                'padding_bottom' => 10,
                                'padding_left' => 10,
                                'padding_right' => 10,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 41,
                                    'largeur' => 64,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":24,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":0,"img_border_radius_top_right":0,"img_border_radius_bottom_left":0,"img_border_radius_bottom_right":0,"img_border_radius_circle":0,"img_size":1,"button_box_shadow":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '{"largeur":40,"hauteur":26,"contenu":"{\"padding_top\":10,\"padding_bottom\":10,\"padding_left\":10,\"padding_right\":10,\"margin_bottom\":0,\"action\":\"page\",\"param\":\"\",\"margin_top\":0,\"margin_left\":0,\"margin_right\":0}"}',
                                    'type' => '',
                                    'fleche' => 'center',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme04/Graphiques/learnybox-theme-04-logo-01.png',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p class=\"normal\" style=\"text-align:center\">Copyright 2023 Learnybox. Tous droits r&eacute;serv&eacute;s.<br />\nCGV - Mentions l&eacute;gales</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }
}
