<?php

namespace Learnybox\Classes\builder\themes\mastertheme9\Examples;

class ProgressbarExample
{
    public static function getExamples(): array
    {
        $examples = [];

        $examples[0]['elements'][0]['update']['contenutexte'] = '50% complété';

        $examples[0]['elements'][0]['update']['contenu'] = json_encode([
            'bar_bgcolor' => '#ffffff',
            'bar_color' => '#000000',
            'text_color' => '#000000',
            'text_shadow' => '#F8F6F2',
            'width' => 50,
            'height' => 30,
            'padding_top' => 0,
            'padding_bottom' => 0,
            'padding_left' => 0,
            'padding_right' => 0,
            'bgcolor1' => '',
            'bgcolor2' => '',
            'element_bg_gradient_angle' => 180,
            'bg_color' => '',
            'bg_opacity' => '1',
            'bg_opacity2' => '1',
            'bg_border' => 0,
            'bg_border_color' => '#000000',
            'bg_border_color_opacity' => '0',
            'bg_border_style' => 'none',
            'bg_border_radius_top_left' => 0,
            'bg_border_radius_top_right' => 0,
            'bg_border_radius_bottom_left' => 0,
            'bg_border_radius_bottom_right' => 0,
            'margin_top' => 10,
            'margin_bottom' => 10,
            'margin_left' => 0,
            'margin_right' => 0,
            'box_shadow' => 0,
            'box_shadow_x' => 0,
            'box_shadow_y' => 0,
            'box_shadow_blur' => 0,
            'box_shadow_color' => '',
            'box_shadow_opacity' => 1,
            'absolute_position' => 0,
            'prevent_global_animation' => false,
        ]);

        $examples[0]['elements'][0]['update']['contenu_mobile'] = '';

        return $examples;
    }
}
