<?php

class Builder_Themes_Mastertheme9_mail3 extends Eden_Class
{
    public function getThemeData(): array
    {
        return [
            'lines' => [
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme04/fonds/learnybox-theme-04-fond-02.jpg',
                        'padding_top' => '40',
                        'padding_bottom' => '40',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'line_theme' => 'line_theme3',
                        'gradient_angle' => 180,
                        'padding_left' => '24',
                        'padding_right' => '24',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bgimage_position' => 'scroll',
                        'bgimage_size' => 'cover',
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'bg_color_page' => false,
                        'delay' => 0,
                        'delay_disappear' => 0,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 12,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 20,
                                'padding_bottom' => 20,
                                'padding_left' => 20,
                                'padding_right' => 20,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => 'auto',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 45,
                                    'largeur' => 70,
                                    'eval' => '0',
                                    'contenu' => '{"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":0,"img_border_radius_top_right":0,"img_border_radius_bottom_left":0,"img_border_radius_bottom_right":0,"img_border_radius_circle":0,"img_size":1,"button_box_shadow":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => 'left',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme04/Graphiques/learnybox-theme-04-logo-02.png',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => 'fadeIn',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => 'fadeOut',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '#FFFFFF',
                        'bgcolor2' => '',
                        'bgimage' => '',
                        'padding_top' => '0',
                        'padding_bottom' => '0',
                        'margin_top' => '',
                        'margin_bottom' => '',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => '1',
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 12,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 20,
                                'padding_bottom' => 20,
                                'padding_left' => 20,
                                'padding_right' => 20,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => 'auto',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h2","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_radius":0,"margin_top":0,"margin_bottom":20,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h3><strong>Newsletter</strong></h3>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => 'fadeIn',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => 'fadeOut',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => 'auto',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p>Et quoniam mirari posse quosdam peregrinos existimo haec lecturos forsitan, si contigerit, quamobrem cum oratio ad ea monstranda deflexerit quae Romae gererentur, nihil praeter seditiones narratur et tabernas et vilitates harum similis alias, summatim causas perstringam nusquam a veritate sponte propria digressurus.</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => 'fadeIn',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => 'fadeOut',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 3,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'padding_top' => '0',
                        'padding_bottom' => '0',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 1,
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'line_theme' => '',
                        'bg_color_page' => false,
                        'delay' => 0,
                        'delay_disappear' => 0,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 12,
                            'position' => 1,
                            'line_position' => 2,
                            'design' => [
                                'padding_top' => 20,
                                'padding_bottom' => 20,
                                'padding_left' => 20,
                                'padding_right' => 20,
                                'col_height' => 0,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":16,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h5><strong>Nulla vitae elit libero</strong></h5>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 0,
                                ],
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => 'auto',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_radius":0,"margin_top":0,"margin_bottom":24,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<p>Cras justo odio, dapibus ac facilisis in, egestas eget quam. Donec ullamcorper nulla non metus auctor fringilla. Vestibulum id ligula porta felis euismod semper. Integer posuere erat a ante venenatis dapibus posuere velit aliquet. Maecenas sed diam eget risus varius blandit sit amet non magna.</p>\n\n<p>Aenean lacinia bibendum nulla sed consectetur. Etiam porta sem malesuada magna mollis euismod. Vestibulum id ligula porta felis euismod semper. Integer posuere erat a ante venenatis dapibus posuere velit aliquet. Nullam id dolor id nibh ultricies vehicula ut id elit.</p>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => 'fadeIn',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => 'fadeOut',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                                [
                                    'objet' => 'button_link',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bg_color1":"","bg_color1Hover":"","bg_color1Clicked":"","color_text":"#000000","color_textHover":"","color_textClicked":"","size_text":16,"size_textHover":"","size_textClicked":"","border_radius":0,"border_radiusHover":"","border_radiusClicked":"","size":"btn-large","height":"btn-h-b","border_color":"#000000","border_colorHover":"","border_colorClicked":"","color_icone":"","color_iconeHover":"","color_iconeClicked":"","open_window":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"align":"left","icone":"","default_color_icone":"","action":"page","param":"","actions":[],"bg_color2":"","border":2,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"contenu":"Lire la suite...","size_icone":0,"position_icone":"","decalage_icone":0,"bg_gradient_angle":180,"font_text":"Open Sans","font_styles":[],"btn_padding_top":14,"btn_padding_bottom":14,"btn_padding_left":20,"btn_padding_right":20,"contenuHover":"","size_iconeHover":0,"position_iconeHover":"","decalage_iconeHover":0,"bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"","font_stylesHover":[],"borderHover":"","button_box_shadow_xHover":"","button_box_shadow_yHover":"","button_box_shadow_blurHover":"","button_box_shadow_colorHover":"","button_box_shadow_opacityHover":1,"btn_padding_topHover":0,"btn_padding_bottomHover":0,"btn_padding_leftHover":0,"btn_padding_rightHover":0,"contenuClicked":"","size_iconeClicked":0,"position_iconeClicked":"","decalage_iconeClicked":0,"bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"","font_stylesClicked":[],"borderClicked":"","button_box_shadow_xClicked":"","button_box_shadow_yClicked":"","button_box_shadow_blurClicked":"","button_box_shadow_colorClicked":"","button_box_shadow_opacityClicked":1,"btn_padding_topClicked":0,"btn_padding_bottomClicked":0,"btn_padding_leftClicked":0,"btn_padding_rightClicked":0,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'Lire la suite...',
                                    'contenu_mobile' => json_encode([
                                        'contenu' => json_encode([
                                            "size_text" => 14,
                                            "size_textHover" => 14,
                                            "size_textClicked" => 14,
                                            "height" => "btn-h-s",
                                            "btn_padding_top" => 6,
                                            "btn_padding_right" => 12,
                                            "btn_padding_bottom" => 6,
                                            "btn_padding_left" => 12,
                                            "btn_padding_topHover" => 6,
                                            "btn_padding_rightHover" => 12,
                                            "btn_padding_bottomHover" => 6,
                                            "btn_padding_leftHover" => 12,
                                            "btn_padding_topClicked" => 6,
                                            "btn_padding_rightClicked" => 12,
                                            "btn_padding_bottomClicked" => 6,
                                            "btn_padding_leftClicked" => 12,
                                        ])
                                    ]),
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 2,
                                ],
                            ],
                        ],
                        [
                            'span' => 12,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 16,
                                'padding_bottom' => 16,
                                'padding_left' => 16,
                                'padding_right' => 16,
                                'bgcolor1' => '',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 0,
                                'box_shadow_blur' => 0,
                                'box_shadow_color' => '',
                                'box_shadow_opacity' => 1,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 0,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'col_height' => 0,
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'image',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 262,
                                    'largeur' => 584,
                                    'eval' => '',
                                    'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"alt":"","action":"page","param":"","open_window":0,"img_border":0,"img_border_color":"#000000","img_border_radius_top_left":0,"img_border_radius_top_right":0,"img_border_radius_bottom_left":0,"img_border_radius_bottom_right":0,"img_border_radius_circle":0,"img_size":0,"button_box_shadow":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"img_border_color_opacity":"0","img_border_style":"none","webkit_filters":{"opacity":"100"},"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => '',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme04/images/learnybox-theme-04-image-12.png',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => false,
                        'padding_top' => '0',
                        'padding_bottom' => '0',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 1,
                        'line_theme' => 'line_theme3',
                        'gradient_angle' => 180,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bgimage_position' => '',
                        'bgimage_size' => 'cover',
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => 0,
                        'bg_overlay_gradient_angle' => 0,
                        'bg_color_page' => false,
                        'delay' => 0,
                        'delay_disappear' => 0,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 12,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 20,
                                'padding_bottom' => 20,
                                'padding_left' => 20,
                                'padding_right' => 20,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => 'auto',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"p","bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                                    'contenutexte' => '<p><span style="color: #fefefe;">team learnybox</span></p>',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => 'fadeIn',
                                    'save_appear' => false,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => 'fadeOut',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => 1,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }
}
