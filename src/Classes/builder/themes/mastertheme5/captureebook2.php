<?php

/**
 * Builder_Themes_Mastertheme5_captureebook2 class.
 *
 * @extends  Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Themes_Mastertheme5_captureebook2 extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        $theme_datas = [];
        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'cover',
                'padding_top' => '70',
                'padding_bottom' => '70',
                'margin_top' => '0',
                'margin_bottom' => '0',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
                'line_theme' => 'line_theme3',
                'gradient_angle' => '0',
                'bg_color_overlay' => '',
                'bg_color_overlay_opacity' => '0',
                'padding_left' => '28',
                'padding_right' => '28',
                'margin_left' => '0',
                'margin_right' => '0',
                'border_width' => '0',
                'border_color' => '',
                'border_color_opacity' => '1',
                'border_radius_top_left' => '0',
                'border_radius_top_right' => '0',
                'border_radius_bottom_left' => '0',
                'border_radius_bottom_right' => '0',
                'border_style' => 'none',
                'box_shadow_x' => '0',
                'box_shadow_y' => '0',
                'box_shadow_blur' => '0',
                'box_shadow_color' => '',
                'box_shadow_opacity' => '1',
                'box_shadow' => '0',
                'bg_color_overlay1' => '',
                'bg_color_overlay2' => '',
                'bg_overlay_gradient_angle' => '240',
                'shape' => [
                    'bottom' => [
                        'shape' => 'shape1',
                        'color' => '[[COLOR_LINE_THEME_2]]',
                        'height' => 300,
                        'repeat' => 0,
                        'flip_horizontal' => 1,
                    ],
                ],
            ],
            'cols' => [
                [
                    'span' => '5',
                    'position' => '0',
                    'line_position' => '2',
                    'design' => [
                        'padding_top' => '10',
                        'padding_bottom' => '10',
                        'padding_left' => '10',
                        'padding_right' => '10',
                        'margin_right' => '30',
                    ],
                    'elements' => [
                        [
                            'objet' => 'image',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '',
                            'largeur' => '',
                            'eval' => '',
                            'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":10,"alt":"","margin_top":0}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => 'center',
                            'time' => '0',
                            'name' => '',
                            'urls' => '[[EBOOK]]',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
                [
                    'span' => '7',
                    'position' => '1',
                    'line_position' => '2',
                    'design' => [
                        'margin_top' => '0',
                        'margin_left' => '30',
                    ],
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"Rubik","font_theme":"h2","padding_top":20,"padding_bottom":0,"padding_left":10,"padding_right":10,"margin_top":0,"margin_bottom":30}',
                            'contenutexte' => '<h2 style=\"text-align: left;\"><span style="color: #ffffff\">[[ACCROCHE]]</span></h2>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"Rubik","font_theme":"h4","padding_top":5,"padding_bottom":0,"padding_left":10,"padding_right":10,"margin_top":0,"margin_bottom":0}',
                            'contenutexte' => '<h4 style=\"text-align: left;\"><span style="color: #FFFFFF\">[[SUB_ACCROCHE]]</span></h4>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'button_link',
                            'bloc_alignement' => '',
                            'span' => 'span6',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '1',
                            'contenu' => '{"contenu":"[[BTN]]","icone":"","color_icone":"","align":"left","color":"#B8CC7F","color_text":"#ffffff","size":"btn-large","size_text":24,"border":0,"border_color":"","border_radius":50,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","contenuHover":"","iconeHover":"","color_iconeHover":"","alignHover":"center","colorHover":"","color_textHover":"#ffffff","sizeHover":"btn-large","size_textHover":24,"borderHover":0,"border_colorHover":"","border_radiusHover":50,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":0,"button_box_shadow_colorHover":"","contenuClicked":"","iconeClicked":"","color_iconeClicked":"","alignClicked":"center","colorClicked":"","color_textClicked":"#ffffff","sizeClicked":"btn-large","size_textClicked":24,"borderClicked":0,"border_colorClicked":"","border_radiusClicked":50,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":0,"button_box_shadow_colorClicked":"","action":"","param":"","position_icone":"left","decalage_icone":20,"position_iconeHover":"left","decalage_iconeHover":20,"position_iconeClicked":"left","decalage_iconeClicked":20,"size_icone":26,"size_iconeHover":26,"size_iconeClicked":26,"default_color_icone":"","bg_color1":"#B2CE74","bg_color2":"","bg_gradient_angle":"","font_text":"","font_styles":[],"btn_padding_top":25,"btn_padding_bottom":25,"btn_padding_left":60,"btn_padding_right":60,"bg_color1Hover":"#9cb860","bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"","font_stylesHover":[],"btn_padding_topHover":25,"btn_padding_bottomHover":25,"btn_padding_leftHover":60,"btn_padding_rightHover":60,"bg_color1Clicked":"#859e52","bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"","font_stylesClicked":[],"btn_padding_topClicked":25,"btn_padding_bottomClicked":25,"btn_padding_leftClicked":60,"btn_padding_rightClicked":60,"margin_top":50,"margin_bottom":0,"height":"btn-h-vb"}',
                            'contenutexte' => '[[BTN]]',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'cover',
                'padding_top' => '80',
                'padding_bottom' => '80',
                'margin_top' => '0',
                'margin_bottom' => '0',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
                'gradient_angle' => '0',
                'bg_color_overlay' => '',
                'bg_color_overlay_opacity' => '0',
                'padding_left' => '0',
                'padding_right' => '0',
                'margin_left' => '0',
                'margin_right' => '0',
                'border_width' => '0',
                'border_color' => '',
                'border_color_opacity' => '1',
                'border_radius_top_left' => '0',
                'border_radius_top_right' => '0',
                'border_radius_bottom_left' => '0',
                'border_radius_bottom_right' => '0',
                'border_style' => 'none',
                'box_shadow_x' => '0',
                'box_shadow_y' => '0',
                'box_shadow_blur' => '0',
                'box_shadow_color' => '',
                'box_shadow_opacity' => '1',
                'box_shadow' => '0',
                'line_theme' => 'line_theme2',
            ],
            'cols' => [
                [
                    'span' => '12',
                    'position' => '1',
                    'line_position' => '0',
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'gradient_angle' => '0',
                        'bgimage' => '',
                        'bgimage_position' => '',
                        'bgimage_size' => 'auto',
                        'bg_color_overlay' => '',
                        'bg_color_overlay_opacity' => '0',
                        'padding_top' => '0',
                        'padding_bottom' => '0',
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_width' => '0',
                        'border_color' => '',
                        'border_color_opacity' => '1',
                        'border_style' => 'none',
                        'border_radius_top_left' => '14',
                        'border_radius_top_right' => '14',
                        'border_radius_bottom_left' => '14',
                        'border_radius_bottom_right' => '14',
                        'box_shadow_x' => '0',
                        'box_shadow_y' => '0',
                        'box_shadow_blur' => '0',
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => '1',
                        'delay' => '0',
                        'delay_disappear' => '0',
                        'full_width' => '0',
                        'box_shadow' => '0',
                        'bg_side' => '',
                        'bg_side_position' => '',
                        'bg_side_width' => '100',
                        'bg_side_height' => '100',
                        'margin_left' => '0',
                        'margin_right' => '0',
                    ],
                    'elements' => [
                        [
                            'objet' => 'citation',
                            'bloc_alignement' => '',
                            'span' => 'span6',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"nom":"[[NOM]]","description":"[[DESCRIPTION]]","theme":"theme5","bg_content":"","bg_content_txt":"","bg_profil":"","bg_profil_txt":"#ffffff","bg_color":"[[COLOR_GRAY]]","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"bg_border_style":"solid","margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":60,"padding_bottom":60,"padding_left":60,"padding_right":60,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":"1","bg_content_color":"","content_color":"#FFFFFF","bg_profil_color":"","profil_color":"#FFFFFF","display_icone":"on","icone":"fa-quote-left","size_icone":"58","color_icone":"#FFFFFF","display_hr":"on","color_hr":"#FFFFFF","size_hr":"5","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"box_shadow":0,"absolute_position":false,"description_color":"#FFFFFF"}',
                            'contenutexte' => '<p style="text-align: center; color:#FFFFFF"><span style="font-size:43px;"><strong>"[[CITATION]]"</strong></span></p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '[[PARTICIPANTS]]',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
                [
                    'span' => '6',
                    'position' => '1',
                    'line_position' => '1',
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'gradient_angle' => '0',
                        'bgimage' => '',
                        'padding_top' => '0',
                        'padding_bottom' => '0',
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_top' => '80',
                        'margin_bottom' => '0',
                        'margin_left' => '0',
                        'margin_right' => '40',
                        'border_width' => '0',
                        'border_color' => '',
                        'border_color_opacity' => '1',
                        'border_style' => 'none',
                        'border_radius_top_left' => '0',
                        'border_radius_top_right' => '0',
                        'border_radius_bottom_left' => '0',
                        'border_radius_bottom_right' => '0',
                        'box_shadow_x' => '0',
                        'box_shadow_y' => '0',
                        'box_shadow_blur' => '0',
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => '1',
                        'full_width' => '0',
                        'box_shadow' => '0',
                        'bg_side' => '',
                        'bg_side_position' => '',
                        'bg_side_width' => '',
                        'bg_side_height' => '',
                    ],
                    'elements' => [
                        [
                            'objet' => 'temoignages',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"nom":"[[NOM]]","description":"[[DESCRIPTION]]","theme":"theme1","bg_color":"#ffffff","bg_opacity":"1","padding_top":60,"padding_bottom":60,"padding_left":60,"padding_right":60,"display_icone":"on","icone":"fa-quote-left","size_icone":"40","color_icone":"[[COLOR_ICONE]]","content_color":"","profil_color":"[[COLOR_USER]]","bg_border":0,"bg_border_color":"","bg_border_style":"none","bg_border_radius_top_left":14,"bg_border_radius_top_right":14,"bg_border_radius_bottom_left":14,"bg_border_radius_bottom_right":14,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"box_shadow":1,"box_shadow_x":4,"box_shadow_y":4,"box_shadow_blur":10,"box_shadow_color":"#000000","box_shadow_opacity":"0.14","bg_content_color":"","bg_profil_color":"","display_hr":"","color_hr":"[[COLOR_HR]]","size_hr":"0","description_color":"[[COLOR_USER_TITLE]]","absolute_position":false}',
                            'contenutexte' => '<p>[[TEMOIGNAGE]]</p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '[[PARTICIPANTS]]',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
                [
                    'span' => '6',
                    'position' => '2',
                    'line_position' => '1',
                    'design' => [
                        'margin_top' => '80',
                        'margin_left' => '40',
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'gradient_angle' => '0',
                        'bgimage' => '',
                        'padding_top' => '0',
                        'padding_bottom' => '0',
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_bottom' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '',
                        'border_color_opacity' => '1',
                        'border_style' => 'none',
                        'border_radius_top_left' => '0',
                        'border_radius_top_right' => '0',
                        'border_radius_bottom_left' => '0',
                        'border_radius_bottom_right' => '0',
                        'box_shadow_x' => '0',
                        'box_shadow_y' => '0',
                        'box_shadow_blur' => '0',
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => '1',
                        'full_width' => '0',
                        'box_shadow' => '0',
                        'bg_side' => '',
                        'bg_side_position' => '',
                        'bg_side_width' => '',
                        'bg_side_height' => '',
                    ],
                    'elements' => [
                        [
                            'objet' => 'temoignages',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"nom":"[[NOM]]","description":"[[DESCRIPTION]]","theme":"theme1","bg_color":"#ffffff","bg_opacity":"1","padding_top":60,"padding_bottom":60,"padding_left":60,"padding_right":60,"display_icone":"on","icone":"fa-quote-left","size_icone":"40","color_icone":"[[COLOR_ICONE]]","content_color":"","profil_color":"[[COLOR_USER]]","bg_border":0,"bg_border_color":"","bg_border_style":"none","bg_border_radius_top_left":14,"bg_border_radius_top_right":14,"bg_border_radius_bottom_left":14,"bg_border_radius_bottom_right":14,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"box_shadow":1,"box_shadow_x":4,"box_shadow_y":4,"box_shadow_blur":10,"box_shadow_color":"#000000","box_shadow_opacity":"0.14","bg_content_color":"","bg_profil_color":"","display_hr":"","color_hr":"[[COLOR_HR]]","size_hr":"0","description_color":"[[COLOR_USER_TITLE]]","absolute_position":false}',
                            'contenutexte' => '<p>[[TEMOIGNAGE]]</p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '[[PARTICIPANTS]]',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        return $theme_datas;
    }
}
