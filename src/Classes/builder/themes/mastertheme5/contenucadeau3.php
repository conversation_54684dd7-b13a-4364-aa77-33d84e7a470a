<?php

/**
 * Builder_Themes_Mastertheme5_contenucadeau3 class.
 *
 * @extends  Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Themes_Mastertheme5_contenucadeau3 extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        $theme_datas = [];
        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '[[FULLWIDTH]]',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'cover',
                'padding_top' => '70',
                'padding_bottom' => '0',
                'margin_top' => '0',
                'margin_bottom' => '0',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
                'line_theme' => 'line_theme4',
                'gradient_angle' => '0',
                'bg_color_overlay' => '',
                'bg_color_overlay_opacity' => '0',
                'padding_left' => '28',
                'padding_right' => '28',
                'margin_left' => '0',
                'margin_right' => '0',
                'border_width' => '0',
                'border_color' => '',
                'border_color_opacity' => '1',
                'border_radius_top_left' => '0',
                'border_radius_top_right' => '0',
                'border_radius_bottom_left' => '0',
                'border_radius_bottom_right' => '0',
                'border_style' => 'none',
                'box_shadow_x' => '0',
                'box_shadow_y' => '0',
                'box_shadow_blur' => '0',
                'box_shadow_color' => '',
                'box_shadow_opacity' => '1',
                'box_shadow' => '0',
                'bg_color_overlay1' => '',
                'bg_color_overlay2' => '',
                'bg_overlay_gradient_angle' => '240',
                'shape' => [
                    'bottom' => [
                        'shape' => 'shape1',
                        'color' => '[[COLOR_LINE_THEME_2]]',
                        'height' => 300,
                        'repeat' => 0,
                        'flip_horizontal' => 1,
                    ],
                ],
            ],
            'cols' => [
                [
                    'span' => '12',
                    'position' => '0',
                    'line_position' => '2',
                    'design' => [
                        'padding_top' => '10',
                        'padding_bottom' => '10',
                        'padding_left' => '10',
                        'padding_right' => '10',
                    ],
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"Rubik","font_theme":"h1","padding_top":10,"padding_bottom":50,"padding_left":0,"padding_right":0,"margin_top":0,"margin_bottom":0}',
                            'contenutexte' => '<h1 style=\"text-align: center;\"><span style="color: #ffffff\">' . __('Félicitations !') . '<br>' . __('Profitez de votre cadeau') . '</span></h1>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
                [
                    'span' => '8',
                    'position' => '1',
                    'line_position' => '3',
                    'design' => [
                        'padding_top' => '40',
                        'padding_bottom' => '60',
                        'padding_left' => '60',
                        'padding_right' => '60',
                        'bgcolor1' => '#ffffff',
                        'bgcolor2' => '',
                        'gradient_angle' => '0',
                        'bgimage' => '',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'margin_left' => '0',
                        'margin_right' => '25',
                        'border_width' => '0',
                        'border_color' => '',
                        'border_color_opacity' => '1',
                        'border_style' => 'none',
                        'border_radius_top_left' => '14',
                        'border_radius_top_right' => '14',
                        'border_radius_bottom_left' => '14',
                        'border_radius_bottom_right' => '14',
                        'box_shadow_x' => '4',
                        'box_shadow_y' => '4',
                        'box_shadow_blur' => '10',
                        'box_shadow_color' => '#000000',
                        'box_shadow_opacity' => '0.14',
                        'full_width' => '1',
                        'box_shadow' => '1',
                        'bg_side' => '',
                        'bg_side_position' => '',
                        'bg_side_width' => '',
                        'bg_side_height' => '',
                    ],
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"Rubik","font_theme":"h3","padding_top":0,"padding_bottom":10,"padding_left":0,"padding_right":0,"margin_top":0,"margin_bottom":0}',
                            'contenutexte' => '<h3 style=\"text-align: left;\">[[SUB_ACCROCHE]]</h3>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"font":"Rubik","font_theme":"h4","padding_top":10,"padding_bottom":30,"padding_left":0,"padding_right":0,"margin_top":0,"margin_bottom":0}',
                            'contenutexte' => '<h4 style=\"text-align: left;\">[[ACCROCHE2]]</h4>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'button_link',
                            'bloc_alignement' => '',
                            'span' => 'span6',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"contenu":"' . __('Je télécharge mon guide maintenant !') . '","icone":"","color_icone":"","align":"left","color":"#B8CC7F","color_text":"#ffffff","size":"btn-large","size_text":24,"border":0,"border_color":"","border_radius":50,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","contenuHover":"","iconeHover":"","color_iconeHover":"","alignHover":"center","colorHover":"","color_textHover":"#ffffff","sizeHover":"btn-large","size_textHover":24,"borderHover":0,"border_colorHover":"","border_radiusHover":50,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":0,"button_box_shadow_colorHover":"","contenuClicked":"","iconeClicked":"","color_iconeClicked":"","alignClicked":"center","colorClicked":"","color_textClicked":"#ffffff","sizeClicked":"btn-large","size_textClicked":24,"borderClicked":0,"border_colorClicked":"","border_radiusClicked":50,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":0,"button_box_shadow_colorClicked":"","action":"","param":"","position_icone":"left","decalage_icone":20,"position_iconeHover":"left","decalage_iconeHover":20,"position_iconeClicked":"left","decalage_iconeClicked":20,"size_icone":26,"size_iconeHover":26,"size_iconeClicked":26,"default_color_icone":"","bg_color1":"#B2CE74","bg_color2":"","bg_gradient_angle":"","font_text":"","font_styles":[],"btn_padding_top":25,"btn_padding_bottom":25,"btn_padding_left":60,"btn_padding_right":60,"bg_color1Hover":"#9cb860","bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"","font_stylesHover":[],"btn_padding_topHover":25,"btn_padding_bottomHover":25,"btn_padding_leftHover":60,"btn_padding_rightHover":60,"bg_color1Clicked":"#859e52","bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"","font_stylesClicked":[],"btn_padding_topClicked":25,"btn_padding_bottomClicked":25,"btn_padding_leftClicked":60,"btn_padding_rightClicked":60,"margin_top":30,"margin_bottom":0,"height":"btn-h-vb"}',
                            'contenutexte' => __('Je télécharge mon guide maintenant'),
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
                [
                    'span' => '4',
                    'position' => '2',
                    'line_position' => '3',
                    'design' => [
                        'padding_top' => '0',
                        'padding_bottom' => '0',
                        'padding_left' => '30',
                        'padding_right' => '0',
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'gradient_angle' => '0',
                        'bgimage' => '',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'margin_left' => '25',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '',
                        'border_color_opacity' => '1',
                        'border_style' => 'none',
                        'border_radius_top_left' => '0',
                        'border_radius_top_right' => '0',
                        'border_radius_bottom_left' => '0',
                        'border_radius_bottom_right' => '0',
                        'box_shadow_x' => '0',
                        'box_shadow_y' => '0',
                        'box_shadow_blur' => '0',
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => '1',
                        'full_width' => '0',
                        'box_shadow' => '0',
                        'bg_side' => '',
                        'bg_side_position' => '',
                        'bg_side_width' => '',
                        'bg_side_height' => '',
                    ],
                    'elements' => [
                        [
                            'objet' => 'image',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '',
                            'largeur' => '',
                            'eval' => '',
                            'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":10,"alt":""}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => 'center',
                            'time' => '0',
                            'name' => '',
                            'urls' => '[[EBOOK]]',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'cover',
                'padding_top' => '100',
                'padding_bottom' => '100',
                'margin_top' => '0',
                'margin_bottom' => '0',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
                'gradient_angle' => '0',
                'bg_color_overlay' => '',
                'bg_color_overlay_opacity' => '0',
                'padding_left' => '0',
                'padding_right' => '0',
                'margin_left' => '0',
                'margin_right' => '0',
                'border_width' => '0',
                'border_color' => '',
                'border_color_opacity' => '1',
                'border_radius_top_left' => '0',
                'border_radius_top_right' => '0',
                'border_radius_bottom_left' => '0',
                'border_radius_bottom_right' => '0',
                'border_style' => 'none',
                'box_shadow_x' => '0',
                'box_shadow_y' => '0',
                'box_shadow_blur' => '0',
                'box_shadow_color' => '',
                'box_shadow_opacity' => '1',
                'box_shadow' => '0',
                'line_theme' => 'line_theme2',
            ],
            'cols' => [
                [
                    'span' => '12',
                    'position' => '1',
                    'line_position' => '1',
                    'design' => [
                        'padding_top' => '60',
                        'padding_bottom' => '60',
                        'padding_left' => '60',
                        'padding_right' => '60',
                        'bgcolor1' => '#ffffff',
                        'bgcolor2' => '',
                        'gradient_angle' => '0',
                        'bgimage' => '',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '',
                        'border_color_opacity' => '1',
                        'border_style' => 'none',
                        'border_radius_top_left' => '14',
                        'border_radius_top_right' => '14',
                        'border_radius_bottom_left' => '14',
                        'border_radius_bottom_right' => '14',
                        'box_shadow_x' => '4',
                        'box_shadow_y' => '4',
                        'box_shadow_blur' => '10',
                        'box_shadow_color' => '#000000',
                        'box_shadow_opacity' => '0.14',
                        'full_width' => '0',
                        'box_shadow' => '1',
                        'bg_side' => '',
                        'bg_side_position' => '',
                        'bg_side_width' => '',
                        'bg_side_height' => '',
                    ],
                    'elements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":10,"font":"","font_theme":"h4"}',
                            'contenutexte' => '<h4>' . __('Voici votre chance d’apprendre plus en moins de temps !') . '</h4>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":10,"font":"","font_theme":"p","margin_top":30}',
                            'contenutexte' => '<p>' . __('Pourquoi apprendre devrait-il être ennuyeux ?') . '<br>' . __('L’objectif d’Apprentissage Rapide est de prendre plaisir à apprendre tous les sujets que vous aimez… Pour ne plus jamais vous ennuyer !') . '</p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":10,"font":"","font_theme":"p","margin_top":30}',
                            'contenutexte' => '<p>' . __('Grâce aux dernières recherches en Neurosciences, vous pouvez en 5 minutes par jour accumuler de nouvelles connaissances dans tous les domaines qui vous intéressent.') . ' ' . __('Vous verrez que la méthode Apprentissage Rapide est radicalement différente de l’apprentissage scolaire…') . '</p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        return $theme_datas;
    }
}
