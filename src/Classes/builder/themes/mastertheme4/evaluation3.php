<?php

/**
 * Builder_Themes_Mastertheme4_evaluation3 class.
 *
 * @extends  Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Themes_Mastertheme4_evaluation3 extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        $theme_datas = [];
        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'bgimage_position' => 'fixed',
                'bgimage_size' => 'cover',
                'padding_top' => '70',
                'padding_bottom' => '117',
                'margin_top' => '0',
                'margin_bottom' => '0',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
                'line_theme' => 'line_theme3',
                'gradient_angle' => '0',
                'bg_color_overlay' => '',
                'bg_color_overlay_opacity' => '0.74',
                'padding_left' => '0',
                'padding_right' => '0',
                'margin_left' => '0',
                'margin_right' => '0',
                'border_width' => '0',
                'border_color' => '',
                'border_color_opacity' => '1',
                'border_radius_top_left' => '0',
                'border_radius_top_right' => '0',
                'border_radius_bottom_left' => '0',
                'border_radius_bottom_right' => '0',
                'border_style' => 'none',
                'box_shadow_x' => '0',
                'box_shadow_y' => '0',
                'box_shadow_blur' => '0',
                'box_shadow_color' => '',
                'box_shadow_opacity' => '1',
                'box_shadow' => '0',
                'bg_color_overlay1' => '',
                'bg_color_overlay2' => '',
                'bg_overlay_gradient_angle' => '135',
            ],
            'cols' => [
                [
                    'span' => '12',
                    'position' => '1',
                    'line_position' => '2',
                    'design' => [
                        'padding_top' => '10',
                        'padding_bottom' => '10',
                        'padding_left' => '10',
                        'padding_right' => '10',
                    ],
                    'elements' => [
                        [
                            'objet' => 'image',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '',
                            'largeur' => '',
                            'eval' => '',
                            'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":10,"alt":""}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => 'center',
                            'time' => '0',
                            'name' => '',
                            'urls' => '[[LOGO]]',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
                [
                    'span' => '12',
                    'position' => '1',
                    'line_position' => '3',
                    'design' => [
                        'bgcolor1' => '#feffff',
                        'bgcolor2' => '',
                        'gradient_angle' => '0',
                        'bgimage' => '',
                        'bgimage_position' => '',
                        'bgimage_size' => 'auto',
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_color_overlay_opacity' => '0',
                        'bg_overlay_gradient_angle' => '0',
                        'padding_top' => '23',
                        'padding_bottom' => '16',
                        'padding_left' => '29',
                        'padding_right' => '29',
                        'margin_top' => '39',
                        'margin_bottom' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '',
                        'border_color_opacity' => '1',
                        'border_style' => 'none',
                        'border_radius_top_left' => '0',
                        'border_radius_top_right' => '0',
                        'border_radius_bottom_left' => '0',
                        'border_radius_bottom_right' => '0',
                        'box_shadow_x' => '0',
                        'box_shadow_y' => '0',
                        'box_shadow_blur' => '20',
                        'box_shadow_color' => '#000000',
                        'box_shadow_opacity' => '0.13',
                        'delay' => '0',
                        'delay_disappear' => '0',
                        'full_width' => '0',
                        'box_shadow' => '1',
                        'bg_side' => '',
                        'bg_side_position' => '',
                        'bg_side_width' => '100',
                        'bg_side_height' => '100',
                    ],
                    'elements' => [
                        [
                            'objet' => 'module',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":10}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => 'evaluation',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'animation' => '',
                            'save_appear' => '1',
                            'waypoint' => '0',
                            'delay_disappear' => '0',
                            'animation_disappear' => '',
                            'hide' => '0',
                            'hide_tablet' => '0',
                            'hide_phone' => '0',
                            'identifiant' => '',
                        ],
                    ],
                ],
            ],
        ];

        return $theme_datas;
    }
}
