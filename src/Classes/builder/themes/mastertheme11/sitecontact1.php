<?php

class Builder_Themes_Mastertheme11_sitecontact1 extends Eden_Class
{
    public function getThemeData(): array
    {
        return [
            'lines' => [
                [
                    'design' => [
                        'bgcolor1' => '',
                        'bgcolor2' => '',
                        'bgimage' => 'https://da32ev14kd4yl.cloudfront.net/versioned/template/images/theme6/fonds/learnybox-theme-06-fond-03.jpg',
                        'bgimage_position' => 'scroll',
                        'bgimage_size' => 'cover',
                        'padding_top' => '120',
                        'padding_bottom' => '120',
                        'margin_top' => '0',
                        'margin_bottom' => '0',
                        'border_top_width' => '',
                        'border_top_color' => '',
                        'border_bottom_width' => '',
                        'border_bottom_color' => '',
                        'full_width' => 0,
                        'delay' => 0,
                        'delay_disappear' => 0,
                        'line_theme' => '',
                        'gradient_angle' => 180,
                        'bg_color_overlay' => '',
                        'bg_color_overlay_opacity' => 0.32,
                        'padding_left' => '0',
                        'padding_right' => '0',
                        'margin_left' => '0',
                        'margin_right' => '0',
                        'border_width' => '0',
                        'border_color' => '#000000',
                        'border_color_opacity' => '0',
                        'border_radius_top_left' => 0,
                        'border_radius_top_right' => 0,
                        'border_radius_bottom_left' => 0,
                        'border_radius_bottom_right' => 0,
                        'border_style' => 'none',
                        'box_shadow_x' => 0,
                        'box_shadow_y' => 0,
                        'box_shadow_blur' => 0,
                        'box_shadow_color' => '',
                        'box_shadow_opacity' => 1,
                        'box_shadow' => 0,
                        'shape' => [
                            'top' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '0',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                            'bottom' => [
                                'shape' => '',
                                'color' => '#FFFFFF',
                                'height' => '150',
                                'repeat' => '0',
                                'flip_horizontal' => '1',
                                'flip_vertical' => '0',
                                'over' => '0',
                            ],
                        ],
                        'bg_color_overlay1' => '',
                        'bg_color_overlay2' => '',
                        'bg_overlay_gradient_angle' => 0,
                        'bg_color_page' => false,
                    ],
                    'contenu_mobile' => '',
                    'cols' => [
                        [
                            'span' => 7,
                            'position' => 1,
                            'line_position' => 1,
                            'design' => [
                                'padding_top' => 24,
                                'padding_bottom' => 24,
                                'padding_left' => 40,
                                'padding_right' => 40,
                                'col_height' => 1,
                                'bgcolor1' => '#F6F7F9',
                                'bgcolor2' => '',
                                'gradient_angle' => 180,
                                'bgimage' => false,
                                'margin_top' => 0,
                                'margin_bottom' => 0,
                                'margin_left' => 0,
                                'margin_right' => 0,
                                'border_width' => 0,
                                'border_color' => '#000000',
                                'border_color_opacity' => '0',
                                'border_radius_top_left' => 0,
                                'border_radius_top_right' => 0,
                                'border_radius_bottom_left' => 0,
                                'border_radius_bottom_right' => 0,
                                'box_shadow_x' => 0,
                                'box_shadow_y' => 5,
                                'box_shadow_blur' => 40,
                                'box_shadow_color' => '#000000',
                                'box_shadow_opacity' => 0.08,
                                'border_style' => 'none',
                                'full_width' => 0,
                                'box_shadow' => 1,
                                'bg_side' => 0,
                                'bg_side_position' => 'left',
                                'bg_side_width' => 100,
                                'bg_side_height' => false,
                                'bgimage_position' => '',
                                'bgimage_size' => 'cover',
                                'bg_color_overlay1' => '',
                                'bg_color_overlay2' => '',
                                'bg_color_overlay_opacity' => 1,
                                'bg_overlay_gradient_angle' => 0,
                                'delay' => 0,
                                'delay_disappear' => 0,
                            ],
                            'contenu_mobile' => '',
                            'elements' => [
                                [
                                    'objet' => 'txt',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"font":"default","font_theme":"h1","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"#000000","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_bottom":24,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"word_break":"by_default","bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_opacity2":"1","bg_border_color_opacity":"0","absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => "<h3><span style=\"color:#C65547\">Nous contacter</span></h3>\n",
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                                [
                                    'objet' => 'contact_form',
                                    'bloc_alignement' => '',
                                    'span' => 'span12',
                                    'x' => 0,
                                    'y' => 0,
                                    'hauteur' => 0,
                                    'largeur' => 0,
                                    'eval' => '',
                                    'contenu' => '{"bg_type":"color","bg_color1":"main_color1","bg_color1Hover":"main_color0","bg_color1Clicked":"main_color0","color_text":"#ffffff","color_textHover":"#ffffff","color_textClicked":"#ffffff","size_text":18,"size_textHover":18,"size_textClicked":18,"border_radius":100,"border_radiusHover":100,"border_radiusClicked":100,"size":"btn-xlarge","font_size":10,"font_color":"main_color0","border_color":"","border_colorHover":"","border_colorClicked":"","color_icone":"#ffffff","color_iconeHover":"#ffffff","color_iconeClicked":"#ffffff","padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0,"margin_bottom":0,"sujets":"[\"Question\",\"Rappelez-moi\",\"Support technique\",\"Support p\\\u00e9dagogique\",\"Rapporter un bug\",\"Autre\"]","icone":"","font":"Poppins","rgpd":false,"rgpd_aff":false,"rgpd_txt":"En indiquant votre adresse mail, vous acceptez que nous vous invitions par mail \u00e0 cette conf\u00e9rence et que nous vous adressions des offres personnalis\u00e9es de formations. Vous pouvez vous d\u00e9sinscrire \u00e0 tout moment en nous adressant un mail et \u00e0 travers les liens de d\u00e9sinscription.","rgpd_aff_txt":"En indiquant votre adresse mail, vous acceptez en \u00e9change de recevoir des offres commerciales de nos partenaires.","rgpd_checked_default":false,"rgpd_under_submit_button":false,"rgpd_aff_checked_default":false,"rgpd_aff_under_submit_button":false,"align":"center","height":"btn-h-b","bg_color2":"","bg_gradient_angle":"","border":0,"button_box_shadow_x":0,"button_box_shadow_y":0,"button_box_shadow_blur":0,"button_box_shadow_color":"","button_box_shadow_opacity":1,"contenu":"Valider","size_icone":14,"position_icone":"left","decalage_icone":0,"font_text":"Poppins","font_styles":[],"btn_padding_top":14,"btn_padding_bottom":14,"btn_padding_left":20,"btn_padding_right":20,"contenuHover":"","size_iconeHover":14,"position_iconeHover":"","decalage_iconeHover":0,"bg_color2Hover":"","bg_gradient_angleHover":"","font_textHover":"Poppins","font_stylesHover":[],"borderHover":0,"button_box_shadow_xHover":0,"button_box_shadow_yHover":0,"button_box_shadow_blurHover":0,"button_box_shadow_colorHover":"","button_box_shadow_opacityHover":1,"btn_padding_topHover":14,"btn_padding_bottomHover":14,"btn_padding_leftHover":20,"btn_padding_rightHover":20,"contenuClicked":"","size_iconeClicked":14,"position_iconeClicked":"","decalage_iconeClicked":0,"bg_color2Clicked":"","bg_gradient_angleClicked":"","font_textClicked":"Poppins","font_stylesClicked":[],"borderClicked":0,"button_box_shadow_xClicked":0,"button_box_shadow_yClicked":0,"button_box_shadow_blurClicked":0,"button_box_shadow_colorClicked":"","button_box_shadow_opacityClicked":1,"btn_padding_topClicked":14,"btn_padding_bottomClicked":14,"btn_padding_leftClicked":20,"btn_padding_rightClicked":20,"bgcolor1":"","bgcolor2":"","element_bg_gradient_angle":180,"bg_color":"","bg_opacity":"1","bg_opacity2":"1","bg_border":0,"bg_border_color":"#000000","bg_border_color_opacity":"0","bg_border_style":"none","bg_border_radius_top_left":0,"bg_border_radius_top_right":0,"bg_border_radius_bottom_left":0,"bg_border_radius_bottom_right":0,"margin_top":0,"margin_left":0,"margin_right":0,"box_shadow":0,"box_shadow_x":0,"box_shadow_y":0,"box_shadow_blur":0,"box_shadow_color":"","box_shadow_opacity":1,"absolute_position":0,"prevent_global_animation":false}',
                                    'contenutexte' => 'Valider',
                                    'contenu_mobile' => '',
                                    'type' => '',
                                    'fleche' => '',
                                    'time' => 0,
                                    'name' => '',
                                    'urls' => '',
                                    'url_webm' => '',
                                    'url_ogv' => '',
                                    'duree' => 0,
                                    'titre' => '',
                                    'delay' => 0,
                                    'animation' => '',
                                    'save_appear' => true,
                                    'waypoint' => false,
                                    'delay_disappear' => 0,
                                    'animation_disappear' => '',
                                    'hide' => false,
                                    'hide_tablet' => false,
                                    'hide_phone' => false,
                                    'identifiant' => '',
                                    'position' => null,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }
}
