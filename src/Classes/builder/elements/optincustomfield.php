<?php

/**
 * Builder_Elements_Optincustomfield class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Elements_Optincustomfield extends \Learnybox\Services\Builder\BuilderElements\AbstractBuilderElements
{
    public function __construct()
    {
        $this->examples = [];
        $this->examples[] = [
            'elements' => [
                [
                    'update' => [
                        'contenu' => [
                            'template' => 'template7',
                            'font_size' => '18',
                            'obligatoire' => false,
                            'displayName_font_size' => '18',
                            'font_color' => '#1e3948',
                            'displayName_font_color' => '#1e3948'
                        ],
                    ],
                ],
            ],
        ];
    }

    /********************************************************/
    /********************** AFFICHAGE ***********************/
    /********************************************************/

    public function afficher_element($data_formulaire, $element, $admin = false)
    {
        $output = '';
        $js = '';

        $template = 'template1';

        $contenu = [];
        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);
        }

        if (!isset($contenu['id_customfield']) or !$contenu['id_customfield']) {
            return ['output' => '', 'js' => ''];
        }

        $customfield = eden()->UserConfig()->getCustomFieldById($contenu['id_customfield']);
        if (!$customfield) {
            return ['output' => '', 'js' => ''];
        }

        if (isset($contenu['template'])) {
            $template = $contenu['template'];
        }

        $style = '';

        if (isset($contenu['displayName_font']) && $contenu['displayName_font'] && 'default' !== $contenu['displayName_font']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' #label-form-input' . $element['ID'] . ' {
                   font-family:' . eden()->Builder_Fonts()->getFontFamily($contenu['displayName_font']) . ';
                }';
        }

        if (isset($contenu['displayName_font_size']) && $contenu['displayName_font_size']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' #label-form-input' . $element['ID'] . ' {
                   font-size:' . $contenu['displayName_font_size'] . 'px;
                }';
        }

        if (isset($contenu['displayName_font_color']) && $contenu['displayName_font_color']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' #label-form-input' . $element['ID'] . ' {
                    color:' . $contenu['displayName_font_color'] . ';
                }';
        }

        if (isset($contenu['font']) && $contenu['font'] && 'default' != $contenu['font']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' .form-group .checkbox>label {
                    font-family:' . eden()->Builder_Fonts()->getFontFamily($contenu['font']) . ';
                }
                #formcontainer' . $element['ID'] . ' #form-input' . $element['ID'] . ' {
                    font-family:' . eden()->Builder_Fonts()->getFontFamily($contenu['font']) . ';
                }
                #formcontainer' . $element['ID'] . ' #form-input' . $element['ID'] . '::placeholder {
                    font-family:' . eden()->Builder_Fonts()->getFontFamily($contenu['font']) . ';
                }
            ';
        }

        if (isset($contenu['font_size']) && $contenu['font_size']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' .form-group .checkbox>label {
                    font-size:' . $contenu['font_size'] . 'px;
                }
                #formcontainer' . $element['ID'] . ' #form-input' . $element['ID'] . ' {
                    font-size:' . $contenu['font_size'] . 'px;
                }
                #formcontainer' . $element['ID'] . ' #form-input' . $element['ID'] . '::placeholder {
                    font-size:' . $contenu['font_size'] . 'px;
                }
            ';
        }

        if (isset($contenu['font_color']) && $contenu['font_color']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' .form-group .checkbox>label {
                    color:' . $contenu['font_color'] . ';
                }
                #formcontainer' . $element['ID'] . ' #form-input' . $element['ID'] . ' {
                    color:' . $contenu['font_color'] . ';
                }
                #formcontainer' . $element['ID'] . ' #form-input' . $element['ID'] . '::placeholder {
                    color:' . $contenu['font_color'] . ';
                }
            ';
        }

        if (isset($contenu['champs_bgcolor_transparent']) && $contenu['champs_bgcolor_transparent']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' #form-input' . $element['ID'] . ' {
                    background:transparent !important;
                }';
        } elseif (isset($contenu['champs_bgcolor']) && $contenu['champs_bgcolor']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' #form-input' . $element['ID'] . ' {
                    background:' . $contenu['champs_bgcolor'] . ' !important;
                }';
        }

        if ($style) {
            $output .= '<style type="text/css">' . $style . '</style>';
        }

        $form = $this->makeForm($customfield, $contenu, $element, $admin);
        if (!$form) {
            return ['output' => '', 'js' => ''];
        }

        if ('input_hidden' == $form['type']) {
            $output = $form['output'];
        } else {
            $output .= '
    <div class="optin ' . $template . '">
        <div class="content" id="formcontainer' . $element['ID'] . '">
            ' . $form['output'] . '
        </div>
    </div>';
        }

        return ['output' => $output, 'js' => $js, 'type' => $form['type']];
    }

    public function admin_display_element($element, $data_formulaire = [], $parent_span = 'span12')
    {
        $output = '';

        $template = 'template1';

        $contenu = [];
        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);
        }

        if (isset($contenu['template'])) {
            $template = $contenu['template'];
        }

        if (!isset($contenu['id_customfield']) or !$contenu['id_customfield']) {
            $output .= '<div class="alert alert-info">' . __('Champ non configuré. Cliquez sur "Modifier" pour le configurer.') . '</div>';
        } else {
            $customfield = eden()->UserConfig()->getCustomFieldById($contenu['id_customfield']);
            if (!$customfield) {
                $output .= '<div class="alert alert-danger">' . __('Le champ sélectionné n\'existe plus. Cliquez sur "Modifier" pour le configurer.') . '</div>';
            } else {
                $afficher_element = $this->afficher_element($data_formulaire, $element, true);
                if (!$afficher_element['output']) {
                    $output .= '<div class="alert alert-info">' . __('Une erreur est survenue lors de la génération du formulaire.') . '</div>';
                } else {
                    $output .= '
	                    <div class="txt">
	                        <div class="content ' . $template . '">
	                            ' . $afficher_element['output'] . '
	                        </div>
	                    </div>';
                }
            }
        }

        return $output;
    }

    /********************************************************/
    /************************ UPDATE ************************/
    /********************************************************/

    public function validatepost_element($cleaned_post)
    {
        $validPost = true;
        $error = '';

        $id_customfield = 0;
        if (isset($cleaned_post['id_customfield'])) {
            $id_customfield = filter_var($cleaned_post['id_customfield'], FILTER_VALIDATE_INT);
        }

        if ($id_customfield) {
            $customfield = eden()->UserConfig()->getCustomFieldById($id_customfield);
            if (!$customfield) {
                $validPost = false;
                $error .= '<li>' . __("Le champ sélectionné n'existe pas") . '</li>';
            }
        } else {
            $field_name = filter_var($cleaned_post['field-name'], FILTER_SANITIZE_SPECIAL_CHARS);
            if (!$field_name) {
                $validPost = false;
                $error .= '<li>' . __('Veuillez sélectionner un champ ou entrer le nom du nouveau champ') . '</li>';
            }

            $field_type = filter_var($cleaned_post['field-type'], FILTER_SANITIZE_SPECIAL_CHARS);
            if (!$field_type) {
                $validPost = false;
                $error .= '<li>' . __('Veuillez sélectionner le type de champ à afficher') . '</li>';
            } else {
                if ('input_radio' == $field_type or 'input_checkbox' == $field_type or 'select' == $field_type) {
                    $reponses = [];
                    if (isset($cleaned_post['field-reponses'])) {
                        $reponses = filter_var($cleaned_post['field-reponses'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
                    }

                    if (!$reponses) {
                        $validPost = false;
                        $error .= '<li>' . __('Veuillez entrer une ou plusieurs réponses') . '</li>';
                    }
                }
            }
        }

        return ['validPost' => $validPost, 'error' => $error];
    }

    public function update_element($cleaned_post, $original_element = [])
    {
        $array_update = [];
        $contenu = '';

        $field = [];

        $id_customfield = 0;
        if (isset($cleaned_post['id_customfield'])) {
            $id_customfield = filter_var($cleaned_post['id_customfield'], FILTER_VALIDATE_INT);
        }

        if (!$id_customfield) {
            $insert_customfield = eden()->UserConfig()->insert_customfield($cleaned_post);
            if (!$insert_customfield['valid']) {
                return ['valid' => false, 'message' => $insert_customfield['message']];
            }

            $id_customfield = $insert_customfield['id_customfield'];
        } else {
            $update_customfield = eden()->UserConfig()->update_customfield($cleaned_post);
            if (!$update_customfield['valid']) {
                return ['valid' => false, 'message' => $update_customfield['message']];
            }
        }

        $customfield = eden()->UserConfig()->getCustomFieldById($id_customfield);
        $uniqid = $customfield['uniqid'];

        $field['name'] = filter_var($cleaned_post['field-name'], FILTER_SANITIZE_SPECIAL_CHARS);
        $field['displayName'] = filter_var($cleaned_post['field-display-name'], FILTER_SANITIZE_SPECIAL_CHARS);
        $field['value'] = filter_var($cleaned_post['field-value'], FILTER_SANITIZE_SPECIAL_CHARS);
        $field['placeholder'] = filter_var($cleaned_post['field-placeholder'], FILTER_SANITIZE_SPECIAL_CHARS);
        $field['get'] = filter_var($cleaned_post['field-get'], FILTER_SANITIZE_SPECIAL_CHARS);
        $field['uniqid'] = $uniqid;
        $template = filter_var($cleaned_post['template'], FILTER_SANITIZE_SPECIAL_CHARS);

        $field['obligatoire'] = false;
        if (isset($cleaned_post['field-obligatoire']) && $cleaned_post['field-obligatoire']) {
            $field['obligatoire'] = true;
        }

        $type = '';
        if (isset($cleaned_post['field-type'])) {
            $type = filter_var($cleaned_post['field-type'], FILTER_SANITIZE_SPECIAL_CHARS);
        }
        if (!$type) {
            $type = $customfield['type'];
        }

        $field['type'] = $type;

        if ($type == 'input_radio' || $type == 'input_checkbox') {
            $template = 'template1';
        }

        //theme
        $font = filter_var($cleaned_post['font'], FILTER_SANITIZE_SPECIAL_CHARS);
        $font_size = filter_var($cleaned_post['font_size'], FILTER_VALIDATE_INT);
        $font_color = filter_var($cleaned_post['font_color'], FILTER_SANITIZE_SPECIAL_CHARS);
        $champs_bgcolor = filter_var($cleaned_post['champs_bgcolor'], FILTER_SANITIZE_SPECIAL_CHARS);
        $displayNameFont = filter_var($cleaned_post['displayName_font'], FILTER_SANITIZE_SPECIAL_CHARS);
        $displayNameFontSize = filter_var($cleaned_post['displayName_font_size'], FILTER_SANITIZE_SPECIAL_CHARS);
        $displayNameFontColor = filter_var($cleaned_post['displayName_font_color'], FILTER_SANITIZE_SPECIAL_CHARS);

        $champs_bgcolor_transparent = false;
        if (isset($cleaned_post['champs_bgcolor_transparent']) && 1 == $cleaned_post['champs_bgcolor_transparent']) {
            $champs_bgcolor_transparent = true;
        }

        $array_update['contenu'] = [
            'id_customfield' => $id_customfield,
            'field' => $field,
            'template' => $template,
            'font' => $font,
            'font_size' => $font_size,
            'font_color' => $font_color,
            'champs_bgcolor' => $champs_bgcolor,
            'champs_bgcolor_transparent' => $champs_bgcolor_transparent,
            'displayName_font' => $displayNameFont,
            'displayName_font_size' => $displayNameFontSize,
            'displayName_font_color' => $displayNameFontColor,
        ];

        return $array_update;
    }

    /********************************************************/
    /********************** FUNCTIONS ***********************/
    /********************************************************/

    public function makeForm($customfield, $datas_form, $element, $admin = false)
    {
        if (!$customfield) {
            return;
        }
        if (!$datas_form) {
            return;
        }

        $form = '';

        $field = $datas_form['field'];

        if (isset($_POST['custom'][$customfield['uniqid']])) {
            $field['value'] = filter_var($_POST['custom'][$customfield['uniqid']], FILTER_SANITIZE_SPECIAL_CHARS);
        }
        if (!$field['value'] and isset($field['get']) and $field['get'] and isset($_GET[$field['get']])) {
            $field['value'] = filter_input(INPUT_GET, $field['get'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        $id = 'form-input' . $element['ID'];
        $name = 'custom[' . $customfield['uniqid'] . ']';
        $type = $field['type'];
        $placeholder = $field['obligatoire'] ? $field['placeholder'] . ' *' : $field['placeholder'];
        $value = $field['value'];

        if ('input_hidden' == $type) {
            if ($admin) {
                return ['type' => $type, 'output' => '<div class="alert well" style="margin-bottom:0px">' . __('Champ caché') . ' : ' . $field['displayName'] . '</div>'];
            }

            return ['type' => $type, 'output' => '<input id="' . $id . '" name="' . $name . '" type="hidden" value="' . $value . '">'];
        }

        $form .= '
        <div class="form-group">
            ' . ((isset($field['displayName']) and $field['displayName']) ? '<label id="label-' . $id . '" for="' . $id . '">' . $field['displayName'] . ((isset($field['obligatoire']) and $field['obligatoire']) ? ' *' : '') . '</label>' : '') . '
            <div class="controls">';

        if ('input_text' == $type or 'input_int' == $type) {
            $class = 'input-xlarge';
            $type_input = 'text';
            if (!$placeholder) {
                $placeholder = __('Entrez votre réponse ici');
            }
            if ('input_int' == $type) {
                $class = 'input-small';
            }

            $form .= '<input class="form-control ' . $class . '" id="' . $id . '" name="' . $name . '" type="' . $type_input . '" placeholder="' . $placeholder . '" value="' . $value . '">';
        } elseif ('input_url' == $type) {
            $form .= '<input class="form-control input-xlarge" id="' . $id . '" name="' . $name . '" type="url" placeholder="' . ($placeholder ?: 'http://') . '" value="' . $value . '">';
        } elseif ('text' == $type) {
            $form .= '<textarea class="form-control" id="' . $id . '" name="' . $name . '" placeholder="' . $placeholder . '" rows="2">' . $value . '</textarea>';
        } elseif ('input_radio' == $type or 'select' == $type or 'input_checkbox' == $type) {
            $reponses = eden()->UserConfig()->getCustomFieldReponses($customfield['id_customfield']);
            if ($reponses) {
                $i = 0;

                if ('select' == $type) {
                    $form .= '<select name="' . $name . '">';
                }

                foreach ($reponses as $id => $reponse) {
                    ++$i;

                    $reponse_id = 'reponse-' . $element['ID'] . '-' . $i;
                    $value = $reponse['id_reponse'];
                    $text = $reponse['reponse'];

                    if ('input_radio' == $type) {
                        $form .= '
        					<div class="checkbox">
        					    <label for="' . $reponse_id . '">
        						    <input id="' . $reponse_id . '" name="' . $name . '" type="radio" value="' . $value . '">' . $text . '
                                </label>
                            </div>';
                    } elseif ('select' == $type) {
                        $form .= '<option value="' . $value . '">' . $text . '</option>';
                    } elseif ('input_checkbox' == $type) {
                        $form .= '
        					<div class="checkbox">
        					    <label for="' . $reponse_id . '">
        							<input id="' . $reponse_id . '" name="' . $name . '[]" type="checkbox" value="' . $value . '">' . $text . '
                                </label>
                            </div>';
                    }
                }

                if ('select' == $type) {
                    $form .= '</select>';
                }
            }
        }

        $form .= '
            </div>
        </div>';

        return ['type' => $type, 'output' => $form];
    }

    /********************************************************/
    /********************** FORMULAIRE **********************/
    /********************************************************/

    /**
     * getForm function.
     *
     * @param int $id_page
     * @param int $id_element
     * @param array $page
     * @param array $element
     *
     * @return array
     */
    public function getForm($id_page, $id_element, $page, $element, $swatches = [])
    {
        $output = '';

        $contenu = [];
        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);
        }

        $id_customfield = 0;
        if (isset($contenu['id_customfield']) and $contenu['id_customfield']) {
            $id_customfield = $contenu['id_customfield'];
        }

        $selectCustomFields = eden()->UserConfig()->generateSelectCustomFields($id_customfield);

        $font = $font_size = $font_color = '';
        $champs_bgcolor = $champs_bgcolor_transparent = '';
        if ($contenu) {
            if (isset($contenu['font'])) {
                $font = $contenu['font'];
            }
            if (isset($contenu['font_size'])) {
                $font_size = $contenu['font_size'];
            }
            if (isset($contenu['font_color'])) {
                $font_color = $contenu['font_color'];
            }
            if (isset($contenu['champs_bgcolor'])) {
                $champs_bgcolor = $contenu['champs_bgcolor'];
            }
            if (isset($contenu['champs_bgcolor_transparent']) and $contenu['champs_bgcolor_transparent']) {
                $champs_bgcolor_transparent = true;
            }
        }
        if (!$font_size) {
            $font_size = 18;
        }

        if (!isset($contenu['template']) or !$contenu['template']) {
            $contenu['template'] = 'template1';
        }

        //configuration du champs
        $field = ['name' => ''];
        if (isset($contenu['field']) and $contenu['field']) {
            $field = $contenu['field'];
        }

        $type = (isset($field['type']) ? $field['type'] : '');

        $output .= '<div class="tab-pane active" id="tab_champs">';

        if ($selectCustomFields) {
            $output .= '
			<div class="form-group">
				<label class="control-label" for="id_customfield">' . __('Sélectionnez un champ') . '</label>
				<div class="controls">
				    <select name="id_customfield" id="id_customfield" data-rel="select2" onchange="getCustomFieldInfos();">
                        ' . $selectCustomFields . '
                    </select>
				</div>
			</div>
			<hr>';
        }

        if ($id_customfield) {
            $output .= '<input type="hidden" name="field-name" value="' . $field['name'] . '">';
        } else {
            $output .= '
	            <div class="form-group">
					<label class="control-label" for="field-name">' . __('Nom') . '</label>
					<span class="help-block">' . __('Ne sera pas affiché mais apparaîtra dans la fiche de votre contact.') . '</span>
					<div class="controls">
						<input class="input-large form-control" name="field-name" id="field-name" type="text" value="' . (isset($field['name']) ? $field['name'] : '') . '">
					</div>
				</div>';
        }

        $output .= '
			<div class="form-group">
				<label class="control-label" for="field-display-name">' . __('Nom à afficher') . '</label>
				<div class="controls">
					<input class="input-xxlarge form-control focused" name="field-display-name" id="field-display-name" type="text" value="' . (isset($field['displayName']) ? $field['displayName'] : '') . '">
				</div>
			</div>

			<div class="control-group" style="margin-bottom:10px">
				<label class="control-label" for="type">' . __('Type') . ' *</label>
				<div class="controls">
					<div class="checkbox">
					    <label for="type-input_radio"><input type="radio" name="field-type" id="type-input_radio" value="input_radio" onchange="$(\'#paramsTexte\').hide(); $(\'#reponses\').show();" ' . ('input_radio' == $type ? 'checked' : '') . ' /> ' . __('Cases à cocher (réponse unique)') . '</label>
					</div>
					<div class="checkbox">
    					<label for="type-input_checkbox"><input type="radio" name="field-type" id="type-input_checkbox" value="input_checkbox" onchange="$(\'#paramsTexte\').hide(); $(\'#reponses\').show();" ' . ('input_checkbox' == $type ? 'checked' : '') . ' /> ' . __('Cases à cocher (réponses multiples)') . '</label>
					</div>
					<div class="checkbox">
    					<label for="type-select"><input type="radio" name="field-type" id="type-select" value="select" onchange="$(\'#paramsTexte\').hide(); $(\'#reponses\').show();" ' . ('select' == $type ? 'checked' : '') . ' /> ' . __('Liste déroulante (réponse unique)') . '</label>
					</div>
					<div class="checkbox">
    					<label for="type-input_text"><input type="radio" name="field-type" id="type-input_text" value="input_text" onchange="$(\'#paramsTexte\').show(); $(\'#reponses\').hide();" ' . ('input_text' == $type ? 'checked' : '') . ' /> ' . __('Champ libre (texte court)') . '</label>
					</div>
					<div class="checkbox">
    					<label for="type-text"><input type="radio" name="field-type" id="type-text" value="text" onchange="$(\'#paramsTexte\').show(); $(\'#reponses\').hide();" ' . ('text' == $type ? 'checked' : '') . ' /> ' . __('Champ libre (texte long)') . '</label>
					</div>
					<div class="checkbox">
    					<label for="type-input_int"><input type="radio" name="field-type" id="type-input_int" value="input_int" onchange="$(\'#paramsTexte\').show(); $(\'#reponses\').hide();" ' . ('input_int' == $type ? 'checked' : '') . ' /> ' . __('Champ libre (nombres)') . '</label>
					</div>
					<div class="checkbox">
    					<label for="type-input_url"><input type="radio" name="field-type" id="type-input_url" value="input_url" onchange="$(\'#paramsTexte\').show(); $(\'#reponses\').hide();" ' . ('input_url' == $type ? 'checked' : '') . ' /> ' . __('Champ libre (adresse internet)') . '</label>
					</div>
					<div class="checkbox">
    					<label for="type-input_hidden"><input type="radio" name="field-type" id="type-input_hidden" value="input_hidden" onchange="$(\'#paramsTexte\').show(); $(\'#reponses\').hide();" ' . ('input_hidden' == $type ? 'checked' : '') . ' /> ' . __('Champ caché') . '</label>
    				</div>
				</div>
			</div>


            <div class="form-group">
				<label class="control-label" for="field-obligatoire">' . __('Obligatoire') . '</label>
				<div class="controls">
					<label for="field-obligatoire" style="margin-top: 4px;">
    					<input type="checkbox" name="field-obligatoire" id="field-obligatoire" ' . ((isset($field['obligatoire']) and $field['obligatoire']) ? 'checked' : '') . ' style="margin-top: 0px;"> ' . __('Cochez cette case pour rendre ce champ obligatoire') . '
    				</label>
    			</div>
            </div>


            <div id="paramsTexte" style="display:' . (('input_text' == $type or 'text' == $type or 'input_int' == $type or 'input_url' == $type) ? 'block' : 'none') . '">

                <div class="form-group">
    				<label class="control-label" for="field-value">' . __('Valeur') . '</label>
    				<div class="controls">
    					<input class="form-control input-xlarge" type="text" name="field-value" id="field-value" value="' . ((isset($field['value']) and $field['value']) ? $field['value'] : '') . '">
    				</div>
                </div>

                <div class="form-group">
    				<label class="control-label" for="field-placeholder">' . __('Valeur par défaut') . '</label>
    				<div class="controls">
    					<input class="form-control input-xlarge" type="text" name="field-placeholder" id="field-placeholder" value="' . ((isset($field['placeholder']) and $field['placeholder']) ? $field['placeholder'] : '') . '">
    				</div>
                </div>

                <div class="form-group">
    				<label class="control-label" for="field-get">' . __('Pré-remplissage') . '</label>
    				<div class="controls">
    					<input class="form-control input-xlarge" type="text" name="field-get" id="field-get" value="' . ((isset($field['get']) and $field['get']) ? $field['get'] : '') . '">
    					<div class="alert alert-info" style="margin-top:10px">
                            ' . __('Le pré-remplissage sert à pré-remplir les différents champs de votre formulaire en passant des paramètres dans l\'adresse de votre page.') . '<br>' . __('Par exemple, si vous indiquez "prenom" dans ce champ, et qu\'un visiteur consulte votre page avec l\'adresse : %smonsite.com%sma-page%sprenom%s', 'http://', '/', '/?', '=') . '<strong>' . $_SESSION['name'] . '</strong>, ' . __('ce champ sera pré-rempli avec la valeur') . ' <strong>' . $_SESSION['name'] . '</strong>.
                        </div>
    				</div>
                </div>

            </div>

            <div id="reponses" style="display:' . (('input_radio' == $type or 'input_checkbox' == $type or 'select' == $type) ? 'block' : 'none') . '">
	            <hr>
	            <h4>' . __('Réponses') . '</h4>
	            <div class="reponses">';

        //nombre de réponses à afficher
        if ($id_customfield) {
            $reponses = eden()->UserConfig()->getCustomFieldReponses($id_customfield);
            if ($reponses) {
                $field['reponses'] = $reponses;
            }
        }

        $max_reponses = 3;
        if (isset($field['reponses'])) {
            $max_reponses = count($field['reponses']);
        }

        if ($max_reponses < 3) {
            $max_reponses = 3;
        }

        for ($i = 0; $i < $max_reponses; ++$i) {
            $num_reponse = $i;
            ++$num_reponse;

            $output .= '
			<div class="form-group">
				<label class="control-label" for="' . $i . '">' . __('Réponse') . ' ' . $num_reponse . '</label>
				<div class="controls">
				    <input type="hidden" class="input_id_reponse" id="' . $i . '" value="">
					<input class="input-xxlarge form-control focused" name="field-reponses[' . (isset($field['reponses'][$i]['id_reponse']) ? $field['reponses'][$i]['id_reponse'] : '') . ']" id="' . $i . '" type="text" value="' . (isset($field['reponses'][$i]['reponse']) ? $field['reponses'][$i]['reponse'] : '') . '">
				</div>
			</div>';
        }

        $output .= '
		        </div>
				<a href="#" onclick="AddReponse()" style="padding:10px">' . __('Ajouter une réponse') . '</a>
            </div>
        </div>';

        //configuration du thème
        $output .= '
    		<div class="tab-pane" id="tab_theme">

                <div class="control-group" style="margin-top: 20px;">
    				<label class="control-label" for="template">' . __('Thème') . '</label>
    				<div class="controls">
    					<label class="radio-inline" style="margin-right:10px">
    						<input type="radio" data-no-uniform="true" name="template" id="template_1" value="template1" ' . (($contenu['template'] and 'template1' == $contenu['template']) ? 'checked' : '') . ' /> <img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template1.png') . '" style="width: 250px; margin-top: -20px;">
    					</label>
    					<br>
    					<label class="radio-inline" style="margin-right:10px; margin-top:10px">
    						<input type="radio" data-no-uniform="true" name="template" id="template_2" value="template2" ' . (($contenu['template'] and 'template2' == $contenu['template']) ? 'checked' : '') . ' /> <img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template2.png') . '" style="width: 250px; margin-top: -5px;">
    					</label>
    					<br>
    					<label class="radio-inline" style="margin-right:10px; margin-top:10px">
    						<input type="radio" data-no-uniform="true" name="template" id="template_3" value="template3" ' . (($contenu['template'] and 'template3' == $contenu['template']) ? 'checked' : '') . ' /> <img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template3.png') . '" style="width: 500px; margin-top: -5px;">
    					</label>
    					<br>
    					<label class="radio-inline" style="margin-right:10px; margin-top:10px">
    						<input type="radio" data-no-uniform="true" name="template" id="template_4" value="template4" ' . (($contenu['template'] and 'template4' == $contenu['template']) ? 'checked' : '') . ' /> <img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template4.png') . '" style="width: 500px; margin-top: -5px;">
    					</label>
    					<br>
    					<label class="radio-inline" style="margin-right:10px; margin-top:10px">
    						<input type="radio" data-no-uniform="true" name="template" id="template_5" value="template5" ' . (($contenu['template'] and 'template5' == $contenu['template']) ? 'checked' : '') . ' /> <img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template5.png') . '" style="width: 500px; margin-top: -5px;">
    					</label>
    					<br>
    					<label class="radio-inline" style="margin-right:10px; margin-top:10px">
    						<input type="radio" data-no-uniform="true" name="template" id="template_6" value="template6" ' . (($contenu['template'] and 'template6' == $contenu['template']) ? 'checked' : '') . ' /> <img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template6.png') . '" style="width: 300px; margin-top: -5px;">
    					</label>
    					<br>
    					<label class="radio-inline" style="margin-right:10px; margin-top:10px">
    						<input type="radio" data-no-uniform="true" name="template" id="template_7" value="template7" ' . (($contenu['template'] and 'template7' == $contenu['template']) ? 'checked' : '') . ' /> <img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template6.png') . '" style="width: 500px; margin-top: -5px;">
    					</label>
    				</div>
    			</div>

                <hr>

                <div class="form-group">
                    <label class="control-label" for="font_size">' . __('Police') . '</label>
                    <div class="controls">
                        <select name="font">
                            ' . eden()->TunnelsPagesElements()->generateSelectFont($font) . '
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label" for="font_size">' . __('Taille de la police') . '</label>
                    <div class="controls">
                        <div class="input-group">
                            <input type="number" min="1" class="form-control input-small" name="font_size" id="font_size" value="' . $font_size . '">
                            <span class="input-group-addon">px</span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label" for="font_colorSelector' . $element['ID'] . '">' . __('Couleur du texte') . '</label>
                    <div class="controls">
                        <input class="form-control input-small color-single" type="text" name="font_color" maxlength="7" id="font_colorSelector' . $element['ID'] . '" value="' . $font_color . '">
                    </div>
                </div>

                <div class="control-group" style="padding-bottom:150px">
                    <label class="control-label" for="champs_colorSelector' . $element['ID'] . '">' . __('Couleur de fond des champs') . '</label>
                    <div class="controls">
                        <input class="form-control input-small color-single" type="text" name="champs_bgcolor" maxlength="7" id="champs_colorSelector' . $element['ID'] . '" value="' . $champs_bgcolor . '">
                        <br>
                        <label for="champs_bgcolor_transparent" style="margin-top:4px">
                            <input type="checkbox" name="champs_bgcolor_transparent" id="champs_bgcolor_transparent" value="transparent" ' . ($champs_bgcolor_transparent ? 'checked' : '') . ' style="margin-top:0px"> ' . __('Transparent') . '
                        </label>
                    </div>
                </div>

            </div>';

        return $output;
    }

    public function getExamples()
    {
        return $this->examples;
    }
}
