<?php

/**
 * Builder_Elements_Tip class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Elements_Tip extends \Learnybox\Services\Builder\BuilderElements\AbstractBuilderElements
{
    public function __construct()
    {
        $this->examples = [];
        $this->examples[] = [
            'elements' => [
                [
                    'display' => '<img src="' . \Learnybox\Helpers\Assets::getImageUrl('builder/examples/tip-1.png') . '">',
                    'update' => [
                        'contenutexte' => '<p>' . __('Pour continuer votre formation, cliquez sur le bouton "Suite" tout en haut à droite.') . '</p>',
                        'urls' => \Learnybox\Helpers\Assets::getImageUrl('app/v4/tips/avatar-tips.png'),
                        'contenu' => '{
                            "padding_top":10,
                            "padding_bottom":10,
                            "padding_left":10,
                            "padding_right":10,
                            "tip_bg_color":"#F0F1F5",
                            "tip_bg_opacity":"1",
                            "tip_border_radius_top_left":16,
                            "tip_border_radius_top_right":16,
                            "tip_border_radius_bottom_left":16,
                            "tip_border_radius_bottom_right":16,
                            "tip_button_box_shadow_x":0,
                            "tip_button_box_shadow_y":2,
                            "tip_button_box_shadow_blur":4,
                            "tip_button_box_shadow_color":"#000000",
                            "tip_button_box_shadow_opacity":"0.14"
                        }',
                    ],
                ],
            ],
        ];
    }

    /********************************************************/
    /********************** AFFICHAGE ***********************/
    /********************************************************/

    public function afficher_element($data_tunnel, $element)
    {
        $output = '';

        //contenu
        $contenutexte = '';
        if ($element['contenutexte']) {
            $contenutexte = '<div class="texte"><div class="editable editable-description" data-input="description" contenteditable="false">' . trim($element['contenutexte']) . '</div></div>';
            $contenutexte = str_replace('\\r', '<br>', $contenutexte);
            $contenutexte = stripslashes(stripslashes($contenutexte));
        }

        //largeur
        $width = $element['largeur'] ?: 300;
        $style = 'width: 80%; max-width:' . $width . 'px;';

        //styles
        $button_box_shadow_x = null;
        $button_box_shadow_y = null;
        $button_box_shadow_blur = null;
        $button_box_shadow_color = null;
        $button_box_shadow_opacity = 1;
        $color_class = '';

        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);

            if (isset($contenu['tip_bg_color']) and $contenu['tip_bg_color']) {
                if ('main_color0' == $contenu['tip_bg_color']) {
                    $color_class = 'btn_main_color0';
                } elseif ('main_color1' == $contenu['tip_bg_color']) {
                    $color_class = 'btn_main_color1';
                } elseif ('main_color2' == $contenu['tip_bg_color']) {
                    $color_class = 'btn_main_color2';
                } else {
                    if (isset($contenu['tip_bg_opacity']) and $contenu['tip_bg_opacity']) {
                        $style .= 'background:' . hex2rgba($contenu['tip_bg_color'], $contenu['tip_bg_opacity']) . ';';
                    } else {
                        $style .= 'background:' . $contenu['tip_bg_color'] . ';';
                    }
                }
            }

            if (isset($contenu['tip_border']) and $contenu['tip_border']) {
                $style .= 'border-width:' . $contenu['tip_border'] . 'px;';
                if (isset($contenu['tip_border_style']) and $contenu['tip_border_style']) {
                    $style .= 'border-style:' . $contenu['tip_border_style'] . ';';
                }
            }
            if (isset($contenu['tip_border_color']) and $contenu['tip_border_color']) {
                $tip_border_color_opacity = 1;
                if (isset($contenu['tip_border_color_opacity'])) {
                    $tip_border_color_opacity = $contenu['tip_border_color_opacity'];
                }

                $style .= 'border-color:' . hex2rgba($contenu['tip_border_color'], $tip_border_color_opacity) . ';';
            }

            if (isset($contenu['tip_border_radius_top_left']) and isset($contenu['tip_border_radius_top_right']) and isset($contenu['tip_border_radius_bottom_left']) and isset($contenu['tip_border_radius_bottom_right'])) {
                if ($contenu['tip_border_radius_top_left'] == $contenu['tip_border_radius_top_right'] and $contenu['tip_border_radius_top_left'] == $contenu['tip_border_radius_bottom_left'] and $contenu['tip_border_radius_top_left'] == $contenu['tip_border_radius_bottom_right']) {
                    $style .= 'border-radius: ' . $contenu['tip_border_radius_top_left'] . 'px; -moz-border-radius: ' . $contenu['tip_border_radius_top_left'] . 'px; -webkit-border-radius: ' . $contenu['tip_border_radius_top_left'] . 'px;' . "\n";
                } else {
                    $style .= 'border-top-left-radius: ' . $contenu['tip_border_radius_top_left'] . 'px; -moz-border-radius-topleft: ' . $contenu['tip_border_radius_top_left'] . 'px; -webkit-border-top-left-radius: ' . $contenu['tip_border_radius_top_left'] . 'px;' . "\n";
                    $style .= 'border-top-right-radius: ' . $contenu['tip_border_radius_top_right'] . 'px; -moz-border-radius-topright: ' . $contenu['tip_border_radius_top_right'] . 'px; -webkit-border-top-right-radius: ' . $contenu['tip_border_radius_top_right'] . 'px;' . "\n";
                    $style .= 'border-bottom-left-radius: ' . $contenu['tip_border_radius_bottom_left'] . 'px; -moz-border-radius-bottomleft: ' . $contenu['tip_border_radius_bottom_left'] . 'px; -webkit-border-bottom-left-radius: ' . $contenu['tip_border_radius_bottom_left'] . 'px;' . "\n";
                    $style .= 'border-bottom-right-radius: ' . $contenu['tip_border_radius_bottom_right'] . 'px; -moz-border-radius-bottomright: ' . $contenu['tip_border_radius_bottom_right'] . 'px; -webkit-border-bottom-right-radius: ' . $contenu['tip_border_radius_bottom_right'] . 'px;' . "\n";
                }
            } elseif (isset($contenu['tip_border_radius']) and $contenu['tip_border_radius']) {
                $style .= 'border-radius: ' . $contenu['tip_border_radius'] . 'px; -moz-border-radius: ' . $contenu['tip_border_radius'] . 'px; -webkit-border-radius: ' . $contenu['tip_border_radius'] . 'px;' . "\n";
            }

            if (isset($contenu['tip_button_box_shadow_x'])) {
                $button_box_shadow_x = $contenu['tip_button_box_shadow_x'];
            }
            if (isset($contenu['tip_button_box_shadow_y'])) {
                $button_box_shadow_y = $contenu['tip_button_box_shadow_y'];
            }
            if (isset($contenu['tip_button_box_shadow_blur'])) {
                $button_box_shadow_blur = $contenu['tip_button_box_shadow_blur'];
            }
            if (isset($contenu['tip_button_box_shadow_color'])) {
                $button_box_shadow_color = $contenu['tip_button_box_shadow_color'];
            }
            if (isset($contenu['tip_button_box_shadow_opacity'])) {
                $button_box_shadow_opacity = $contenu['tip_button_box_shadow_opacity'];
            }

            if (!$button_box_shadow_blur) {
                $button_box_shadow_blur = 0;
            }

            if ($button_box_shadow_color && null !== $button_box_shadow_x && null !== $button_box_shadow_y) {
                $style .= 'box-shadow: ' . $button_box_shadow_x . 'px ' . $button_box_shadow_y . 'px ' . $button_box_shadow_blur . 'px ' . hex2rgba($button_box_shadow_color, $button_box_shadow_opacity) . '; -webkit-box-shadow: ' . $button_box_shadow_x . 'px ' . $button_box_shadow_y . 'px ' . $button_box_shadow_blur . 'px ' . hex2rgba($button_box_shadow_color, $button_box_shadow_opacity) . '; -moz-box-shadow: ' . $button_box_shadow_x . 'px ' . $button_box_shadow_y . 'px ' . $button_box_shadow_blur . 'px ' . hex2rgba($button_box_shadow_color, $button_box_shadow_opacity) . ';';
            }

            if (isset($contenu['alignement'])) {
                if ($contenu['alignement'] === 'left' || $contenu['alignement'] === 'right') {
                    $style .= 'float: ' . $contenu['alignement'] . ';';
                    if ('' != trim($element['urls']) && 'right' === $contenu['alignement']) {
                        $style .= 'margin-right: 33px;';
                    }
                } else {
                    $style .= 'margin: 0 auto; display: block;';
                }
            }
        }

        if ('' != trim($element['urls'])) {
            $style .= 'margin-bottom: 33px;';
        }

        $output .= '
		    <div class="tipblock ' . $color_class . '" style="' . $style . '">
		        ' . $contenutexte;

        //Arrow placement
        $output .= '<div class="tip-arrow ';

        if ('' != trim($element['fleche'])) {
            $output .= $element['fleche'] . '" ';
        } else {
            $output .= '" style="display:none;"';
        }
        $output .= '></div>';

        if ('' != trim($element['urls'])) {
            $output .= '<div class="tip-avatar"><img src="' . $element['urls'] . '"></div>';
        }

        $output .= '</div>';

        return $output;
    }

    public function admin_display_element($element, $data_tunnel = [], $parent_span = 'span12')
    {
        $output = $this->afficher_element($data_tunnel, $element);

        return $output;
    }

    /********************************************************/
    /************************ UPDATE ************************/
    /********************************************************/

    public function validatepost_element($cleaned_post)
    {
        $validPost = true;
        $error = '';

        $contenutexte = filter_var($cleaned_post['description'], FILTER_UNSAFE_RAW);
        if (!$contenutexte) {
            $validPost = false;
            $error .= '<li>' . __('Veuillez entrer un texte') . '</li>';
        }

        return ['validPost' => $validPost, 'error' => $error];
    }

    public function update_element($cleaned_post, $original_element = [])
    {
        $array_update = [];

        $array_update['largeur'] = filter_var($cleaned_post['largeur'], FILTER_VALIDATE_INT);
        $array_update['contenutexte'] = filter_var($cleaned_post['description'], FILTER_UNSAFE_RAW);

        $fleche = '';
        if (isset($cleaned_post['fleche'])) {
            $fleche = filter_var($cleaned_post['fleche'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        $array_update['fleche'] = $fleche;

        $array_update['urls'] = '';
        if (isset($cleaned_post['avatar']) and $cleaned_post['avatar']) {
            $array_update['urls'] = filter_var($cleaned_post['avatar'], FILTER_VALIDATE_URL);
            if (!$array_update['urls']) {
                $array_update['urls'] = '';
            }
        }

        return $array_update;
    }

    public function update_element_design($cleaned_post, $original_element = [], $swatches = [])
    {
        $array_update = [];
        $array_update['contenu'] = [];

        $border = '';
        $border_color = '';
        $border_radius = '';
        $button_box_shadow_x = '';
        $button_box_shadow_y = '';
        $button_box_shadow_blur = '';
        $button_box_shadow_color = '';
        $alignement = 'center';
        $button_box_shadow_opacity = 1;

        //border
        if (isset($cleaned_post['border'])) {
            $border = filter_var($cleaned_post['border'], FILTER_VALIDATE_INT);
        }
        if (isset($cleaned_post['border_color']) and $cleaned_post['border_color']) {
            $border_color = filter_var($cleaned_post['border_color'], FILTER_SANITIZE_SPECIAL_CHARS);
        }
        if (isset($cleaned_post['border_radius'])) {
            $border_radius = filter_var($cleaned_post['border_radius'], FILTER_VALIDATE_INT);
        }

        //alignement
        if (isset($cleaned_post['alignement'])) {
            $alignement = filter_var($cleaned_post['alignement'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        $array_update['contenu']['border'] = $border;
        $array_update['contenu']['border_color'] = $border_color;
        $array_update['contenu']['border_radius'] = $border_radius;
        $array_update['contenu']['alignement'] = $alignement;

        return $array_update;
    }

    public function update_element_parametres($cleaned_post, $original_element = [], $swatches = [])
    {
        $array_update = [];

        $array_update['contenu'] = [];

        $bg_opacity = filter_var($cleaned_post['tip_bg_opacity'], FILTER_VALIDATE_INT) / 100;
        if (!$bg_opacity) {
            $bg_opacity = 1;
        }

        $array_update['contenu']['tip_bg_color'] = filter_var($cleaned_post['tip_bg_color'], FILTER_SANITIZE_SPECIAL_CHARS);
        $array_update['contenu']['tip_bg_opacity'] = $bg_opacity;
        $array_update['contenu']['tip_border'] = filter_var($cleaned_post['tip_border'], FILTER_VALIDATE_INT);
        $array_update['contenu']['tip_border_color'] = filter_var($cleaned_post['tip_border_color'], FILTER_SANITIZE_SPECIAL_CHARS);
        $array_update['contenu']['tip_border_radius_top_left'] = filter_var($cleaned_post['tip_border_radius_top_left'], FILTER_VALIDATE_INT);
        $array_update['contenu']['tip_border_radius_top_right'] = filter_var($cleaned_post['tip_border_radius_top_right'], FILTER_VALIDATE_INT);
        $array_update['contenu']['tip_border_radius_bottom_left'] = filter_var($cleaned_post['tip_border_radius_bottom_left'], FILTER_VALIDATE_INT);
        $array_update['contenu']['tip_border_radius_bottom_right'] = filter_var($cleaned_post['tip_border_radius_bottom_right'], FILTER_VALIDATE_INT);
        $array_update['contenu']['tip_border_color_opacity'] = filter_var($cleaned_post['tip_border_color_opacity'], FILTER_VALIDATE_INT) / 100;

        if (isset($cleaned_post['tip_border_style'])) {
            $array_update['contenu']['tip_border_style'] = filter_var($cleaned_post['tip_border_style'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        $array_update['contenu']['tip_button_box_shadow_x'] = filter_var($cleaned_post['tip_button_box_shadow_x'], FILTER_VALIDATE_INT);
        $array_update['contenu']['tip_button_box_shadow_y'] = filter_var($cleaned_post['tip_button_box_shadow_y'], FILTER_VALIDATE_INT);
        $array_update['contenu']['tip_button_box_shadow_blur'] = filter_var($cleaned_post['tip_button_box_shadow_blur'], FILTER_VALIDATE_INT);
        $array_update['contenu']['tip_button_box_shadow_color'] = filter_var($cleaned_post['tip_button_box_shadow_color'], FILTER_SANITIZE_SPECIAL_CHARS);
        $array_update['contenu']['tip_button_box_shadow_opacity'] = filter_var($cleaned_post['tip_button_box_shadow_opacity'], FILTER_VALIDATE_INT) / 100;

        return $array_update;
    }

    /********************************************************/
    /********************** FUNCTIONS ***********************/
    /********************************************************/

    public function getExamples()
    {
        return $this->examples;
    }
}
