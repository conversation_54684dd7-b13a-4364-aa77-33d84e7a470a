<?php

use Learnybox\Services\Icons\FontawesomeIconsService;

/**
 * Builder_Elements_Optin class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Elements_Optin extends \Learnybox\Services\Builder\BuilderElements\AbstractBuilderElements
{
    public function __construct()
    {
        $this->examples = [];
        $this->examples[] = [
            'elements' => [
                [
                    'display' => '<img src="' . \Learnybox\Helpers\Assets::getImageUrl('builder/examples/optin-1.png') . '">',
                    'update' => [
                        'contenu' => [
                            'template' => 'template6',
                            'font_color' => '#555555',
                            'font_color_checkbox' => '#555555',
                            'font_size' => 14,
                            'bg_type' => 'color',
                            'bg_color1' => 'main_color0',
                            "bg_color1Hover" => "main_color0",
                            "bg_color1Clicked" => "main_color0",
                            "color_text" => "#ffffff",
                            "color_textHover" => "#ffffff",
                            "color_textClicked" => "#ffffff",
                            "size_text" => 16,
                            "size_textHover" => 16,
                            "size_textClicked" => 16,
                            "border_radius" => 6,
                            "border_radiusHover" => 6,
                            "border_radiusClicked" => 6,
                            "size" => "btn-large",
                            "height" => "btn-h-n",
                            "border_color" => "#000000",
                            "border_colorHover" => "#000000",
                            "border_colorClicked" => "#000000",
                            "color_icone" => "#ffffff",
                            "color_iconeHover" => "#ffffff",
                            "color_iconeClicked" => "#ffffff",
                            'champs_bgcolor' => '#ffffff',
                            'champs_bgcolor_transparent' => 1
                        ],
                    ],
                ],
            ],
        ];
    }

    /********************************************************/
    /********************** AFFICHAGE ***********************/
    /********************************************************/

    public function afficher_element($data_tunnel, $element, bool $admin = false)
    {
        $output = '';
        $js = '';

        $template = 'template1';

        $contenu = [];
        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);
        }

        if (isset($contenu['template'])) {
            $template = $contenu['template'];
        }

        $in_popup = false;
        if (isset($data_tunnel['id_popup'])) {
            $in_popup = true;
        }

        $style = '';
        if (isset($contenu['font']) and $contenu['font']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' { font-family:' . eden()->Builder_Fonts()->getFontFamily($contenu['font']) . '; }
                #formcontainer' . $element['ID'] . ' .btn { font-family:' . eden()->Builder_Fonts()->getFontFamily($contenu['font']) . '; }
                #formcontainer' . $element['ID'] . ' input[type="text"],
                #formcontainer' . $element['ID'] . ' input[type="email"],
                #formcontainer' . $element['ID'] . ' input[type="password"],
                #formcontainer' . $element['ID'] . ' textarea {
                   font-family:' . eden()->Builder_Fonts()->getFontFamily($contenu['font']) . ';
                }
                #formcontainer' . $element['ID'] . ' ::-webkit-input-placeholder {
                   font-family:' . eden()->Builder_Fonts()->getFontFamily($contenu['font']) . ';
                }
                #formcontainer' . $element['ID'] . ' :-moz-placeholder {
                   font-family:' . eden()->Builder_Fonts()->getFontFamily($contenu['font']) . ';
                }
                #formcontainer' . $element['ID'] . ' ::-moz-placeholder {
                   font-family:' . eden()->Builder_Fonts()->getFontFamily($contenu['font']) . ';
                }
                #formcontainer' . $element['ID'] . ' :-ms-input-placeholder {
                   font-family:' . eden()->Builder_Fonts()->getFontFamily($contenu['font']) . ';
                }';
        }
        if (isset($contenu['font_size']) and $contenu['font_size']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' input[type="text"],
                #formcontainer' . $element['ID'] . ' input[type="email"],
                #formcontainer' . $element['ID'] . ' input[type="password"],
                #formcontainer' . $element['ID'] . ' textarea {
                   font-size:' . $contenu['font_size'] . 'px;
                }
                #formcontainer' . $element['ID'] . ' ::-webkit-input-placeholder {
                   font-size:' . $contenu['font_size'] . 'px;
                }
                #formcontainer' . $element['ID'] . ' :-moz-placeholder {
                   font-size:' . $contenu['font_size'] . 'px;
                }
                #formcontainer' . $element['ID'] . ' ::-moz-placeholder {
                   font-size:' . $contenu['font_size'] . 'px;
                }
                #formcontainer' . $element['ID'] . ' :-ms-input-placeholder {
                   font-size:' . $contenu['font_size'] . 'px;
                }';
        }

        if (isset($contenu['font_color']) and $contenu['font_color']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' ::-webkit-input-placeholder {
                   color:' . $contenu['font_color'] . ';
                }
                #formcontainer' . $element['ID'] . ' :-moz-placeholder {
                   color:' . $contenu['font_color'] . ';
                }
                #formcontainer' . $element['ID'] . ' ::-moz-placeholder {
                   color:' . $contenu['font_color'] . ';
                }
                #formcontainer' . $element['ID'] . ' :-ms-input-placeholder {
                   color:' . $contenu['font_color'] . ';
                }

                #formcontainer' . $element['ID'] . ' input[type="text"],
                #formcontainer' . $element['ID'] . ' input[type="email"],
                #formcontainer' . $element['ID'] . ' input[type="password"],
                #formcontainer' . $element['ID'] . ' textarea {
                    color:' . $contenu['font_color'] . ';
                }';
        }

        if (isset($contenu['font_color_checkbox']) and $contenu['font_color_checkbox']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' label {
                    color:' . $contenu['font_color_checkbox'] . ';
                }';
        }

        if (isset($contenu['champs_bgcolor_transparent']) and $contenu['champs_bgcolor_transparent']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' input[type="text"],
                #formcontainer' . $element['ID'] . ' input[type="email"],
                #formcontainer' . $element['ID'] . ' input[type="password"],
                #formcontainer' . $element['ID'] . ' textarea {
                    background:transparent;
                }';
        } elseif (isset($contenu['champs_bgcolor']) and $contenu['champs_bgcolor']) {
            $style .= '
                #formcontainer' . $element['ID'] . ' input[type="text"],
                #formcontainer' . $element['ID'] . ' input[type="email"],
                #formcontainer' . $element['ID'] . ' input[type="password"],
                #formcontainer' . $element['ID'] . ' textarea {
                    background:' . $contenu['champs_bgcolor'] . ';
                }';
        }

        if ($style) {
            $output .= '<style type="text/css">' . $style . '</style>';
        }

        if (isset($contenu['form']) and $contenu['form']) {
            $form = $this->makeForm($contenu, $element, $in_popup, $data_tunnel, $admin);
            if (!$form) {
                $output = '';
            } else {
                $output .= '
                    <div class="optin ' . $template . '">
                        <div class="content" id="formcontainer' . $element['ID'] . '">
                            ' . $form . '
                        </div>
                    </div>';

                if (!isset($data_tunnel['idformation'])) {
                    if (isset($data_tunnel['id_popup'])) {
                        $id_integration = 0;
                        if (isset($data_tunnel['id_integration'])) {
                            $id_integration = $data_tunnel['id_integration'];
                        }

                        $subscribe_action = 'PopupSubscribe(' . $data_tunnel['id_popup'] . ', ' . $id_integration . ', ' . $element['ID'] . ');';
                    } else {
                        $subscribe_action = 'Subscribe(' . $data_tunnel['id_page'] . ', ' . $element['ID'] . ');';
                    }

                    if (isset($data_tunnel['id_tunnel'])) {
                        $cpg = '';
                        $aff = '';
                        if (isset($_GET['cpg'])) {
                            $cpg = filter_input(INPUT_GET, 'cpg', FILTER_SANITIZE_SPECIAL_CHARS);
                        }
                        if (isset($_GET['aff'])) {
                            $aff = filter_input(INPUT_GET, 'aff', FILTER_SANITIZE_SPECIAL_CHARS);
                        }

                        $subscribe_action = 'Subscribe(' . $data_tunnel['id_tunnel'] . ', ' . $data_tunnel['id_page'] . ', ' . $element['ID'] . ', \'' . $cpg . '\', \'' . $aff . '\');';
                    }

                    $js .= '
                    <script type="text/javascript">
                    $(\'#formcontainer' . $element['ID'] . ' form\').submit(function(event) {
                        event.preventDefault();
                        ' . $subscribe_action . '
                        $(\'#formcontainer' . $element['ID'] . ' form\').unbind(\'submit\').submit();
                    });
                    </script>';
                }
            }
        }

        return ['output' => $output, 'js' => $js];
    }

    public function admin_display_element($element, $data_tunnel = [], $parent_span = 'span12')
    {
        $output = '';

        $template = 'template1';

        $contenu = [];
        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);
        }

        if (isset($contenu['template'])) {
            $template = $contenu['template'];
        }

        if (!isset($contenu['form']) or !$contenu['form']) {
            $output .= '<div class="alert alert-info">' . __('Formulaire de capture non configuré. Cliquez sur "Modifier" pour le configurer.') . '</div>';
        } else {
            $afficher_element = $this->afficher_element($data_tunnel, $element, true);
            if (!$afficher_element['output']) {
                $output .= '<div class="alert alert-info">' . __('Une erreur est survenue lors de la génération du formulaire.') . '</div>';
            } else {
                $output .= '
                    <div class="txt">
                        <div class="content ' . $template . '">
                            ' . $afficher_element['output'] . '
                        </div>
                    </div>';
            }
        }

        return $output;
    }

    /********************************************************/
    /************************ UPDATE ************************/
    /********************************************************/

    public function validatepost_element($cleaned_post)
    {
        $validPost = true;
        $error = '';

        if (isset($cleaned_post['id_formulaire']) and $cleaned_post['id_formulaire']) {
            $id_formulaire = filter_var($cleaned_post['id_formulaire'], FILTER_VALIDATE_INT);
            if ($id_formulaire) {
                return ['validPost' => $validPost, 'error' => $error];
            }
        }

        $action = $url_webm = $texte = '';
        if (isset($cleaned_post['action'])) {
            $action = filter_var($cleaned_post['action'], FILTER_VALIDATE_URL);
            if (!$action and '//' == substr($cleaned_post['action'], 0, 2)) {
                $action = 'https:' . $cleaned_post['action'];
                $action = filter_var($action, FILTER_VALIDATE_URL);
            }
        }
        if (isset($cleaned_post['url_webm'])) {
            $url_webm = filter_var($cleaned_post['url_webm'], FILTER_SANITIZE_SPECIAL_CHARS);
        }
        if (isset($cleaned_post['contenu'])) {
            $texte = filter_var($cleaned_post['contenu'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        if (!$action) {
            $validPost = false;
            $error .= '<li>' . __('Impossible de détecter la destination du formulaire 2') . '</li>';
        }
        if (!$texte and !$url_webm) {
            $validPost = false;
            $error .= '<li>' . __('Veuillez entrer le texte du bouton ou sélectionner une image') . '</li>';
        }

        $fields = false;
        foreach ($cleaned_post as $cp_name => $cp_value) {
            if (false !== strpos($cp_name, 'fields-')) {
                $fields = true;
                break;
            }
        }
        if (!$fields) {
            $validPost = false;
            $error .= '<li>' . __('Aucun champ détecté dans votre formulaire') . '</li>';
        }

        return ['validPost' => $validPost, 'error' => $error];
    }

    public function update_element($cleaned_post, $original_element = [])
    {
        $array_update = [];

        //formulaire LearnyMail
        if (isset($cleaned_post['id_formulaire']) and $cleaned_post['id_formulaire']) {
            $id_formulaire = filter_var($cleaned_post['id_formulaire'], FILTER_VALIDATE_INT);
            if ($id_formulaire) {
                $array_update['contenutexte'] = $id_formulaire;
                $array_update['objet'] = 'lbar_optin';

                return $array_update;
            }
        }

        //form
        $action = filter_var($cleaned_post['action'], FILTER_SANITIZE_SPECIAL_CHARS);
        $method = filter_var($cleaned_post['method'], FILTER_SANITIZE_SPECIAL_CHARS);

        //ajout de https si on ne l'a pas
        $action = filter_var($action, FILTER_VALIDATE_URL);
        if (!$action and '//' == substr($cleaned_post['action'], 0, 2)) {
            $action = 'https:' . $cleaned_post['action'];
            $action = filter_var($action, FILTER_VALIDATE_URL);
        }

        //transformation de l'url en https si possible
        if (!defined('CUSTOM_DOMAINE')) {
            $action = str_replace('http://', 'https://', $action);
        }

        $fields = [];

        foreach ($cleaned_post as $cp_name => $cp_value) {
            if (false !== strpos($cp_name, 'fields-')) {
                $explode = explode('-', $cp_name);
                $type = array_pop($explode);
                array_shift($explode); //pour enlever fields-
                $field_name = implode('-', $explode);
                $fields[$field_name][$type] = $cp_value;
            }
        }

        if ($fields) {
            foreach ($fields as $id => $field) {
                if (isset($field['active']) and $field['active'] == 1) {
                    $fields[$id]['active'] = true;
                } else {
                    $fields[$id]['active'] = false;
                }
            }
        }

        $form = [
            'action' => $action,
            'method' => $method,
            'fields' => $fields,
        ];

        //button
        $array_update['url_webm'] = '';
        if (isset($cleaned_post['url_webm'])) {
            $array_update['url_webm'] = filter_var($cleaned_post['url_webm'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        if ($array_update['url_webm']) {
            $largeur = filter_var($cleaned_post['largeur'], FILTER_VALIDATE_INT);
            $hauteur = filter_var($cleaned_post['hauteur'], FILTER_VALIDATE_INT);

            if (!$largeur and false !== strpos($array_update['url_webm'], SITE_URL)) {
                $full_url = WEB_PATH . '/' . str_replace(SITE_URL, '', $array_update['url_webm']);
                $full_url = urldecode($full_url);

                $getID3 = new getID3();
                $imageInfos = $getID3->analyze($full_url);
                if ($imageInfos and isset($imageInfos['video']['resolution_x']) and isset($imageInfos['video']['resolution_y'])) {
                    $largeur = $imageInfos['video']['resolution_x'];
                    $hauteur = $imageInfos['video']['resolution_y'];
                }
            }

            $array_update['largeur'] = $largeur;
            $array_update['hauteur'] = $hauteur;
        }

        $text = filter_var($cleaned_post['contenu'], FILTER_SANITIZE_SPECIAL_CHARS);
        $text = nl2br($text);

        $icone = filter_var($cleaned_post['icone'], FILTER_SANITIZE_SPECIAL_CHARS);
        $default_color_icone = filter_var($cleaned_post['icone_color'], FILTER_SANITIZE_SPECIAL_CHARS);

        //theme
        $font = filter_var($cleaned_post['font'], FILTER_SANITIZE_SPECIAL_CHARS);
        $font_size = filter_var($cleaned_post['font_size'], FILTER_VALIDATE_INT);
        $font_color = filter_var($cleaned_post['font_color'], FILTER_SANITIZE_SPECIAL_CHARS);
        $font_color_checkbox = filter_var($cleaned_post['font_color_checkbox'], FILTER_SANITIZE_SPECIAL_CHARS);
        $champs_bgcolor = filter_var($cleaned_post['champs_bgcolor'], FILTER_SANITIZE_SPECIAL_CHARS);

        $template = 'template1';
        if (isset($cleaned_post['template'])) {
            $template = filter_var($cleaned_post['template'], FILTER_SANITIZE_SPECIAL_CHARS);
        }

        $champs_bgcolor_transparent = false;
        if (isset($cleaned_post['champs_bgcolor_transparent']) && 1 == $cleaned_post['champs_bgcolor_transparent']) {
            $champs_bgcolor_transparent = true;
        }

        $array_update['contenu'] = [
            'form' => $form,
            'template' => $template,
            'font' => $font,
            'font_size' => $font_size,
            'font_color' => $font_color,
            'font_color_checkbox' => $font_color_checkbox,
            'champs_bgcolor' => $champs_bgcolor,
            'champs_bgcolor_transparent' => $champs_bgcolor_transparent,
            'icone' => $icone,
            'default_color_icone' => $default_color_icone,
            'contenu' => $text,
        ];

        return $array_update;
    }

    public function update_element_design($cleaned_post, $original_element = [], $swatches = [])
    {
        $array_update = eden()->Builder_Buttons()->update_button_design($cleaned_post, $original_element, $swatches);

        return $array_update;
    }

    public function update_element_parametres($cleaned_post, $original_element = [], $swatches = [])
    {
        $array_update = eden()->Builder_Buttons()->update_button_parametres($cleaned_post, $original_element, $swatches);

        return $array_update;
    }

    /********************************************************/
    /********************** FUNCTIONS ***********************/
    /********************************************************/

    public function getExamples()
    {
        return $this->examples;
    }

    public function makeForm($datas_form, $element, $in_popup = false, $data_tunnel = [], bool $admin = false)
    {
        if (!$datas_form) {
            return;
        }

        $datas_form = $this->verifForm($datas_form);
        if (!$datas_form) {
            return;
        }

        $form = '<form class="form" method="' . $datas_form['form']['method'] . '" action="' . $datas_form['form']['action'] . '" ' . ($in_popup ? 'target="_top"' : '') . '>' . "\n";
        $hidden_inputs = '';

        if (isset($datas_form['form']['hidden_fields']) and $datas_form['form']['hidden_fields']) {
            $form .= '<div style="display:none">' . "\n";
            foreach ($datas_form['form']['hidden_fields'] as $field_name => $field_value) {
                $form .= '<input type="hidden" name="' . $field_name . '" value="' . $field_value . '">' . "\n";
            }
            $form .= '</div>' . "\n";
        }

        array_sort($datas_form['form']['fields'], 'position', 'asc');

        foreach ($datas_form['form']['fields'] as $field) {
            if (!$field['active']) {
                continue;
            }

            $field['name'] = str_replace('||||||||', '][]', $field['name']);
            $field['name'] = str_replace('|||||', '[]', $field['name']);
            $field['name'] = str_replace('|||', ']', $field['name']);
            $field['name'] = str_replace('||', '[', $field['name']);

            $required = false;
            if (isset($field['required']) and $field['required']) {
                $required = true;
            }
            if (strtolower($field['type']) == 'email') {
                $required = true;
            }

            if ('email' == strtolower($field['type'])) {
                $email = '';

                if (isset($_GET['email'])) {
                    $email = filter_input(INPUT_GET, 'email', FILTER_VALIDATE_EMAIL);
                }

                if (isset($_SESSION['email']) and !$email) {
                    if (Learnybox\Services\RightsService::isUser()) {
                        $email = $_SESSION['email'];
                    }
                }

                if ($email) {
                    $field['value'] = $email;
                }
            }

            if ('name' == $field['type']) {
                $prenom = '';
                if (isset($_GET['prenom'])) {
                    $prenom = filter_input(INPUT_GET, 'prenom', FILTER_SANITIZE_SPECIAL_CHARS);
                }
                if (isset($_GET['name']) and !$prenom) {
                    $prenom = filter_input(INPUT_GET, 'name', FILTER_SANITIZE_SPECIAL_CHARS);
                }
                if (isset($_GET['fname']) and !$prenom) {
                    $prenom = filter_input(INPUT_GET, 'fname', FILTER_SANITIZE_SPECIAL_CHARS);
                }
                if (isset($_SESSION['name']) and !$prenom) {
                    if (Learnybox\Services\RightsService::isUser()) {
                        $prenom = $_SESSION['name'];
                    }
                }

                if ($prenom) {
                    $field['value'] = $prenom;
                }
                $field['type'] = 'text';
            }
            if ('parrain' == $field['name'] or false !== strpos($field['name'], 'affilie')) {
                if (isset($_GET['aff'])) {
                    $field['value'] = filter_input(INPUT_GET, 'aff', FILTER_SANITIZE_SPECIAL_CHARS);
                }
            }

            if (isset($field['get']) and $field['get'] and isset($_GET[$field['get']])) {
                $field['value'] = filter_input(INPUT_GET, $field['get'], FILTER_SANITIZE_SPECIAL_CHARS);
            }

            if ('hidden' == $field['type']) {
                if ('redirect' == $field['name'] or 'redirect_onlist' == $field['name']) {
                    if (isset($field['idpage']) and $field['idpage']) {
                        if (false !== strpos($field['idpage'], '-')) {
                            $explode = explode('-', $field['idpage']);
                            $id_tunnel = $explode[0];
                            $id_page = $explode[1];

                            if (is_numeric($id_tunnel) and is_numeric($id_page)) {
                                $tunnel_page = eden()->TunnelsPages()->getPageById($id_page, $id_tunnel);
                                if ($tunnel_page) {
                                    $field['value'] = Tools::getLink($tunnel_page['id_domaine'], 'site', $tunnel_page['permalink']);
                                }
                            }
                        } elseif ($redirection and is_numeric($redirection)) {
                            $page = eden()->Pages()->getPageById($redirection);
                            if ($page) {
                                $field['value'] = Tools::getLink($page['id_domaine'], 'site', $page['permalink']);
                            }
                        }
                    }
                }

                $hidden_inputs .= '<input type="hidden" name="' . $field['name'] . '" value="' . $field['value'] . '">';
            } elseif ('select' == $field['type']) {
                $options = [];
                if (isset($field['options'])) {
                    $options = base64_decode($field['options']);
                    $options = json_decode($options, true);
                }

                $form .= '
                <div class="form-group">
                    <div class="controls" style="text-align:center;">
                        ' . ($field['placeholder'] ? '<label>' . $field['placeholder'] . ($required ? ' *' : '') . '</label>' : '') . '
                        <select class="form-control input-xlarge" name="' . $field['name'] . '" ' . ($required ? 'required' : '') . ' style="margin: 0 auto">';
                if ($options) {
                    foreach ($options as $option) {
                        $form .= '<option value="' . $option . '"';
                        if ($option == $field['value']) {
                            $form .= ' selected';
                        }
                        $form .= '>' . $option . '</option>';
                    }
                }
                $form .= '
                        </select>
                    </div>
                </div>';
            } elseif ('textarea' == $field['type']) {
                $form .= '
                <div class="form-group">
                    <div class="controls" style="text-align:center">
                        <textarea class="form-control input-xlarge" name="' . $field['name'] . '" placeholder="' . ((isset($field['placeholder']) and $field['placeholder']) ? $field['placeholder'] : '') . '" rows="3" ' . ($required ? 'required' : '') . ' style="margin:0 auto">' . $field['value'] . '</textarea>
                    </div>
                </div>';
            } elseif ('checkbox' == $field['type']) {
                $text_checkbox = $field['value'];
                if (isset($field['placeholder']) and $field['placeholder']) {
                    $text_checkbox = $field['placeholder'];
                }

                $form .= '
                <div class="form-group">
                    <div class="controls">
                        <label for="' . $field['name'] . '">
                            <input type="checkbox" name="' . $field['name'] . '" id="' . $field['name'] . '" ' . ($required ? 'required' : '') . ' value="' . $field['value'] . '"> ' . $text_checkbox . '
                        </label>
                    </div>
                </div>';
            } else {
                $form .= '
                <div class="form-group">
                    <div class="controls" style="text-align:center">
                        <input type="' . $field['type'] . '" class="form-control input-xlarge" name="' . $field['name'] . '" placeholder="' . ((isset($field['placeholder']) and $field['placeholder']) ? $field['placeholder'] : $field['value']) . ($required ? ' *' : '') . '" value="' . $field['value'] . '" ' . ($required ? 'required' : '') . '>
                    </div>
                </div>';
            }
        }

        //button
        $align = 'center';
        $text = __('Valider');
        if (isset($datas_form['align']) and $datas_form['align']) {
            $align = $datas_form['align'];
        }
        if (isset($datas_form['contenu']) and $datas_form['contenu']) {
            $text = $datas_form['contenu'];
        }

        $element['contenutexte'] = $text;
        $get_button = eden()->Builder_Buttons()->displayButton('button', $element, 'btn_optin' . $element['ID'], '', $data_tunnel, $admin);

        if ($element['url_webm']) {
            $form .= '
                <div class="form_action" style="text-align:' . $align . ';">
                    ' . $get_button['output'] . '
                </div>';
        } else {
            if ($get_button['style']) {
                $form .= '<style type="text/css">' . $get_button['style'] . '</style>';
            }

            $form .= '
			    <div class="form_action" style="text-align:' . $align . ';">
			        ' . $get_button['output'] . '
                </div>';
        }

        if ($hidden_inputs) {
            $form .= '<div style="display:none">' . $hidden_inputs . '</div>';
        }

        $form .= '</form>';

        return $form;
    }

    public function verifForm($datas_form)
    {
        if (!isset($datas_form['form']['action']) or !$datas_form['form']['action']) {
            return false;
        }
        if (!isset($datas_form['form']['method']) or !$datas_form['form']['method']) {
            $datas_form['form']['method'] = 'post';
        }
        if (!isset($datas_form['form']['fields']) or !$datas_form['form']['fields']) {
            $datas_form['form']['fields'] = [];
            $datas_form['form']['fields'][] = [
                'type' => 'email',
                'name' => 'email',
                'value' => '',
                'placeholder' => __('Votre adresse email'),
                'position' => 0,
                'active' => 1,
            ];
        }
        if (!isset($datas_form['button']) or !$datas_form['button']) {
            $datas_form['button'] = [
                'align' => 'center',
                'color' => '',
                'color_text' => '',
                'size' => 'btn-large',
                'text' => __('Valider'),
                'image' => '',
            ];
        }

        return $datas_form;
    }

    /**
     * PrepareForm function.
     *
     * @param array $contenu
     * @param array $element
     * @param bool $in_javascript : si true, on ne renvoie pas un tableau
     * @param bool $in_page : si true, on met à jour la table pages_elements
     *
     * @return array
     */
    public function PrepareForm($contenu, $element = [], $in_javascript = false, $in_page = false, $in_formation = false, $in_popup = false)
    {
        //mise à jour du formulaire HTML dans contenutext
        if (isset($contenu['html_form']) and isset($element['ID']) and $element['ID']) {
            $table = DB_PREFIX . 'tunnels_pages_elements';
            if ($in_page) {
                $table = DB_PREFIX . 'pages_elements';
            }
            if ($in_formation) {
                $table = 'ptf_pages_elements';
            }
            if ($in_popup) {
                $table = DB_PREFIX . 'popups_elements';
            }

            $database = MySQL::getInstance();
            try {
                $database->updateRows($table, ['contenutexte' => htmlspecialchars($contenu['html_form'])], "ID='" . $element['ID'] . "'");
            } catch (Eden_Error $e) {
                return ['valid' => false, 'message' => __("Erreur lors de la mise à jour d'un élément.")];
            }
        }

        return ['valid' => true];


        $font = $font_color = $font_color_checkbox = '';
        $font_size = 18;
        $champs_bgcolor = '';
        $champs_bgcolor_transparent = false;
        $icone = '';
        $default_color_icone = '';
        $text = __('Valider');

        extract($contenu);

        if (!isset($contenu['template']) or !$contenu['template']) {
            $contenu['template'] = 'template1';
        }

        $output_champs = '';
        $output_button = '';
        $output_theme = '';
        $output_redirection = '';
        $output_champs_additionnels = '';

        $output_html = '<div class="tab-pane active" id="tab_html">';

        if (!isset($contenu['form']) or !$contenu['form']) {
            $output_html .= '
                <div class="form-group">
    				<label class="control-label">' . __('Sélectionnez un formulaire LearnyMail') . '</label>
    				<div class="controls">
                        <select name="id_formulaire" data-rel="select2">
                            ' . eden()->Lbar_Formulaires()->generateSelectFormulaire() . '
                        </select>
                    </div>
                </div>

                <div class="form-group">
                	<label class="control-label">' . __('Ou créez un nouveau formulaire LearnyMail') . '</label>
                	<div class="controls">
                        <button class="btn btn-default" onclick="ReplaceElement(' . $element['ID'] . ', \'lbar_optin\'); return false;">' . __('Créer un formulaire LearnyMail') . '</button>
                    </div>
                </div>

                <label class="control-label">' . __('Ou connectez-vous sur votre autorépondeur et récupérez le code HTML de votre formulaire. Copiez-le ci-dessous et cliquez sur le bouton "Analyser".') . '</label>';
        } else {
            $output_html .= '<label class="control-label">' . __('Connectez-vous sur votre autorépondeur et récupérez le code HTML de votre formulaire. Copiez-le ci-dessous et cliquez sur le bouton "Analyser".') . '</label>';
        }

        $output_html .= '
                <div class="control-group" style="margin:0">
    				<label for="html_form' . $element['ID'] . '">' . __('Code HTML') . ' *</label>
    			</div>
    			<div class="controls" style="margin-left:0">
    				<textarea class="form-control" id="html_form' . $element['ID'] . '" name="html_form" style="width:90%" rows="6">' . htmlspecialchars(htmlspecialchars_decode($element['contenutexte'])) . '</textarea>
    			</div>
    			<a id="BtnAnalyse" class="btn btn-primary" href="#" onclick="ParseForm(' . $element['ID'] . '); return false;">' . __('Analyser') . '</a>
            </div>';

        if (!isset($contenu['form']) or !$contenu['form']) {
            $output_champs = '<div class="tab-pane" id="tab_champs"><div class="alert alert-info">' . __('Non disponible.') . '<br>' . __('Cliquez sur "Formulaire HTML" et analysez votre formulaire HTML.') . '</div></div>';

            $output_button = '<div class="tab-pane" id="tab_button"><div class="alert alert-info">' . __('Non disponible.') . '<br>' . __('Cliquez sur "Formulaire HTML" et analysez votre formulaire HTML.') . '</div></div>';
            $output_theme = '<div class="tab-pane" id="tab_theme"><div class="alert alert-info">' . __('Non disponible.') . '<br>' . __('Cliquez sur "Formulaire HTML" et analysez votre formulaire HTML.') . '</div></div>';
            $output_redirection = '<div class="tab-pane" id="tab_redirection"><div class="alert alert-info">' . __('Non disponible.') . '<br>' . __('Cliquez sur "Formulaire HTML" et analysez votre formulaire HTML.') . '</div></div>';
            $output_champs_additionnels = '<div class="tab-pane" id="tab_champs_additionnels"><div class="alert alert-info">' . __('Non disponible.') . '<br>' . __('Cliquez sur "Formulaire HTML" et analysez votre formulaire HTML.') . '</div></div>';
        } else {
            //configuration des champs
            $output_champs .= '
            <div class="tab-pane" id="tab_champs">

                <input type="hidden" name="action" value="' . $contenu['form']['action'] . '">
                <input type="hidden" name="method" value="' . $contenu['form']['method'] . '">

                <h4>' . __('Champs du formulaire') . '</h4>

            	<div class="panel-group" id="accordion_champs" role="tablist" aria-multiselectable="true">';

            //prenom et email
            foreach ($contenu['form']['fields'] as $id => $field) {
                if ('name' != $field['type'] and 'email' != $field['type']) {
                    continue;
                }

                $title = $field['name'];
                if ('name' == $field['type']) {
                    $title = __('Prénom');
                }
                if ('email' == $field['type']) {
                    $title = __('Email');
                }

                $output_champs .= '
                	<div class="panel panel-default">
				        <div class="panel-heading" role="tab">
				            <a role="button" data-toggle="collapse" data-parent="#accordion_champs" href="#collapse' . $field['name'] . '">' . $title . '</a>
				        </div>

				        <div id="collapse' . $field['name'] . '" class="panel-collapse collapse" role="tabpanel">
							<div class="panel-body">';

                if ('name' == $field['type']) {
                    $output_champs .= '
    				    <input type="hidden" name="fields-prenom-type" value="name">
    					<input type="hidden" name="fields-prenom-value" value="">
    					<input type="hidden" name="fields-prenom-name" value="' . $field['name'] . '">

                        <div class="switch">
		                    <div class="switch-left">
		                        <label for="fields-prenom-active" class="control-label">' . __('Activer') . '</label>
		                    </div>
		                    <div class="switch-right">
		                        <input type="checkbox" class="ios-toggle ios-toggle-round ios-toggle-white ios-toggle-red" name="fields-prenom-active" id="fields-prenom-active" data-no-uniform="true" ' . ((!isset($field['active']) or (isset($field['active']) and $field['active'])) ? 'checked' : '') . '>
		                        <label for="fields-prenom-active"></label>
		                    </div>
		                </div>

                        <div class="form-group">
							<label class="control-label" for="fields-prenom-placeholder">' . __('Valeur par défaut') . '</label>
							<div class="controls">
								<input class="form-control input" id="fields-prenom-placeholder" name="fields-prenom-placeholder" type="text" value="' . ((isset($field['placeholder']) and $field['placeholder']) ? $field['placeholder'] : __('Votre prénom')) . '">
							</div>
						</div>

						<div class="form-group">
							<label class="control-label" for="fields-prenom-get">' . __('Pré-remplissage') . '</label>
							<div class="controls">
								<input class="form-control input" id="fields-prenom-get" name="fields-prenom-get" type="text" value="' . ((isset($field['get']) and $field['get']) ? $field['get'] : 'prenom') . '">
							</div>
						</div>

						<div class="form-group">
							<label class="control-label" for="fields-prenom-position">' . __('Position') . '</label>
							<div class="controls">
								<input class="form-control input-small" id="fields-prenom-position" name="fields-prenom-position" type="number" value="' . ((isset($field['position']) and $field['position']) ? $field['position'] : '0') . '">
							</div>
						</div>

						<div class="switch">
		                    <div class="switch-left">
		                        <label for="fields-prenom-required" class="control-label">' . __('Obligatoire') . '</label>
		                    </div>
		                    <div class="switch-right">
		                        <input type="checkbox" class="ios-toggle ios-toggle-round ios-toggle-white ios-toggle-red" name="fields-prenom-required" id="fields-prenom-required" data-no-uniform="true" ' . ((isset($field['required']) and $field['required']) ? 'checked' : '') . '>
		                        <label for="fields-prenom-required"></label>
		                    </div>
		                </div>';

                    unset($contenu['form']['fields'][$id]);
                }

                if ('email' == $field['type']) {
                    $output_champs .= '
    					<input type="hidden" name="fields-' . $field['name'] . '-type" value="email">
    					<input type="hidden" name="fields-' . $field['name'] . '-active" value="1">
    					<input type="hidden" name="fields-' . $field['name'] . '-value" value="">
    					<input type="hidden" name="fields-' . $field['name'] . '-name" value="' . $field['name'] . '">

                        <div class="form-group">
							<label class="control-label" for="fields-' . $field['name'] . '-placeholder">' . __('Valeur par défaut') . '</label>
							<div class="controls">
								<input class="form-control input" id="fields-' . $field['name'] . '-placeholder" name="fields-' . $field['name'] . '-placeholder" type="text" value="' . ((isset($field['placeholder']) and $field['placeholder']) ? $field['placeholder'] : __('Votre adresse email')) . '">
							</div>
						</div>

						<div class="form-group">
							<label class="control-label" for="fields-' . $field['name'] . '-get">' . __('Pré-remplissage') . '</label>
							<div class="controls">
								<input class="form-control input" id="fields-' . $field['name'] . '-get" name="fields-' . $field['name'] . '-get" type="text" value="' . ((isset($field['get']) and $field['get']) ? $field['get'] : 'email') . '">
							</div>
						</div>

						<div class="form-group">
							<label class="control-label" for="fields-' . $field['name'] . '-position">' . __('Position') . '</label>
							<div class="controls">
								<input class="form-control input-small" id="fields-' . $field['name'] . '-position" name="fields-' . $field['name'] . '-position" type="number" value="' . ((isset($field['position']) and $field['position']) ? $field['position'] : '1') . '">
							</div>
						</div>';

                    unset($contenu['form']['fields'][$id]);
                }

                $output_champs .= '
		                    </div>
	                    </div>
	                </div>';
            }

            $output_champs .= '
                </div>

                <div class="text-info" style="margin-top:20px">
                    ' . __('Le pré-remplissage sert à pré-remplir les différents champs de votre formulaire en passant des paramètres dans l\'adresse de votre page.') . '<br>' . __('Par exemple, si vous indiquez "prenom" dans ce champ, et qu\'un visiteur consulte votre page avec l\'adresse : %smonsite.com%sma-page%sprenom%s', 'http://', '/', '/?', '=') . '<strong>' . $_SESSION['name'] . '</strong>, ' . __('ce champ sera pré-rempli avec la valeur') . ' <strong>' . $_SESSION['name'] . '</strong>.
                </div>
            </div>';

            //configuration du bouton
            $output_button .= '
            	<div class="tab-pane" id="tab_button">

	            	<div class="form-group">
		                <label class="control-label" for="contenu">' . __('Texte du bouton') . '</label>
		                <div class="controls">
		                    <textarea class="form-control" name="contenu" id="contenu">' . str_replace('<br />', "\n", $text) . '</textarea>
		                </div>
		            </div>

		            <div class="form-group">
					    <div class="iconeSelector" id="container_icone" data-target="icone" data-icone="' . $icone . '" data-icone-color="' . $default_color_icone . '">
							<label class="control-label" for="icone">' . __('Icône') . '</label>
							<span class="iconeSelector-icone-container">
								<span class="iconeSelector-icone">' . FontawesomeIconsService::getIconHtml($icone ?: 'fa-ban', $default_color_icone) . '</span>
							</span>
							<input type="hidden" name="icone" id="icone" value="' . $icone . '">
							<input type="hidden" name="icone_color" id="icone_color" value="' . $default_color_icone . '">
					    </div>
					</div>

				</div>';

            //configuration du thème
            $fonts = [];
            $font_sizes = [];
            if (isset($page['design']) and $page['design']) {
                $design = json_decode($page['design'], true);
                if (isset($design['fonts']) and $design['fonts']) {
                    $fonts = $design['fonts'];
                }
            }

            $this->fonts = eden()->Builder_Fonts()->getDefaultFonts();

            $font_family = __('Par défaut');
            if (isset($this->fonts[$font])) {
                $font_family = $this->fonts[$font]['name'];
            }

            $output_theme .= '
        		<div class="tab-pane" id="tab_theme">

                    <div class="control-group">
        				<label class="control-label" for="template">' . __('Thème') . '</label>
        				<div class="controls">
        					<label class="radio-inline radio-check">
        						<input type="radio" data-no-uniform="true" name="template" id="template_1" value="template1" ' . (($contenu['template'] and 'template1' == $contenu['template']) ? 'checked' : '') . '><label class="radio-check-icon"></label><img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template1.png') . '">
        					</label>
        					<br>
        					<label class="radio-inline radio-check">
        						<input type="radio" data-no-uniform="true" name="template" id="template_2" value="template2" ' . (($contenu['template'] and 'template2' == $contenu['template']) ? 'checked' : '') . '><label class="radio-check-icon"></label><img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template2.png') . '">
        					</label>
        					<br>
        					<label class="radio-inline radio-check">
        						<input type="radio" data-no-uniform="true" name="template" id="template_3" value="template3" ' . (($contenu['template'] and 'template3' == $contenu['template']) ? 'checked' : '') . '><label class="radio-check-icon"></label><img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template3.png') . '">
        					</label>
        					<br>
        					<label class="radio-inline radio-check">
        						<input type="radio" data-no-uniform="true" name="template" id="template_4" value="template4" ' . (($contenu['template'] and 'template4' == $contenu['template']) ? 'checked' : '') . '><label class="radio-check-icon"></label><img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template4.png') . '">
        					</label>
        					<br>
        					<label class="radio-inline radio-check">
        						<input type="radio" data-no-uniform="true" name="template" id="template_5" value="template5" ' . (($contenu['template'] and 'template5' == $contenu['template']) ? 'checked' : '') . '><label class="radio-check-icon"></label><img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template5.png') . '">
        					</label>
        					<br>
        					<label class="radio-inline radio-check">
        						<input type="radio" data-no-uniform="true" name="template" id="template_6" value="template6" ' . (($contenu['template'] and 'template6' == $contenu['template']) ? 'checked' : '') . '><label class="radio-check-icon"></label><img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template6.png') . '">
        					</label>
        					<br>
        					<label class="radio-inline radio-check">
        						<input type="radio" data-no-uniform="true" name="template" id="template_8" value="template8" ' . (($contenu['template'] and 'template8' == $contenu['template']) ? 'checked' : '') . '><label class="radio-check-icon"></label><img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/optin/template8.png') . '">
        					</label>
        				</div>
        			</div>

                    <hr>

                    <div class="select_font" style="margin-top: 0;">
		                <label class="control-label">' . __('Polices') . '</label>
		                <input type="hidden" id="font" name="font" value="' . $font . '">
		                <a class="btn-show-fonts" data-font="' . $font . '">' . $font_family . '</a>
		            </div>
		            <div class="fonts">';

            if ($fonts) {
                foreach ($fonts as $font) {
                    $output_theme .= '<a class="btn-change-font" data-font="' . $font . '" style="font-family:' . $font . '">' . $font . '</a>';
                }
            }

            foreach ($this->fonts as $font => $datas) {
                $output_theme .= '<a class="btn-change-font" data-font="' . $font . '" style="font-family:' . $datas['family'] . '">' . $datas['name'] . '</a>';
            }

            $output_theme .= '
		        	</div>
                    <div class="form-group hidden-phone">
		        		<label class="control-label" for="font_size">' . __('Taille de la police') . '</label>
		        		<div class="container-range">
				    		<input id="font_size" name="font_size" type="range" value="' . ($font_size ?: '0') . '" step="1" min="0" max="100">
				    		<span class="range-number"><input id="input_font_size" type="number" value="' . $font_size . '"></span>
		                </div>
		    		</div>

                    <div class="form-group">
					    <div class="colorSelector" id="container_font_color" data-target="font_color" data-color="' . $font_color . '">
							<label class="control-label" for="font_color">' . __('Couleur du texte') . '</label>
							<span class="colorSelector-color-container">
								<span class="colorSelector-color" ' . ($font_color ? 'style="background:' . $font_color . '"' : '') . '></span>
							</span>
							<input type="hidden" name="font_color" id="font_color" value="' . $font_color . '">
					    </div>
					</div>

					<div class="form-group">
					    <div class="colorSelector" id="container_font_color_checkbox" data-target="font_color_checkbox" data-color="' . $font_color_checkbox . '">
							<label class="control-label" for="font_color_checkbox">' . __('Couleur du texte (cases à cocher)') . '</label>
							<span class="colorSelector-color-container">
								<span class="colorSelector-color" ' . ($font_color_checkbox ? 'style="background:' . $font_color_checkbox . '"' : '') . '></span>
							</span>
							<input type="hidden" name="font_color_checkbox" id="font_color_checkbox" value="' . $font_color_checkbox . '">
					    </div>
					</div>

                    <div class="form-group">
					    <div class="colorSelector" id="container_champs_bgcolor" data-target="champs_bgcolor" data-color="' . $champs_bgcolor . '">
							<label class="control-label" for="champs_bgcolor">' . __('Couleur de fond des champs') . '</label>
							<span class="colorSelector-color-container">
								<span class="colorSelector-color" ' . ($champs_bgcolor ? 'style="background:' . $champs_bgcolor . '"' : '') . '></span>
							</span>
							<input type="hidden" name="champs_bgcolor" id="champs_bgcolor" value="' . $champs_bgcolor . '">
					    </div>
					</div>

                    <div class="form-group">
						<div class="switch">
			                <div class="switch-left">
			                	<label class="control-label" for="champs_bgcolor_transparent">' . __('Fond des champs transparent') . '</label>
			                </div>
			                <div class="switch-right">
			                    <input type="checkbox" name="champs_bgcolor_transparent" id="champs_bgcolor_transparent" class="ios-toggle ios-toggle-round ios-toggle-white ios-toggle-red" ' . ($champs_bgcolor_transparent ? 'checked' : '') . ' data-no-uniform="true">
			                    <label for="champs_bgcolor_transparent"></label>
			                </div>
		                </div>
					</div>

                </div>';

            //configuration de la redirection
            $output_redirection .= '<div class="tab-pane" id="tab_redirection">';

            if (false !== strpos($contenu['form']['action'], 'aweber.com') or false !== strpos($contenu['form']['action'], 'sg-autorepondeur.com')) {
                $redirect = false;
                $redirect_onlist = false;

                if ($contenu['form']['fields']) {
                    foreach ($contenu['form']['fields'] as $field) {
                        if ('redirect' != $field['name'] and 'redirect_onlist' != $field['name']) {
                            continue;
                        }
                        if ('redirect' == $field['name']) {
                            $redirect = true;
                        }
                        if ('redirect_onlist' == $field['name']) {
                            $redirect_onlist = true;
                        }
                    }
                }

                if (!$redirect) {
                    $contenu['form']['fields'][] = ['name' => 'redirect', 'active' => true, 'value' => ''];
                }
                if (!$redirect_onlist) {
                    $contenu['form']['fields'][] = ['name' => 'redirect_onlist', 'active' => true, 'value' => ''];
                }

                foreach ($contenu['form']['fields'] as $field) {
                    if ('redirect' != $field['name'] and 'redirect_onlist' != $field['name']) {
                        continue;
                    }

                    if (isset($field['idpage']) and $field['idpage']) {
                        $selectPage = eden()->TunnelsPages()->generateSelectTunnelsPages($field['idpage']);
                    } else {
                        $selectPage = eden()->TunnelsPages()->generateSelectTunnelsPages();
                    }

                    if ('redirect_onlist' == $field['name']) {
                        $output_redirection .= '
                            <h4 style="margin-bottom:0">' . __('Redirection après inscription') . '</h4>
                            <span class="help-block">' . __('Dans le cas où le visiteur est déjà inscrit dans votre liste.') . '</span>';
                    } else {
                        $output_redirection .= '<h4>' . __('Redirection après inscription') . '</h4>';
                    }

                    $output_redirection .= '
            			<div class="form-group">
            				<label class="control-label">' . __('Activer') . '</label>
            				<div class="controls">
            					<input type="hidden" name="fields-' . $field['name'] . '-position" value="999">
                                <input type="hidden" name="fields-' . $field['name'] . '-placeholder" value="">
                                <input type="hidden" name="fields-' . $field['name'] . '-type" value="hidden">
            					<input type="hidden" name="fields-' . $field['name'] . '-name" value="' . $field['name'] . '">
            					<input type="checkbox" name="fields-' . $field['name'] . '-active" ' . ((!isset($field['active']) or (isset($field['active']) and $field['active'])) ? 'checked' : '') . '> ' . __('Activer') . '
            				</div>
                        </div>

            			<div class="form-group">
            				<label class="control-label">' . __('Sélectionnez une page...') . '</label>
            				<div class="controls">
            					<select name="fields-' . $field['name'] . '-idpage" data-rel="select2">
            					    ' . $selectPage . '
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
            				<label class="control-label">' . __('...ou entrez une adresse') . '</label>
            				<div class="controls">
                                <input class="form-control input-xxlarge" type="text" name="fields-' . $field['name'] . '-value" value="' . ((isset($field['value']) and $field['value']) ? $field['value'] : '') . '">
            				</div>
                        </div>
                        <hr>';
                }
            } else {
                $output_redirection .= '
                    <h4>' . __('Redirection après inscription') . '</h4>
                    <div class="alert alert-info">' . __('Votre autorépondeur ne prend pas en charge la modification de l\'adresse de redirection dans le formulaire.') . '<br>' . __('La configuration de la redirection après inscription se fait donc directement dans votre autorépondeur.') . '</div>';
            }

            $output_redirection .= '</div>';

            //configuration des champs additionnels
            $output_champs_additionnels .= '
                <div class="tab-pane" id="tab_champs_additionnels">
                    <h4>' . __('Champs additionnels') . '</h4>';

            $i = 2;
            if (!$contenu['form']['fields']) {
                $output_champs_additionnels .= '<div class="alert alert-info">' . __('Aucun champ additionnel trouvé dans votre formulaire') . '</div>';
            } else {
                $output_champs_additionnels .= '<div class="panel-group" id="accordion_champs_additionnels" role="tablist" aria-multiselectable="true">';

                $id_field = 0;
                foreach ($contenu['form']['fields'] as $field) {
                    if ('redirect' == $field['name'] or 'redirect_onlist' == $field['name']) {
                        continue;
                    }

                    $id_field++;

                    $selectType = $this->generateSelectFieldType($field['type']);

                    $field_name = str_replace('||||||||', '][]', $field['name']);
                    $field_name = str_replace('|||||', '[]', $field_name);
                    $field_name = str_replace('|||', ']', $field_name);
                    $field_name = str_replace('||', '[', $field_name);

                    $options = '';
                    if ('select' == $field['type']) {
                        if (isset($field['options'])) {
                            $options = $field['options'];
                        } else {
                            $options = $field['placeholder'];
                            $field['placeholder'] = '';
                        }
                        if (is_array($options)) {
                            $options = base64_encode(json_encode($options));
                        }
                    }

                    $field_accordion_id = str_replace(['[', ']', '(', ')', ' '], '', $field_name);
                    $field_id = $id_field . $field['name'];

                    $output_champs_additionnels .= '
                	<div class="panel panel-default">
				        <div class="panel-heading" role="tab">
				            <a role="button" data-toggle="collapse" data-parent="#accordion_champs_additionnels" href="#collapse' . $field_accordion_id . '">' . $field_accordion_id . '</a>
				        </div>

				        <div id="collapse' . $field_accordion_id . '" class="panel-collapse collapse" role="tabpanel">
							<div class="panel-body">

		    				    <input type="hidden" name="fields-' . $field_id . '-name" value="' . $field['name'] . '">
								' . ($options ? '<input type="hidden" name="fields-' . $field_id . '-options" value="' . $options . '">' : '') . '

		                        <div class="switch">
				                    <div class="switch-left">
				                        <label for="fields-' . $field['name'] . '-active" class="control-label">' . __('Activer') . '</label>
				                    </div>
				                    <div class="switch-right">
				                        <input type="checkbox" class="ios-toggle ios-toggle-round ios-toggle-white ios-toggle-red" name="fields-' . $field_id . '-active" id="fields-' . $field['name'] . '-active" data-no-uniform="true" ' . ((!isset($field['active']) or (isset($field['active']) and $field['active'])) ? 'checked' : '') . '>
				                        <label for="fields-' . $field['name'] . '-active"></label>
				                    </div>
				                </div>

				                <div class="form-group">
									<label class="control-label" for="fields-' . $field['name'] . '-type">' . __('Type') . '</label>
									<div class="controls">
										<select name="fields-' . $field_id . '-type">
		            						' . $selectType . '
		            					</select>
									</div>
								</div>

		                        <div class="form-group">
									<label class="control-label" for="fields-' . $field['name'] . '-value">' . __('Valeur') . '</label>
									<div class="controls">
										<input class="form-control input" id="fields-' . $field['name'] . '-value" name="fields-' . $field_id . '-value" type="text" value="' . (isset($field['value']) ? $field['value'] : '') . '">
									</div>
								</div>

								<div class="form-group">
									<label class="control-label" for="fields-' . $field['name'] . '-placeholder">' . __('Valeur par défaut') . '</label>
									<div class="controls">
										<input class="form-control input" id="fields-' . $field['name'] . '-placeholder" name="fields-' . $field_id . '-placeholder" type="text" value="' . (isset($field['placeholder']) ? $field['placeholder'] : '') . '">
									</div>
								</div>

								<div class="form-group">
									<label class="control-label" for="fields-' . $field['name'] . '-get">' . __('Pré-remplissage') . '</label>
									<div class="controls">
										<input class="form-control input" id="fields-' . $field['name'] . '-get" name="fields-' . $field_id . '-get" type="text" value="' . (isset($field['get']) ? $field['get'] : '') . '">
									</div>
								</div>

								<div class="form-group">
									<label class="control-label" for="fields-' . $field['name'] . '-position">' . __('Position') . '</label>
									<div class="controls">
										<input class="form-control input-small" id="fields-' . $field['name'] . '-position" name="fields-' . $field_id . '-position" type="number" value="' . ((isset($field['position']) and $field['position']) ? $field['position'] : $i) . '">
									</div>
								</div>

								<div class="switch">
				                    <div class="switch-left">
				                        <label for="fields-' . $field['name'] . '-required" class="control-label">' . __('Obligatoire') . '</label>
				                    </div>
				                    <div class="switch-right">
				                        <input type="checkbox" class="ios-toggle ios-toggle-round ios-toggle-white ios-toggle-red" name="fields-' . $field_id . '-required" id="fields-' . $field['name'] . '-required" data-no-uniform="true" ' . ((isset($field['required']) and $field['required']) ? 'checked' : '') . '>
				                        <label for="fields-' . $field['name'] . '-required"></label>
				                    </div>
				                </div>

							</div>
						</div>
					</div>';

                    ++$i;
                }

                $output_champs_additionnels .= '</div>';
            }

            $output_champs_additionnels .= '</div>';
        }

        if (!$in_javascript) {
            return $output_html . $output_champs . $output_button . $output_theme . $output_redirection . $output_champs_additionnels;
        }

        return [
            'output_html' => $output_html,
            'output_champs' => $output_champs,
            'output_button' => $output_button,
            'output_theme' => $output_theme,
            'output_redirection' => $output_redirection,
            'output_champs_additionnels' => $output_champs_additionnels,
        ];
    }

    public function generateSelectFieldType($selected_type = '')
    {
        $types = [
            'text' => __('Champ texte'),
            'textarea' => __('Champ texte long'),
            'number' => __('Nombre'),
            'hidden' => __('Champ caché'),
            'select' => __('Liste déroulante'),
            'checkbox' => __('Case à cocher'),
        ];

        $output = '';
        foreach ($types as $type => $nom) {
            $output .= '<option value="' . $type . '"';
            if ($selected_type == $type) {
                $output .= ' selected';
            }
            $output .= '>';

            $output .= $nom . '</option>';
        }

        return $output;
    }
}
