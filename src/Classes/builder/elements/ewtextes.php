<?php

/**
 * Builder_Elements_Ewtextes class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Builder_Elements_Ewtextes extends \Learnybox\Services\Builder\BuilderElements\AbstractBuilderElements
{
    public function __construct()
    {
    }

    /********************************************************/
    /********************** AFFICHAGE ***********************/
    /********************************************************/

    public function afficher_element($data_tunnel, $element)
    {
        $output = '';

        if (!isset($data_tunnel['webinaire']) or !$data_tunnel['webinaire']) {
            return;
        }

        $output .= '
        <div class="hidden" id="texte" style="margin-top:20px">
			<div class="box-content" id="textehtml"></div>
		</div>';

        return $output;
    }

    public function admin_display_element($element, $data_tunnel = [], $parent_span = 'span12')
    {
        $output = '';

        $is_in_webinaire = false;

        if (isset($data_tunnel['webinaire']) and $data_tunnel['webinaire']) {
            $is_in_webinaire = true;
        } elseif (isset($data_tunnel['tunnel']['id_webinaire']) and $data_tunnel['tunnel']['id_webinaire']) {
            $is_in_webinaire = true;
        } else {
            $page = eden()->TunnelsPages()->getPageById($element['id_page']);
            if ($page and $page['id_webinaire']) {
                $is_in_webinaire = true;
            }
        }

        if (!$is_in_webinaire) {
            return;
        }

        $output .= '
        <div class="txt">
            <div class="content">
                <div class="texte">
                    <div class="alert alert-info">
                        <h4>' . __('Notifications en direct') . '</h4>
                        ' . __('Lorsque vous publierez des notifications en direct, elles apparaîtront ici.') . '
                    </div>
                </div>
            </div>
        </div>';

        return $output;
    }

    /********************************************************/
    /************************ UPDATE ************************/
    /********************************************************/

    public function validatepost_element($cleaned_post)
    {
        $validPost = true;
        $error = '';

        return ['validPost' => $validPost, 'error' => $error];
    }

    public function update_element($cleaned_post, $original_element = [])
    {
        $array_update = [];

        return $array_update;
    }

    /********************************************************/
    /********************** FUNCTIONS ***********************/
    /********************************************************/
}
