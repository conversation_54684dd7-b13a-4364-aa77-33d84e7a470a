<?php

namespace Learnybox\Classes\evaluations;

use Learnybox\Repositories\Evaluation\EvaluationQuestionRepository;

class EvaluationQuestion
{
    protected EvaluationQuestionRepository $evaluationQuestionRepository;

    public function __construct(
        EvaluationQuestionRepository $evaluationQuestionRepository
    ) {
        $this->evaluationQuestionRepository = $evaluationQuestionRepository;
    }

    public function updateQuestionsPosition(int $idEvaluation, int $idPage, array $questions): array
    {
        $position = 0;
        foreach ($questions as $idQuestion) {
            try {
                $this->evaluationQuestionRepository->update(['position' => $position], "id_question='$idQuestion' AND id_evaluation='$idEvaluation' AND id_page='$idPage'");
            } catch (\Eden_Error $e) {
                return ["valid" => false, "message" => __("Erreur lors de la mise à jour de la question.")];
            }
            $position++;
        }

        return ["valid" => true];
    }
}
