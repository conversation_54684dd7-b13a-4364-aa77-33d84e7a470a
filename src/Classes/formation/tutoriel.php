<?php

use Learnybox\Entity\Formation\Formation;
use Learnybox\Services\Integration\IntegrationsEventsService;
use Learnybox\Services\Formations\FormationsService;

class Formation_Tutoriel
{
    private $database;
    private $integrationsEventsService;
    private $formationsService;

    public function __construct(
        IntegrationsEventsService $integrationsEventsService,
        FormationsService $formationsService
    ) {
        $this->database = MySQL::getInstance();
        $this->integrationsEventsService = $integrationsEventsService;
        $this->formationsService = $formationsService;
    }

    public function createFormation()
    {
        $nomformation = '';
        $master_theme = 0;
        $specific_theme = 0;
        $theme = '';
        $methodologie = '';
        $logo = '';
        $nb_modules = 0;
        $nb_pages = 0;
        $elements = [];

        if (isset($_SESSION['ftutoriel']) and $_SESSION['ftutoriel']) {
            foreach ($_SESSION['ftutoriel'] as $session_name => $session_value) {
                ${$session_name} = $session_value;
            }
        }

        if (!$methodologie) {
            $methodologie = 'program';
        }
        if (!$nomformation) {
            $nomformation = __('Ma première formation');
        }
        if (!$logo) {
            $logo = \Learnybox\Helpers\Assets::getImageUrl('tunnels/logo-exemple.png');
        }
        if (!$elements) {
            $elements = ['headline', 'videoplayer', 'txt', 'downloads', 'comments'];
        }

        //Méthodologie
        $authorized_methodologies = ['program', 'selfprogram', 'freeaccess_total', 'freeaccess', 'vip'];
        if (!$methodologie or !in_array($methodologie, $authorized_methodologies)) {
            $methodologie = 'program';
        }

        $id_theme = 0;
        if (!$theme) {
            $theme = 'focuson';
        }

        if ($theme == 'focuson') {
            if (!$master_theme) {
                $master_theme = 1;
            }
            if (!$specific_theme) {
                $specific_theme = 1;
            }

            $getTheme = eden()->Builder_Themes()->getThemeByMasterTheme($master_theme, $specific_theme);
            if (!$getTheme) {
                return ['valid' => false, 'message' => __("Erreur : le thème sélectionné n'existe pas.")];
            }
            $id_theme = $getTheme['id_theme'];
        }

        //verif uniqid
        try {
            $uniqid = $this->formationsService->getValidPermalink(permalink($nomformation));
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la génération du lien unique.')];
        }


        //creation du random_id
        $random_id = createRandomID();

        //template email de bienvenue
        $template_email_bienvenue = eden()->Formation_Formation()->getEmailBienvenue();
        $sujet_bienvenue = $template_email_bienvenue['sujet_bienvenue'];
        $email_bienvenue = $template_email_bienvenue['email_bienvenue'];

        $array_insert = [
            'id_client' => $_SESSION['id_client'],
            'nomformation' => $nomformation,
            'uniqid' => $uniqid,
            'etat' => 'enable',
            'date_start' => '',
            'nb_learning_days' => 0,
            'type' => $methodologie,
            'logo' => $logo,
            'banniere' => '',
            'sommaire_all_elements' => 1,
            'id_master_theme' => $master_theme,
            'id_theme' => $id_theme,
            'theme' => $theme,
            'allow_forum' => 0,
            'garantie' => 0,
            'garantie_elements' => '',
            'faq_categories' => 'all',
            'pages_menu' => json_encode(Formation::PAGES_MENU),
            'sujet_bienvenue' => $sujet_bienvenue,
            'email_bienvenue' => $email_bienvenue,
            'random_id' => $random_id,
            'langue' => \Learnybox\Helpers\I18nHelper::getLang(),
            'datecreation' => date('Y-m-d H:i:s'),
        ];

        $version = 0;
        if ('focuson' == $theme) {
            $array_insert['version'] = 2;
            $version = 2;
        }

        if ('selfprogram' == $methodologie) {
            $array_insert['nb_learning_days'] = 3;
        }

        //insertion
        try {
            $this->database->insertRow('ptf_formations', $array_insert);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de la formation.")];
        }

        $idformation = $this->database->getLastInsertedId();
        if (!$idformation) {
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de la formation.")];
        }

        //ajout de la formation aux administrateurs
        $admins = eden()->Utilisateurs()->getAllSuperUsers();
        if ($admins) {
            foreach ($admins as $_admin) {
                // Do not add formation if coach has formation limits
                $formations = eden()->UserConfig()->getUserConfigByName('coach_formations', $_admin['user_id']);
                if ($formations) {
                    continue;
                }

                //addMembre vérifie et enregistre le membre si besoin
                $addMembre = eden()->Formation_Membres()->addMembre($_admin['user_id']);
                if ($addMembre) {
                    $addFormation = eden()->Formation_Membres()->addFormationToMembre($idformation, $_admin['user_id']);
                    if (!$addFormation['valid']) {
                        return ['valid' => false, 'message' => $addFormation['message']];
                    }
                }
            }
        }

        //menu type 3 pour les nouvelles formations
        if ($version == 2) {
            $theme = eden()->Formation_Themes()->getThemeByName($theme);
            if ($theme) {
                $array_insert_theme = [
                    'id_client' => $_SESSION['id_client'],
                    'idformation' => $idformation,
                    'id_theme' => $theme['id_theme'],
                    'design' => json_encode(['type_menu' => 3]),
                    'menu' => 3,
                    'date' => date('Y-m-d H:i:s'),
                ];

                try {
                    $this->database->insertRow('ptf_themes_configuration', $array_insert_theme);
                } catch (Eden_Error $e) {
                    return ["valid" => false, "message" => __("Erreur lors de l'enregistrement de la personnalisation du thème.")];
                }
            }
        }

        //création du menu "Accueil"
        $menu = [
            'idformation' => $idformation,
            'displayName' => __('Accueil'),
            'type' => 'fpage',
            'element' => 'index',
            'icone' => 'fa-home',
            'active' => 'oui',
            'parent' => 0,
            'param' => '',
        ];
        $insert = eden()->Formation_Menu()->insert_menu($menu);
        if (!$insert['valid']) {
            return ['valid' => false, 'message' => $insert['message']];
        }

        //création du menu de la formation
        $menu = [
            'idformation' => $idformation,
            'displayName' => __('Menu de ma formation'),
            'type' => 'formation',
            'active' => 'oui',
            'displayType' => 'arborescence',
            'modules_all' => true,
        ];
        $insert = eden()->Formation_Menu()->insert_menu($menu);
        if (!$insert['valid']) {
            return ['valid' => false, 'message' => $insert['message']];
        }

        //création du menu "Aide"
        $menu = [
            'idformation' => $idformation,
            'displayName' => __('Aide'),
            'type' => 'fpage',
            'element' => 'aide',
            'icone' => 'fa-question-circle',
            'active' => 'oui',
            'parent' => 0,
            'param' => '',
        ];
        $insert = eden()->Formation_Menu()->insert_menu($menu);
        if (!$insert['valid']) {
            return ['valid' => false, 'message' => $insert['message']];
        }

        //set in session
        $formation = eden()->Formation_Formation()->getFormationById($idformation);
        if (!$formation) {
            return ['valid' => false, 'message' => __('Une erreur est survenue lors de la création de la formation')];
        }

        $_SESSION['idformation'] = $idformation;
        $_SESSION['formation'] = $formation;

        //création des pages et modules

        //insertion des modules
        $array_modules = [];

        $array_modules[0] = [
            'idpage' => 0,
            'id_client' => $_SESSION['id_client'],
            'titre' => __('Introduction'),
            'display_title' => 1,
            'hide_suite' => 0,
            'allow_votes' => 1,
            'position' => 0,
            'elements' => [],
        ];

        $array_modules[0]['elements'][] = [
            'ID' => 0,
            'objet' => 'txt',
            'span' => 'span12',
            'contenutexte' => '<h3 style="text-align:center; font-size:36px;">' . __('Bienvenue dans la formation') . '<br>' . $formation['nomformation'] . '</h3>'
        ];
        $array_modules[0]['elements'][] = [
            'ID' => 0,
            'objet' => 'videoplayer',
            'span' => 'span12',
            'urls' => 'https://www.youtube.com/watch?v=6396yj8UrXQ',
            'hauteur' => '406',
            'largeur' => '720'
        ];
        if ($version == 2) {
            $example = eden()->Builder_Examples()->getExample('notepad', '0-0');
            $contenu = '';
            $contenutexte = '';
            if ($example) {
                if (isset($example['contenu'])) {
                    $contenu = $example['contenu'];
                }
                if (isset($example['contenutexte'])) {
                    $contenutexte = $example['contenutexte'];
                }
            }
            $array_modules[0]['elements'][] = [
                'ID' => 0,
                'objet' => 'notepad',
                'span' => 'span12',
                'contenu' => $contenu,
                'contenutexte' => $contenutexte
            ];
        }
        $array_modules[0]['elements'][] = [
            'ID' => 0,
            'objet' => 'comments',
            'span' => 'span12',
            'type' => 'commentaires',
            'contenu' => 'auto',
            'contenutexte' => ''
        ];

        for ($i = 1; $i <= $nb_modules; ++$i) {
            $position = $i;
            ++$position;

            $array_modules[$i] = [
                'idmodule' => 0,
                'id_client' => $_SESSION['id_client'],
                'nommodule' => 'Module ' . $i,
                'difficulte' => 0,
                'duree' => 0,
                'etat' => 'publie',
                'delay' => 0,
                'position' => $position,
                'childrens' => [],
            ];

            for ($j = 1; $j <= $nb_pages; ++$j) {
                $array_modules[$i]['childrens'][$j] = [
                    'idpage' => 0,
                    'id_client' => $_SESSION['id_client'],
                    'titre' => 'Page ' . $j,
                    'display_title' => 1,
                    'hide_suite' => 0,
                    'allow_votes' => 1,
                    'position' => 1,
                    'elements' => [],
                ];

                foreach ($elements as $element) {
                    if ('headline' == $element) {
                        $array_modules[$i]['childrens'][$j]['elements'][] = ['ID' => 0, 'objet' => 'txt', 'span' => 'span12', 'contenutexte' => '<h3 style="text-align:center; font-size:36px;">Titre de la page ' . $j . '</h2>'];
                    } elseif ('videoplayer' == $element) {
                        $array_modules[$i]['childrens'][$j]['elements'][] = ['ID' => 0, 'objet' => 'videoplayer', 'span' => 'span12', 'urls' => 'https://www.youtube.com/watch?v=6396yj8UrXQ', 'hauteur' => '406', 'largeur' => '720'];
                    } elseif ('mp3player' == $element) {
                        $array_modules[$i]['childrens'][$j]['elements'][] = ['ID' => 0, 'objet' => 'mp3player', 'span' => 'span6', 'urls' => ''];
                    } elseif ('txt' == $element) {
                        $array_modules[$i]['childrens'][$j]['elements'][] = ['ID' => 0, 'objet' => 'txt', 'span' => 'span6', 'contenu' => '{"bg_color":"#d9edf7","bg_opacity":"1","border_color":"#98bbc2"}', 'contenutexte' => '<p>' . __('Nous avons vu...') . '</p>'];
                    } elseif ('downloads' == $element) {
                        $array_modules[$i]['childrens'][$j]['elements'][] = ['ID' => 0, 'objet' => 'downloads', 'span' => 'span12', 'contenu' => '{"icone":"fa-file-pdf-o","align":"left","open_window":"on","description":"' . __('Faites un clic droit sur le lien ci-dessus pour télécharger votre fichier.') . '"}', 'contenutexte' => __('Télécharger le fichier')];
                    } elseif ('notes' == $element) {
                        $array_modules[$i]['childrens'][$j]['elements'][] = ['ID' => 0, 'objet' => 'notes', 'span' => 'span6', 'titre' => __('Bloc-notes')];
                    } elseif ('enregistreur' == $element) {
                        $array_modules[$i]['childrens'][$j]['elements'][] = ['ID' => 0, 'objet' => 'audio_recorder', 'span' => 'span6'];
                    } elseif ('image' == $element) {
                        $array_modules[$i]['childrens'][$j]['elements'][] = ['ID' => 0, 'objet' => 'image', 'span' => 'span12', 'contenu' => 'center', 'fleche' => 'center', 'urls' => \Learnybox\Helpers\Assets::getImageUrl('default-image.png')];
                    }
                }

                //on met les commentaires à la fin
                foreach ($elements as $element) {
                    if ('comments' == $element) {
                        $array_modules[$i]['childrens'][$j]['elements'][] = ['ID' => 0, 'objet' => 'comments', 'span' => 'span12', 'type' => 'commentaires', 'contenu' => 'auto', 'contenutexte' => ''];
                    }
                }
            }
        }

        //recherche du design des pages
        $design = [];
        $formationThemeID = $theme;
        if (2 == $version) {
            if ($id_theme) {
                $get_theme = eden()->Builder_Themes()->getThemeById($id_theme);
                if ($get_theme) {
                    $masterTheme = eden()->Builder_MasterThemes()->getMasterThemeById($get_theme['id_master_theme']);
                    $formationThemeID = $masterTheme['link'] . '-' . $get_theme['link'];
                    if ($get_theme['design']) {
                        $design = json_decode($get_theme['design'], true);
                    } else {
                        $specific_theme = eden()->Builder_SpecificThemes()->getSpecificThemeById($get_theme['id_specific_theme']);
                        if ($specific_theme and $specific_theme['design']) {
                            $design = json_decode($specific_theme['design'], true);
                        }
                    }
                }
            }
        }

        //création de toutes les pages
        array_walk($array_modules, [eden()->Formation_Modules(), 'duplicateArborescenceTemplate'], [0, $idformation, $version, $design]);

        unset($_SESSION['ftutoriel']);

        //call segment.io
        $event = [
            'eventName' => 'Formation Created',
            'FormationThemeID' => $formationThemeID,
            'Method' => $methodologie,
            'Name' => $nomformation,
            'Modules' => $nb_modules,
            'Pages' => $nb_modules * $nb_modules,
            'Elements' => implode(',', $elements),
        ];
        $this->integrationsEventsService->sendEvent('track', $event);

        //send active formations to segment.io
        eden()->Formation_Formation()->sendActiveFormationsToSegment();

        return ['valid' => true, 'idformation' => $idformation];
    }
}
