<?php

/**
 * Api_Zapier_Formation_Comment class.
 *
 * @extends  Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Api_Zapier_Formation_Comment extends Eden_Class
{
    public function __construct()
    {
    }

    /********************************************************/
    /*********************** TRIGGERS ***********************/
    /********************************************************/

    /**
     * created function.
     * Returns the newly created comment.
     *
     * @param array $comment
     *
     * @return array
     */
    public function created($comment)
    {
        $datas = [
            'user_id' => $comment['user_id'],
            'nom' => $comment['nom'],
            'prenom' => $comment['prenom'],
            'email' => $comment['email'],
            'commentaire' => $comment['commentaire'],
            'date' => $comment['date'],
            'id_formation' => $comment['idformation'],
            'idmodule' => $comment['idmodule'],
            'idpage' => $comment['idpage'],
            'nom_formation' => $comment['nom_formation'],
        ];

        return $datas;
    }

    /********************************************************/
    /************************ SAMPLE ************************/
    /********************************************************/

    /**
     * sample function.
     * Return a sample result used during Zap edition.
     *
     * @return array
     */
    public function sample()
    {
        $comments = eden()->Formation_Commentaires()->getLast10Comments();
        if (!$comments) {
            return;
        }

        $sample = [];
        foreach ($comments as $comment) {
            $user = eden()->Utilisateurs()->getUserById($comment['user_id']);
            if (!$user) {
                continue;
            }

            $formation = eden()->Formation_Formation()->getFormationById($comment['idformation']);

            $sample[] = [
                'user_id' => $comment['user_id'],
                'nom' => $user['lname'],
                'prenom' => $user['fname'],
                'email' => $user['email'],
                'commentaire' => $comment['commentaire'],
                'etat' => $comment['etat'],
                'date' => $comment['date'],
                'id_formation' => $comment['idformation'],
                'nom_formation' => $formation['nomformation'],
                'idmodule' => $comment['idmodule'],
                'idpage' => $comment['idpage'],
            ];
        }

        return $sample;
    }
}
