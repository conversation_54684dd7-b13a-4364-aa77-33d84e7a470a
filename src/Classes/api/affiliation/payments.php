<?php

/**
 * Api_Affiliation_Payments class.
 *
 * @extends  Api
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Api_Affiliation_Payments extends Eden_Class
{
    private $database;

    public function __construct()
    {
        $this->database = MySQL::getInstance();
    }

    /********************************************************/
    /******************** TRANSACTIONS API ******************/
    /********************************************************/

    /**
     * get function.
     *
     * @param array $cleaned_post
     *
     * @return array
     */
    public function get($cleaned_post)
    {
        $datas = [
            'nb_payments' => 0,
            'payments' => [],
        ];

        //limites
        $id_page = 1;
        if (isset($cleaned_post['id_page'])) {
            $id_page = filter_var($cleaned_post['id_page'], FILTER_VALIDATE_INT);
        }

        --$id_page;
        if ($id_page < 0) {
            $id_page = 0;
        }
        $datas['id_page'] = $id_page;
        ++$datas['id_page'];

        $start = $id_page * 100;
        $limit = 100;

        $payments = $this->database
            ->search('aff_payments')
            ->addFilter("id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('date', 'DESC')
            ->setStart($start)
            ->setRange($limit)
            ->getRows();

        if ($payments) {
            $datas['nb_payments'] = count($payments);

            $i = 0;
            foreach ($payments as $payment) {
                $datas['payments'][$i] = $payment;
                unset($datas['payments'][$i]['id_client']);
                ++$i;
            }
        }

        return ['status' => true, 'datas' => $datas];
    }
}
