<?php

/**
 * VideoEncode class.
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class VideoEncode
{
    private $database;

    public function __construct()
    {
        $this->database = MySQL::getInstance();
    }

    /********************************************************/
    /********************** GET VIDEOS **********************/
    /********************************************************/

    /**
     * getVideoToEncodeById function
     *
     * @param int $id
     *
     * @return array
     */
    public function getVideoToEncodeById($id)
    {
        $result = $this->database
            ->search('ptf_videos_encode')
            ->addFilter("ID='$id'")
            ->getRow();

        return $result;
    }

    /**
     * getVideoToEncode function
     *
     * @param int $limit
     *
     * @return array
     */
    public function getVideoToEncode($limit = 1)
    {
        $result = $this->database
            ->search('ptf_videos_encode')
            ->addFilter("is_encoded='0' AND is_encoding='0' AND error='0'")
            ->addSort('date', 'ASC')
            ->setStart(0)
            ->setRange($limit)
            ->getRows();

        return $result;
    }

    /**
     * getVideoEncoding
     *
     * @return array
     */
    public function getVideoEncoding()
    {
        $result = $this->database
            ->search('ptf_videos_encode')
            ->addFilter("is_encoding='1' AND error='0'")
            ->getRow();

        return $result;
    }

    /**
     * isVideoEncoding
     *
     * @param string $file
     *
     * @return bool
     */
    public function isVideoEncoding($file)
    {
        $result = $this->database
            ->search('ptf_videos_encode')
            ->addFilter("file='" . addslashes($file) . "' AND is_encoded='0'")
            ->getRow();
        if ($result) {
            return true;
        }

        return false;
    }

    /**
     * setVideoEncoding
     *
     * @param int $id_video
     * @param bool $start
     *
     * @return array
     */
    public function setVideoEncoding($id_video, $start = true)
    {
        if ($start) {
            $array_update = ['is_encoding' => 1, 'date_start' => date('Y-m-d H:i:s')];
        } else {
            $array_update = ['is_encoding' => 0,  'is_encoded' => 1, 'date_end' => date('Y-m-d H:i:s')];
        }

        try {
            $this->database->updateRows('ptf_videos_encode', $array_update, "ID='$id_video'");
        } catch (Eden_Error $e) {
            \Learnybox\Services\Logger\LoggerService::log(\Monolog\Logger::ERROR, 'Function setVideoEncoding : erreur lors de la mise à jour de la vidéo<br>' . nl2br($e));

            return ['valid' => false, 'message' => __('Function setVideoEncoding : erreur lors de la mise à jour de la vidéo') . '<br>' . nl2br($e)];
        }

        return ['valid' => true];
    }

    /**
     * setVideoError
     *
     * @param int $id_video
     *
     * @return array
     */
    public function setVideoError($id_video)
    {
        try {
            $this->database->updateRows('ptf_videos_encode', ['error' => 1], "ID='$id_video'");
        } catch (Eden_Error $e) {
            \Learnybox\Services\Logger\LoggerService::log(\Monolog\Logger::ERROR, 'Function setVideoError : erreur lors de la mise à jour de la vidéo<br>' . nl2br($e));

            return ['valid' => false, 'message' => __('Function setVideoError : erreur lors de la mise à jour de la vidéo') . '<br>' . nl2br($e)];
        }

        return ['valid' => true];
    }

    public function cronVideoEncode()
    {
        $log = '';
        $error = '';

        ini_set('memory_limit', '500M');
        ini_set('post_max_size', '500M');
        ini_set('upload_max_filesize', '500M');
        ini_set('max_execution_time', 0);

        //verification si une vidéo est en cours d'encodage
        $videoEncoding = $this->getVideoEncoding();
        if ($videoEncoding) {
            $diff = time() - strtotime($videoEncoding['date_start']);
            if ($diff < 900) {
                return ['valid' => true, 'error' => $error, 'log' => $log];
            }

            $this->setVideoEncoding($videoEncoding['ID'], false);
            $this->setVideoError($videoEncoding['ID']);
            //on continue
        }

        $videoToEncode = $this->getVideoToEncode();
        if (!$videoToEncode) {
            return ['valid' => true, 'error' => $error, 'log' => $log];
        }

        $videoToEncode = array_shift($videoToEncode);

        $encodeVideo = $this->encodeVideo($videoToEncode['ID']);
        $log .= $encodeVideo['log'];
        $error .= $encodeVideo['error'];

        return ['valid' => !$error, 'error' => $error, 'log' => $log];
    }

    public function encodeVideo($videoId)
    {
        $log = 'Encodage de la vidéo ' . $videoId . '<br>';
        $error = '';

        ini_set('memory_limit', '2048M');
        ini_set('post_max_size', '1024M');
        ini_set('upload_max_filesize', '1024M');
        ini_set('max_execution_time', 0);

        $videoToEncode = $this->getVideoToEncodeById($videoId);
        if (!$videoToEncode) {
            $error .= 'Cette vidéo n\'existe pas';
            return ['valid' => !$error, 'error' => $error, 'log' => $log];
        }

        $client = eden()->Clients()->getClientById($videoToEncode['id_client']);
        if (!$client or !$client['active']) {
            $this->setVideoEncoding($videoToEncode['ID'], false);
            $this->setVideoError($videoToEncode['ID']);

            return ['valid' => !$error, 'error' => $error, 'log' => $log];
        }

        //verification si une vidéo est en cours d'encodage
        $videoEncoding = $this->getVideoEncoding();
        if ($videoEncoding) {
            $diff = time() - strtotime($videoEncoding['date_start']);
            if ($diff < 900) {
                $queue = new \Learnybox\Services\Aws\Sqs\QueueService('imports');
                $message = new \Learnybox\Services\Aws\Sqs\MessageService([
                    'action' => 'video_encode',
                    'param' => $videoId,
                ]);
                $queue->sendMessage($message, 60);

                return ['valid' => true, 'error' => $error, 'log' => $log];
            }
        }

        //set Encoding = true
        $setVideoEncoding = $this->setVideoEncoding($videoToEncode['ID'], true);
        if (!$setVideoEncoding['valid']) {
            $error .= 'Erreur setVideoEncoding : ' . $setVideoEncoding['message'];
        }

        //$videoToEncode['file'] = mb_convert_encoding($videoToEncode['file'], 'ISO-8859-1');
        $videoToEncode['file'] = stripslashes($videoToEncode['file']);
        $log .= 'Traitement de la vidéo ' . $videoToEncode['file'] . '<br><br>';

        $s3File = str_replace(MEDIAS_PATH . '/', '', $videoToEncode['file']);

        $s3Service = new \Learnybox\Services\Aws\S3\MediasService();
        $getFile = $s3Service->getFile($s3File);
        if (!$getFile['valid']) {
            $error .= 'Erreur lors de l\'accès à la vidéo : ce fichier n\'existe pas.';
        }

        $video_file = CACHE_PATH . '/video_encode/' . basename($s3File);

        if (!$error) {
            $fp = fopen(CACHE_PATH . '/video_encode/' . basename($s3File), 'w');
            fwrite($fp, $getFile['file']);
            fclose($fp);

            //get ID3 tag
            $getID3 = new getID3();
            $videoInfos = $getID3->analyze($video_file);

            if (!$videoInfos or !isset($videoInfos['mime_type']) or !$videoInfos['mime_type']) {
                $error .= 'Erreur lors de la récupération des informations de la vidéo';
            }

            if (!$error and false === strpos($videoInfos['mime_type'], 'video/')) {
                $error .= 'Erreur lors de la récupération du mime_type de la vidéo : ' . $videoInfos['mime_type'];
            }
        }

        //verif durée
        if (!isset($videoInfos['playtime_seconds'])) {
            $error .= 'Erreur : impossible de récupérer la durée de la vidéo';
        }
        if (!$error) {
            $duration = (int) $videoInfos['playtime_seconds'];
            if ($duration < 3) {
                $error .= 'Erreur : la vidéo dure moins de 3 secondes (' . $duration . ')';
            }
        }

        //get dimensions
        $width = 0;
        $height = 0;
        if (!$error) {
            if (isset($videoInfos['video']['resolution_x']) and isset($videoInfos['video']['resolution_y'])) {
                $width = $videoInfos['video']['resolution_x'];
                $height = $videoInfos['video']['resolution_y'];
            }

            if (!$width or !$height) {
                $error .= 'Erreur lors de la récupération des dimensions de la vidéo';
            }
        }

        if ($error) {
            eden()->VideoEncode()->setVideoEncoding($videoToEncode['ID'], false);
            eden()->VideoEncode()->setVideoError($videoToEncode['ID']);

            //\Learnybox\Services\Logger\LoggerService::log(\Monolog\Logger::ERROR, $log . $error);
            return ['valid' => true, 'error' => $error, 'log' => $log];
        }

        //scale if needed
        $scale = '';
        if ($width > 720) {
            $scale = '720';
        }

        //encoding
        $output_file = $videoInfos['filepath'] . '/video-encode-' . $videoToEncode['ID'] . '.mp4';

        $conversion = $this->convertVideoToMp4($video_file, $output_file, $width . 'x' . $height, $scale);
        if (!$conversion['valid']) {
            $error .= 'Erreur lors de la conversion : ' . $conversion['message'];
        } else {
            $log .= '<br><br>' . json_encode($conversion['message']) . '<br><br>';

            // suppression des éventuels fichiers journaux que FFMPEG peut laisser trainer
            if (file_exists('ffmpeg2pass.log')) {
                @unlink('ffmpeg2pass.log');
            }
            if (file_exists('x264_2pass.log')) {
                @unlink('x264_2pass.log');
            }
            if (file_exists('ffmpeg2pass.log.mbtree')) {
                @unlink('ffmpeg2pass.log.mbtree');
            }
            if (file_exists('x264_2pass.log.mbtree')) {
                @unlink('x264_2pass.log.mbtree');
            }

            //fichier de destination final : /home/<USER>/subdir/nomdelavideo.mp4
            $info = pathinfo($s3File);
            $output_file_final = $info['dirname'] . '/' . $info['filename'] . '.mp4';

            //upload sur S3
            $uploadFile = $s3Service->uploadFile($output_file, $output_file_final);
            if (!$uploadFile['valid']) {
                $error .= 'Erreur lors de l\'envoi sur S3 : ' . $uploadFile['message'];
            }

            $destination_file = $uploadFile['url'];

            //delete encoded file
            @unlink($output_file);

            //delete source file
            @unlink($video_file);

            $deleteFile = $s3Service->deleteFile($s3File);
            if (!$deleteFile['valid']) {
                $error .= 'Erreur lors de la suppression du fichier ' . $s3File . ' sur S3 : ' . $deleteFile['message'];
            }
        }

        //set Encoding = END
        if (!$error) {
            $setVideoEncoding = $this->setVideoEncoding($videoToEncode['ID'], false);
            if (!$setVideoEncoding['valid']) {
                $error .= $setVideoEncoding['message'];
            }
        }

        if ($error) {
            eden()->VideoEncode()->setVideoEncoding($videoToEncode['ID'], false);
            eden()->VideoEncode()->setVideoError($videoToEncode['ID']);

            \Learnybox\Services\Logger\LoggerService::log(\Monolog\Logger::ERROR, $log . $error);
        }

        return ['valid' => true, 'error' => $error, 'log' => $log];
    }

    public function convertVideoToMp4($sourceFile, $outputFile, $videoResolution = '320x240', $scale = '720', $audioBitrate = '128k')
    {
        $commandline = EZFFMPEG_BIN_PATH . ' -i "' . $sourceFile . '" -codec:v libx264 -crf 18 -profile:v high -b:v 500k -maxrate 500k -bufsize 1000k ';

        if ($scale) {
            $commandline .= '-vf scale=' . $scale . ':-2';
        } else {
            $commandline .= '-s ' . $videoResolution;
        }

        $commandline .= ' -threads 1 -codec:a aac -movflags faststart -b:a ' . $audioBitrate . ' -y "' . $outputFile . '"';

        $handle = popen($commandline . ' 2>&1', 'r');
        if (!$handle) {
            return ['valid' => false, 'message' => __('Function popen error')];
        }

        $return = '';
        while (!feof($handle)) {
            $return .= fread($handle, 2096) . '<br>';
        }
        pclose($handle);

        if (!file_exists($outputFile) || filesize($outputFile) <= 0) {
            //Echec, pas de conversion
            return ['valid' => false, 'message' => $commandline . ' / !file_exists("' . $outputFile . '") / Command output : ' . $return];
        }

        return ['valid' => true, 'message' => $return];
    }
}
