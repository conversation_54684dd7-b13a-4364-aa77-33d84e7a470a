<?php

use Learnybox\Helpers\Assets;
use Learnybox\Helpers\RouterHelper;

class Popups_Elements extends Eden_Class
{
    private $database;
    public $champ_comment;
    public $comment_approuve = 'auto';

    public function __construct()
    {
        $this->database = MySQL::getInstance();

        $this->champ_comment = [
            'prenom' => ['titre' => __('Prénom'), 'active' => 'non', 'obligatoire' => 'non'],
            'nom' => ['titre' => __('Nom'), 'active' => 'non', 'obligatoire' => 'non'],
            'telephone' => ['titre' => __('Téléphone'), 'active' => 'non', 'obligatoire' => 'non'],
            'adresse' => ['titre' => __('Votre adresse'), 'active' => 'non', 'obligatoire' => 'non'],
            'site' => ['titre' => __('Votre site'), 'active' => 'non', 'obligatoire' => 'non'],
        ];
    }

    /********************************************************/
    /*********************** ELEMENTS ***********************/
    /********************************************************/

    /**
     * getPopupElements
     * Retourne les éléments du popup.
     *
     * @param   $id_popup int
     *
     * @return array popups_elements.*
     */
    public function getPopupElements($id_popup, $id_line)
    {
        $result = $this->database
            ->search(DB_PREFIX . 'popups_elements')
            ->addFilter("id_popup='$id_popup' AND id_line='$id_line' AND parent='0'")
            ->addSort('position', 'ASC')
            ->getRows();

        return $result;
    }

    /**
     * getPopupElementsByBox
     * Retourne les éléments d'une boite.
     *
     * @param $id_popup int
     * @return array popups_elements.*
     *
     */
    public function getPopupElementsByBox($id_popup, $id_box)
    {
        $result = $this->database
            ->search(DB_PREFIX . 'popups_elements')
            ->addFilter("id_popup='$id_popup' AND parent='$id_box'")
            ->addSort('position', 'ASC')
            ->getRows();

        return $result;
    }

    /**
     * getPopupElementById
     * Retourne les éléments du popup.
     *
     * @param $id_popup int
     * @return array popups_elements.*
     *
     */
    public function getPopupElementById($idelement)
    {
        $result = $this->database
            ->search(DB_PREFIX . 'popups_elements')
            ->addFilter("ID='$idelement'")
            ->getRow();

        return $result;
    }

    /**
     * getPopupElementsByObjet
     * Retourne les éléments du popup.
     *
     * @param $id_popup int
     * @return array popups_elements.*
     *
     */
    public function getPopupElementsByObjet($id_popup, $objet)
    {
        $result = $this->database
            ->search(DB_PREFIX . 'popups_elements')
            ->addFilter("id_popup='$id_popup' AND objet='$objet'")
            ->getRows();

        return $result;
    }

    /**
     * getPopupElementsByObjetAndType
     * Retourne les éléments du popup.
     *
     * @param $id_popup int
     * @return array popups_elements.*
     *
     */
    public function getPopupElementsByObjetAndType($id_popup, $objet, $type)
    {
        $result = $this->database
            ->search(DB_PREFIX . 'popups_elements')
            ->addFilter("id_popup='$id_popup' AND objet='$objet' AND type='$type'")
            ->getRows();

        return $result;
    }

    /**
     * getLastPopupElement function.
     *
     * @param int $id_popup
     *
     * @return array
     */
    public function getLastPopupElement($id_popup)
    {
        $result = $this->database
            ->search(DB_PREFIX . 'popups_elements')
            ->addFilter("id_popup='$id_popup'")
            ->addSort('position', 'DESC')
            ->getRow();

        return $result;
    }

    /**
     * getLastPopupElementByBox function.
     *
     * @param int $id_popup
     *
     * @return array
     */
    public function getLastPopupElementByBox($id_popup, $id_parent)
    {
        $result = $this->database
            ->search(DB_PREFIX . 'popups_elements')
            ->addFilter("id_popup='$id_popup' AND parent='$id_parent'")
            ->addSort('position', 'DESC')
            ->getRow();

        return $result;
    }

    /**
     * getElementByName function.
     *
     * @param string $name
     *
     * @return array
     */
    public function getElementByName($name)
    {
        $result = $this->database
            ->search(DB_PREFIX . 'popups_elements')
            ->addFilter("name='$name'")
            ->getRow();

        return $result;
    }

    /********************************************************/
    /******************** AFFICHAGE SITE ********************/
    /********************************************************/

    /**
     * afficher_popup function.
     * Retourne l'affichage du popup reçue en paramètre.
     *
     * @param $data_popup : array(id_popup, etc...)
     *
     * @return array
     */
    public function afficher_popup($data_popup)
    {
        $output = '';
        $js = '';
        $style = '';
        $iframeResizer = false;
        $social_share = false;
        $formulaire_paiement = false;

        $style .= '#PopUp' . $data_popup['id_popup'] . ' { height: 100%; }' . "\n";

        $config = $data_popup['popup']['design'];
        if ($config) {
            $config = json_decode($config, true);

            if (isset($config['bgimage']) and $config['bgimage']) {
                $bgimage_position = 'fixed';
                $bgimage_size = 'cover';
                if (isset($config['bgimage_position']) and $config['bgimage_position']) {
                    $bgimage_position = $config['bgimage_position'];
                }
                if (isset($config['bgimage_size']) and $config['bgimage_size']) {
                    $bgimage_size = $config['bgimage_size'];
                }

                $style .= '
                #PopUp' . $data_popup['id_popup'] . ' {
                    background: url(\'' . $config['bgimage'] . '\');
                    background-attachment: ' . $bgimage_position . ';
                    background-position: center top;
                    background-repeat: no-repeat;
                    background-color: transparent;
                    -webkit-background-size: ' . $bgimage_size . ';
                    -moz-background-size: ' . $bgimage_size . ';
                    -o-background-size: ' . $bgimage_size . ';
                    background-size: ' . $bgimage_size . ';
                    width:100%;
                    height:100%;
                }' . "\n";
            } elseif (isset($config['bgcolor1']) and $config['bgcolor1'] and isset($config['bgcolor2']) and $config['bgcolor2']) {
                $style .= '
                #PopUp' . $data_popup['id_popup'] . ' {
                    background: ' . $config['bgcolor1'] . ';
                    background: -moz-linear-gradient(top,  ' . $config['bgcolor1'] . ' 0%, ' . $config['bgcolor2'] . ' 100%);
                    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,' . $config['bgcolor1'] . '), color-stop(100%,' . $config['bgcolor2'] . '));
                    background: -webkit-linear-gradient(top,  ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    background: -o-linear-gradient(top,  ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    background: -ms-linear-gradient(top,  ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    background: linear-gradient(to bottom,  ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\'' . $config['bgcolor1'] . '\', endColorstr=\'' . $config['bgcolor2'] . '\',GradientType=0 );
                }' . "\n";
            } elseif (isset($config['bgcolor1']) and $config['bgcolor1']) {
                $style .= '#PopUp' . $data_popup['id_popup'] . ' { background-color: ' . $config['bgcolor1'] . '; }' . "\n";
            }

            if (isset($config['margin_top'])) {
                $style .= '#PopUp' . $data_popup['id_popup'] . ' #main-container { margin-top: ' . $config['margin_top'] . 'px; }' . "\n";
            }
            if (isset($config['margin_bottom'])) {
                $style .= '#PopUp' . $data_popup['id_popup'] . ' #main-container { margin-bottom: ' . $config['margin_bottom'] . 'px; }' . "\n";
            }
            if (isset($config['padding_top'])) {
                $style .= '#PopUp' . $data_popup['id_popup'] . ' #main-container { padding-top: ' . $config['padding_top'] . 'px; }' . "\n";
            }
            if (isset($config['padding_bottom'])) {
                $style .= '#PopUp' . $data_popup['id_popup'] . ' #main-container { padding-bottom: ' . $config['padding_bottom'] . 'px; }' . "\n";
            }
        }

        //style
        if ($data_popup['popup']['additionnal_css']) {
            $style .= $data_popup['popup']['additionnal_css'] . "\n";
        }

        $output .= '<div id="PopUp' . $data_popup['id_popup'] . '">';

        //récupération des lignes du popup sélectionnée
        $lines = eden()->Popups_Lines()->getAllLines($data_popup['id_popup'], $data_popup['id_version']);
        if ($lines) {
            foreach ($lines as $line) {
                $displayLine = $this->displayLine($line, $data_popup['id_popup']);
                $output .= $displayLine['start'];
                $style .= $displayLine['style'];

                //affichage des éléments
                $elements = $this->getPopupElements($data_popup['id_popup'], $line['id_line']);
                if ($elements) {
                    foreach ($elements as $element) {
                        if ($element['hide']) {
                            continue;
                        }
                        if ('social_share' == $element['objet']) {
                            $social_share = true;
                        }

                        $afficher_element = $this->afficher_element($data_popup, $element);
                        $output .= $afficher_element['output'];
                        $js .= $afficher_element['js'];
                        if (isset($afficher_element['iframeResizer']) and $afficher_element['iframeResizer']) {
                            $iframeResizer = true;
                        }
                        if (isset($afficher_element['social_share']) and $afficher_element['social_share']) {
                            $social_share = true;
                        }
                        if (isset($afficher_element['formulaire_paiement']) and $afficher_element['formulaire_paiement']) {
                            $formulaire_paiement = true;
                        }
                    }
                }

                $output .= $displayLine['end'];
            }
        }

        $output .= '</div>';

        $final_output = '';
        if ($style) {
            $final_output .= '
            <style type="text/css">
                ' . $style . '
            </style>';
        }

        $final_output .= $output;
        $final_output .= '<div style="display:none">[[CSRF]]</div>';

        if ($iframeResizer) {
            Assets::addJs('themes/t/js/iframeResizer.min.js', null);
        }
        if ($social_share) {
            Assets::addJs('themes/t/js/social.js', null);
        }

        if ($formulaire_paiement) {
            Assets::addJs('themes/t/js/paymentform.js', null);
        }

        return ['output' => $final_output, 'js' => $js];
    }

    public function displayLine($line, $id_popup)
    {
        $start = '';
        $end = '';
        $style = '';

        $config = $line['design'];
        if ($config) {
            $config = json_decode($config, true);

            if ($config['bgimage']) {
                $bgimage_position = 'fixed';
                $bgimage_size = 'cover';
                if (isset($config['bgimage_position']) and $config['bgimage_position']) {
                    $bgimage_position = $config['bgimage_position'];
                }
                if (isset($config['bgimage_size']) and $config['bgimage_size']) {
                    $bgimage_size = $config['bgimage_size'];
                }

                $style .= '
                    background: url(\'' . $config['bgimage'] . '\');
                    background-attachment: ' . $bgimage_position . ';
                    background-position: center top;
                    background-repeat: no-repeat;
                    background-color: transparent;
                    -webkit-background-size: ' . $bgimage_size . ';
                    -moz-background-size: ' . $bgimage_size . ';
                    -o-background-size: ' . $bgimage_size . ';
                    background-size: ' . $bgimage_size . ';
                    width:100%;
                    height:100%;' . "\n";
            } elseif ($config['bgcolor1'] and $config['bgcolor2']) {
                $style .= '
                    background: ' . $config['bgcolor1'] . ';
                    background: -moz-linear-gradient(top, ' . $config['bgcolor1'] . ' 0%, ' . $config['bgcolor2'] . ' 100%);
                    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,' . $config['bgcolor1'] . '), color-stop(100%,' . $config['bgcolor2'] . '));
                    background: -webkit-linear-gradient(top, ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    background: -o-linear-gradient(top, ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    background: -ms-linear-gradient(top, ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    background: linear-gradient(to bottom, ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\'' . $config['bgcolor1'] . '\', endColorstr=\'' . $config['bgcolor2'] . '\',GradientType=0 );' . "\n";
            } elseif ($config['bgcolor1']) {
                if (isset($config['bgcolor1_opacity']) and $config['bgcolor1_opacity']) {
                    $style .= 'background-color: ' . hex2rgba($config['bgcolor1'], $config['bgcolor1_opacity']) . ';';
                } else {
                    $style .= 'background-color: ' . $config['bgcolor1'] . ';' . "\n";
                }
            }

            if ($config['margin_top']) {
                $style .= 'margin-top: ' . $config['margin_top'] . 'px;' . "\n";
            }
            if ($config['margin_bottom']) {
                $style .= 'margin-bottom: ' . $config['margin_bottom'] . 'px;' . "\n";
            }
            if ($config['padding_top']) {
                $style .= 'padding-top: ' . $config['padding_top'] . 'px;' . "\n";
            }
            if ($config['padding_bottom']) {
                $style .= 'padding-bottom: ' . $config['padding_bottom'] . 'px;' . "\n";
            }

            if ($config['border_top_width'] and $config['border_top_color']) {
                $style .= 'border-top: ' . $config['border_top_width'] . 'px solid ' . $config['border_top_color'] . ';' . "\n";
            }
            if ($config['border_bottom_width'] and $config['border_bottom_color']) {
                $style .= 'border-bottom: ' . $config['border_bottom_width'] . 'px solid ' . $config['border_bottom_color'] . ';' . "\n";
            }

            if ($style) {
                $style = '
                #PopUp' . $id_popup . ' #line' . $line['id_line'] . ' {
                    ' . $style . '
                }' . "\n";
            }
        }

        //délais
        $delay = 0;
        $delay_disappear = 0;
        $element_infos = '';
        $class = '';

        if (isset($config['delay']) and $config['delay']) {
            $delay = $config['delay'];
            $element_infos = 'p=' . $line['id_popup'] . '&z=' . $line['id_line'];
            $element_infos = base64_encode($element_infos);

            $class .= ' hidden';
        }

        if (isset($config['delay_disappear']) and $config['delay_disappear']) {
            $delay_disappear = $config['delay_disappear'];
        }

        if (isset($config['full_width']) and $config['full_width']) {
            $start = '
                <div id="line' . $line['id_line'] . '" ' . ($delay ? 'data-z-delay="' . $delay . '" data-el="' . $element_infos . '"' : '') . ' ' . ($delay_disappear ? 'data-d-delay="' . $delay_disappear . '"' : '') . ' class="line' . $class . '">
                    <div class="container">
                        <div class="wrapper">
                            <div class="row">
                                <div class="grid-sizer col-md-1 col-sm-1"></div>
                            </div>
                            <div class="row">';
        } else {
            $start = '
                <div class="container">
                    <div id="line' . $line['id_line'] . '" ' . ($delay ? 'data-z-delay="' . $delay . '" data-el="' . $element_infos . '"' : '') . ' ' . ($delay_disappear ? 'data-d-delay="' . $delay_disappear . '"' : '') . ' class="line' . $class . '">
                        <div class="wrapper">
                            <div class="row">
                                <div class="grid-sizer col-md-1 col-sm-1"></div>
                            </div>
                            <div class="row">';
        }

        $end .= '
                            </div>
                        </div>
                    </div>
                </div>';

        return ['style' => $style, 'start' => $start, 'end' => $end];
    }

    public function adminDisplayLine($line)
    {
        $start = '';
        $end = '';
        $style = '';

        $config = $line['design'];
        if ($config) {
            $config = json_decode($config, true);

            if ($config['bgimage']) {
                $bgimage_position = 'fixed';
                $bgimage_size = 'cover';
                if (isset($config['bgimage_position']) and $config['bgimage_position']) {
                    $bgimage_position = $config['bgimage_position'];
                }
                if (isset($config['bgimage_size']) and $config['bgimage_size']) {
                    $bgimage_size = $config['bgimage_size'];
                }

                $style .= '
                    background: url(\'' . $config['bgimage'] . '\');
                    background-attachment: ' . $bgimage_position . ';
                    background-position: center top;
                    background-repeat: no-repeat;
                    background-color: transparent;
                    -webkit-background-size: ' . $bgimage_size . ';
                    -moz-background-size: ' . $bgimage_size . ';
                    -o-background-size: ' . $bgimage_size . ';
                    background-size: ' . $bgimage_size . ';
                    width:100%;
                    height:100%;' . "\n";
            } elseif ($config['bgcolor1'] and $config['bgcolor2']) {
                $style .= '
                    background: ' . $config['bgcolor1'] . ';
                    background: -moz-linear-gradient(top, ' . $config['bgcolor1'] . ' 0%, ' . $config['bgcolor2'] . ' 100%);
                    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,' . $config['bgcolor1'] . '), color-stop(100%,' . $config['bgcolor2'] . '));
                    background: -webkit-linear-gradient(top, ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    background: -o-linear-gradient(top, ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    background: -ms-linear-gradient(top, ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    background: linear-gradient(to bottom, ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\'' . $config['bgcolor1'] . '\', endColorstr=\'' . $config['bgcolor2'] . '\',GradientType=0 );' . "\n";
            } elseif ($config['bgcolor1']) {
                if (isset($config['bgcolor1_opacity']) and $config['bgcolor1_opacity']) {
                    $style .= 'background-color: ' . hex2rgba($config['bgcolor1'], $config['bgcolor1_opacity']) . ';';
                } else {
                    $style .= 'background-color: ' . $config['bgcolor1'] . ';' . "\n";
                }
            }

            if ($config['margin_top']) {
                $style .= 'margin-top: ' . $config['margin_top'] . 'px;' . "\n";
            }
            if ($config['margin_bottom']) {
                $style .= 'margin-bottom: ' . $config['margin_bottom'] . 'px;' . "\n";
            }
            if ($config['padding_top']) {
                $style .= 'padding-top: ' . $config['padding_top'] . 'px;' . "\n";
            }
            if ($config['padding_bottom']) {
                $style .= 'padding-bottom: ' . $config['padding_bottom'] . 'px;' . "\n";
            }

            if ($config['border_top_width'] and $config['border_top_color']) {
                $style .= 'border-top: ' . $config['border_top_width'] . 'px solid ' . $config['border_top_color'] . ';' . "\n";
            }
            if ($config['border_bottom_width'] and $config['border_bottom_color']) {
                $style .= 'border-bottom: ' . $config['border_bottom_width'] . 'px solid ' . $config['border_bottom_color'] . ';' . "\n";
            }

            if ($style) {
                $style = '
                #PopUp' . $line['id_popup'] . ' #line' . $line['id_line'] . ' {
                    ' . $style . '
                }' . "\n";
            }
        }

        if (isset($config['full_width']) and $config['full_width']) {
            $start = '
                <div id="line' . $line['id_line'] . '" class="line">
                    <div class="row">';
        } else {
            $start = '
                <div id="line' . $line['id_line'] . '" class="line">
                    <div class="row">';
        }

        $end .= '
                    </div>
                </div>';

        return ['style' => $style, 'start' => $start, 'end' => $end];
    }

    /********************************************************/
    /******************* AFFICHAGE ADMIN ********************/
    /********************************************************/

    public function admin_display_popup($data_popup)
    {
        $output = '';
        $js = '';
        $style = '';

        $config = $data_popup['popup']['design'];
        if ($config) {
            $config = json_decode($config, true);

            if (isset($config['bgimage']) and $config['bgimage']) {
                $bgimage_position = 'fixed';
                $bgimage_size = 'cover';
                if (isset($config['bgimage_position']) and $config['bgimage_position']) {
                    $bgimage_position = $config['bgimage_position'];
                }
                if (isset($config['bgimage_size']) and $config['bgimage_size']) {
                    $bgimage_size = $config['bgimage_size'];
                }

                $style .= '
                #PopUp' . $data_popup['id_popup'] . ' #main-composition {
                    background: url(\'' . $config['bgimage'] . '\');
                    background-attachment: ' . $bgimage_position . ';
                    background-position: center top;
                    background-repeat: no-repeat;
                    background-color: transparent;
                    -webkit-background-size: ' . $bgimage_size . ';
                    -moz-background-size: ' . $bgimage_size . ';
                    -o-background-size: ' . $bgimage_size . ';
                    background-size: ' . $bgimage_size . ';
                    width:100%;
                    height:100%;
                    min-height:600px;
                }' . "\n";
            } elseif (isset($config['bgcolor1']) and $config['bgcolor1'] and isset($config['bgcolor2']) and $config['bgcolor2']) {
                $style .= '
                #PopUp' . $data_popup['id_popup'] . ' #main-composition {
                    background: ' . $config['bgcolor1'] . ';
                    background: -moz-linear-gradient(top,  ' . $config['bgcolor1'] . ' 0%, ' . $config['bgcolor2'] . ' 100%);
                    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,' . $config['bgcolor1'] . '), color-stop(100%,' . $config['bgcolor2'] . '));
                    background: -webkit-linear-gradient(top,  ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    background: -o-linear-gradient(top,  ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    background: -ms-linear-gradient(top,  ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    background: linear-gradient(to bottom,  ' . $config['bgcolor1'] . ' 0%,' . $config['bgcolor2'] . ' 100%);
                    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\'' . $config['bgcolor1'] . '\', endColorstr=\'' . $config['bgcolor2'] . '\',GradientType=0 );
                }' . "\n";
            } elseif (isset($config['bgcolor1']) and $config['bgcolor1']) {
                $style .= '#PopUp' . $data_popup['id_popup'] . ' #main-composition { background-color: ' . $config['bgcolor1'] . '; }' . "\n";
            }
        }

        //style
        if ($data_popup['popup']['additionnal_css']) {
            $style .= $data_popup['popup']['additionnal_css'] . "\n";
        }

        //récupération des lignes du popup sélectionnée
        $lines = eden()->Popups_Lines()->getAllLines($data_popup['id_popup'], $data_popup['id_version']);
        if ($lines) {
            foreach ($lines as $line) {
                $lineContainer = $this->adminDisplayLineContainer($data_popup['id_popup'], $line, $data_popup);
                $style .= $lineContainer['style'];
                $output .= $lineContainer['output'];
            }
        }

        if ($data_popup['popup']['additionnal_js']) {
            $output .= $data_popup['popup']['additionnal_js'] . "\n";
        }

        $final_output = '
        <div id="PopUp' . $data_popup['id_popup'] . '" data-id_popup="' . $data_popup['id_popup'] . '" style="width:' . $data_popup['popup']['width'] . 'px; margin: 0 auto;">
            <div id="main-composition" ' . ((!isset($_SESSION['editor_level']) or 'expert' != $_SESSION['editor_level']) ? 'class="start"' : '') . ' data-id_popup="' . $data_popup['id_popup'] . '">';

        if ($style) {
            $final_output .= '
            <style type="text/css">
                ' . $style . '
            </style>';
        }

        $final_output .= $output;
        $final_output .= '</div></div>';

        if (isset($_SESSION['editor_level']) and 'expert' == $_SESSION['editor_level']) {
            $final_output .= '
                <div class="linecontainer addline">
                    <a id="BtnAddLine" class="btn btn-default" onclick="$(\'#insertafterline\').val(0); AddLine(' . $data_popup['id_popup'] . ', ' . $data_popup['id_version'] . ');"><i class="fa fa-plus"></i> Ajouter une zone</a>
                </div>';
        }

        return $final_output;
    }

    public function adminDisplayLineContainer($id_popup, $line, $data_popup = [])
    {
        $displayLine = $this->adminDisplayLine($line);
        $style = $displayLine['style'];

        $output = '<div id="linecontainer' . $line['id_line'] . '" class="linecontainer">';

        if (isset($_SESSION['editor_level']) and 'expert' == $_SESSION['editor_level']) {
            $output .= '
                <div class="lineactions">
                    <div class="btn-group">
                        <button class="btn btn-small btn-deplacer editline" style="cursor:pointer"><i class="fa fa-arrows"></i> ' . __('Déplacer') . '</button>
                        <button class="btn btn-small editline" onclick="EditLine(' . $id_popup . ', ' . $line['id_line'] . ');" style="cursor:pointer"><i class="fa fa-edit"></i> ' . __('Modifier') . '</button>
                        <button class="btn btn-small btn-warning duplicateline" onclick="DuplicateLine(' . $id_popup . ', ' . $line['id_line'] . ');" style="cursor:pointer"><i class="fa fa-copy"></i> ' . __('Dupliquer') . '</button>
                        <button class="btn btn-small btn-danger deleteline" onclick="ConfirmDeleteLine(' . $id_popup . ', ' . $line['id_line'] . ');" style="cursor:pointer"><i class="fa fa-trash-o"></i> ' . __('Supprimer') . '</button>
                        <button class="btn btn-small btn-primary" id="BtnAddElement" onclick="$(\'#insertinline\').val(' . $line['id_line'] . '); $(\'#insertafterelement\').val(0); $(\'#insertinbox\').val(0); $(\'#AddElement\').modal(\'show\');"><i class="fa fa-plus"></i> ' . __('Ajouter un élément') . '</button>
                    </div>
                </div>';
        }

        $output .=
            $displayLine['start'] . '
            <div class="sortable-popups" data-id_popup="' . $id_popup . '" data-id_line="' . $line['id_line'] . '">
                <div class="row">
                    <div class="grid-sizer col-md-1 col-sm-1"></div>
                </div>';

        $elements = $this->getPopupElements($id_popup, $line['id_line']);
        if ($elements) {
            foreach ($elements as $element) {
                $output .= $this->admin_display_element($element, $data_popup);
            }
        }

        $output .= '</div>';
        $output .= $displayLine['end'];
        $output .= '</div>';

        return ['output' => $output, 'style' => $style];
    }

    /********************************************************/
    /****************** AFFICHAGE ELEMENTS ******************/
    /********************************************************/

    /**
     * afficher_element function.
     *
     * @param $data_popup : array(id_popup, etc...)
     * @param $element array()
     *
     * @return array
     */
    public function afficher_element($data_popup, $element)
    {
        $output = '';
        $js = '';
        $iframeResizer = false;
        $formulaire_paiement = false;

        $social_share = false;
        if ('social_share' == $element['objet']) {
            $social_share = true;
        }

        $span = 'span6';
        if ($element['span']) {
            $span = $element['span'];
        }
        if ($element['parent']) {
            $span = 'span12';
        }

        $class = str_replace('span', 'col-md-', $span);
        $class .= str_replace('span', ' col-sm-', $span);
        $class .= ' col-xs-12';

        if (!$element['parent']) {
            $class .= ' box-masonry';
        }
        if ('module' == $element['objet']) {
            $class .= ' item';
        }

        //délai
        $delay = 0;
        $delay_disappear = 0;
        $element_infos = '';

        if ($element['delay']) {
            $delay = $element['delay'];

            $element_infos = 'p=' . $data_popup['id_popup'] . '&e=' . $element['ID'];
            $element_infos = base64_encode($element_infos);

            $class .= ' hidden';
        }

        $animateClass = 'fadeIn';
        if ($element['animation']) {
            $animateClass = $element['animation'];
        }

        if ($element['delay_disappear']) {
            $delay_disappear = $element['delay_disappear'];
        }

        $disappearClass = 'fadeIn';
        if ($element['animation_disappear']) {
            $disappearClass = $element['animation_disappear'];
        }

        if ($element['hide_tablet']) {
            $class .= ' hidden-sm';
        }
        if ($element['hide_phone']) {
            $class .= ' hidden-xs';
        }

        if ($element['bloc_alignement'] and 'auto' != $element['bloc_alignement']) {
            $class = str_replace('box-masonry', '', $class);

            switch ($element['bloc_alignement']) {
                case 'center':
                    $class .= ' bloc-center';
                    break;
                case 'right':
                    $class .= ' pull-right';
                    break;
            }

            $output .= '
                <div class="col-md-12 col-sm-12 col-xs-12 box-masonry">
                    <div class="row">';
        }

        $output .= '<div class="' . $class . '"';

        if ($delay) {
            $output .= ' data-class="' . $animateClass . '"';
        }

        if ($delay) {
            $output .= ' data-delay="' . $delay . '" style="height:0; min-height: 0px; max-height: 0px;"';
            if ($element_infos) {
                $output .= ' data-el="' . $element_infos . '"';
            }
        }
        if ($delay_disappear) {
            $output .= ' data-d-delay="' . $delay_disappear . '" data-d-class="' . $disappearClass . '"';
        }

        $output .= '>';

        $class = ucfirst(str_replace(['-', '_'], '', $element['objet']));
        if (class_exists('TunnelsPagesElements_' . $class)) {
            $styles = eden()->TunnelsPagesElements()->getStyles($element);
            $container_style = $styles['container_style'];
            $bg_style = $styles['bg_style'];
            $inner_style = $styles['inner_style'];

            $output .= '
                <div class="maincontainer ' . $element['objet'] . '"' . ($container_style ? ' style="' . $container_style . '"' : '') . '>
                    ' . ($bg_style ? '<div class="background" style="' . $bg_style . '"></div>' : '') . '
                    <div class="content"' . ($inner_style ? ' style="' . $inner_style . '"' : '') . '>';

            $afficher_element = eden()->{'TunnelsPagesElements_' . $class}()->afficher_element($data_popup, $element);
            if (is_array($afficher_element)) {
                $output .= $afficher_element['output'];
                $js .= $afficher_element['js'];

                if (isset($afficher_element['iframeResizer']) and $afficher_element['iframeResizer']) {
                    $iframeResizer = true;
                }
                if (isset($afficher_element['social_share']) and $afficher_element['social_share']) {
                    $social_share = true;
                }
                if (isset($afficher_element['formulaire_paiement']) and $afficher_element['formulaire_paiement']) {
                    $formulaire_paiement = true;
                }
            } else {
                $output .= $afficher_element;
            }

            $output .= '
                    </div>
                    <div style="clear:both"></div>
                </div>';
        }

        $output .= '</div>';

        if ($element['bloc_alignement'] and 'auto' != $element['bloc_alignement']) {
            $output .= '
                    </div>
                </div>
            </div>
            <div class="row">';
        }

        return ['output' => $output, 'js' => $js, 'iframeResizer' => $iframeResizer, 'social_share' => $social_share, 'formulaire_paiement' => $formulaire_paiement];
    }

    /********************************************************/
    /************************* ADMIN ************************/
    /********************************************************/

    /**
     * admin_display_element function.
     *
     * @param array $element
     *
     * @return string
     */
    public function admin_display_element($element, $data_popup = [], $parent_span = 'span12')
    {
        $output = '';

        if (!$data_popup) {
            $popup = eden()->Popups_Popups()->getPopupById($element['id_popup']);
            if (!$popup) {
                return '
                <div class="alert alert-danger">
                    <i class="fa fa-warning"></i> ' . __('Ce popup n\'existe pas.') . '<br>
                    <a href="' . RouterHelper::generate('app_popups') . '" class="btn btn-small"><i class="fa fa-arrow-left"></i> ' . __('Retour') . '</a>
                </div>';
            }

            $data_popup = [
                'id_popup' => $popup['id_popup'],
                'nom_popup' => $popup['nom'],
                'popup' => $popup,
            ];
        }

        $span = 'span6';
        if ($element['span']) {
            $span = $element['span'];
        }
        if ($element['parent']) {
            $span = 'span12';
        }

        $class = str_replace('span', 'col-md-', $span);
        $class .= str_replace('span', ' col-sm-', $span);
        $span = $class;

        //header
        if ($element['bloc_alignement'] and 'auto' != $element['bloc_alignement']) {
            $class = '';
            switch ($element['bloc_alignement']) {
                case 'center':
                    $class = 'bloc-center';
                    break;
                case 'right':
                    $class = 'pull-right';
                    break;
            }

            $output .= '
                <div class="col-md-12 col-sm-12 box box-masonry" id="' . $element['ID'] . '">
                    <div class="row">
                        <div class="' . $class . ' box ' . (!$element['parent'] ? '' : 'box-element') . ' ' . $span . '">';
        } else {
            $output .= '<div class="box ' . (!$element['parent'] ? 'box-masonry' : 'box-element') . ' ' . $span . '" id="' . $element['ID'] . '">';
        }

        $output .= '<div class="box-content">' . "\n";

        if (isset($_SESSION['editor_level']) and 'expert' == $_SESSION['editor_level']) {
            $class = 'box-icon';
            if ('txt' == $element['objet']) {
                $class = 'box-icon3';
            }
            if ('box' == $element['objet']) {
                $class = 'box-icon box-icon-box box-icon3';
            }
            if ($element['parent']) {
                $class = 'box-icon2';
                if ('txt' == $element['objet']) {
                    $class = 'box-icon4';
                }
                if ('box' == $element['objet']) {
                    $class = 'box-icon box-icon-box box-icon3';
                }
            }

            $output .= '
	            <div class="' . $class . '" ' . (('videoplayer' == $element['objet'] or 'comments' == $element['objet'] or 'box' == $element['objet']) ? 'style="height:50px"' : '') . '>
	                <div class="icons">
	                    <div class="btn-group">
            	            <button class="btn btn-small btn-deplacer"><i class="fa fa-arrows"></i><span class="hidden-for-column3"> ' . __('Déplacer') . '</span></button>
                            <button class="btn btn-small" onclick="EditElement(' . $element['ID'] . ', \'' . $element['objet'] . '\'); return false;"><i class="fa fa-edit"></i><span class="hidden-for-column3"> ' . __('Modifier') . '</span></button>
                            <button class="btn btn-small btn-warning" onclick="DuplicateElement(' . $element['ID'] . ');" style="cursor:pointer"><i class="fa fa-copy"></i><span class="hidden-for-column3"> ' . __('Dupliquer') . '</span></button>';

            if ('box' == $element['objet']) {
                $output .= '<button class="btn btn-small btn-danger" onclick="ConfirmDeleteElement(' . $element['ID'] . ', \'' . $element['objet'] . '\'); return false;"><i class="fa fa-times"></i><span class="hidden-for-column3"> ' . __('Supprimer') . '</span></button>';
            } else {
                $output .= '<button class="btn btn-small btn-danger" onclick="DeleteElement(' . $element['ID'] . '); return false;"><i class="fa fa-times"></i><span class="hidden-for-column3"> ' . __('Supprimer') . '</span></button>';
            }

            if ('col-md-1' != $span and 'col-md-2' != $span and 'col-md-3' != $span and 'col-md-4' != $span and ($parent_span and 'col-md-1' != $parent_span and 'col-md-2' != $parent_span and 'col-md-3' != $parent_span and 'col-md-4' != $parent_span) and 'txt' != $element['objet'] and 'box' != $element['objet']) {
                $output .= '
                        </div>
                        <br>
                        <button class="btn btn-small btn-primary" onclick="$(\'#insertafterelement\').val(' . $element['ID'] . '); $(\'#insertinline\').val(' . $element['id_line'] . '); $(\'#insertinbox\').val(' . $element['parent'] . '); $(\'#AddElement\').modal(\'show\');" style="margin: 0 auto; display: block; margin-top: 5px;"><i class="fa fa-download"></i><span class="hidden-for-column3"> ' . __('Ajouter un élément ci-dessous') . '</span></button>';
            } else {
                $output .= '
                            <button class="btn btn-small btn-primary" onclick="$(\'#insertafterelement\').val(' . $element['ID'] . '); $(\'#insertinline\').val(' . $element['id_line'] . '); $(\'#insertinbox\').val(' . $element['parent'] . '); $(\'#AddElement\').modal(\'show\');"><i class="fa fa-download"></i><span class="hidden-for-column3"> ' . __('Ajouter un élément ci-dessous') . '</span></button>
                        </div>';
            }

            $output .= '
                    </div>
                </div>';
        } elseif ('emptyblock' != $element['objet']) {
            $class = 'box-icon';
            if ('txt' == $element['objet']) {
                $class = 'box-icon3';
            }
            if ('box' == $element['objet']) {
                $class = 'box-icon box-icon-box box-icon3';
            }
            if ($element['parent']) {
                $class = 'box-icon2';
                if ('txt' == $element['objet']) {
                    $class = 'box-icon4';
                }
            }

            $output .= '
                <div class="' . $class . '" ' . (('videoplayer' == $element['objet'] or 'comments' == $element['objet'] or 'box' == $element['objet']) ? 'style="height:50px"' : '') . '>
                    <div class="icons">
                        <button class="btn btn-small" onclick="EditElement(' . $element['ID'] . ', \'' . $element['objet'] . '\'); return false;"><i class="fa fa-edit"></i> ' . ('txt' == $element['objet'] ? __('Paramètres') : __('Modifier')) . '</button>
                    </div>
                    ' . ($element['hide'] ? '<span class="eye-toggle" data-rel="tooltip" title="' . __('Cet élément est caché') . '" data-placement="right"><i class="fa fa-eye-slash"></i></span>' : '') . '
                </div>';
        }

        $styles = eden()->TunnelsPagesElements()->getStyles($element);
        $container_style = $styles['container_style'];
        $bg_style = $styles['bg_style'];
        $inner_style = $styles['inner_style'];

        if ($element['y']) {
            $container_style .= 'height:' . $element['y'] . 'px;';
        }

        $output .= '
            <div class="maincontainer ' . $element['objet'] . '"' . ($container_style ? ' style="' . $container_style . '"' : '') . '>
                ' . ($bg_style ? '<div class="background" style="' . $bg_style . '"></div>' : '') . '
                <div class="content"' . ($inner_style ? ' style="' . $inner_style . '"' : '') . '>';

        $class = ucfirst(str_replace(['-', '_'], '', $element['objet']));
        if (class_exists('TunnelsPagesElements_' . $class)) {
            $output .= eden()->{'TunnelsPagesElements_' . $class}()->admin_display_element($element, $data_popup, $parent_span);
        } else {
            $output .= '<div class="alert alert-danger">' . __('Erreur : type d\'élément non reconnu.') . '</div>';
        }

        $output .= '
    	           </div>
                    <div style="clear:both"></div>
                </div>

        	</div>
		</div>';

        if ($element['bloc_alignement'] and 'auto' != $element['bloc_alignement']) {
            $output .= '
                </div>
            </div>';
        }

        return $output;
    }

    /**
     * getElementForm function.
     *
     * @param int $id_popup
     * @param int $id_element
     *
     * @return array
     */
    public function getElementForm($id_popup, $id_element)
    {
        $output = '';

        $popup = eden()->Popups_Popups()->getPopupById($id_popup);
        if (!$popup) {
            return ['valid' => false, 'message' => __('Ce popup n\'existe pas.')];
        }

        //elements
        $element = $this->getPopupElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __('Cet élément n\'existe pas.')];
        }
        if ($element['id_popup'] != $id_popup) {
            return ['valid' => false, 'message' => __('Cet élément n\'existe pas.')];
        }

        $span = 'span6';
        if ($element['span']) {
            $span = $element['span'];
        }

        //header
        $output .= '
		<div class="row">
		<form class="form" method="post" action="" id="EditElementForm">
            <div id="error"></div>';

        //aide
        $video = '';
        $title = '';

        switch ($element['objet']) {
            case 'box':
                $video = 'element_box';
                $title = __('Box');
                break;

            case 'button_checkout':
                $video = 'element_checkout';
                $title = __('Bouton de paiement');
                break;

            case 'countdown':
                $video = 'element_countdown';
                $title = __('Compte à rebours');
                break;

            case 'comments':
                $video = 'element_comments';
                $title = __('Bloc de commentaires');
                break;

            case 'downloads':
                $video = 'element_downloads';
                $title = __('Téléchargement');
                break;

            case 'emptyblock':
                $video = 'element_emptyblock';
                $title = __('Bloc vide');
                break;

            case 'icone':
                $video = 'element_icone';
                $title = __('Icône');
                break;

            case 'image':
                $video = 'element_image';
                $title = __('Image');
                break;

            case 'link':
                $video = 'element_link';
                $title = __('Lien');
                break;

            case 'module':
                $video = 'element_module';
                $title = __('Module');
                break;

            case 'mp3player':
                $video = 'element_mp3player';
                $title = __('Playeur MP3');
                break;

            case 'optin':
                $video = 'element_optin';
                $title = __('Formulaire de capture');
                break;

            case 'separator':
                $video = 'element_separator';
                $title = __('Séparateur');
                break;

            case 'texte':
                $video = 'element_texte';
                $title = __('Bloc de texte');
                break;

            case 'videoplayer':
                $video = 'element_video';
                $title = __('Vidéo');
                break;
        }

        if ($video and $title) {
            $output .= '
                <div class="alert-modal-video">
                    <a class="pull-left" onclick="CreateModalVideo(\'' . $title . '\', \'' . $video . '\'); return false;">
                        <i class="fa fa-play-circle-o"></i><br>' . __('Vidéo d\'aide') . '
                    </a>
                </div>';
        }

        //start reglages
        $reglages = '';

        if ($element['parent']) {
            $reglages .= '<input type="hidden" name="bloc_alignement" value="auto">';
        } else {
            $reglages .= '
                <div class="form-group">
    				<label class="control-label" for="bloc_alignement1">' . __('Alignement') . '</label>
    				<span class="help-block">' . __('Définit l\'alignement de cet élément sur la page. Si vous choisissez "Automatique", il se positionnera à côté des autres éléments.') . '<br>' . __('Si vous choisissez un autre réglage, aucun autre élément ne sera affiché à côté de cet élément.') . '</span>
    				<div class="controls">
    				    <label class="radio-inline" for="bloc_alignement1" style="margin-right:20px">
        					<input name="bloc_alignement" id="bloc_alignement1" type="radio" value="auto" ' . ((!$element['bloc_alignement'] or 'auto' == $element['bloc_alignement']) ? 'checked="checked"' : '') . '> ' . __('Automatique') . '
        				</label>
    				    <label class="radio-inline" for="bloc_alignement2" style="margin-right:20px">
        					<input name="bloc_alignement" id="bloc_alignement2" type="radio" value="left" ' . ('left' == $element['bloc_alignement'] ? 'checked="checked"' : '') . '> ' . __('Gauche') . '
        				</label>
        				<label class="radio-inline" for="bloc_alignement3" style="margin-right:20px">
        					<input name="bloc_alignement" id="bloc_alignement3" type="radio" value="center" ' . ('center' == $element['bloc_alignement'] ? 'checked="checked"' : '') . '> ' . __('Centré') . '
    					</label>
        				<label class="radio-inline" for="bloc_alignement4" style="margin-right:20px">
        				    <input name="bloc_alignement" id="bloc_alignement4" type="radio" value="right" ' . ('right' == $element['bloc_alignement'] ? 'checked="checked"' : '') . '> ' . __('Droite') . '
        				</label>
    				</div>
    			</div>';
        }

        if ($element['parent']) {
            $reglages .= '<input type="hidden" name="span" value="span12">';
        } elseif (!isset($_SESSION['editor_level']) or 'expert' != $_SESSION['editor_level']) {
            $reglages .= '<input type="hidden" name="span" value="' . $element['span'] . '">';
        } else {
            $reglages .= '
			<div class="form-group">
				<label class="control-label" for="span">' . __('Largeur de l\'élément') . '</label>
				<div class="controls">
					<select name="span" id="span">
					    ' . $this->generateSelectSpan($element['span']) . '
					</select>
				</div>
			</div>';
        }

        $animationSelect = $this->generateSelectAnimation($element['animation']);
        $animationDisappearSelect = $this->generateSelectAnimationDisappear($element['animation_disappear']);

        $reglages .= '
			<div class="form-group">
				<label class="control-label" for="hide">' . __('Cacher cet élément') . '</label>
				<div class="controls">
				    <label for="hide" style="font-weight:normal">
    					<input name="hide" id="hide" type="checkbox" ' . ($element['hide'] ? 'checked' : '') . '> ' . __('Cochez cette case pour ne pas afficher cet élément : il ne sera jamais affiché. Si vous voulez afficher cet élément au bout d\'un certain temps, ne cochez pas cette case.') . '
    				</label>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label" for="hide_tablet">' . __('Cacher cet élément sur tablette') . '</label>
				<div class="controls">
				    <label for="hide_tablet" style="font-weight:normal">
    					<input name="hide_tablet" id="hide_tablet" type="checkbox" ' . ($element['hide_tablet'] ? 'checked' : '') . '> ' . __('Cochez cette case pour ne pas afficher cet élément sur tablette.') . '
    				</label>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label" for="hide_phone">' . __('Cacher cet élément sur téléphone') . '</label>
				<div class="controls">
				    <label for="hide_phone" style="font-weight:normal">
    					<input name="hide_phone" id="hide_phone" type="checkbox" ' . ($element['hide_phone'] ? 'checked' : '') . '> ' . __('Cochez cette case pour ne pas afficher cet élément sur téléphone.') . '
    				</label>
				</div>
			</div>

            <hr>

			<div class="form-group">
				<label class="control-label" for="delay">' . __('Délai d\'apparition') . '</label>
				<span class="help-block">' . __('Vous permet de faire apparaître cet élément plusieurs secondes après l\'ouverture du popup. Si vous définissez "10", cet élément apparaîtra au bout de 10 secondes après l\'ouverture du popup.') . '</span>
				<div class="controls">
				    <div class="input-group">
    					<input class="form-control input-small" name="delay" id="delay" type="number" value="' . ($element['delay'] ?: '0') . '">
    					<span class="input-group-addon">' . __('secondes') . '</span>
                    </div>
					<span class="help-block"><small>' . __('Notez que lorsqu\'un visiteur aura vu cet élément apparaître, ce dernier sera visible dès le prochain chargement de la page pour ce visiteur.') . ' ' . __('Notez aussi qu\'en tant qu\'administrateur, même si vous avez déjà vu cet élément, il n\'apparaîtra qu\'au bout du temps défini, ceci afin de vous permettre de vérifier que tout fonctionne bien.') . '</small></span>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label" for="animation">' . __('Animation') . '</label>
				<div class="controls">
				    <select name="animation">
    					' . $animationSelect . '
    				</select>
    				<button class="btn btn-secondary" onclick="Animate(); return false;" style="padding: 4px 12px;">' . __('Test') . '</button>
    				<div id="TestAnimation">' . __('Animez-moi !') . '</div>
				</div>
            </div>

			<hr>


			<div class="form-group">
				<label class="control-label" for="delay_disappear">' . __('Délai de disparition') . '</label>
				<span class="help-block">' . __('Vous permet de faire disparaître cet élément au bout de plusieurs secondes après l\'ouverture du popup. Si vous définissez "10", cet élément disparaîtra au bout de 10 secondes après l\'ouverture du popup.') . '</span>
				<div class="controls">
				    <div class="input-group">
    					<input class="form-control input-small" name="delay_disappear" id="delay_disappear" type="number" value="' . ($element['delay_disappear'] ?: '0') . '">
    					<span class="input-group-addon">' . __('secondes') . '</span>
    				</div>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label" for="animation_disappear">' . __('Animation') . '</label>
				<div class="controls">
					<select name="animation_disappear">
    					' . $animationDisappearSelect . '
    				</select>
    				<button class="btn btn-secondary" onclick="AnimateDisappear(); return false;" style="padding: 4px 12px;">' . __('Test') . '</button>
    				<div id="TestAnimationDisappear">' . __('Animez-moi !') . '</div>
				</div>
            </div>';

        //apparence
        $bg_color = $bg_opacity = $border_color = '';
        $bg_border = $bg_border_color = $bg_border_radius = null;
        $margin_top = $margin_bottom = $margin_left = $margin_right = 0;
        $padding_top = $padding_bottom = $padding_left = $padding_right = 0;

        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);
            if ($contenu) {
                foreach ($contenu as $contenu_name => $contenu_value) {
                    if ('id_popup' == $contenu_name) {
                        continue;
                    }
                    ${$contenu_name} = $contenu_value;
                }
            }
        }
        if (!$bg_opacity) {
            $bg_opacity = 1;
        }
        if (null === $bg_border) {
            $bg_border = 0;
        }
        if (null === $bg_border_color) {
            $bg_border_color = '';
        }
        if (null === $bg_border_radius) {
            $bg_border_radius = 0;
        }

        $apparence = '
            <div class="row">
                <div class="col-md-5">

                    <div class="form-group">
                        <label class="control-label" for="bg_colorSelector' . $element['ID'] . '">' . __('Couleur de fond') . '</label>
                        <div class="controls">
                            <input class="form-control input-small" type="text" name="bg_color" maxlength="7" data-opacity="' . $bg_opacity . '" id="bg_colorSelector' . $element['ID'] . '" value="' . $bg_color . '">
                            <input type="hidden" name="bg_opacity" id="bg_colorSelector' . $element['ID'] . 'opacity" value="' . $bg_opacity . '">
                       </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label" for="bg_border">' . __('Bordure') . '</label>
                        <div class="controls">
                            <div class="input-group">
                                <input class="form-control input-small" type="number" min="0" name="bg_border" max="99" id="bg_border" value="' . $bg_border . '">
                                <span class="input-group-addon">px</span>
                            </div>
                       </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label" for="bg_border_colorSelector' . $element['ID'] . '">' . __('Couleur de la bordure') . '</label>
                        <div class="controls">
                            <input class="form-control input-small" type="text" name="bg_border_color" maxlength="7" id="bg_border_colorSelector' . $element['ID'] . '" value="' . $bg_border_color . '">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label" for="bg_border_radius">' . __('Arrondi') . '</label>
                        <div class="controls">
                            <div class="input-group">
                                <input class="form-control input-small" type="number" min="0" max="999" name="bg_border_radius" maxlength="7" id="bg_border_radius" value="' . $bg_border_radius . '">
                                <span class="input-group-addon">px</span>
                            </div>
                       </div>
                    </div>

                </div>

                <div class="col-md-7">
                    <div class="style none"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'none\');"><i class="fa fa-times"></i></a></div>
                    <div class="style orange"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'orange\');"></a></div>
                    <div class="style orange2"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'orange2\');"></a></div>
                    <div class="style green"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'green\');"></a></div>
                    <div class="style green2"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'green2\');"></a></div>
                    <div class="style blue"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'blue\');"></a></div>
                    <div class="style blue2"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'blue2\');"></a></div>
                    <div class="style blue3"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'blue3\');"></a></div>
                    <div class="style purple"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'purple\');"></a></div>
                    <div class="style red"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'red\');"></a></div>
                    <div class="style red2"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'red2\');"></a></div>
                    <div class="style grey"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'grey\');"></a></div>
                    <div class="style grey2"><a onclick="UpdateColor(\'' . $element['ID'] . '\', \'grey2\');"></a></div>
                </div>

            </div>

            <div class="form-group">
                <label class="control-label">' . __('Marges externes') . '</label>
                <div class="controls">

                    <div class="row">
                        <div class="col-md-4 col-md-offset-1">
                            <input class="form-control input-small" type="number" min="0" max="999" name="margin_top" value="' . $margin_top . '">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-2">
                            <input class="form-control input-small" type="number" min="0" max="999" name="margin_left" value="' . $margin_left . '">
                        </div>
                        <div class="col-md-2">
                            <input class="form-control input-small" type="number" min="0" max="999" name="margin_right" value="' . $margin_right . '">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 col-md-offset-1">
                            <input class="form-control input-small" type="number" min="0" max="999" name="margin_bottom" value="' . $margin_bottom . '">
                        </div>
                    </div>
                </div>
            </div>


            <div class="form-group">
                <label class="control-label">' . __('Marges internes') . '</label>
                <div class="controls">

                    <div class="row">
                        <div class="col-md-4 col-md-offset-1">
                            <input class="form-control input-small" type="number" min="0" max="999" name="padding_top" id="padding_top" value="' . $padding_top . '">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-2">
                            <input class="form-control input-small" type="number" min="0" max="999" name="padding_left" id="padding_left" value="' . $padding_left . '">
                        </div>
                        <div class="col-md-2">
                            <input class="form-control input-small" type="number" min="0" max="999" name="padding_right" id="padding_right" value="' . $padding_right . '">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 col-md-offset-1">
                            <input class="form-control input-small" type="number" min="0" max="999" name="padding_bottom" id="padding_bottom" value="' . $padding_bottom . '">
                        </div>
                    </div>
                </div>
            </div>


            <script type="text/javascript">
            $("#bg_colorSelector' . $element['ID'] . '").minicolors({
                opacity: true,
                theme: "bootstrap",
                change: function(hex, opacity) {
                    if( !hex ) return;
                    if( opacity ) hex += ", " + opacity;
                    if( opacity )
                        $("#bg_colorSelector' . $element['ID'] . 'opacity").val(opacity);
                },
            });

            $("#bg_border_colorSelector' . $element['ID'] . '").minicolors({
                opacity: false,
                theme: "bootstrap",
            });
            </script>';

        $class = ucfirst(str_replace(['-', '_'], '', $element['objet']));
        if (class_exists('TunnelsPagesElements_' . $class)) {
            $tabs = eden()->Tabs()->displayTabs($element['objet']);
            if ($tabs) {
                $output .= '
        		    <ul class="nav nav-tabs" id="myTab">
        		        ' . $tabs . '
        		        <li><a href="#tab_reglages"><i class="fa fa-cogs"></i> ' . __('Réglages') . '</a></li>
        		        <li><a href="#tab_apparence"><i class="fa fa-picture-o"></i> ' . __('Apparence') . '</a></li>
        		    </ul>
        		    <div class="tab-content">
                        ' . eden()->{'TunnelsPagesElements_' . $class}()->getForm($id_popup, $id_element, $popup, $element) . '
                        <div class="tab-pane" id="tab_reglages">
                            ' . $reglages . '
                        </div>
                        <div class="tab-pane" id="tab_apparence">
                            ' . $apparence . '
                        </div>
                    </div>';
            } else {
                $output .= '
                <ul class="nav nav-tabs" id="myTab">
                    <li class="active"><a href="#tab_home"><i class="fa fa-home"></i> ' . __('Paramètres') . '</a></li>
                    <li><a href="#tab_reglages"><i class="fa fa-cogs"></i> ' . __('Réglages') . '</a></li>
                    <li><a href="#tab_apparence"><i class="fa fa-picture-o"></i> ' . __('Apparence') . '</a></li>
                </ul>

                <div class="tab-content">
                    <div class="tab-pane active" id="tab_home">
                        ' . eden()->{'TunnelsPagesElements_' . $class}()->getForm($id_popup, $id_element, $popup, $element) . '
                    </div>
                    <div class="tab-pane" id="tab_reglages">
                        ' . $reglages . '
                    </div>
                    <div class="tab-pane" id="tab_apparence">
                        ' . $apparence . '
                    </div>
                </div>';
            }
        } else {
            $output .= '<div class="alert alert-danger">' . __('Erreur : type d\'élément non reconnu.') . '</div>';
        }

        $output .= '
			<input type="hidden" name="id_popup" value="' . $id_popup . '" />
			<input type="hidden" name="id_element" value="' . $id_element . '" />
        </form>
        </div>';

        return ['valid' => true, 'html' => $output];
    }

    /**
     * generateSelectSpan function.
     *
     * @param string $span (default: '')
     *
     * @return string
     */
    public function generateSelectSpan($span = '')
    {
        $output = '';
        if (!$span) {
            $span = 'span6';
        }

        $array_spans = [
            'span1' => '8%',
            'span2' => '16%',
            'span3' => '25%',
            'span4' => '33%',
            'span5' => '41%',
            'span6' => '50%',
            'span7' => '58%',
            'span8' => '66%',
            'span9' => '75%',
            'span10' => '83%',
            'span11' => '91%',
            'span12' => '100%',
        ];
        foreach ($array_spans as $_span => $width) {
            $output .= '<option value="' . $_span . '"';
            if ($span == $_span) {
                $output .= ' selected';
            }
            $output .= '>' . $width . '</option>';
        }

        return $output;
    }

    public function insert_element($id_popup, $type, $args = [], $afterElement = 0, $id_line = 0, $id_parent = 0)
    {
        $return_line = false;

        $array_insert = [
            'id_client' => $_SESSION['id_client'],
            'id_popup' => $id_popup,
            'objet' => $type,
            'span' => 'span6',
            'parent' => $id_parent,
            'datecreation' => date('Y-m-d H:i:s'),
            'datemodification' => date('Y-m-d H:i:s'),
        ];

        //idline
        if (!$id_line) {
            $line = eden()->Popups_Lines()->getLastLineByPopup($id_popup);
            if (!$line) {
                //create first line
                $return_line = true;
                $line = eden()->Popups_Lines()->insert_line(['id_popup' => $id_popup]);
                if (!$line) {
                    return ['valid' => false, 'message' => $line['message']];
                }
            }
            $id_line = $line['id_line'];
        }
        $array_insert['id_line'] = $id_line;

        //calcul de la position
        $position = 0;

        if ($afterElement) {
            $element = $this->getPopupElementById($afterElement);
            if ($element) {
                $position = $element['position'];
                ++$position;

                //mise à jour de tous les éléments suivants
                $elements = $this->getPopupElements($id_popup, $id_line);
                if ($elements) {
                    foreach ($elements as $_element) {
                        if ($_element['position'] < $position) {
                            continue;
                        }

                        $temp_position = $_element['position'];
                        ++$temp_position;

                        try {
                            $this->database->updateRows(DB_PREFIX . 'popups_elements', ['position' => $temp_position], "ID='" . $_element['ID'] . "' AND id_popup='$id_popup'");
                        } catch (Eden_Error $e) {
                            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de la position d'un élément.")];
                        }
                    }
                }
            }
        }

        if (!$position and $id_parent) {
            $lastElement = $this->getLastPopupElementByBox($id_popup, $id_parent);
            if ($lastElement) {
                $position = $lastElement['position'];
                ++$position;
            }
        }

        if (!$position) {
            $lastElement = $this->getLastPopupElement($id_popup);
            if ($lastElement) {
                $position = $lastElement['position'];
                ++$position;
            }
        }

        $array_insert['position'] = $position;

        //args
        if ($args) {
            foreach ($args as $typeArg => $content) {
                $array_insert[$typeArg] = $content;
            }
        }

        //span
        $bigelements = ['videoplayer', 'separator', 'module', 'comments', 'optin', 'txt', 'optin_rgpd', 'optin_rgpd_aff'];
        if (!isset($args['span']) and in_array($type, $bigelements)) {
            $array_insert['span'] = 'span12';
        }

        //comments
        if ('comments' == $type) {
            $array_insert['type'] = 'commentaires';
        }

        //button_lightbox
        if ('button_lightbox' == $type) {
            $array_insert['objet'] = 'box';
            $array_insert['eval'] = 1;
        }

        //témoignages
        if ('temoignages' == $type) {
            $array_insert['contenu'] = json_encode([
                'bg_border' => '2',
                'bg_border_color' => '#507cc5',
                'bg_content' => '#88b7d5',
                'padding_top' => '10',
                'padding_bottom' => '10',
                'padding_left' => '10',
                'padding_right' => '10',
            ]);
        }

        //insertion
        try {
            $this->database->insertRow(DB_PREFIX . 'popups_elements', $array_insert);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de l'insertion de l'élément.")];
        }

        $id_element = $this->database->getLastInsertedId();

        if ($return_line) {
            return ['valid' => true, 'id_line' => $id_line, 'id_element' => $id_element, 'return_line' => true];
        }

        return ['valid' => true, 'id_line' => $id_line, 'id_element' => $id_element];
    }

    public function validatepost_element($cleaned_post)
    {
        //initialisation
        $validPost = true;
        $error = '';

        $id_element = filter_var($cleaned_post['id_element'], FILTER_VALIDATE_INT);
        $id_popup = filter_var($cleaned_post['id_popup'], FILTER_VALIDATE_INT);

        if (!$id_element) {
            $validPost = false;
            $error .= '<li>' . __("Erreur fatale : le numéro de l'élément n'a pas été reçu") . '</li>';
        }
        if (!$id_popup) {
            $validPost = false;
            $error .= '<li>' . __("Erreur fatale : le numéro du popup n'a pas été reçu") . '</li>';
        }

        //element
        $element = $this->getPopupElementById($id_element);
        if (!$element) {
            $validPost = false;
            $error .= '<li>' . __("Erreur fatale : cet élément n'existe pas") . '</li>';
        }

        $popup = eden()->Popups_Popups()->getPopupById($id_popup);
        if (!$popup) {
            $validPost = false;
            $error .= '<li>' . __('Erreur : ce popup n\'existe pas') . '</li>';
        }

        if (!$validPost) {
            return ['valid' => $validPost, 'message' => $error];
        }

        $class = ucfirst(str_replace(['-', '_'], '', $element['objet']));
        if (class_exists('TunnelsPagesElements_' . $class)) {
            $validatepost = eden()->{'TunnelsPagesElements_' . $class}()->validatepost_element($cleaned_post);
            $validPost = $validatepost['validPost'];
            $error .= $validatepost['error'];
        } else {
            $validPost = false;
            $error .= '<li>' . __("Erreur fatale : type d'élément inconnu") . '</li>';
        }

        return ['valid' => $validPost, 'message' => $error];
    }

    public function update_element($cleaned_post)
    {
        $id_element = filter_var($cleaned_post['id_element'], FILTER_VALIDATE_INT);
        $id_popup = filter_var($cleaned_post['id_popup'], FILTER_VALIDATE_INT);
        $bloc_alignement = filter_var($cleaned_post['bloc_alignement'], FILTER_SANITIZE_SPECIAL_CHARS);
        $span = filter_var($cleaned_post['span'], FILTER_SANITIZE_SPECIAL_CHARS);
        $delay = filter_var($cleaned_post['delay'], FILTER_VALIDATE_INT);
        $animation = filter_var($cleaned_post['animation'], FILTER_SANITIZE_SPECIAL_CHARS);
        $delay_disappear = filter_var($cleaned_post['delay_disappear'], FILTER_VALIDATE_INT);
        $animation_disappear = filter_var($cleaned_post['animation_disappear'], FILTER_SANITIZE_SPECIAL_CHARS);

        //sauvegarde de l'élément
        $saveElementBackup = $this->saveElementBackup($id_element);
        if (!$saveElementBackup['valid']) {
            return ['valid' => false, 'message' => $saveElementBackup['message']];
        }

        $hide = $hide_tablet = $hide_phone = 0;
        if (isset($cleaned_post['hide'])) {
            $hide = 1;
        }
        if (isset($cleaned_post['hide_tablet'])) {
            $hide_tablet = 1;
        }
        if (isset($cleaned_post['hide_phone'])) {
            $hide_phone = 1;
        }

        $array_update = [
            'span' => $span,
            'bloc_alignement' => $bloc_alignement,
            'delay' => $delay,
            'animation' => $animation,
            'delay_disappear' => $delay_disappear,
            'animation_disappear' => $animation_disappear,
            'hide' => $hide,
            'hide_tablet' => $hide_tablet,
            'hide_phone' => $hide_phone,
            'contenutexte' => '',
            'datemodification' => date('Y-m-d H:i:s'),
        ];

        //element
        $element = $this->getPopupElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur fatale : cet élément n'existe pas.")];
        }

        $class = ucfirst(str_replace(['-', '_'], '', $element['objet']));
        if (!class_exists('TunnelsPagesElements_' . $class)) {
            return ['valid' => false, 'message' => __("Erreur fatale : type d'élément inconnu.")];
        }

        $update = eden()->{'TunnelsPagesElements_' . $class}()->update_element($cleaned_post);
        if ($update) {
            foreach ($update as $key => $val) {
                $array_update[$key] = $val;
            }

            if (isset($update['objet'])) {
                $element['objet'] = $update['objet'];
            }
        }

        //cas spécifiques
        if ('optin' == $element['objet']) {
            unset($array_update['contenutexte']);
        }

        //apparence
        if (!isset($array_update['contenu'])) {
            $array_update['contenu'] = [];
        }

        $array_update['contenu']['bg_color'] = filter_var($cleaned_post['bg_color'], FILTER_SANITIZE_SPECIAL_CHARS);
        $bg_opacity = filter_var($cleaned_post['bg_opacity'], FILTER_SANITIZE_SPECIAL_CHARS);
        if (!$bg_opacity) {
            $bg_opacity = 1;
        }
        $array_update['contenu']['bg_opacity'] = $bg_opacity;

        $array_update['contenu']['bg_border'] = filter_var($cleaned_post['bg_border'], FILTER_VALIDATE_INT);
        $array_update['contenu']['bg_border_color'] = filter_var($cleaned_post['bg_border_color'], FILTER_SANITIZE_SPECIAL_CHARS);
        $array_update['contenu']['bg_border_radius'] = filter_var($cleaned_post['bg_border_radius'], FILTER_VALIDATE_INT);

        $array_update['contenu']['margin_top'] = filter_var($cleaned_post['margin_top'], FILTER_VALIDATE_INT);
        $array_update['contenu']['margin_bottom'] = filter_var($cleaned_post['margin_bottom'], FILTER_VALIDATE_INT);
        $array_update['contenu']['margin_left'] = filter_var($cleaned_post['margin_left'], FILTER_VALIDATE_INT);
        $array_update['contenu']['margin_right'] = filter_var($cleaned_post['margin_right'], FILTER_VALIDATE_INT);

        $array_update['contenu']['padding_top'] = filter_var($cleaned_post['padding_top'], FILTER_VALIDATE_INT);
        $array_update['contenu']['padding_bottom'] = filter_var($cleaned_post['padding_bottom'], FILTER_VALIDATE_INT);
        $array_update['contenu']['padding_left'] = filter_var($cleaned_post['padding_left'], FILTER_VALIDATE_INT);
        $array_update['contenu']['padding_right'] = filter_var($cleaned_post['padding_right'], FILTER_VALIDATE_INT);

        $array_update['contenu'] = json_encode($array_update['contenu']);

        //strip PHP tags
        if (isset($array_update['contenutexte']) and false !== strpos($array_update['contenutexte'], '<?')) {
            $array_update['contenutexte'] = preg_replace('/\<(\?|\%)(.*)(\?|\%)\>/s', '', $array_update['contenutexte']);
        }

        //mise à jour
        try {
            $this->database->updateRows(DB_PREFIX . 'popups_elements', $array_update, "id_popup=$id_popup AND ID=$id_element");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
        }

        return ['valid' => true];
    }

    public function update_element_duree($id_element, $duree)
    {
        try {
            $this->database->updateRows(DB_PREFIX . 'popups_elements', ['duree' => $duree], "ID='$id_element'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour d'un élément.")];
        }

        return ['valid' => true];
    }

    public function update_element_txt($id_element, $texte)
    {
        $element = $this->getPopupElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur fatale : cet élément n'existe pas.")];
        }

        $popup = eden()->Popups_Popups()->getPopupById($element['id_popup']);
        if (!$popup) {
            return ['valid' => false, 'message' => __("Erreur fatale : ce popup n'existe pas.")];
        }

        $saveElementBackup = $this->saveElementBackup($id_element);
        if (!$saveElementBackup['valid']) {
            return ['valid' => false, 'message' => $saveElementBackup['message']];
        }

        $texte = urldecode($texte);
        $texte = addslashes($texte);

        //strip PHP tags
        if (false !== strpos($texte, '<?')) {
            $texte = preg_replace('/\<(\?|\%)(.*)(\?|\%)\>/s', '', $texte);
        }

        //mise à jour
        try {
            $this->database->updateRows(DB_PREFIX . 'popups_elements', ['contenutexte' => $texte, 'datemodification' => date('Y-m-d H:i:s')], "ID='$id_element'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
        }

        return ['valid' => true];
    }

    public function update_ajax_elements($id_popup, $id_line, $elements, $type = '')
    {
        $i = 0;
        foreach ($elements as $_element) {
            $element = $this->getPopupElementById($_element);
            if (!$element) {
                return ['valid' => false, 'message' => __("Erreur : l'élément n'existe pas")];
            }
            if ($type and $element['objet'] == $type) {
                continue;
            }

            //mise à jour
            try {
                $this->database->updateRows(DB_PREFIX . 'popups_elements', ['position' => $i, 'datemodification' => date('Y-m-d H:i:s')], "ID='$_element' AND id_line='$id_line'");
            } catch (Eden_Error $e) {
                return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
            }
            ++$i;
        }

        return ['valid' => true];
    }

    public function replace_elements($field, $original, $final)
    {
        try {
            $this->database->updateRows(DB_PREFIX . 'popups_elements', [$field => $final], "$field='$original'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément")];
        }

        return ['valid' => true];
    }

    public function duplicate_element($id_element)
    {
        $id_element = filter_var($id_element, FILTER_VALIDATE_INT);

        $element = $this->getPopupElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur fatale : cet élément n'existe pas")];
        }

        $id_popup = $element['id_popup'];
        $id_line = $element['id_line'];

        //find position
        $position = 1;
        if (isset($cleaned_post['position'])) {
            $position = filter_var($cleaned_post['position'], FILTER_VALIDATE_INT);
        } else {
            $position = $element['position'];
            ++$position;

            //mise à jour de tous les éléments suivants
            $elements = $this->getPopupElements($id_popup, $id_line);
            if ($elements) {
                foreach ($elements as $_element) {
                    if ($_element['position'] < $position) {
                        continue;
                    }

                    $temp_position = $_element['position'];
                    ++$temp_position;

                    try {
                        $this->database->updateRows(DB_PREFIX . 'popups_elements', ['position' => $temp_position], "ID='" . $_element['ID'] . "' AND id_popup='$id_popup'");
                    } catch (Eden_Error $e) {
                        return ['valid' => false, 'message' => __('Erreur lors de la mise à jour de la position des éléments suivants.')];
                    }
                }
            }
        }

        //insertion
        $array_insert = $element;
        $array_insert['position'] = $position;
        $array_insert['datecreation'] = date('Y-m-d H:i:s');
        $array_insert['datemodification'] = date('Y-m-d H:i:s');
        unset($array_insert['ID']);

        try {
            $this->database->insertRow(DB_PREFIX . 'popups_elements', $array_insert);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la duplication de l'élément.")];
        }

        $new_id_element = $this->database->getLastInsertedId();
        if (!$new_id_element) {
            return ['valid' => false, 'message' => __("Erreur lors de la duplication de l'élément.")];
        }

        //elements
        if ('box' == $element['objet']) {
            //duplication des éléments de cette box
            $sub_elements = $this->getPopupElementsByBox($id_popup, $id_element);
            if ($sub_elements) {
                foreach ($sub_elements as $sub_element) {
                    $array_insert = $sub_element;
                    unset($array_insert['ID']);
                    $array_insert['parent'] = $new_id_element;
                    $array_insert['datecreation'] = date('Y-m-d H:i:s');
                    $array_insert['datemodification'] = date('Y-m-d H:i:s');

                    //insertion
                    try {
                        $this->database->insertRow(DB_PREFIX . 'popups_elements', $array_insert);
                    } catch (Eden_Error $e) {
                        return ['valid' => false, 'message' => __("Erreur lors de la duplication de l'élément.")];
                    }
                }
            }
        }

        return ['valid' => true, 'id_element' => $new_id_element];
    }

    public function delete_element($id_popup, $id_element)
    {
        $popup = eden()->Popups_Popups()->getPopupById($id_popup);
        if (!$popup) {
            return ['valid' => false, 'message' => __("Erreur fatale : ce popup n'existe pas.")];
        }

        $element = $this->getPopupElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas")];
        }
        if ($element['id_popup'] != $id_popup) {
            return ['valid' => false, 'message' => __("Erreur : cet élément n'existe pas")];
        }

        $saveElementBackup = $this->saveElementBackup($id_element);
        if (!$saveElementBackup['valid']) {
            return ['valid' => false, 'message' => $saveElementBackup['message']];
        }

        $filter = [];
        $filter[] = ['ID=%s', $id_element];
        $filter[] = ['id_popup=%s', $id_popup];

        try {
            $this->database->deleteRows(DB_PREFIX . 'popups_elements', $filter);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la suppression de l'élément.")];
        }

        $filter = [];
        $filter[] = ['parent=%s', $id_element];
        $filter[] = ['id_popup=%s', $id_popup];

        try {
            $this->database->deleteRows(DB_PREFIX . 'popups_elements', $filter);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la suppression de l'élément.")];
        }

        return ['valid' => true];
    }

    /********************************************************/
    /*********************** BACKUPS ************************/
    /********************************************************/

    public function getLastBackupByelement($id_element)
    {
        $result = $this->database
            ->search(DB_PREFIX . 'popups_elements_backups')
            ->addFilter("ID='$id_element' AND id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('id_backup', 'DESC')
            ->getRow();

        return $result;
    }

    public function getLast4Backups()
    {
        $result = $this->database
            ->search(DB_PREFIX . 'popups_elements_backups')
            ->addFilter("id_client='" . $_SESSION['id_client'] . "'")
            ->addSort('id_backup', 'DESC')
            ->setStart(0)
            ->setRange(4)
            ->getRows();

        return $result;
    }

    public function saveElementBackup($id_element)
    {
        $element = $this->getPopupElementById($id_element);
        if (!$element) {
            return ['valid' => false, 'message' => __("Cet élément n'existe pas.")];
        }

        $last4 = $this->getLast4Backups();
        if (count($last4) >= 4) {
            $last_backup = array_pop($last4);

            try {
                $this->database->query('DELETE FROM ' . DB_PREFIX . "popups_elements_backups WHERE id_backup < '" . $last_backup['id_backup'] . "' AND id_client='" . $_SESSION['id_client'] . "'");
            } catch (Eden_Error $e) {
                return ['valid' => false, 'message' => __("Erreur lors de la suppression de l'élément.")];
            }
        }

        $array_insert = $element;
        $array_insert['id_client'] = $_SESSION['id_client'];
        $array_insert['datecreation'] = date('Y-m-d H:i:s');
        $array_insert['datemodification'] = date('Y-m-d H:i:s');

        try {
            $this->database->insertRow(DB_PREFIX . 'popups_elements_backups', $array_insert);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de l'insertion de l'élément.")];
        }

        return ['valid' => true];
    }

    public function restore_element($id_element)
    {
        $last_backup = $this->getLastBackupByelement($id_element);
        if (!$last_backup) {
            return ['valid' => false, 'message' => __('Aucune sauvegarde trouvée.')];
        }

        $element = $this->getPopupElementById($id_element);
        if (!$element) {
            $array_insert = $last_backup;
            unset($array_insert['id_backup']);
            unset($array_insert['id_client']);
            unset($array_insert['id_col_backup']);

            try {
                $this->database->insertRow(DB_PREFIX . 'popups_elements', $array_insert);
            } catch (Eden_Error $e) {
                return ['valid' => false, 'message' => __("Erreur lors de l'insertion de l'élément.")];
            }
        } else {
            $array_update = $last_backup;
            unset($array_update['id_backup']);
            unset($array_update['id_client']);
            unset($array_update['id_col_backup']);

            try {
                $this->database->updateRows(DB_PREFIX . 'popups_elements', $array_update, "ID='$id_element'");
            } catch (Eden_Error $e) {
                return ['valid' => false, 'message' => __("Erreur lors de la mise à jour de l'élément.")];
            }
        }

        return ['valid' => true];
    }

    public function delete_last_elements()
    {
        $filter = [];
        $filter[] = ['datecreation<=%s', date('Y-m-d') . ' 00:00:00'];

        try {
            $this->database->deleteRows(DB_PREFIX . 'popups_elements_backups', $filter);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de la suppression de l'élément.")];
        }

        return ['valid' => true];
    }

    /********************************************************/
    /********************** FUNCTIONS ***********************/
    /********************************************************/

    public function getYoutubeId($url)
    {
        $videoId = '';

        $parse = parse_url($url);

        if (isset($parse['query'])) {
            $query = explode('&', $parse['query']);
            if ($query) {
                foreach ($query as $_query) {
                    if (false !== strpos($_query, 'v=')) {
                        $videoId = str_replace('v=', '', $_query);
                    }
                }
            }
        }

        if (!$videoId and isset($parse['path'])) {
            $path = explode('/', $parse['path']);
            $path = array_filter($path);
            if (in_array('v', $path) or in_array('embed', $path)) {
                foreach ($path as $id => $_path) {
                    if ('v' == $_path or 'embed' == $_path) {
                        $videoId = $path[++$id];
                        break;
                    }
                }
            }

            if (!$videoId and 1 == count($path)) {
                $videoId = $path[1];
            }
        }

        if ('watch' == $videoId) {
            $videoId = '';
        }

        return $videoId;
    }

    public function generateSelectFieldType($selected_type = '')
    {
        $types = [
            'text' => 'Champ texte',
            'textarea' => 'Champ texte long',
            'number' => 'Nombre',
            'hidden' => 'Champ caché',
            'select' => 'Liste déroulante',
        ];

        $output = '';
        foreach ($types as $type => $nom) {
            $output .= '<option value="' . $type . '"';
            if ($selected_type == $type) {
                $output .= ' selected';
            }
            $output .= '>';

            $output .= $nom . '</option>';
        }

        return $output;
    }

    public function generateSelectAnimation($animation = '')
    {
        $output = '';

        $animations = [
            __('Apparition') => [
                'fadeIn' => __('Apparition'),
                'fadeInDown' => __('Apparition d\'en haut'),
                'fadeInDownBig' => __('Apparition d\'en haut (plus long)'),
                'fadeInLeft' => __('Apparition de gauche'),
                'fadeInLeftBig' => __('Apparition de gauche (plus long)'),
                'rollIn' => __('Apparition de gauche (et rotation)'),
                'fadeInRight' => __('Apparition de droite'),
                'fadeInRightBig' => __('Apparition de droite (plus long)'),
                'lightSpeedIn' => __('Apparition de droite (et distorsion)'),
                'fadeInUp' => __('Apparition du bas'),
                'fadeInUpBig' => __('Apparition du bas (plus long)'),
            ],

            __('Effets spéciaux') => [
                'flash' => __('Flash'),
                'pulse' => __('Impulsion'),
                'rubberBand' => __('Etirer'),
                'shake' => __('Secouer'),
                'swing' => __('Swing'),
                'tada' => __('Tada'),
                'wobble' => __('Osciller'),
                'jello' => __('Distorsion'),
            ],

            __('Rebonds') => [
                'bounceIn' => __('Rebond'),
                'bounce' => __('Plusieurs rebonds du haut'),
                'bounceInDown' => __('Rebond du haut'),
                'bounceInLeft' => __('Rebond de gauche'),
                'bounceInRight' => __('Rebond de droite'),
                'bounceInUp' => __('Rebond d\'en bas'),
            ],

            __('Rotations 3D') => [
                'flip' => __('Rotation 3D'),
                'flipInX' => __('Rotation 3D horizontale'),
                'flipInY' => __('Rotation 3D verticale'),
            ],

            __('Rotations') => [
                'rotateIn' => __('Rotation'),
                'rotateInDownLeft' => __('Rotation vers le bas (gauche)'),
                'rotateInDownRight' => __('Rotation vers le bas (droite)'),
                'rotateInUpLeft' => __('Rotation vers le haut (gauche)'),
                'rotateInUpRight' => __('Rotation vers le haut (droite)'),
            ],

            __('Apparitions coulissantes') => [
                'slideInUp' => __('Apparition d\'en bas'),
                'slideInDown' => __('Apparition d\'en haut'),
                'slideInLeft' => __('Apparition de gauche'),
                'slideInRight' => __('Apparition de droite'),
            ],

            __('Zooms') => [
                'zoomIn' => __('Zoom'),
                'zoomInDown' => __('Zoom provenant d\'en haut'),
                'zoomInLeft' => __('Zoom provenant de gauche'),
                'zoomInRight' => __('Zoom provenant de droite'),
                'zoomInUp' => __('Zoom provenant d\'en bas'),
            ],
        ];

        foreach ($animations as $_animation_group => $_animations) {
            $output .= '<optgroup label="' . $_animation_group . '">';

            foreach ($_animations as $_animation => $_animation_name) {
                $output .= '<option value="' . $_animation . '"';
                if ($animation and $animation == $_animation) {
                    $output .= ' selected';
                }
                $output .= '>' . $_animation_name . '</option>';
            }
            $output .= '</optgroup>';
        }

        return $output;
    }

    public function generateSelectAnimationDisappear($animation = '')
    {
        $output = '';

        $animations = [
            __('Disparitions') => [
                'fadeOut' => __('Disparition'),
                'fadeOutDown' => __('Disparition vers le bas'),
                'fadeOutDownBig' => __('Disparition vers le bas (plus long)'),
                'fadeOutLeft' => __('Disparition vers la gauche'),
                'fadeOutLeftBig' => __('Disparition vers la gauche (plus long)'),
                'fadeOutRight' => __('Disparition vers la droite'),
                'fadeOutRightBig' => __('Disparition vers la droite (plus long)'),
                'lightSpeedOut' => __('Disparition vers la droite (et distorsion)'),
                'rollOut' => __('Disparition vers la droite (et rotation)'),
                'fadeOutUp' => __('Disparition vers le haut'),
                'fadeOutUpBig' => __('Disparition vers le haut (plus long)'),
            ],

            __('Rebonds et disparition') => [
                'bounceOut' => __('Rebond et disparition'),
                'bounceOutDown' => __('Rebond vers le bas'),
                'bounceOutLeft' => __('Rebond vers la gauche'),
                'bounceOutRight' => __('Rebond vers la droite'),
                'bounceOutUp' => __('Rebond vers le haut'),
            ],

            __('Rotations 3D') => [
                'flipOutX' => __('Rotation 3D horizontale'),
                'flipOutY' => __('Rotation 3D verticale'),
            ],

            __('Rotations') => [
                'rotateOut' => __('Rotation'),
                'rotateOutDownLeft' => __('Rotation vers le bas (gauche)'),
                'rotateOutDownRight' => __('Rotation vers le bas (droite)'),
                'rotateOutUpLeft' => __('Rotation vers le haut (gauche)'),
                'rotateOutUpRight' => __('Rotation vers le haut (droite)'),
            ],

            __('Disparitions coulissantes') => [
                'slideOutUp' => __('Disparition coulissante vers le haut'),
                'slideOutDown' => __('Disparition coulissante vers le bas'),
                'slideOutLeft' => __('Disparition coulissante vers la gauche'),
                'slideOutRight' => __('Disparition coulissante vers la droite'),
            ],

            __('Zooms arrière') => [
                'zoomOut' => __('Zoom arrière'),
                'zoomOutDown' => __('Zoom vers le bas'),
                'zoomOutLeft' => __('Zoom vers la gauche'),
                'zoomOutRight' => __('Zoom vers la droite'),
                'zoomOutUp' => __('Zoom vers le haut'),
            ],
        ];

        foreach ($animations as $_animation_group => $_animations) {
            $output .= '<optgroup label="' . $_animation_group . '">';

            foreach ($_animations as $_animation => $_animation_name) {
                $output .= '<option value="' . $_animation . '"';
                if ($animation and $animation == $_animation) {
                    $output .= ' selected';
                }
                $output .= '>' . $_animation_name . '</option>';
            }
            $output .= '</optgroup>';
        }

        return $output;
    }
}
