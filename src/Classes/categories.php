<?php

use Learnybox\Services\DispatcherService;
use Learnybox\Services\Categorie\CategorieService;

/**
 * Categories class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Categories extends Eden_Class
{
    private $database;
    private CategorieService $categorieService;

    public function __construct(
        CategorieService $categorieService
    ) {
        $this->database = MySQL::getInstance();
        $this->categorieService = $categorieService;
    }

    /********************************************************/
    /*********************** CATÉGORIES *********************/
    /********************************************************/

    /**
     * getCategorieById function.
     *
     * @param int $id_categorie
     *
     * @return array
     */
    public function getCategorieById($id_categorie, $id_client = null)
    {
        if (null === $id_client) {
            $id_client = $_SESSION['id_client'];
        }

        $result = $this->database
            ->search(DB_PREFIX . 'categories')
            ->addFilter("id_categorie='$id_categorie' AND id_client='$id_client'")
            ->getRow();

        return $result;
    }

    /**
     * getCategorieByName function.
     *
     * @param string $nom_categorie
     *
     * @return array
     */
    public function getCategorieByName($nom_categorie)
    {
        $result = $this->database
            ->search(DB_PREFIX . 'categories')
            ->addFilter("nom='" . addslashes($nom_categorie) . "' AND id_client='" . $_SESSION['id_client'] . "'")
            ->getRow();

        return $result;
    }

    /**
     * getCategorieByPermalink function.
     *
     * @param string $permalink
     *
     * @return array
     */
    public function getCategorieByPermalink($permalink, $id_client = null)
    {
        if (null === $id_client) {
            $id_client = $_SESSION['id_client'];
        }

        $result = $this->database
            ->search(DB_PREFIX . 'categories')
            ->addFilter("permalink='" . addslashes($permalink) . "' AND id_client='$id_client'")
            ->getRow();

        return $result;
    }

    /**
     * getAllCategories function.
     *
     * @return array
     */
    public function getAllCategories($id_domaine = null)
    {
        if ($id_domaine === null) {
            $result = $this->database
                ->search(DB_PREFIX . 'categories')
                ->addFilter("id_client='" . $_SESSION['id_client'] . "'")
                ->addSort('nom', 'ASC')
                ->getRows();
        } else {
            $result = $this->database
                ->search(DB_PREFIX . 'categories')
                ->addFilter("id_domaine='$id_domaine' AND id_client='" . $_SESSION['id_client'] . "'")
                ->addSort('nom', 'ASC')
                ->getRows();
        }


        return $result;
    }

    /**
     * @param int $id_domaine
     * @return int
     */
    public function getCountCategories($id_domaine = 0)
    {
        if (!$id_domaine) {
            $result = $this->database
                ->search(DB_PREFIX . 'categories')
                ->addFilter("id_client='" . $_SESSION['id_client'] . "'")
                ->getTotal();
        } else {
            $result = $this->database
                ->search(DB_PREFIX . 'categories')
                ->addFilter("id_domaine='$id_domaine' AND id_client='" . $_SESSION['id_client'] . "'")
                ->getTotal();
        }

        if (!$result) {
            return 0;
        }

        return $result;
    }

    /**
     * generateSelectCategories function.
     *
     * @param int $id_categorie (default: 0)
     *
     * @return string
     */
    public function generateSelectCategories($id_categorie = 0)
    {
        $output = '<option value="0">' . __('Toutes les catégories') . '</option>';

        $categories = $this->getAllCategories();
        if ($categories) {
            foreach ($categories as $id => $_categorie) {
                $output .= '<option value="' . $_categorie['id_categorie'] . '"';
                if ($id_categorie and $id_categorie == $_categorie['id_categorie']) {
                    $output .= ' selected';
                }
                $output .= '>' . $_categorie['nom'] . '</option>';
            }
        }

        return $output;
    }

    /********************************************************/
    /************************* SITE *************************/
    /********************************************************/

    /**
     * generate_categories_cloud function.
     *
     * @param int $id_categorie (default: 0)
     * @param string $type (default: '')
     *
     * @return string
     */
    public function generateCategoriesCloud($id_categorie = 0, $type = '', $id_domaine = null)
    {
        $output = '';

        if ($id_domaine === null) {
            $id_domaine = $_SESSION['actual_domaine_id'];
        }

        $categorie = false;
        if ($id_categorie) {
            $categorie = $this->getCategorieById($id_categorie);
        } else {
            $permalink_categorie = eden()->{DispatcherService::class}()->returnType('param');
            //$nom_categorie = urldecode($nom_categorie);
            if ($permalink_categorie) {
                //$nom_categorie = str_replace('%20', ' ', $nom_categorie);
                $categorie = $this->getCategorieByPermalink($permalink_categorie);
            }
        }

        if (!$type) {
            if (!$_SESSION['id_client']) {
                $type = 'blog';
            } else {
                $type = 'articles';
            }
        }

        $categories = $this->getAllCategories($id_domaine);
        if (!$categories) {
            return;
        }

        $articles = eden()->Articles()->getCountArticlesPubliesGroupByCategorie();
        if ($articles) {
            $articles = array_change_key($articles, 'id_categorie', 'nb_articles');
        }

        $output .= '<div class="categories">';

        //toutes les catégories
        if ($categorie) {
            $output .= '<a class="type" href="' . Tools::getLink($id_domaine, 'site', $type) . '">' . __('Tous les') . ' ' . ('blog' == $type ? 'articles' : $type) . '</a><br>';
        } else {
            $output .= '<a class="type" href="' . Tools::getLink($id_domaine, 'site', $type) . '"><span class="label label-info">' . __('Tous les') . ' ' . ('blog' == $type ? 'articles' : $type) . '</span></a><br>';
        }

        foreach ($categories as $_categorie) {
            $categorie_link = Tools::getLink($id_domaine, 'site', $type, $_categorie['permalink']);
            if ($categorie and $categorie['id_categorie'] == $_categorie['id_categorie']) {
                $output .= '<a class="categorie" href="' . $categorie_link . '"><span class="label label-info">' . $_categorie['nom'] . '</span></a>';
            } else {
                $output .= '<a class="categorie" href="' . $categorie_link . '">' . $_categorie['nom'] . '</a>';
            }

            if ('articles' == $type or 'blog' == $type) {
                //$nb_articles = eden()->Articles()->getCountArticlesPubliesByCategorie($_categorie['id_categorie']);
                $nb_articles = 0;
                if (isset($articles[$_categorie['id_categorie']])) {
                    $nb_articles = $articles[$_categorie['id_categorie']];
                }

                if ($nb_articles and 0 != $nb_articles) {
                    $output .= ' (' . $nb_articles . ')';
                }
            }

            $output .= '<br>';
        }

        $output .= '</div>';

        return $output;
    }

    /**
     * strCategorie function.
     * format categorie name.
     *
     * @param int $id_categorie
     *
     * @return string
     */
    public function strCategorie($id_categorie)
    {
        $type = '';

        $categorie = $this->getCategorieById($id_categorie);
        if (!$categorie) {
            return '<span class="label label-default">' . __('Catégorie inconnue') . '</span>';
        }

        $array_labels = ['', 'label-warning', 'label-danger', 'label-info', 'label-success', 'label-inverse'];
        $label = $array_labels[($id_categorie % 6)];

        $type .= '<span class="label ' . $label . '">' . $categorie['nom'] . '</span>';

        return $type;
    }

    /********************************************************/
    /************************* ADMIN ************************/
    /********************************************************/

    /**
     * validatepost_categorie_add function.
     *
     * @param array $postarray
     *
     * @return array
     */
    public function validatepost_categorie_add($postarray)
    {
        //initialisation
        $validPost = true;
        $errorString = '';

        $nom = filter_var($postarray['nom'], FILTER_SANITIZE_SPECIAL_CHARS);
        if (!$nom) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez entrer un nom') . '</li>';
        }

        return ['valid' => $validPost, 'message' => $errorString];
    }

    /**
     * validatepost_categorie_edit function.
     *
     * @param array $postarray
     *
     * @return array
     */
    public function validatepost_categorie_edit($postarray)
    {
        //initialisation
        $validPost = true;
        $errorString = '';

        $id_categorie = filter_var($postarray['id_categorie'], FILTER_VALIDATE_INT);
        $nom = filter_var($postarray['nom'], FILTER_SANITIZE_SPECIAL_CHARS);

        if (!$id_categorie) {
            $validPost = false;
            $errorString .= '<li>' . __('Erreur fatale : le numéro de la catégorie n\'a pas été reçu') . '</li>';
        }
        if (!$nom) {
            $validPost = false;
            $errorString .= '<li>' . __('Veuillez entrer le nom de la catégorie') . '</li>';
        }

        return ['valid' => $validPost, 'message' => $errorString];
    }

    /**
     * insert_categorie function.
     *
     * @param array $cleaned_post
     *
     * @return array
     */
    public function insert_categorie($cleaned_post)
    {
        $nom = filter_var($cleaned_post['nom'], FILTER_SANITIZE_SPECIAL_CHARS);
        $permalink = filter_var($cleaned_post['permalink'], FILTER_SANITIZE_SPECIAL_CHARS);
        $seo_titre = filter_var($cleaned_post['seo_titre'], FILTER_SANITIZE_SPECIAL_CHARS);
        $seo_description = filter_var($cleaned_post['seo_description'], FILTER_SANITIZE_SPECIAL_CHARS);

        $description = '';
        if (isset($_POST['description']) and '' != trim($_POST['description'])) {
            $description = filter_input(INPUT_POST, 'description', FILTER_UNSAFE_RAW);
        }

        //permalink
        if (!$permalink) {
            $permalink = $nom;
        }

        try {
            $permalink = $this->categorieService->getValidPermalink($permalink);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la génération du lien unique.')];
        }

        //domaine
        $id_domaine = 0;
        if (isset($cleaned_post['id_domaine'])) {
            $id_domaine = filter_var($cleaned_post['id_domaine'], FILTER_VALIDATE_INT);
        }

        $array_insert = [
            'id_client' => $_SESSION['id_client'],
            'id_domaine' => $id_domaine,
            'nom' => $nom,
            'permalink' => $permalink,
            'description' => $description,
            'seo_titre' => $seo_titre,
            'seo_description' => $seo_description,
            'date' => date('Y-m-d H:i:s'),
        ];

        //insertion
        try {
            $this->database->insertRow(DB_PREFIX . 'categories', $array_insert);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de la catégorie.")];
        }

        return ['valid' => true];
    }

    /**
     * update_categorie function.
     *
     * @param array $cleaned_post
     *
     * @return array
     */
    public function update_categorie($cleaned_post)
    {
        $id_categorie = filter_var($cleaned_post['id_categorie'], FILTER_VALIDATE_INT);
        $nom = filter_var($cleaned_post['nom'], FILTER_SANITIZE_SPECIAL_CHARS);
        $permalink = filter_var($cleaned_post['permalink'], FILTER_SANITIZE_SPECIAL_CHARS);
        $seo_titre = filter_var($cleaned_post['seo_titre'], FILTER_SANITIZE_SPECIAL_CHARS);
        $seo_description = filter_var($cleaned_post['seo_description'], FILTER_SANITIZE_SPECIAL_CHARS);

        $description = '';
        if (isset($_POST['description']) and '' != trim($_POST['description'])) {
            $description = filter_input(INPUT_POST, 'description', FILTER_UNSAFE_RAW);
        }

        //permalink
        if (!$permalink) {
            $permalink = $nom;
        }

        try {
            $permalink = $this->categorieService->getValidPermalink($permalink, $id_categorie);
        } catch (\Exception $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la génération du lien unique.')];
        }

        //domaine
        $id_domaine = 0;
        if (isset($cleaned_post['id_domaine'])) {
            $id_domaine = filter_var($cleaned_post['id_domaine'], FILTER_VALIDATE_INT);
        }

        $array_update = [
            'id_domaine' => $id_domaine,
            'nom' => $nom,
            'permalink' => $permalink,
            'description' => $description,
            'seo_titre' => $seo_titre,
            'seo_description' => $seo_description,
        ];

        //mise à jour
        try {
            $this->database->updateRows(DB_PREFIX . 'categories', $array_update, "id_categorie='$id_categorie' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la mise à jour de la catégorie.')];
        }

        return ['valid' => true];
    }

    /**
     * delete_categorie function.
     *
     * @param int $id_categorie
     *
     * @return array
     */
    public function delete_categorie($id_categorie)
    {
        $filter = [];
        $filter[] = ['id_categorie=%s', $id_categorie];
        $filter[] = ['id_client=%s', $_SESSION['id_client']];

        try {
            $this->database->deleteRows(DB_PREFIX . 'categories', $filter);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la suppression de la catégorie.')];
        }

        return ['valid' => true];
    }
}
