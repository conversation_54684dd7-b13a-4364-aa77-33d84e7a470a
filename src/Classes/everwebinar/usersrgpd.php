<?php

/**
 * EverWebinar_Usersrgpd class.
 *
 * @extends Eden_Class
 * @category Model
 * @package  TL
 * <AUTHOR> <<EMAIL>>
 * @link     http://thieumlabs.com
 */
class EverWebinar_Usersrgpd extends Eden_Class
{
    private $database;

    public function __construct()
    {
        $this->database = MySQL::getInstance();
    }


    /********************************************************/
    /********************* UTILISATEURS *********************/
    /********************************************************/


    /**
     * getUserHistory function.
     *
     * @access public
     * @param  int $id_user
     * @param  int $id_client
     * @return array
     */
    public function getUserHistory($id_user, $id_client = 0)
    {
        if (!$id_client) {
            $id_client = $_SESSION['id_client'];
        }
        $result = $this->database
            ->search('ew_webinaire_users_rgpd')
            ->addFilter("id_user='$id_user' AND id_client='$id_client'")
            ->getRows();
        return $result;
    }


    /**
     * getFirstHistory function.
     *
     * @access public
     * @param  int $id_user
     * @param  string $type (default: normal)
     * @param  int $id_client
     * @return array
     */
    public function getFirstHistory($id_user, $type = 'normal', $id_client = 0)
    {
        if (!$id_client) {
            $id_client = $_SESSION['id_client'];
        }
        $result = $this->database
            ->search('ew_webinaire_users_rgpd')
            ->addFilter("id_user='$id_user' AND type='$type' AND id_client='$id_client'")
            ->addSort('id_history', 'ASC')
            ->getRow();
        return $result;
    }


    /********************************************************/
    /************************* ADMIN ************************/
    /********************************************************/

    /**
     * insertHistory function.
     *
     * @access public
     * @param  int $userId
     * @param  array $userInfos
     * @return array
     */
    public function insertHistory($userId, $userInfos, ?int $idClient = null)
    {
        if ($idClient === null) {
            $idClient = $_SESSION['id_client'];
        }

        if (isset($userInfos['rgpd']) && $userInfos['rgpd']) {
            //insertion
            $arrayInsert = [
                'id_client' => $idClient,
                'id_user' => $userId,
                'active' => $userInfos['rgpd'],
                'type' => 'normal',
                'date' => $userInfos['rgpd_date'],
                'notice' => $userInfos['rgpd_notice'],
                'date_creation' => date('Y-m-d H:i:s'),
            ];
            try {
                $this->database->insertRow('ew_webinaire_users_rgpd', $arrayInsert);
            } catch (Eden_Error $e) {
                return ["valid" => false, "message" => __("Erreur lors de l'enregistrement des données.")];
            }
        }

        if (isset($userInfos['rgpd_aff']) && $userInfos['rgpd_aff']) {
            //insertion
            $arrayInsert = [
                'id_client' => $idClient,
                'id_user' => $userId,
                'active' => $userInfos['rgpd_aff'],
                'type' => 'aff',
                'date' => $userInfos['rgpd_aff_date'],
                'notice' => $userInfos['rgpd_aff_notice'],
                'date_creation' => date('Y-m-d H:i:s'),
            ];
            try {
                $this->database->insertRow('ew_webinaire_users_rgpd', $arrayInsert);
            } catch (Eden_Error $e) {
                return ["valid" => false, "message" => __("Erreur lors de l'enregistrement des données.")];
            }
        }

        return ["valid" => true];
    }
}
