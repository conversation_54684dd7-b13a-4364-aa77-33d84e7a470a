<?php

use Learnybox\Services\Integration\IntegrationsEventsService;

/**
 * Lbar_BlogBroadcast class.
 *
 * @extends Eden_Class
 *
 * @category Controller
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class Lbar_BlogBroadcast extends Eden_Class
{
    private $database;

    /**
     * @var IntegrationsEventsService
     */
    private $integrationsEventsService;

    /**
     * Lbar_BlogBroadcast constructor.
     * @param IntegrationsEventsService $integrationsEventsService
     */
    public function __construct(IntegrationsEventsService $integrationsEventsService)
    {
        $this->database = MySQL::getInstance();
        $this->integrationsEventsService = $integrationsEventsService;
    }

    /********************************************************/
    /******************* BLOG BROADCAST *********************/
    /********************************************************/

    /**
     * getBlogBroadCastById function.
     *
     * @param int $id_blog_broadcast
     *
     * @return array
     */
    public function getBlogBroadCastById($id_blog_broadcast, $id_client = null)
    {
        if (null === $id_client) {
            $id_client = $_SESSION['id_client'];
        }

        $result = $this->database
            ->search('lbar_blog_broadcast')
            ->addFilter("id_blog_broadcast='$id_blog_broadcast' AND id_client='$id_client'")
            ->getRow();

        return $result;
    }

    /**
     * getAllBlogBroadCast function.
     *
     * @return array
     */
    public function getAllBlogBroadCast($id_client = null)
    {
        if (null === $id_client) {
            $id_client = $_SESSION['id_client'];
        }

        $result = $this->database
            ->search('lbar_blog_broadcast')
            ->addFilter("id_client='$id_client'")
            ->addSort('id_blog_broadcast', 'ASC')
            ->getRows();

        return $result;
    }

    /**
     * getCountBlogBroadCast function.
     *
     * @return array
     */
    public function getCountBlogBroadCast($id_client = null)
    {
        if (null === $id_client) {
            $id_client = $_SESSION['id_client'];
        }

        $result = $this->database
            ->search('lbar_blog_broadcast')
            ->addFilter("id_client='$id_client'")
            ->getTotal();

        return $result;
    }

    /**
     * getCountActiveBlogBroadCast function.
     *
     * @return array
     */
    public function getCountActiveBlogBroadCast($id_client = null)
    {
        if (null === $id_client) {
            $id_client = $_SESSION['id_client'];
        }

        $result = $this->database
            ->search('lbar_blog_broadcast')
            ->addFilter("etat='active' AND id_client='$id_client'")
            ->getTotal();

        return $result;
    }

    /********************************************************/
    /**************** BLOG BROADCAST ADMIN ******************/
    /********************************************************/

    /* adminGetAllBlogBroadCast function.
     *
     * @access public
     * @return array
     */
    public function adminGetAllBlogBroadCast()
    {
        $result = $this->database
            ->search('lbar_blog_broadcast')
            ->addSort('id_blog_broadcast', 'ASC')
            ->getRows();

        return $result;
    }

    /**
     * adminGetAllActiveBlogBroadCast function.
     *
     * @return array
     */
    public function adminGetAllActiveBlogBroadCast()
    {
        $result = $this->database
            ->search('lbar_blog_broadcast')
            ->addFilter("etat='active'")
            ->addSort('id_blog_broadcast', 'ASC')
            ->getRows();

        return $result;
    }

    /**
     * adminGetAllActiveBlogBroadCast function.
     *
     * @return array
     */
    public function adminGetAllActiveBlogBroadCastByHour($heure_envoi)
    {
        $heure_envoi = ltrim($heure_envoi, '0');

        $result = $this->database
            ->search('lbar_blog_broadcast')
            ->addFilter("etat='active' AND heure_envoi='$heure_envoi'")
            ->addSort('id_blog_broadcast', 'ASC')
            ->getRows();

        return $result;
    }

    /********************************************************/
    /*********************** INSERT *************************/
    /********************************************************/

    /**
     * insert function.
     *
     * @param array $cleaned_post
     *
     * @return array
     */
    public function insert($cleaned_post)
    {
        $id_mail = filter_var($cleaned_post['id_mail'], FILTER_VALIDATE_INT);
        $nom = filter_var($cleaned_post['nom'], FILTER_SANITIZE_SPECIAL_CHARS);
        $feed_url = filter_var($cleaned_post['feed_url'], FILTER_SANITIZE_URL);
        $jour_envoi = filter_var($cleaned_post['jour_envoi'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
        $heure_envoi = filter_var($cleaned_post['heure_envoi'], FILTER_SANITIZE_SPECIAL_CHARS);
        $timezone = filter_var($cleaned_post['timezone'], FILTER_SANITIZE_SPECIAL_CHARS);
        $nb_article = filter_var($cleaned_post['nb_article'], FILTER_SANITIZE_NUMBER_INT);
        $etat = 'active';

        $mail = eden()->Lbar_Mails()->getMailById($id_mail);
        if (!$mail) {
            return ['valid' => false, 'message' => __('Cet email n\'existe pas')];
        }

        $sequences = [];
        $exclude_sequences = [];
        $tags = [];
        $exclude_tags = [];

        if (isset($cleaned_post['sequences']) and $cleaned_post['sequences']) {
            $sequences = filter_var($cleaned_post['sequences'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
        }

        if (isset($cleaned_post['exclude_sequences']) and $cleaned_post['exclude_sequences']) {
            $exclude_sequences = filter_var($cleaned_post['exclude_sequences'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
        }

        if (isset($cleaned_post['tags']) and $cleaned_post['tags']) {
            $tags = filter_var($cleaned_post['tags'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
        }

        if (isset($cleaned_post['exclude_tags']) and $cleaned_post['exclude_tags']) {
            $exclude_tags = filter_var($cleaned_post['exclude_tags'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
        }

        if (SERVER_TIMEZONE != $timezone) {
            $datetime = new DateTime(date('Y-m-d') . ' ' . $heure_envoi . ':00:00', new DateTimeZone($timezone));
            $datetime->setTimezone(new DateTimeZone(SERVER_TIMEZONE));
            $heure_envoi = $datetime->format('H');
        }

        //insertion
        $array_insert = [
            'nom' => $nom,
            'id_client' => $_SESSION['id_client'],
            'feed_url' => $feed_url,
            'id_mail' => $id_mail,
            'jour_envoi' => json_encode($jour_envoi),
            'heure_envoi' => $heure_envoi,
            'timezone' => $timezone,
            'nb_article' => $nb_article,
            'sequences' => json_encode($sequences),
            'exclude_sequences' => json_encode($exclude_sequences),
            'tags' => json_encode($tags),
            'exclude_tags' => json_encode($exclude_tags),
            'nb_envois' => '0',
            'nb_opens' => '0',
            'date_creation' => date('Y-m-d H:i:s'),
            'etat' => $etat,
        ];

        try {
            $this->database->insertRow('lbar_blog_broadcast', $array_insert);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de l'envoi.")];
        }

        $id_envoi = $this->database->getLastInsertedId();
        if (!$id_envoi) {
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de l'envoi.")];
        }

        $event = [
            'eventName' => 'Mail Newsletter created',
            'days' => implode(',', $jour_envoi),
            'hour' => $heure_envoi,
            'nbArticles' => $nb_article,
        ];
        $this->integrationsEventsService->sendEvent('track', $event);

        $this->sendNbNewslettersToSegment();

        return ['valid' => true, 'id_envoi' => $id_envoi];
    }

    /**
     * update function.
     *
     * @param array $cleaned_post
     *
     * @return array
     */
    public function update($cleaned_post)
    {
        $id_blog_broadcast = filter_var($cleaned_post['id_blog_broadcast'], FILTER_VALIDATE_INT);
        $nom = filter_var($cleaned_post['nom'], FILTER_SANITIZE_SPECIAL_CHARS);
        $feed_url = filter_var($cleaned_post['feed_url'], FILTER_SANITIZE_URL);
        $heure_envoi = filter_var($cleaned_post['heure_envoi'], FILTER_SANITIZE_SPECIAL_CHARS);
        $timezone = filter_var($cleaned_post['timezone'], FILTER_SANITIZE_SPECIAL_CHARS);
        $nb_article = filter_var($cleaned_post['nb_article'], FILTER_VALIDATE_INT);
        $etat = 'active';

        $jour_envoi = [];
        if (isset($cleaned_post['jour_envoi'])) {
            $jour_envoi = filter_var($cleaned_post['jour_envoi'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
        }
        if (!$jour_envoi) {
            return ["valid" => false, "message" => __('Veuillez sélectionner au moins un jour d\'envoi de la newsletter')];
        }

        $blog_broadcast = $this->getBlogBroadCastById($id_blog_broadcast);
        if (!$blog_broadcast) {
            return ['valid' => false, 'message' => __('Cette newsletter n\'existe pas')];
        }

        $sequences = [];
        $exclude_sequences = [];
        $post_sequences = [];
        if (isset($cleaned_post['sequences'])) {
            $post_sequences = filter_var($cleaned_post['sequences'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
        }

        if ($post_sequences) {
            foreach ($post_sequences as $id_sequence => $state) {
                if ('included' == $state) {
                    array_push($sequences, $id_sequence);
                } else {
                    array_push($exclude_sequences, $id_sequence);
                }
            }
        }

        $tags = [];
        $exclude_tags = [];
        $post_tags = [];
        if (isset($cleaned_post['tags'])) {
            $post_tags = filter_var($cleaned_post['tags'], FILTER_SANITIZE_SPECIAL_CHARS, FILTER_REQUIRE_ARRAY);
        }

        if ($post_tags) {
            foreach ($post_tags as $id_tag => $state) {
                if ('included' == $state) {
                    array_push($tags, $id_tag);
                } else {
                    array_push($exclude_tags, $id_tag);
                }
            }
        }

        if (SERVER_TIMEZONE != $timezone) {
            $datetime = new DateTime(date('Y-m-d') . ' ' . $heure_envoi . ':00:00', new DateTimeZone($timezone));
            $datetime->setTimezone(new DateTimeZone(SERVER_TIMEZONE));
            $heure_envoi = $datetime->format('H');
        }

        //mise à jour
        $array_update = [
            'nom' => $nom,
            'id_client' => $_SESSION['id_client'],
            'feed_url' => $feed_url,
            'jour_envoi' => json_encode($jour_envoi),
            'heure_envoi' => $heure_envoi,
            'timezone' => $timezone,
            'nb_article' => $nb_article,
            'sequences' => json_encode($sequences),
            'exclude_sequences' => json_encode($exclude_sequences),
            'tags' => json_encode($tags),
            'exclude_tags' => json_encode($exclude_tags),
            'etat' => $etat,
        ];

        try {
            $this->database->updateRows('lbar_blog_broadcast', $array_update, "id_blog_broadcast='$id_blog_broadcast' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de l'envoi.")];
        }

        return ['valid' => true];
    }

    /**
     * enable function.
     *
     * @param int id_blog_broadcast
     *
     * @return array
     */
    public function enable($id_blog_broadcast)
    {
        try {
            $this->database->updateRows('lbar_blog_broadcast', ['etat' => 'active'], "id_blog_broadcast='$id_blog_broadcast' AND id_client='" . $_SESSION['id_client'] . "'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la mise à jour de blog broadcast.')];
        }

        $this->sendNbNewslettersToSegment();

        return ['valid' => true];
    }

    /**
     * disable function.
     *
     * @param int id_blog_broadcast
     *
     * @return array
     */
    public function disable($id_blog_broadcast, $id_client = null)
    {
        if ($id_client === null) {
            $id_client = $_SESSION['id_client'];
        }

        try {
            $this->database->updateRows('lbar_blog_broadcast', ['etat' => 'desactive'], "id_blog_broadcast='$id_blog_broadcast' AND id_client='$id_client'");
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la mise à jour de blog broadcast.')];
        }

        $this->sendNbNewslettersToSegment();

        return ['valid' => true];
    }

    /**
     * delete function.
     *
     * @param int $id_blog_broadcast
     *
     * @return array
     */
    public function delete($id_blog_broadcast)
    {
        $blog_broadcast = $this->getBlogBroadCastById($id_blog_broadcast);
        if (!$blog_broadcast) {
            return ['valid' => false, 'message' => __("Ce mail n'existe pas")];
        }

        $filter = [];
        $filter[] = ['id_blog_broadcast=%s', $id_blog_broadcast];
        $filter[] = ['id_client=%s', $_SESSION['id_client']];

        try {
            $this->database->deleteRows('lbar_blog_broadcast', $filter);
        } catch (Eden_Error $e) {
            return ['valid' => false, 'message' => __('Erreur lors de la suppression du blog broadcast.')];
        }

        $this->sendNbNewslettersToSegment();

        return ['valid' => true];
    }

    /**
     * blog_broadcast_edit_form function.
     *
     * @param int $id_blog_broadcast
     *
     * @return array
     */
    public function blog_broadcast_edit_form($id_blog_broadcast)
    {
        $blog_broadcast = $this->getBlogBroadCastById($id_blog_broadcast);
        if (!$blog_broadcast) {
            return ['valid' => false, 'message' => __('Erreur lors de la récupération du blog broadcast.')];
        }

        $_SESSION['lbar_blogbroadcast'] = [];
        $_SESSION['lbar_blogbroadcast']['id_blog_broadcast'] = $id_blog_broadcast;
        $_SESSION['lbar_blogbroadcasttutoriel'] = $blog_broadcast;

        return ['valid' => true];
    }

    /********************************************************/
    /**************** BLOG BROADCAST ENVOIS *****************/
    /********************************************************/

    /**
     * sendBlogBroadcasts function.
     *
     * @return array
     */
    public function sendBlogBroadcasts()
    {
        $error = false;
        $log = '';

        // Heure courante et date aujourd'hui
        $jour = date('d');
        $mois = date('m');
        $annee = date('Y');
        $heure = date('H');
        $heure_now = $heure . ':00';

        $joursem = ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'];
        $timestamp = mktime(0, 0, 0, $mois, $jour, $annee);
        $jour_now = $joursem[date('w', $timestamp)];

        // Récupérer les blog broadcast
        $blog_broadcasts = $this->adminGetAllActiveBlogBroadCastByHour($heure);
        if (!$blog_broadcasts) {
            return ['valid' => true, 'error' => $error, 'log' => __('Aucun envoi programmé')];
        }

        foreach ($blog_broadcasts as $blog_broadcast) {
            $jours_envoi = json_decode($blog_broadcast['jour_envoi']);

            if (!in_array($jour_now, $jours_envoi)) {
                continue;
            }

            // Récupération des paramètres du blog broadcast
            $id_blog_broadcast = $blog_broadcast['id_blog_broadcast'];
            $feed_url = $blog_broadcast['feed_url'];
            $id_client = $blog_broadcast['id_client'];
            $nb_article = $blog_broadcast['nb_article'];
            $id_mail = $blog_broadcast['id_mail'];
            $jour_blog_broadcast = $blog_broadcast['jour_envoi'];
            $heure_blog_broadcast = $blog_broadcast['heure_envoi'];

            $log .= 'Blog broadcast à traiter : #' . $id_blog_broadcast . '<br/>';

            // Récupération du mail
            $mail = eden()->Lbar_Mails()->getMailById($id_mail, $id_client);
            if (!$mail) {
                $this->disable($id_blog_broadcast, $id_client);
                $log .= 'Erreur : email introuvable.<br>';
                continue;
            }

            // Récupération du flux RSS et enregistrement des articles
            libxml_use_internal_errors(true);
            $flux = @simplexml_load_file(rawurlencode($feed_url));
            if (!$flux) {
                $flux = @simplexml_load_file($feed_url);
            }

            if (!$flux) {
                $flux = $this->simplexml_load_file_curl($feed_url);
                if ($flux) {
                    $flux = @simplexml_load_string($flux);
                }
            }

            if (!$flux) {
                $log .= 'Impossible de récupérer le flux RSS<br>';
                continue;
            }

            $donnee = $flux->channel;

            // Lecture des données
            if (isset($donnee->item)) {
                $articles = [];
                $_id_articles = [];
                $_nb_article = 0;

                // Récupération date dernier article envoyé
                $timestamp_last_article = 1;
                $last_article = eden()->Lbar_BlogBroadcastArticle()->getLastArticle($id_blog_broadcast, $id_client);
                if ($last_article) {
                    $timestamp_last_article = strtotime($last_article['date']);
                }

                foreach ($donnee->item as $valeur) {
                    $timestamp_article = strtotime($valeur->pubDate);

                    // Test si il y'a de nouveaux articles
                    if ($timestamp_article > $timestamp_last_article) {
                        // Recherche d'une image ?
                        $textedescription = strip_tags($valeur->description);

                        $image = '';

                        if (isset($valeur->image->url) and $valeur->image->url) {
                            $image = $valeur->image->url;
                        } elseif (isset($valeur->image) and $valeur->image) {
                            $image = $valeur->image;
                        } elseif (isset($valeur->description->a[0]->img['src'][0]) and $valeur->description->a[0]->img['src'][0]) {
                            $image = $valeur->description->a[0]->img['src'][0];
                        }

                        if ('' == trim($image)) {
                            //recherche de l'image dans la description

                            $test_image = preg_match_all('/\<img(.*?)src=[\'"]([\w\W]*?)[\'"](.*?)>/', $valeur->description, $m);
                            if ($test_image) {
                                foreach ($m[2] as $id_image => $_url_image) {
                                    if (false !== strpos($_url_image, '.jpg') or false !== strpos($_url_image, '.png') or false !== strpos($_url_image, '.gif')) {
                                        $image = $_url_image;
                                        $textedescription = str_replace($m[0][$id_image], '', $textedescription);
                                        break;
                                    }
                                }
                                if ('' == trim($image)) {
                                    $image = $m[2][0];
                                    $textedescription = str_replace($m[0][0], '', $textedescription);
                                }
                            }
                        }

                        // enregistrement de l'article
                        $array_insert = [
                            'id_client' => $id_client,
                            'id_blog_broadcast' => $id_blog_broadcast,
                            'feed_url' => $feed_url,
                            'author' => $valeur->author,
                            'date' => date('Y-m-d H:i:s', strtotime($valeur->pubDate)),
                            'titre' => stripslashes($valeur->title),
                            'image' => $image,
                            'description' => $textedescription,
                            'lien' => $valeur->link,
                            'statut' => 'waiting',
                        ];
                        $articles[] = $array_insert;

                        $article = eden()->Lbar_BlogBroadcastArticle()->insert_article($array_insert);
                        $_id_articles[] = $article['id_article'];

                        ++$_nb_article;

                        // Nombre d'articles atteint ?
                        if ($_nb_article >= $nb_article) {
                            break;
                        }
                    } else {
                        // Pas de nouveaux articles disponibles
                        break;
                    }
                }

                if ($_id_articles) {
                    // On a enregistré des articles --> duplication du mail
                    $array_duplicate = [
                        'id_client' => $id_client,
                        'id_mail' => $id_mail,
                        'sujet' => $mail['sujet'],
                        'type' => 'envoi',
                        'param' => $id_blog_broadcast,
                    ];

                    $duplicate = eden()->Lbar_Mails()->duplicate_mail($array_duplicate);
                    if (!$duplicate['valid']) {
                        $error = true;
                        $log .= __('Erreur lors de la duplication du mail') . ' : ' . $duplicate['message'];
                        continue;
                    }

                    $new_id_mail = $duplicate['id_mail'];

                    //transformation de l'élément articles_rss
                    if ($mail['version'] == 2) {
                        $elements = eden()->{'Learnybox\Services\Builder\BuilderElements\BuilderElementsMails'}()->getElementsByObjet($new_id_mail, 'article_rss', $id_client);
                    } else {
                        $elements = eden()->Lbar_Mails_Elements()->getMailElementsByObjet($new_id_mail, 'article_rss');
                    }
                    if ($elements) {
                        foreach ($elements as $element) {
                            if ($mail['version'] == 2) {
                                $data_mail = $mail;
                                if ($data_mail['design']) {
                                    $data_mail['design'] = json_decode($data_mail['design'], true);
                                }
                                $data_mail['articles'] = $articles;
                                $output = eden()->Builder_Elements_Articlerss()->afficher_element($data_mail, $element);
                            } else {
                                $output = '';
                                $output_articles = [];

                                $style_article = '';
                                $style_description = '';
                                $style_titre = '';
                                $style_image = '';

                                // Récupération du contenu du mail
                                $contenutext = '
                                    [[ARTICLE]]
                                    <div class="[[SPAN]]">
                                        <div class="article media">
                                            [[ARTICLE_IMAGE]]
                                            <div class="media-body">
                                                [[ARTICLE_NOM]]
                                                [[ARTICLE_DATE]]
                                                [[ARTICLE_DESCRIPTION]]
                                                [[ARTICLE_LIEN]]
                                            </div>
                                            <div style="clear:both"></div>
                                        </div>
                                    </div>
                                    [[/ARTICLE]]';

                                // Récupération des styles
                                $align = 'left';
                                $template = 'template1';
                                if ($element['contenu']) {
                                    $infos = json_decode($element['contenu'], true);

                                    if (isset($infos['button']['align']) and $infos['button']['align']) {
                                        $align = $infos['button']['align'];
                                    }

                                    // Style de l'article
                                    if (isset($infos['article_border']) and $infos['article_border']) {
                                        $style_article .= 'border:' . $infos['article_border'] . 'px solid;';
                                    }
                                    if (isset($infos['article_border_color']) and $infos['article_border_color']) {
                                        $style_article .= 'border-color:' . $infos['article_border_color'] . ';';
                                    }
                                    if (isset($infos['article_border_radius']) and $infos['article_border_radius']) {
                                        $style_article .= 'border-radius:' . $infos['article_border_radius'] . 'px;';
                                    }

                                    // Style du titre
                                    if (isset($infos['title_font']) and $infos['title_font']) {
                                        $style_titre .= 'font-family:' . $infos['title_font'] . ';';
                                    }
                                    if (isset($infos['title_font_size']) and $infos['title_font_size']) {
                                        $style_titre .= 'font-size:' . $infos['title_font_size'] . 'px;';
                                    }
                                    if (isset($infos['title_font_color']) and $infos['title_font_color']) {
                                        $style_titre .= 'color:' . $infos['title_font_color'] . ';';
                                    }

                                    // Style du texte
                                    if (isset($infos['description_font']) and $infos['description_font']) {
                                        $style_description .= 'font-family:' . $infos['description_font'] . ';';
                                    }
                                    if (isset($infos['description_font_size']) and $infos['description_font_size']) {
                                        $style_description .= 'font-size:' . $infos['description_font_size'] . 'px;';
                                    }
                                    if (isset($infos['description_font_color']) and $infos['description_font_color']) {
                                        $style_description .= 'color:' . $infos['description_font_color'] . ';';
                                    }

                                    // Style de l'image
                                    if (isset($infos['img_border']) and $infos['img_border']) {
                                        $style_image .= 'border:' . $infos['img_border'] . 'px solid;';
                                    }
                                    if (isset($infos['img_border_color']) and $infos['img_border_color']) {
                                        $style_image .= 'border-color:' . $infos['img_border_color'] . ';';
                                    }
                                    if (isset($infos['img_border_radius']) and $infos['img_border_radius']) {
                                        $style_image .= 'border-radius:' . $infos['img_border_radius'] . 'px;';
                                    }

                                    if (isset($infos['template'])) {
                                        $template = $infos['template'];
                                    }
                                }

                                // Style du bouton
                                $icone = '';
                                $align = 'left';
                                $color = '';
                                $color_text = '';
                                $size = '';
                                $border = $border_color = $border_radius = null;
                                $hover_color = $hover_color_text = '';
                                $hover_border = $hover_border_color = null;

                                if (isset($infos['button']['icone']) and $infos['button']['icone']) {
                                    $icone = $infos['button']['icone'];
                                }
                                if (isset($infos['button']['align']) and $infos['button']['align']) {
                                    $align = $infos['button']['align'];
                                }
                                if (isset($infos['button']['color']) and $infos['button']['color']) {
                                    $color = $infos['button']['color'];
                                }
                                if (isset($infos['button']['color_text']) and $infos['button']['color_text']) {
                                    $color_text = $infos['button']['color_text'];
                                }
                                if (isset($infos['button']['size']) and $infos['button']['size']) {
                                    $size = $infos['button']['size'];
                                }

                                if (isset($infos['button']['border'])) {
                                    $border = $infos['button']['border'];
                                }
                                if (isset($infos['button']['border_color'])) {
                                    $border_color = $infos['button']['border_color'];
                                }
                                if (isset($infos['button']['border_radius'])) {
                                    $border_radius = $infos['button']['border_radius'];
                                }

                                if (isset($infos['button']['hover_color']) and $infos['button']['hover_color']) {
                                    $hover_color = $infos['button']['hover_color'];
                                }
                                if (isset($infos['button']['hover_color_text']) and $infos['button']['hover_color_text']) {
                                    $hover_color_text = $infos['button']['hover_color_text'];
                                }
                                if (isset($infos['button']['hover_border'])) {
                                    $hover_border = $infos['button']['hover_border'];
                                }
                                if (isset($infos['button']['hover_border_color'])) {
                                    $hover_border_color = $infos['button']['hover_border_color'];
                                }

                                $btn_style = '';
                                if ($color) {
                                    $btn_style .= 'background:' . $color . ';';
                                }
                                if ($color_text) {
                                    $btn_style .= 'color:' . $color_text . ';';
                                }
                                if (null !== $border) {
                                    $btn_style .= 'border:' . $border . 'px solid;';
                                }
                                if (null !== $border_color) {
                                    $btn_style .= 'border-color:' . $border_color . ';';
                                }
                                if (null !== $border_radius) {
                                    $btn_style .= 'border-radius: ' . $border_radius . 'px; -webkit-border-radius: ' . $border_radius . 'px; -moz-border-radius: ' . $border_radius . 'px;';
                                }

                                $btn = '<a href="' . \Learnybox\Helpers\TextVarHelper::getI18nTag('LINK_ARTICLE') . '" class="btn btn-primary ' . $size . ' btn_article" style="' . $btn_style . '" target="_blank">' . ($icone ? '<i class="fa ' . $icone . '"></i> ' : '') . $element['contenutexte'] . '</a>';


                                $nb_columns = 2;
                                if (isset($infos['nb_columns']) and $infos['nb_columns']) {
                                    $nb_columns = $infos['nb_columns'];
                                }

                                foreach ($articles as $article) {
                                    // Mise en forme de l'image si il y en a une
                                    $image = '';
                                    if ($article['image'] and isset($infos['show_image']) and 'oui' == $infos['show_image']) {
                                        $image = '
                                        <div class="media-left picture">
                                            <a href="' . $article['lien'] . '">
                                                <img class="pic" src="' . $article['image'] . '" alt="' . $article['titre'] . '" style="' . $style_image . '">
                                            </a>
                                        </div>';
                                    }

                                    // Mise en forme et application des styles
                                    $titre = '<h4><a href="' . $article['lien'] . '" style="' . $style_titre . '">' . $article['titre'] . '</a></h4>';
                                    $date = '<p>' . date_french('d F Y', strtotime($article['date'])) . '</p>';

                                    $textedescription = '';
                                    if (isset($infos['show_description']) and 'oui' == $infos['show_description']) {
                                        $textedescription = '<p style="' . $style_description . '">' . $article['description'] . '</p>';
                                    }

                                    $articleBtn = $btn;
                                    $articleBtn = \Learnybox\Helpers\TextVarHelper::replaceVar('LINK_ARTICLE', $article['lien'], $articleBtn);
                                    $lien = '<div style="text-align:' . $align . '">' . $articleBtn . '</div>';

                                    // Ajout de l'article dans le corps du mail
                                    $_article = $contenutext;
                                    $_article = \Learnybox\Helpers\TextVarHelper::replaceVar('ARTICLE', ' ', $_article);
                                    $_article = \Learnybox\Helpers\TextVarHelper::replaceVar('ARTICLE', ' ', $_article, ['tag_format' => '[/%s]']);

                                    $_article = \Learnybox\Helpers\TextVarHelper::replaceVars([
                                        'ARTICLE_NOM' => $titre,
                                        'ARTICLE_DATE' => $date,
                                        'ARTICLE_DESCRIPTION' => $textedescription,
                                        'ARTICLE_IMAGE' => $image,
                                        'ARTICLE_LIEN' => $lien,
                                    ], $_article);

                                    if ($style_article) {
                                        $_article = str_replace('<div class="article media">', '<div class="article media" style="' . $style_article . '">', $_article);
                                    }

                                    $output_articles[] = $_article;
                                }

                                $hover_style = '';
                                if ($hover_color) {
                                    $hover_style .= 'background:' . $hover_color . ' !important;';
                                }
                                if ($hover_color_text) {
                                    $hover_style .= 'color:' . $hover_color_text . ' !important;';
                                }
                                if (null !== $hover_border) {
                                    $hover_style .= 'border:' . $hover_border . 'px solid !important;';
                                }
                                if (null !== $hover_border_color) {
                                    $hover_style .= 'border-color:' . $hover_border_color . ' !important;';
                                }

                                if ($hover_style) {
                                    $output .= '<style type="text/css">.btn_article:hover { ' . $hover_style . ' }</style>';
                                }

                                $span = floor(12 / $nb_columns);

                                $i = 0;
                                foreach ($output_articles as $output_article) {
                                    $output .= (0 == $i % $nb_columns ? '<div class="row" id="articles_' . $template . '">' : '');
                                    ++$i;

                                    $output .= \Learnybox\Helpers\TextVarHelper::replaceVar('SPAN', 'col-md-' . $span . ' col-sm-' . $span . ' col-xs-12', $output_article);
                                    $output .= (0 == $i % $nb_columns ? '</div>' : '');
                                }
                                $output .= (0 != $i % $nb_columns ? '</div>' : '');
                            }

                            //mise à jour de l'élément
                            $array_update = [
                                'objet' => 'txt',
                                'contenutexte' => $output,
                            ];

                            try {
                                $this->database->updateRows('lbar_mails_elements', $array_update, "ID='" . $element['ID'] . "' AND id_mail='$new_id_mail'");
                            } catch (Eden_Error $e) {
                                $error = true;
                                $log .= __('Erreur lors de la mise à jour de la position d\'un élément') . ' : ' . $e;
                                continue 2;
                            }
                        }
                    }

                    //Enregistrement de l'envoi
                    $array_insert = [
                        'id_client' => $id_client,
                        'id_mail' => $new_id_mail,
                        'sequences' => $blog_broadcast['sequences'],
                        'exclude_sequences' => $blog_broadcast['exclude_sequences'],
                        'tags' => $blog_broadcast['tags'],
                        'exclude_tags' => $blog_broadcast['exclude_tags'],
                        'id_user_action' => 0,
                        'nb_envois' => 0,
                        'nb_opens' => 0,
                        'etat' => 'waiting',
                        'date' => date('Y-m-d H:i:s'),
                        'date_timezone' => date('Y-m-d H:i:s'),
                    ];

                    try {
                        $this->database->insertRow('lbar_envois', $array_insert);
                    } catch (Eden_Error $e) {
                        return ['valid' => false, 'message' => __("Erreur lors de l'enregistrement de l'envoi.")];
                    }

                    $id_envoi = $this->database->getLastInsertedId();
                    $log .= '1 nouvel envoi créé (' . $id_envoi . ')<br/><br/>';

                    // Enregistrement de l'envoi blog broadcast
                    $array_insert = [
                        'id_client' => $id_client,
                        'id_blog_broadcast' => $id_blog_broadcast,
                        'id_envoi' => $id_envoi,
                        'id_mail' => $new_id_mail,
                        'id_articles' => $_id_articles,
                        'date_creation' => date('Y-m-d H:i:s'),
                    ];

                    $blog_broadcast_envoi = eden()->Lbar_BlogBroadcastEnvoi()->insert_envoi($array_insert);
                    if (!$blog_broadcast_envoi['valid']) {
                        $error = true;
                        $log .= __('Erreur lors de la création de l\'envoi blog broadcast') . ' : ' . $blog_broadcast_envoi['message'];
                        continue;
                    }

                    //mise à jour du mail
                    try {
                        $this->database->updateRows('lbar_mails', ['param' => $id_envoi], "id_mail='$new_id_mail' AND id_client='$id_client'");
                    } catch (Eden_Error $e) {
                        $error = true;
                        $log .= __('Erreur lors de la mise à jour du mail') . ' : ' . $e;
                        continue;
                    }

                    $log .= $_nb_article . ' article(s) enregistrés(s)<br/>';
                } else {
                    $log .= 'Pas de nouvel article<br>';
                }
            }
        }

        return ['valid' => !$error, 'error' => $error, 'log' => $log];
    }

    public function simplexml_load_file_curl($url)
    {
        $xml = '';

        if (in_array('curl', get_loaded_extensions())) {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.8.1.13) Gecko/20080311 Firefox/2.0.0.13');
            $xml = curl_exec($ch);
            curl_close($ch);
        }

        return $xml;
    }

    public function sendNbNewslettersToSegment()
    {
        $nbNewsletters = $this->getCountActiveBlogBroadCast();
        $event = [
            'Nb Active Newsletters' => $nbNewsletters,
        ];
        $this->integrationsEventsService->sendEvent('identify', $event);
    }
}
