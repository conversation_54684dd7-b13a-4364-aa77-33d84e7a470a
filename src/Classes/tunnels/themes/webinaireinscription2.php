<?php

/**
 * TunnelsThemes_webinaireinscription2 class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class TunnelsThemes_webinaireinscription2 extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        if (defined('SITE_LOGO') and SITE_LOGO) {
            $logo = SITE_LOGO;
        } else {
            $logo = \Learnybox\Helpers\Assets::getImageUrl('tunnels/logo-exemple.png');
        }

        $id_conference = 0;
        $nom_conference = 'Titre de ma conférence';
        $date_conference2 = date('Y-m-d H:i:s', strtotime('+7 days'));
        $date_conference = datetimefr($date_conference2);

        if (isset($_SESSION['conference']) and $_SESSION['conference']) {
            $id_conference = $_SESSION['id_conference'];
            $nom_conference = $_SESSION['conference']['nom'];
            $date_conference2 = $_SESSION['conference']['date'];
            $date_conference = datetimefr($_SESSION['conference']['date_timezone'], false);
        }

        $theme_datas = [];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'elements' => [
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'center',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h2 style="text-align: center;"><span style="font-size:36px;"><span style="color:#273340;">' . $nom_conference . '</span></span></h2>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'box',
                    'bloc_alignement' => 'center',
                    'span' => 'span6',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '1',
                    'contenu' => '{"icone":"","align":"center","color":"#3c96cd","size":"btn-xlarge"}',
                    'contenutexte' => strtoupper(__('Je réserve ma place')),
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                    'subelements' => [
                        [
                            'objet' => 'progress_bar',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '50',
                            'y' => '20',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'conference_optin',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"text":"' . __('Je m\'inscris') . '","icone":"fa-check","align":"center","color":"#5cb85c","color_text":"#ffffff","size":"btn-xlarge","border":1,"border_color":"#5cb85c","border_radius":4,"font_color":"","hover_color":"","hover_color_text":"","hover_border":false,"hover_border_color":"","template":"template2","font":"Arial","font_size":18,"champs_bgcolor":"","champs_bgcolor_transparent":false,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => $id_conference,
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                    ],
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<p style="text-align: center;"><span style="color: rgb(27, 133, 198); font-family: \'Helvetica Neue\', Helvetica, Arial, sans-serif; font-size: 17px; text-align: center;">&gt;&gt; ' . __('Places limitées') . ' &lt;&lt;</span></p>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '#273340',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '20',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'elements' => [
                [
                    'objet' => 'fa-icon',
                    'bloc_alignement' => 'auto',
                    'span' => 'span1',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '{"icone":"fa-group","align":"center","open_window":0,"color":"#ffffff","size":"50"}',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span5',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h2 style="margin-top: -10px;"><span style="color:#FFFFFF;">' . __('Places limitées !') . '</span></h2><p><span style="color:#FFFFFF;">' . __('Nous ne pourrons pas accueillir tout le monde, inscrivez-vous maintenant !') . '</span></p>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'fa-icon',
                    'bloc_alignement' => 'auto',
                    'span' => 'span1',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '{"icone":"fa-video-camera","align":"center","open_window":0,"color":"#ffffff","size":"50"}',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span5',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h2 style="margin-top: -10px;"><span style="color:#FFFFFF;">' . __('Début de la conférence') . '</span></h2><p><span style="color:#FFFFFF;">' . $date_conference . '</span></p>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '40',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'elements' => [
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h2 style="text-align: center;"><span style="color:#1b85c6;">' . __('Pendant cette conférence&nbsp;<strong>GRATUITE sur internet</strong><br />vous découvrirez') . ' :</span></h2>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'box',
                    'bloc_alignement' => 'auto',
                    'span' => 'span9',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '0',
                    'contenu' => '[]',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                    'subelements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => '<div><img alt="" src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/webinar/no1.png') . '" style="margin-left: 20px; margin-right: 20px; margin-bottom:50px; float:left" /><h2>' . __('Comment créer vos formations de A à Z...') . '</h2><p>' . __('Je vous expliquerai comment créer votre plan, tourner vos vidéos et intégrer votre formation sur LearnyBox') . '</p></div>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => '<div><img alt="" src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/webinar/no2.png') . '" style="margin-left: 20px; margin-right: 20px; margin-bottom:60px; float:left" /><h2>' . __('Comment créer votre premier tunnel de vente pour vendre votre formation...') . '</h2><p>' . __('Je vous expliquerai le concet des tunnels de vente, comment créer vos&nbsp;pages de capture et de vente <strong>facilement</strong> dans LearnyBox') . '</p></div>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => '<div><img alt="" src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/webinar/no3.png') . '" style="margin-left: 20px; margin-right: 20px; margin-bottom:60px; float:left" /><h2>' . __('Comment décupler vos ventes avec l\'outil d\'affiliation intégré...') . '</h2><p>' . __('Je vous montrerai comment utiliser l\'outil d\'affiliation et trouver des partenaires qui pourront booster les ventes de votre formation') . '</p></div>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => '<h2 style="text-align: center;"><span style="color:#1b85c6;">' . __('Et bien plus encore...') . '</span></h2>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                    ],
                ],
                [
                    'objet' => 'box',
                    'bloc_alignement' => 'auto',
                    'span' => 'span3',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '0',
                    'contenu' => '{"bg_color":"#f9f9f9","bg_opacity":"1.00","border_color":""}',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                    'subelements' => [
                        [
                            'objet' => 'image',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '250',
                            'eval' => '0',
                            'contenu' => '',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => 'center',
                            'time' => '0',
                            'name' => '',
                            'urls' => $logo,
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => '<h2 style="text-align: center;"><span style="color:#1b85c6 ;">' . SITE_NAME . '</span></h2>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => '<p><span style="font-size:18px;">' . __('LearnyBox est la boîte à outils — tout en 1 — de vos formations en ligne, tout simplement !') . '</span></p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '#26364b',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '20',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'elements' => [
                [
                    'objet' => 'countdown',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '2',
                    'contenu' => '',
                    'contenutexte' => $date_conference2,
                    'type' => 'date_fixe',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h1 style="text-align: center;"><span style="color:#FFFFFF;">' . strtoupper(__('Réservez votre place avant l\'évènement !')) . '</span></h1>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'box',
                    'bloc_alignement' => 'center',
                    'span' => 'span6',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '1',
                    'contenu' => '{"icone":"","align":"center","color":"#1b85c6","size":"btn-xlarge"}',
                    'contenutexte' => __('Cliquez ici pour réserver votre place'),
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                    'subelements' => [
                        [
                            'objet' => 'progress_bar',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '50',
                            'y' => '20',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'conference_optin',
                            'bloc_alignement' => '',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"text":"' . __('Je m\'inscris') . '","icone":"fa-check","align":"center","color":"#5cb85c","color_text":"#ffffff","size":"btn-xlarge","border":1,"border_color":"#5cb85c","border_radius":4,"font_color":"","hover_color":"","hover_color_text":"","hover_border":false,"hover_border_color":"","template":"template2","font":"Arial","font_size":18,"champs_bgcolor":"","champs_bgcolor_transparent":false,"bg_color":"","bg_opacity":"1","bg_border":0,"bg_border_color":"","bg_border_radius":0,"margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":0,"padding_bottom":0,"padding_left":0,"padding_right":0}',
                            'contenutexte' => $id_conference,
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                    ],
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<p style="text-align: center;"><span style="color:#FFFFFF;">&gt;&gt; ' . __('Places limitées') . '&nbsp;&lt;&lt;</span></p>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '20',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'elements' => [
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span6',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<p>' . __('Conditions d\'utilisation') . ' | ' . \Learnybox\Helpers\TextVarHelper::getI18nTag('LINK_POLITIQUE_CONFIDENTIALITE') . ' | ' . \Learnybox\Helpers\TextVarHelper::getI18nTag('LINK_MENTIONS_LEGALES') . '</p>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span6',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<p style="text-align: right;">Copyright ' . date('Y') . ' - ' . SITE_NAME . ' - ' . __('Tous droits réservés') . '</p>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
            ],
        ];

        return $theme_datas;
    }
}
