<?php

/**
 * TunnelsThemes_webinaireinscription class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class TunnelsThemes_webinaireinscription extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        if (defined('SITE_LOGO') and SITE_LOGO) {
            $logo = SITE_LOGO;
        } else {
            $logo = \Learnybox\Helpers\Assets::getImageUrl('tunnels/logo-exemple.png');
        }

        $id_conference = 0;
        $nom_conference = 'Titre de ma conférence';
        $date_conference = datetimefr(date('Y-m-d H:i:s', strtotime('+7 days')));

        if (isset($_SESSION['conference']) and $_SESSION['conference']) {
            $id_conference = $_SESSION['id_conference'];
            $nom_conference = $_SESSION['conference']['nom'];
            $date_conference = datetimefr($_SESSION['conference']['date_timezone'], false);
        }

        $theme_datas = [];

        $theme_datas['design'] = [
            'bgcolor1' => '#38384b',
            'bgcolor2' => '',
            'bgimage' => '',
            'bgvideo' => '',
            'bgvideo_url' => '',
            'banniere' => '',
            'banniere_bgcolor' => '',
        ];

        $theme_datas['lines'][0] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '10',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
            ],
            'elements' => [
                [
                    'objet' => 'image',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '',
                    'largeur' => '',
                    'eval' => '0',
                    'contenu' => '',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => 'center',
                    'time' => '0',
                    'name' => '',
                    'urls' => $logo,
                    'url_webm' => '0',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'hide' => '0',
                ],
            ],
        ];

        $theme_datas['lines'][1] = [
            'elements' => [
                [
                    'objet' => 'box',
                    'span' => 'span6',
                    'bloc_alignement' => 'center',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '{"bg_color":"#ffffff","bg_opacity":"1.00","border_color":"","margin_top":0,"margin_bottom":0,"margin_left":0,"margin_right":0,"padding_top":20,"padding_bottom":20,"padding_left":20,"padding_right":20}',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'hide' => '0',
                    'subelements' => [
                        [
                            'objet' => 'txt',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => '<h2 style="text-align: center;"><span style="font-size:24px"><span style="color:#000000;"><span style="font-family:verdana,geneva,sans-serif;">' . __('Remplissez le formulaire ci-dessous<br />pour vous inscrire à la conférence') . '</span></span></span></h2><hr><h1 style="text-align: center;"><span style="font-size:28px;"><span style="font-family:verdana,geneva,sans-serif;"><span style="color:#FC9B37;">' . $nom_conference . '</span></span></span><br><span style="font-size:18px"><span style="color:#000000;"><span style="font-family:verdana,geneva,sans-serif;">' . $date_conference . '</span></span></span></h1>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'txt',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p><p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'conference_optin',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => $id_conference,
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'hide' => '0',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][2] = [
            'design' => [
                'bgcolor1' => '#ffffff',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '30',
                'padding_bottom' => '100',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
            ],
            'elements' => [],
        ];

        $presentateurs = [];
        if ($id_conference) {
            $presentateurs = eden()->Webinar_Presentateurs()->getPresentateursByConf($id_conference);
        }

        $presentateurs_elements = [];
        if (!$presentateurs) {
            $presentateurs_elements[] = '<div class="media"><div class="media-body"><h4 style="text-align: right;">' . __('Présentateur 1') . '</h4><p style="text-align: right;">' . __('Description du présentateur 1') . '</p></div><div class="media-right"><img class="media-object img-thumbnail" src="' . \Learnybox\Helpers\Assets::getImageUrl('user/default_avatar.png') . '" /></div></div>';
            $presentateurs_elements[] = '<div class="media"><div class="media-left"><img class="media-object img-thumbnail" src="' . \Learnybox\Helpers\Assets::getImageUrl('user/default_avatar.png') . '" /></div><div class="media-body"><h4>' . __('Présentateur 2') . '</h4><p>' . __('Description du présentateur 2') . '</p></div></div>';
        }

        $default_avatar = \Learnybox\Helpers\Assets::getImageUrl('user/default_avatar.png');
        $contenu_texte = '<div class="media"><div class="media-left"><img class="media-object img-thumbnail" src="' . \Learnybox\Helpers\TextVarHelper::getI18nTag('AVATAR') . '" style="width: 120px;" /></div><div class="media-body"><h4>' . \Learnybox\Helpers\TextVarHelper::getI18nTag('NOM') . '</h4><p>' . \Learnybox\Helpers\TextVarHelper::getI18nTag('TITRE') . '</p></div></div>';

        if ($presentateurs) {
            foreach ($presentateurs as $presentateur) {
                $presentateur_infos = eden()->Webinar_Presentateurs()->getPresentateurById($presentateur['id_presentateur']);
                if ($presentateur_infos) {
                    $presentateur_element = $contenu_texte;

                    $presentateur_element = \Learnybox\Helpers\TextVarHelper::replaceVars([
                        'NOM' => $presentateur_infos['prenom'] . ' ' . $presentateur_infos['nom'],
                        'TITRE' => $presentateur_infos['titre'],
                        'AVATAR' => $presentateur_infos['avatar'] ? $presentateur_infos['avatar'] : $default_avatar,
                    ], $presentateur_element);

                    $presentateurs_elements[] = $presentateur_element;
                }
            }
        }

        if ($presentateurs_elements) {
            foreach ($presentateurs_elements as $_element) {
                $theme_datas['lines'][2]['elements'][] = [
                    'objet' => 'txt',
                    'span' => 'span6',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => $_element,
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'hide' => '0',
                ];
            }
        }

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '#2c2b3a',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '20',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
            ],
            'elements' => [
                [
                    'objet' => 'txt',
                    'span' => 'span6',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<p><span style="color:#CCC;">&copy; Copyright ' . date('Y') . ' - ' . SITE_NAME . '</span></p>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'span' => 'span6',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<p style="text-align: right;"><span style="color:#CCC;">' . __('Contact') . ' &nbsp;| &nbsp;' . \Learnybox\Helpers\TextVarHelper::getI18nTag('LINK_MENTIONS_LEGALES') . '</span></p>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'hide' => '0',
                ],
            ],
        ];

        return $theme_datas;
    }
}
