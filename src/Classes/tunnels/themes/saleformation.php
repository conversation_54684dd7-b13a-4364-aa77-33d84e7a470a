<?php

/**
 * TunnelsThemes_saleformation class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class TunnelsThemes_saleformation extends Eden_Class
{
    public function __construct()
    {
    }

    public function getThemeData()
    {
        $theme_datas = [];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '40',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'elements' => [
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h1 style="text-align: center;"><span style="font-family:impact;"><span style="color:#D00B0B;">' . __('Découvrez comment prendre la parole en toute confiance') . '</span></span></h1>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'separator',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '20',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'videoplayer',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '1',
                    'contenu' => '',
                    'contenutexte' => 'https://www.youtube.com/watch?v=',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'separator',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '20',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h2 style="text-align: center;"><span style="color:#000000;">' . strtoupper(__('Une nouvelle aventure démarre maintenant')) . '</span></h2>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '#1c1c1c',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '20',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'elements' => [
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span6',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h2 style="text-align: center;"><span style="font-size:36px;"><span style="color:#FFFFFF;">' . strtoupper(__('Formation')) . '<br />' . strtoupper(__('10 jours pour avoir confiance en vous')) . '</span></span></h2><h2 style="text-align: center;"><span style="font-size:24px;"><span style="color:#FFFFFF;">' . __('Découvrez 10 jours d\'outils concrets pour obtenir les résultats que vous méritez') . '</span></span></h2>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'box',
                    'bloc_alignement' => 'auto',
                    'span' => 'span6',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '0',
                    'contenu' => '{"bg_color":"#eeeeee","bg_opacity":"1.00","border_color":""}',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                    'subelements' => [
                        [
                            'objet' => 'txt',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '',
                            'contenutexte' => '<h4 style="text-align: center;"><span style="font-size:22px;">' . __('Lancez-vous dès maintenant<br />dans votre aventure de 10 jours.') . '</span></h4><p style="text-align: center;">' . __('Réglement en 1 fois par carte bleue d\'un montant de 97&euro;') . '</p>',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => '',
                            'url_webm' => '',
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                        [
                            'objet' => 'button_link',
                            'bloc_alignement' => 'auto',
                            'span' => 'span12',
                            'x' => '0',
                            'y' => '0',
                            'hauteur' => '0',
                            'largeur' => '0',
                            'eval' => '',
                            'contenu' => '{"icone":"","align":"center","open_window":0,"color":"","color_text":"","size":"btn"}',
                            'contenutexte' => '',
                            'type' => '',
                            'fleche' => '',
                            'time' => '0',
                            'name' => '',
                            'urls' => 'http://monsite.com',
                            'url_webm' => \Learnybox\Helpers\Assets::getImageUrl('tunnels/elements/btn-acces.png'),
                            'url_ogv' => '',
                            'duree' => '0',
                            'titre' => '',
                            'delay' => '0',
                            'delay_disappear' => '0',
                            'hide' => '0',
                        ],
                    ],
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '40',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'elements' => [
                [
                    'objet' => 'fa-icon',
                    'bloc_alignement' => 'auto',
                    'span' => 'span2',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '{"icone":"fa-calendar","align":"right","open_window":0,"color":"#395fab","size":"72"}',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span4',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h4><span style="color:#000000;">' . __('Une vidéo tous les jours pendant 10 jours') . '</span></h4>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'fa-icon',
                    'bloc_alignement' => 'auto',
                    'span' => 'span2',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '{"icone":"fa-group","align":"right","open_window":0,"color":"#9fb144","size":"72"}',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span4',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h4><span style="color:#000000;">' . __('Des exemples concrets pour vous aider à progresser plus vite') . '</span></h4>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'separator',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'fa-icon',
                    'bloc_alignement' => 'auto',
                    'span' => 'span2',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '{"icone":"fa-file-text-o","align":"right","open_window":0,"color":"#6b6b6b","size":"72"}',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span4',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h4><span style="color:#000000;">' . __('Des exercices de mise en pratique pour progresser rapidement') . '</span></h4>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'fa-icon',
                    'bloc_alignement' => 'auto',
                    'span' => 'span2',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '{"icone":"fa-search-plus","align":"right","open_window":0,"color":"#5cac73","size":"72"}',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span4',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h4><span style="color:#000000;">' . __('Des objectifs précis à réaliser pour chaque exercice') . '</span></h4>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'separator',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'fa-icon',
                    'bloc_alignement' => 'auto',
                    'span' => 'span2',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '{"icone":"fa-envelope","align":"right","open_window":0,"color":"#1c008a","size":"72"}',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span4',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h4><span style="color:#000000;">' . __('Vous recevrez des emails de rappel tous les jours pour accéder au nouvel exercice') . '</span></h4>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'fa-icon',
                    'bloc_alignement' => 'auto',
                    'span' => 'span2',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '{"icone":"fa-line-chart","align":"right","open_window":0,"color":"#ff6600","size":"72"}',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span4',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h4><span style="color:#000000;">' . __('Une évolution rapide : chaque jour, vous sentirez les bénéfices de ce programme') . '</span></h4>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '#f9f8f6',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '20',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '1',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'elements' => [
                [
                    'objet' => 'fa-icon',
                    'bloc_alignement' => 'auto',
                    'span' => 'span4',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '{"icone":"fa-video-camera","align":"center","open_window":0,"color":"#474747","size":"172"}',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span8',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h2>' . __('Des vidéos pédagogiques et impactantes') . '</h2><p>' . __('IMAGINEZ&hellip; Vous vous levez le matin et vous visionnez votre vidéo, elle est courte et intense.') . '</p><p>' . __('Vous avez ensuite la journée pour appliquer la clé et faire le point sur votre progression.') . '</p><p>' . __('Et vous savez que le lendemain ça recommence.') . '</p>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
            ],
        ];

        $theme_datas['lines'][] = [
            'design' => [
                'bgcolor1' => '',
                'bgcolor2' => '',
                'bgimage' => '',
                'padding_top' => '40',
                'padding_bottom' => '',
                'margin_top' => '',
                'margin_bottom' => '',
                'border_top_width' => '',
                'border_top_color' => '',
                'border_bottom_width' => '',
                'border_bottom_color' => '',
                'full_width' => '0',
                'delay' => '0',
                'delay_disappear' => '0',
            ],
            'elements' => [
                [
                    'objet' => 'image',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '0',
                    'contenu' => '',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => 'center',
                    'time' => '0',
                    'name' => '',
                    'urls' => \Learnybox\Helpers\Assets::getImageUrl('tunnels/elements/btn-acces.png'),
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<p style="text-align: center;">' . __('Réglement en 1 fois par carte bleue d\'un montant de 97&euro;') . '</p>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'separator',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '50',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'txt',
                    'bloc_alignement' => 'auto',
                    'span' => 'span12',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '0',
                    'largeur' => '0',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '<h3 align="center"><span style="font-size:36px;"><span style="color:#000000;">' . __('Decouvrez notre programme en avant-première !') . '</span></span></h3>',
                    'type' => '',
                    'fleche' => '',
                    'time' => '0',
                    'name' => '',
                    'urls' => '',
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
                [
                    'objet' => 'image-lightbox',
                    'bloc_alignement' => 'center',
                    'span' => 'span6',
                    'x' => '0',
                    'y' => '0',
                    'hauteur' => '360',
                    'largeur' => '500',
                    'eval' => '',
                    'contenu' => '',
                    'contenutexte' => '["https:\/\/' . LB_COM . '\/images\/tunnels\/elements\/formation.png","","","",""]',
                    'type' => '',
                    'fleche' => 'center',
                    'time' => '0',
                    'name' => '',
                    'urls' => \Learnybox\Helpers\Assets::getImageUrl('tunnels/elements/formation-small.png'),
                    'url_webm' => '',
                    'url_ogv' => '',
                    'duree' => '0',
                    'titre' => '',
                    'delay' => '0',
                    'delay_disappear' => '0',
                    'hide' => '0',
                ],
            ],
        ];

        return $theme_datas;
    }
}
