<?php

/**
 * TunnelsPagesElements_Funnelmenu class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class TunnelsPagesElements_Funnelmenu extends TunnelsPagesElements
{
    private $database;

    public function __construct()
    {
        $this->database = MySQL::getInstance();
    }

    /********************************************************/
    /********************** AFFICHAGE ***********************/
    /********************************************************/

    public function afficher_element($data_tunnel, $element)
    {
        $output = '';

        if ($element['contenu']) {
            $infos = json_decode($element['contenu'], true);

            $template = 'template1';
            $display_thumbnails = false;

            $style = '';
            if (isset($infos['font']) and $infos['font']) {
                $style .= 'font-family:' . $infos['font'] . ';';
            }
            if (isset($infos['font_size']) and $infos['font_size']) {
                $style .= 'font-size:' . $infos['font_size'] . 'px;';
            }
            if (isset($infos['font_color']) and $infos['font_color']) {
                $style .= 'color:' . $infos['font_color'] . ';';
            }

            if (isset($infos['template'])) {
                $template = $infos['template'];
            }
            if (isset($infos['display_thumbnails'])) {
                $display_thumbnails = $infos['display_thumbnails'];
            }

            if (isset($infos['pages']) and $infos['pages']) {
                $output .= '<div class="row" id="funnel_' . $template . '">';

                $nb_pages = 0;

                foreach ($infos['pages'] as $tunnel_page_id => $tunnel_page) {
                    if (!isset($tunnel_page['active']) or !$tunnel_page['active']) {
                        continue;
                    }

                    $page = eden()->TunnelsPages()->getPageById($tunnel_page_id);
                    if (!$page) {
                        continue;
                    }

                    ++$nb_pages;
                    $titre = '';

                    $page_available = true;
                    $hasAccess = eden()->TunnelsSubscriptions()->hasAccessToPage($data_tunnel['tunnel'], $page);
                    if (isset($hasAccess['can_see'])) {
                        $page_available = $hasAccess['can_see'];
                    }
                    if (isset($hasAccess['redirection']) and $hasAccess['redirection']) {
                        $page_available = false;
                    }

                    if ($page_available) {
                        $titre = $tunnel_page['title'];
                    } else {
                        if (isset($tunnel_page['title_unavailable']) and $tunnel_page['title_unavailable']) {
                            $titre = $tunnel_page['title_unavailable'];
                        } else {
                            $titre = __('Bientôt disponible');
                        }
                    }

                    $thumbnail = \Learnybox\Helpers\Assets::getImageUrl('tunnels/funnel_menu/default_thumbnail.png');
                    if (isset($tunnel_page['image']) and $tunnel_page['image']) {
                        $thumbnail = $tunnel_page['image'];
                    }

                    if (!$page_available) {
                        $thumbnail = \Learnybox\Helpers\Assets::getImageUrl('tunnels/funnel_menu/default_thumbnail_unavailable.png');
                        if (isset($tunnel_page['image-unavailable']) and $tunnel_page['image-unavailable']) {
                            $thumbnail = $tunnel_page['image-unavailable'];
                        }
                    }

                    $output .= '<div class="tpage [[SPAN]]">';
                    if ($page_available) {
                        $output .= '<a href="' . Tools::getLink($page['id_domaine'], 'site', $page['permalink']) . '">';
                    }

                    if ($display_thumbnails) {
                        $output .= '<img class="thumbnail" src="' . $thumbnail . '" alt="' . $titre . '">';
                    }

                    $output .= '<div class="title"' . ($style ? ' style="' . $style . '"' : '') . '">' . str_replace('&curren', '&amp;curren', $titre) . '</div>';
                    if ($page_available) {
                        $output .= '</a>';
                    }

                    $output .= '</div>';
                }

                $span = '';
                if ($nb_pages and 'template2' != $template) {
                    $floor = floor(12 / $nb_pages);
                    if ($floor <= 1) {
                        $floor = 2;
                    }
                    $span = 'col-md-' . $floor;

                    if ($nb_pages == 5 or $nb_pages > 6) {
                        $width = floor(100 / $nb_pages * 100) / 100;
                        $width = number_format($width, 2, '.', '');
                        $output = \Learnybox\Helpers\TextVarHelper::replaceVar('SPAN', $span . '" style="width:' . $width . '%">', $output);
                    }
                }
                $output = \Learnybox\Helpers\TextVarHelper::replaceVar('SPAN', $span, $output);

                $output .= '</div>';
            }
        }

        return $output;
    }

    public function admin_display_element($element, $data_tunnel = [], $parent_span = 'span12')
    {
        $output = '';

        if (!$element['contenu']) {
            $output .= '<div class="alert alert-info">' . __('Menu non configuré') . '</div>';
        } else {
            $infos = json_decode($element['contenu'], true);

            $style = '';
            if (isset($infos['font']) and $infos['font']) {
                $style .= 'font-family:' . $infos['font'] . ';';
            }
            if (isset($infos['font_size']) and $infos['font_size']) {
                $style .= 'font-size:' . $infos['font_size'] . 'px;';
            }
            if (isset($infos['font_color']) and $infos['font_color']) {
                $style .= 'color:' . $infos['font_color'] . ';';
            }

            $template = 'template1';
            $display_thumbnails = false;

            if (isset($infos['template'])) {
                $template = $infos['template'];
            }
            if (isset($infos['display_thumbnails'])) {
                $display_thumbnails = $infos['display_thumbnails'];
            }

            if (!isset($infos['pages']) or !$infos['pages']) {
                $output .= '<div class="alert alert-info">' . __('Aucune page activée dans ce menu.') . '</div>';
            } else {
                $output .= '<div class="row" id="funnel_' . $template . '">';

                $nb_pages = 0;

                foreach ($infos['pages'] as $tunnel_page_id => $tunnel_page) {
                    if (!isset($tunnel_page['active']) or !$tunnel_page['active']) {
                        continue;
                    }

                    $page = eden()->TunnelsPages()->getPageById($tunnel_page_id);
                    if (!$page) {
                        continue;
                    }

                    ++$nb_pages;
                    $titre = '';

                    $titre = str_replace('&curren', '&amp;curren', $tunnel_page['title']);

                    $thumbnail = \Learnybox\Helpers\Assets::getImageUrl('tunnels/funnel_menu/default_thumbnail.png');
                    if (isset($tunnel_page['image']) and $tunnel_page['image']) {
                        $thumbnail = $tunnel_page['image'];
                    }

                    $output .= '
                    <div class="tpage [[SPAN]]">
                        <a href="' . Tools::getLink($page['id_domaine'], 'site', $page['permalink']) . '">';

                    if ($display_thumbnails) {
                        $output .= '<img class="thumbnail" src="' . $thumbnail . '" alt="' . $titre . '">';
                    }

                    $output .= '
                            <div class="title"' . ($style ? ' style="' . $style . '"' : '') . '>' . $titre . '</div>
                        </a>
                    </div>';
                }

                $span = '';
                if ($nb_pages and 'template2' != $template) {
                    $span = 'col-md-' . floor(12 / $nb_pages);

                    if ($nb_pages > 6) {
                        $width = floor(100 / $nb_pages * 100) / 100;
                        $width = number_format($width, 2, '.', '');
                        $output = \Learnybox\Helpers\TextVarHelper::replaceVar('SPAN', $span . '" style="width:' . $width . '%">', $output);
                    }
                }
                $output = \Learnybox\Helpers\TextVarHelper::replaceVar('SPAN', $span, $output);

                $output .= '</div>';
            }
        }

        return $output;
    }

    /********************************************************/
    /********************** FORMULAIRE **********************/
    /********************************************************/

    public function getForm($id_page, $id_element, $page, $element)
    {
        if (!isset($page['id_tunnel']) or !$page['id_tunnel']) {
            return;
        }

        $output = '';

        $infos = [];
        $font = $font_size = $font_color = '';

        if ($element['contenu']) {
            $infos = json_decode($element['contenu'], true);
            if (isset($infos['font'])) {
                $font = $infos['font'];
            }
            if (isset($infos['font_size'])) {
                $font_size = $infos['font_size'];
            }
            if (isset($infos['font_color'])) {
                $font_color = $infos['font_color'];
            }
        }
        if (!$font_size) {
            $font_size = 18;
        }

        if (!isset($infos['template']) or !$infos['template']) {
            $infos['template'] = 'template1';
        }

        $pages = [];
        if (isset($infos['pages'])) {
            $pages = $infos['pages'];
        }

        $output .= '
		<div class="tab-pane active" id="tab_funnel_menu">

            <div class="control-group" style="margin-top: 20px;">
				<label class="control-label" for="template">' . __('Thème') . '</label>
				<div class="controls">
					<label class="radio-inline" style="margin-right:10px">
						<input type="radio" data-no-uniform="true" name="funnel_menu[template]" id="template_1" value="template1" ' . (($infos['template'] and 'template1' == $infos['template']) ? 'checked' : '') . ' /> <img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/funnel_menu/template1.png') . '" style="width: 250px; margin-top: -20px;">
					</label>
					<br>
					<label class="radio-inline" style="margin-right:10px; margin-top:10px">
						<input type="radio" data-no-uniform="true" name="funnel_menu[template]" id="template_3" value="template3" ' . (($infos['template'] and 'template3' == $infos['template']) ? 'checked' : '') . ' /> <img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/funnel_menu/template3.png') . '" style="width: 350px; margin-top: -5px;">
					</label>
					<br>
					<label class="radio-inline" style="margin-right:10px; margin-top:10px">
						<input type="radio" data-no-uniform="true" name="funnel_menu[template]" id="template_2" value="template2" ' . (($infos['template'] and 'template2' == $infos['template']) ? 'checked' : '') . ' /> <img src="' . \Learnybox\Helpers\Assets::getImageUrl('tunnels/funnel_menu/template2.png') . '" style="margin-top: -5px; height:250px">
					</label>
				</div>
			</div>

        	<div class="form-group">
        		<label class="control-label">' . __('Afficher les vignettes') . '</label>
        		<div class="controls">
        		    <label for="display_thumbnails">
            		    <input type="checkbox" name="funnel_menu[display_thumbnails]" id="display_thumbnails" value="oui" ' . ((isset($infos['display_thumbnails']) and 'oui' == $infos['display_thumbnails']) ? 'checked' : '') . '> ' . __('Afficher les vignettes des pages') . '
                    </label>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="font_size">' . __('Police') . '</label>
                <div class="controls">
                    <select name="font">
                        ' . eden()->TunnelsPagesElements()->generateSelectFont($font) . '
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="font_size">' . __('Taille de la police') . '</label>
                <div class="controls">
                    <div class="input-group">
                        <input type="number" min="1" class="input-small form-control" name="font_size" id="font_size" value="' . $font_size . '">
                        <span class="input-group-addon">px</span>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="control-label" for="font_colorSelector' . $element['ID'] . '">' . __('Couleur du texte') . '</label>
                <div class="controls">
                    <input class="form-control input-small" type="text" name="font_color" maxlength="7" id="font_colorSelector' . $element['ID'] . '" value="' . $font_color . '">
                </div>
            </div>

        </div>

		<div class="tab-pane" id="tab_funnel_pages">';

        $tunnel_pages = [];
        if (isset($page['id_tunnel'])) {
            $tunnel_pages = eden()->TunnelsPages()->getPagesByTunnel($page['id_tunnel'], 'content');
        }
        if (!$tunnel_pages) {
            $output .= '<div class="alert alert-info">' . __('Aucune page de contenu trouvée dans votre tunnel de vente.') . '</div>';
        } else {
            foreach ($tunnel_pages as $tunnel_page) {
                $id_tunnel_page = $tunnel_page['id_page'];

                $output .= '
                    <h4><strong>' . $tunnel_page['nom'] . '</strong></h4>
                	<div class="form-group">
                		<div class="controls">
                		    <label for="pages_' . $id_tunnel_page . '_active">
                    		    <input type="checkbox" name="funnel_menu[pages][' . $id_tunnel_page . '][active]" id="pages_' . $id_tunnel_page . '_active" value="oui" ' . ((!isset($pages[$id_tunnel_page]) or (isset($pages[$id_tunnel_page]['active']) and $pages[$id_tunnel_page]['active'] == 'oui')) ? 'checked' : '') . '> ' . __('Cochez cette case pour insérer cette page dans le menu') . '
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                		<label class="control-label" for="pages_' . $id_tunnel_page . '_title">' . __('Titre de la page') . '</label>
                		<div class="controls">
                		    <input type="text" class="input form-control" name="funnel_menu[pages][' . $id_tunnel_page . '][title]" id="pages_' . $id_tunnel_page . '_title" value="' . ((isset($pages[$id_tunnel_page]['title']) and $pages[$id_tunnel_page]['title']) ? $pages[$id_tunnel_page]['title'] : $tunnel_page['nom']) . '">
                        </div>
                    </div>

                    <div class="form-group">
                		<label class="control-label" for="pages_' . $id_tunnel_page . '_title_unavailable">' . __('Titre de la page') . '<br><small>(' . __('page non disponible') . ')</small></label>
                		<div class="controls">
                		    <input type="text" class="input form-control" name="funnel_menu[pages][' . $id_tunnel_page . '][title_unavailable]" id="pages_' . $id_tunnel_page . '_title_unavailable" value="' . ((isset($pages[$id_tunnel_page]['title_unavailable']) and $pages[$id_tunnel_page]['title_unavailable']) ? $pages[$id_tunnel_page]['title_unavailable'] : __('Bientôt disponible')) . '">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label" for="image-' . $id_tunnel_page . '">' . __('Vignette') . ' <small>(max: 250x140px)</small></label>
    					<div class="controls">';

                if (isset($pages[$id_tunnel_page]['image']) and $pages[$id_tunnel_page]['image']) {
                    $output .= '
                        <div class="preview" id="preview-image-' . $id_tunnel_page . '" style="margin-left:0px">
                            <img class="img-thumbnail" src="' . $pages[$id_tunnel_page]['image'] . '" width="100"  />
                            <i class="fa fa-times" onclick="$(\'#image-' . $id_tunnel_page . '\').val(\'\'); $(\'#preview-image-' . $id_tunnel_page . '\').remove();"></i>
                        </div>
                        <div style="clear:both"></div>';
                }

                $output .= '
    						<div class="input-group">
    							<input class="form-control input-xxlarge" id="image-' . $id_tunnel_page . '" name="funnel_menu[pages][' . $id_tunnel_page . '][image]" type="text" value="' . ((isset($pages[$id_tunnel_page]['image']) and $pages[$id_tunnel_page]['image']) ? $pages[$id_tunnel_page]['image'] : '') . '" />
    							<span class="input-group-btn"><button class="btn btn-default" type="button" id="file" onClick="return selectFileWithCKFinder(\'image-' . $id_tunnel_page . '\');">' . __('Parcourir') . '</button></span>
    						</div>
    					</div>
                    </div>


                    <div class="form-group">
                        <label class="control-label" for="image-unavailable-' . $id_tunnel_page . '">' . __('Vignette') . ' <small>(' . __('page non disponible') . ')</small></label>
    					<div class="controls">';

                if (isset($pages[$id_tunnel_page]['image-unavailable']) and $pages[$id_tunnel_page]['image-unavailable']) {
                    $output .= '
                        <div class="preview" id="preview-image-unavailable-' . $id_tunnel_page . '" style="margin-left:0px">
                            <img class="img-thumbnail" src="' . $pages[$id_tunnel_page]['image-unavailable'] . '" width="100"  />
                            <i class="fa fa-times" onclick="$(\'#image-unavailable-' . $id_tunnel_page . '\').val(\'\'); $(\'#preview-image-unavailable-' . $id_tunnel_page . '\').remove();"></i>
                        </div>
                        <div style="clear:both"></div>';
                }

                $output .= '
    						<div class="input-group">
    							<input class="form-control input-xxlarge" id="image-unavailable-' . $id_tunnel_page . '" name="funnel_menu[pages][' . $id_tunnel_page . '][image-unavailable]" type="text" value="' . ((isset($pages[$id_tunnel_page]['image-unavailable']) and $pages[$id_tunnel_page]['image-unavailable']) ? $pages[$id_tunnel_page]['image-unavailable'] : '') . '" />
    							<span class="input-group-btn"><button class="btn btn-default" type="button" id="file" onClick="return selectFileWithCKFinder(\'image-unavailable-' . $id_tunnel_page . '\');">' . __('Parcourir') . '</button></span>
    						</div>
    					</div>
                    </div>';
            }
        }

        $tunnel_pages = eden()->TunnelsPages()->getPagesByTunnel($page['id_tunnel'], 'sale');
        if (!$tunnel_pages) {
            $output .= '<div class="alert alert-info">' . __('Aucune page de vente trouvée dans votre tunnel de vente.') . '</div>';
        } else {
            foreach ($tunnel_pages as $tunnel_page) {
                $id_tunnel_page = $tunnel_page['id_page'];

                $output .= '
                    <hr>
                    <h4>' . $tunnel_page['nom'] . '</h4>
                	<div class="form-group">
                		<label class="control-label">' . __('Activer') . '</label>
                		<div class="controls">
                		    <label for="pages_' . $id_tunnel_page . '_active">
                    		    <input type="checkbox" name="funnel_menu[pages][' . $id_tunnel_page . '][active]" id="pages_' . $id_tunnel_page . '_active" value="oui" ' . ((!isset($pages[$id_tunnel_page]) or (isset($pages[$id_tunnel_page]['active']) and $pages[$id_tunnel_page]['active'] == 'oui')) ? 'checked' : '') . '> ' . __('Cochez cette case pour insérer cette page dans le menu') . '
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                		<label class="control-label" for="pages_' . $id_tunnel_page . '_title">' . __('Titre de la page') . '</label>
                		<div class="controls">
                		    <input type="text" class="input form-control" name="funnel_menu[pages][' . $id_tunnel_page . '][title]" id="pages_' . $id_tunnel_page . '_title" value="' . ((isset($pages[$id_tunnel_page]['title']) and $pages[$id_tunnel_page]['title']) ? $pages[$id_tunnel_page]['title'] : $tunnel_page['nom']) . '">
                        </div>
                    </div>

                    <div class="form-group">
                		<label class="control-label" for="pages_' . $id_tunnel_page . '_title_unavailable">' . __('Titre de la page') . '<br><small>(' . __('page non disponible') . ')</small></label>
                		<div class="controls">
                		    <input type="text" class="input form-control" name="funnel_menu[pages][' . $id_tunnel_page . '][title_unavailable]" id="pages_' . $id_tunnel_page . '_title_unavailable" value="' . ((isset($pages[$id_tunnel_page]['title_unavailable']) and $pages[$id_tunnel_page]['title_unavailable']) ? $pages[$id_tunnel_page]['title_unavailable'] : __('Bientôt disponible')) . '">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label" for="image-' . $id_tunnel_page . '">' . __('Vignette') . '<br><small>(max: 250x140px)</small></label>
    					<div class="controls">';

                if (isset($pages[$id_tunnel_page]['image']) and $pages[$id_tunnel_page]['image']) {
                    $output .= '
                        <div class="preview" id="preview-image-' . $id_tunnel_page . '" style="margin-left:0px">
                            <img class="img-thumbnail" src="' . $pages[$id_tunnel_page]['image'] . '" width="100"  />
                            <i class="fa fa-times" onclick="$(\'#image-' . $id_tunnel_page . '\').val(\'\'); $(\'#preview-image-' . $id_tunnel_page . '\').remove();"></i>
                        </div>
                        <div style="clear:both"></div>';
                }

                $output .= '
    						<div class="input-group">
    							<input class="form-control input-xxlarge" id="image-' . $id_tunnel_page . '" name="funnel_menu[pages][' . $id_tunnel_page . '][image]" type="text" value="' . ((isset($pages[$id_tunnel_page]['image']) and $pages[$id_tunnel_page]['image']) ? $pages[$id_tunnel_page]['image'] : '') . '" />
    							<span class="input-group-btn"><button class="btn btn-default" type="button" id="file" onClick="return selectFileWithCKFinder(\'image-' . $id_tunnel_page . '\');">' . __('Parcourir') . '</button></span>
    						</div>
    					</div>
                    </div>

                    <div class="form-group">
                        <label class="control-label" for="image-unavailable-' . $id_tunnel_page . '">' . __('Vignette') . '<br><small>(' . __('page non disponible') . ')</small></label>
    					<div class="controls">';

                if (isset($pages[$id_tunnel_page]['image-unavailable']) and $pages[$id_tunnel_page]['image-unavailable']) {
                    $output .= '
                        <div class="preview" id="preview-image-unavailable-' . $id_tunnel_page . '" style="margin-left:0px">
                            <img class="img-thumbnail" src="' . $pages[$id_tunnel_page]['image-unavailable'] . '" width="100"  />
                            <i class="fa fa-times" onclick="$(\'#image-unavailable-' . $id_tunnel_page . '\').val(\'\'); $(\'#preview-image-unavailable-' . $id_tunnel_page . '\').remove();"></i>
                        </div>
                        <div style="clear:both"></div>';
                }

                $output .= '
    						<div class="input-group">
    							<input class="form-control input-xxlarge" id="image-unavailable-' . $id_tunnel_page . '" name="funnel_menu[pages][' . $id_tunnel_page . '][image-unavailable]" type="text" value="' . ((isset($pages[$id_tunnel_page]['image-unavailable']) and $pages[$id_tunnel_page]['image-unavailable']) ? $pages[$id_tunnel_page]['image-unavailable'] : '') . '" />
    							<span class="input-group-btn"><button class="btn btn-default" type="button" id="file" onClick="return selectFileWithCKFinder(\'image-unavailable-' . $id_tunnel_page . '\');">' . __('Parcourir') . '</button></span>
    						</div>
    					</div>
                    </div>';
            }
        }

        $output .= '
            </div>

            <script type="text/javascript">
            $("#font_colorSelector' . $element['ID'] . '").minicolors({
                opacity: false,
                theme: "bootstrap",
            });
            </script>';

        return $output;
    }

    /********************************************************/
    /************************ UPDATE ************************/
    /********************************************************/

    public function validatepost_element($cleaned_post)
    {
        $validPost = true;
        $error = '';

        return ['validPost' => $validPost, 'error' => $error];
    }

    public function update_element($cleaned_post)
    {
        $array_update = [];
        $array_update['contenu'] = $cleaned_post['funnel_menu'];

        $array_update['contenu']['font'] = filter_var($cleaned_post['font'], FILTER_SANITIZE_SPECIAL_CHARS);
        $array_update['contenu']['font_size'] = filter_var($cleaned_post['font_size'], FILTER_VALIDATE_INT);
        $array_update['contenu']['font_color'] = filter_var($cleaned_post['font_color'], FILTER_SANITIZE_SPECIAL_CHARS);

        return $array_update;
    }

    /********************************************************/
    /********************** FUNCTIONS ***********************/
    /********************************************************/
}
