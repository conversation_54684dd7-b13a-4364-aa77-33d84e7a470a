<?php

use Learnybox\Services\Partials\ConfigAddressService;

/**
 * TunnelsPagesElements_Affiliationoptin class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class TunnelsPagesElements_Affiliationoptin extends TunnelsPagesElements
{
    private $database;

    /**
     * @var \DI\Container
     */
    protected $container;

    public function __construct()
    {
        $this->database = MySQL::getInstance();
        $this->container = \Learnybox\Services\DI\ContainerBuilderService::getInstance();
    }

    /********************************************************/
    /********************** AFFICHAGE ***********************/
    /********************************************************/

    public function afficher_element($data_tunnel, $element)
    {
        $output = '';

        // clean post values
        $cleaned_post = clean_post($_POST);
        foreach ($cleaned_post as $key => $value) {
            if (!is_array($value)) {
                ${$key} = $value;
            }
        }

        $pays = 'FR';
        if (isset($cleaned_post['pays']) and $cleaned_post['pays']) {
            $pays = $cleaned_post['pays'];
        }
        $region = '';
        if (isset($cleaned_post['region']) and $cleaned_post['region']) {
            $region = $cleaned_post['region'];
        }


        if (isset($_SESSION['customer']) and $_SESSION['customer']) {
            if (!isset($cleaned_post['fname'])) {
                $cleaned_post['fname'] = $_SESSION['customer']['prenom'];
            }
            if (!isset($cleaned_post['lname'])) {
                $cleaned_post['lname'] = $_SESSION['customer']['nom'];
            }
            if (!isset($cleaned_post['code_postal']) and isset($_SESSION['customer']['cp'])) {
                $cleaned_post['code_postal'] = $_SESSION['customer']['cp'];
            }

            $vars = ['email', 'adresse', 'ville', 'code_postal', 'pays', 'region', 'societe'];
            foreach ($vars as $var) {
                if (!isset($cleaned_post[$var]) and isset($_SESSION['customer'][$var])) {
                    $cleaned_post[$var] = $_SESSION['customer'][$var];
                }
            }
        }

        $selectPays = $this->container->get(ConfigAddressService::class)->renderSelectCountry($cleaned_post);

        $origin = '';
        if (isset($data_tunnel['id_page'])) {
            $origin = $data_tunnel['id_page'];
            if (isset($data_tunnel['id_tunnel']) and $data_tunnel['id_tunnel']) {
                $origin = $data_tunnel['id_tunnel'] . '-' . $data_tunnel['id_page'];
            }
        }

        //création du formulaire
        if ('master' == $_SESSION['client_uniqid']) {
            $content = Eden_Template::i()->set('cleaned_post', $cleaned_post)
                ->set('selectPays', $selectPays)
                ->set('origin', $origin)
                ->parsePHP(VIEWS_PATH . '/affiliation/inscription_lb_tunnel.php');
        } else {
            $content = Eden_Template::i()->set('cleaned_post', $cleaned_post)
                ->set('selectPays', $selectPays)
                ->set('origin', $origin)
                ->parsePHP(VIEWS_PATH . '/affiliation/inscription_tunnel.php');
        }

        $style = [];
        if ($element['contenu']) {
            $style = json_decode($element['contenu'], true);
        }

        $align = 'center';
        if (isset($style['align']) and $style['align']) {
            $align = $style['align'];
        }

        $elementButton = $element;

        $elementButton['contenutexte'] = __('Valider');

        $get_button = $this->container->get(\Builder_Buttons::class)->displayButton('button', $elementButton, 'btn_submit_' . $elementButton['ID'], '', $data_tunnel);
        $button = '<div class="button_link" style="text-align:' . $align . ';">' . $get_button['output'] . '</div>';
        if ($get_button['style']) {
            $output .= '<style type="text/css">' . $get_button['style'] . '</style>';
        }

        $content = \Learnybox\Helpers\TextVarHelper::replaceVar('BUTTON', $button, $content);

        if ($element['contenutexte']) {
            $content = \Learnybox\Helpers\TextVarHelper::replaceVar('REDIRECT', '[[REDIRECT]]' . "\n" . '<input type="hidden" name="campaigns" value="' . $element['contenutexte'] . '">', $content);
        }

        $redirection = '';
        if ($element['urls'] and false !== strpos($element['urls'], '-')) {
            $explode = explode('-', $element['urls']);
            $id_tunnel = $explode[0];
            $id_page = $explode[1];

            if (is_numeric($id_tunnel) and is_numeric($id_page)) {
                $tunnel_page = eden()->TunnelsPages()->getPageById($id_page, $id_tunnel);
                if ($tunnel_page) {
                    $permalink = $tunnel_page['permalink'];
                    $redirection = Tools::getLink($tunnel_page['id_domaine'], 'site', $tunnel_page['permalink']);
                }
            }
        } elseif ($element['urls'] and is_numeric($element['urls'])) {
            $page = eden()->Pages()->getPageById($element['urls']);
            if ($page) {
                $redirection = Tools::getLink($page['id_domaine'], 'site', $page['permalink']);
            }
        }

        $content = \Learnybox\Helpers\TextVarHelper::replaceVar('REDIRECT', $redirection ? '<input type="hidden" name="redirect" value="' . $redirection . '">' : '', $content);

        $output .= $content;

        return $output;
    }

    public function admin_display_element($element, $data_tunnel = [], $parent_span = 'span12')
    {
        $output = '';

        $output .= '<div class="alert alert-info"><h4>' . __('Formulaire d\'inscription affilié') . '</h4></div>';

        return $output;
    }

    /********************************************************/
    /********************** FORMULAIRE **********************/
    /********************************************************/

    public function getForm($id_page, $id_element, $page, $element)
    {
        $output = '';

        $selectPage = eden()->Pages()->generateSelectAllPages($element['urls']);

        $output .= '
            <div class="form-group">
				<label class="control-label" for="urls">' . __('Redirection après inscription') . '</label>
				<div class="controls">
					<select name="urls" id="urls" data-rel="select2">
					    ' . $selectPage . '
                    </select>
				</div>
			</div>';

        $campaigns_privees = [];
        $campaigns = eden()->Aff_Campaigns()->getAllCampaigns();
        if ($campaigns) {
            foreach ($campaigns as $campaign) {
                if ('privee' == $campaign['type']) {
                    array_push($campaigns_privees, $campaign);
                }
            }
        }

        if ($campaigns_privees) {
            $campaigns_actives = explode(',', $element['contenutexte']);

            $output .= '
                <div class="form-group">
                    <label class="control-label" for="campaigns">' . __('Inscrire les affiliés dans les campagnes privées suivantes : ') . '</label>
                    <span class="help-block">' . __('Une fois inscrit, vos affiliés auront accès à toutes vos campagnes publiques, mais vous pouvez leur donner accès à certaines de vos campagnes privées en les sélectionnant ci-dessous.') . '</span>';

            foreach ($campaigns_privees as $id => $_campaign) {
                $output .= '
                <div class="checkbox">
                    <label for="campaign' . $_campaign['id_campaign'] . '">
                        <input type="checkbox" id="campaign' . $_campaign['id_campaign'] . '" name="campaigns[]" value="' . $_campaign['id_campaign'] . '"';

                if ($campaigns_actives and in_array($_campaign['id_campaign'], $campaigns_actives)) {
                    $output .= ' checked';
                }

                $output .= '>' . $_campaign['nom'] . '
                    </label>
                </div>';
            }

            $output .= '</div>';
        }

        return $output;
    }

    /********************************************************/
    /************************ UPDATE ************************/
    /********************************************************/

    public function validatepost_element($cleaned_post)
    {
        $validPost = true;
        $error = '';

        return ['validPost' => $validPost, 'error' => $error];
    }

    public function update_element($cleaned_post)
    {
        $array_update = [];
        $array_update['urls'] = filter_var($cleaned_post['urls'], FILTER_SANITIZE_SPECIAL_CHARS);

        if (isset($cleaned_post['campaigns'])) {
            $campaigns = filter_var($cleaned_post['campaigns'], FILTER_VALIDATE_INT, FILTER_REQUIRE_ARRAY);
            if ($campaigns) {
                $array_update['contenutexte'] = implode(',', $campaigns);
            }
        }

        return $array_update;
    }

    /********************************************************/
    /********************** FUNCTIONS ***********************/
    /********************************************************/
}
