<?php

/**
 * TunnelsPagesElements_box class.
 *
 * @extends Eden_Class
 *
 * @category Model
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see     http://thieumlabs.com
 */
class TunnelsPagesElements_Box extends TunnelsPagesElements
{
    private $database;

    public function __construct()
    {
        $this->database = MySQL::getInstance();
    }

    /********************************************************/
    /********************** AFFICHAGE ***********************/
    /********************************************************/

    public function afficher_element($data_tunnel, $element)
    {
        $output = '';
        $js = '';
        $iframeResizer = false;
        $social_share = false;
        $formulaire_paiement = false;
        $modalPopup = '';

        $p_style_colors = '';
        if ($element['contenu']) {
            $colors = json_decode($element['contenu'], true);
            if (isset($colors['p_bg_color']) and $colors['p_bg_color']) {
                $p_style_colors .= 'background: ' . $colors['p_bg_color'] . ';';
            }
            if (isset($colors['p_bg_opacity']) and $colors['p_bg_opacity']) {
                $p_style_colors .= 'opacity: ' . $colors['p_bg_opacity'] . ';';
            }
            if (isset($colors['p_border_color']) and $colors['p_border_color']) {
                $p_style_colors .= 'border: 1px solid ' . $colors['p_border_color'] . ';';
            }
            if (isset($colors['p_border_radius']) and $colors['p_border_radius']) {
                $p_style_colors .= 'border-radius: ' . $colors['p_border_radius'] . 'px; -moz-border-radius: ' . $colors['p_border_radius'] . 'px; -webkit-border-radius: ' . $colors['p_border_radius'] . 'px;';
            }
            if (isset($colors['p_height']) and $colors['p_height']) {
                $p_style_colors .= 'height: ' . $colors['p_height'] . 'px;';
            }
        }

        if ($p_style_colors) {
            $output .= '<style type="text/css">#ModalBox' . $element['ID'] . ' .modal-content { ' . $p_style_colors . ' }</style>';
        }

        if (isset($colors['p_width']) and $colors['p_width']) {
            $output .= '<style type="text/css">@media (min-width:768px) { #ModalBox' . $element['ID'] . ' .modal-dialog { width: ' . $colors['p_width'] . 'px; } #ModalBox' . $element['ID'] . ' .modal-content { width: ' . $colors['p_width'] . 'px; } }</style>';
        }

        if ($element['eval']) {
            $icone = '';
            $align = 'left';
            $color = '';
            $color_text = '';
            $size = '';
            $border = $border_color = $border_radius = null;
            $hover_color = $hover_color_text = '';
            $hover_border = $hover_border_color = null;
            if ($element['contenu']) {
                $contenu = json_decode($element['contenu'], true);
                if (isset($contenu['icone']) and $contenu['icone']) {
                    $icone = $contenu['icone'];
                }
                if (isset($contenu['align']) and $contenu['align']) {
                    $align = $contenu['align'];
                }
                if (isset($contenu['color']) and $contenu['color']) {
                    $color = $contenu['color'];
                }
                if (isset($contenu['color_text']) and $contenu['color_text']) {
                    $color_text = $contenu['color_text'];
                }
                if (isset($contenu['size']) and $contenu['size']) {
                    $size = $contenu['size'];
                }
                if (isset($contenu['border'])) {
                    $border = $contenu['border'];
                }
                if (isset($contenu['border_color']) and $contenu['border_color']) {
                    $border_color = $contenu['border_color'];
                }
                if (isset($contenu['border_radius'])) {
                    $border_radius = $contenu['border_radius'];
                }

                if (isset($contenu['hover_color']) and $contenu['hover_color']) {
                    $hover_color = $contenu['hover_color'];
                }
                if (isset($contenu['hover_color_text']) and $contenu['hover_color_text']) {
                    $hover_color_text = $contenu['hover_color_text'];
                }
                if (isset($contenu['hover_border'])) {
                    $hover_border = $contenu['hover_border'];
                }
                if (isset($contenu['hover_border_color'])) {
                    $hover_border_color = $contenu['hover_border_color'];
                }
            }

            //style du bouton
            $style = '';
            if ($color) {
                $style .= 'background:' . $color . ';';
            }
            if ($color_text) {
                $style .= 'color:' . $color_text . ';';
            }
            if (null !== $border) {
                $style .= 'border:' . $border . 'px solid;';
            }
            if (null !== $border_color) {
                $style .= 'border-color:' . $border_color . ';';
            }
            if (null !== $border_radius) {
                $style .= 'border-radius: ' . $border_radius . 'px; -webkit-border-radius: ' . $border_radius . 'px; -moz-border-radius: ' . $border_radius . 'px;';
            }

            $hover_style = '';
            if ($hover_color) {
                $hover_style .= 'background:' . $hover_color . ' !important;';
            }
            if ($hover_color_text) {
                $hover_style .= 'color:' . $hover_color_text . ' !important;';
            }
            if (null !== $hover_border) {
                $hover_style .= 'border:' . $hover_border . 'px solid !important;';
            }
            if (null !== $hover_border_color) {
                $hover_style .= 'border-color:' . $hover_border_color . ' !important;';
            }

            if ($hover_style) {
                $output .= '<style type="text/css">#btn_box_' . $element['ID'] . ':hover { ' . $hover_style . ' }</style>';
            }

            if ($element['url_webm']) {
                $style = $width = $height = '';
                if ($element['largeur']) {
                    $width = ' width="' . $element['largeur'] . '"';
                    $style .= 'width:' . $element['largeur'] . 'px;';
                }
                if ($element['hauteur']) {
                    $height = ' height="' . $element['hauteur'] . '"';
                    $style .= 'height:' . $element['hauteur'] . 'px;';
                }

                $output .= '
				    <div class="button_link" style="text-align:' . $align . ';">
				        <a href="#ModalBox' . $element['ID'] . '" data-toggle="modal"><img src="' . $element['url_webm'] . '" ' . $width . $height . ' ' . ($style ? 'style="' . $style . '"' : '') . '></a>
				    </div>';
            } else {
                $output .= '
				    <div class="button_link" style="text-align:' . $align . ';">
				        <a class="btn btn-primary ' . $size . '" id="btn_box_' . $element['ID'] . '" href="#ModalBox' . $element['ID'] . '" data-toggle="modal" style="' . $style . '">' . ($icone ? '<i class="fa ' . $icone . '"></i> ' : '') . $element['contenutexte'] . '</a>
				    </div>';
            }

            $modalPopup .= '
                <div class="modal fade" id="ModalBox' . $element['ID'] . '">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true" style="margin-right: 5px;">×</button>
                            <div class="modal-body">
                                <div class="row">';
        }

        if ($element['y']) {
            $output .= '<div style="height:' . $element['y'] . 'px">';
        }

        //affichage des éléments de cette box
        if (isset($data_tunnel['idformation'])) {
            $elements = eden()->Formation_PagesElements()->getPageElementsByBox($data_tunnel['idpage'], $element['ID']);

            if ($elements) {
                foreach ($elements as $_element) {
                    $afficher_element = eden()->Formation_PagesElements()->afficher_element($data_tunnel, $_element);
                    if ($element['eval']) {
                        $modalPopup .= $afficher_element['output'];
                    } else {
                        $output .= $afficher_element['output'];
                    }
                    $js .= $afficher_element['js'];
                    if (isset($afficher_element['iframeResizer']) and $afficher_element['iframeResizer']) {
                        $iframeResizer = true;
                    }
                }
            }
        } else {
            if (isset($data_tunnel['id_tunnel'])) {
                $elements = eden()->TunnelsPagesElements()->getPageElementsByBox($data_tunnel['id_page'], $element['ID']);
            } elseif (isset($data_tunnel['id_popup'])) {
                $elements = eden()->Popups_Elements()->getPopupElementsByBox($data_tunnel['id_popup'], $element['ID']);
            } elseif (isset($data_tunnel['id_mail'])) {
                $elements = eden()->Lbar_Mails_Elements()->getMailElementsByBox($element['id_mail'], $element['ID']);
            } elseif (isset($data_tunnel['id_formulaire'])) {
                $elements = eden()->Lbar_Formulaires_Elements()->getFormulaireElementsByBox($element['id_formulaire'], $element['ID']);
            } else {
                $elements = eden()->Pages_Elements()->getPageElementsByBox($data_tunnel['id_page'], $element['ID']);
            }

            if ($elements) {
                foreach ($elements as $_element) {
                    if ($_element['hide']) {
                        continue;
                    }

                    $data_tunnel['in_box'] = true;

                    if (isset($data_tunnel['id_tunnel'])) {
                        $afficher_element = eden()->TunnelsPagesElements()->afficher_element($data_tunnel, $_element);
                    } elseif (isset($data_tunnel['id_popup'])) {
                        $afficher_element = eden()->Popups_Elements()->afficher_element($data_tunnel, $_element);
                    } elseif (isset($data_tunnel['id_mail'])) {
                        $afficher_element = eden()->Lbar_Mails_Elements()->afficher_element($data_tunnel, $_element);
                    } elseif (isset($data_tunnel['id_formulaire'])) {
                        $afficher_element = eden()->Lbar_Formulaires_Elements()->afficher_element($data_tunnel, $_element);
                    } else {
                        $afficher_element = eden()->Pages_Elements()->afficher_element($data_tunnel, $_element);
                    }

                    if ($element['eval']) {
                        $modalPopup .= $afficher_element['output'];
                    } else {
                        if (isset($data_tunnel['id_mail'])) {
                            $output .= '<table style="width: 100%;"><tr>' . $afficher_element['output'] . '</tr></table>';
                        } else {
                            $output .= $afficher_element['output'];
                        }
                    }

                    $js .= $afficher_element['js'];
                    if (isset($afficher_element['iframeResizer']) and $afficher_element['iframeResizer']) {
                        $iframeResizer = true;
                    }
                    if (isset($afficher_element['social_share']) and $afficher_element['social_share']) {
                        $social_share = true;
                    }
                    if (isset($afficher_element['formulaire_paiement']) and $afficher_element['formulaire_paiement']) {
                        $formulaire_paiement = true;
                    }
                }
            }
        }

        $output .= '<div style="clear:both"></div>';

        if ($element['y']) {
            $output .= '</div>';
        }

        //close ModalBox
        if ($element['eval']) {
            $modalPopup .= '
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            ';
            $modalPopup = eden()->Csrf()->replaceForm($modalPopup);
        }
        $output .= $modalPopup;
        $js .= '<script type="text/javascript">
              function ModalEvent(id_element) {
                $(\'#ModalBox\'+id_element).on(\'shown.bs.modal\', function () {
                  $(\'body\').addClass(\'modal-open\');
                  if ($(\'#ModalBox\'+id_element+\' .videoplayer\').length > 0) {
                    $(\'#ModalBox\'+id_element+\' .videoplayer\').each(function() {
                      if ($(this).find(\'video\').length > 0) {
                        if (typeof $(this).find(\'video\').get(0).player == \'object\')
                          $(this).find(\'video\').get(0).player.remove();
                        $(this).find(\'video\').mediaelementplayer();
                      }
                    });
                  }
                });
                $(\'#ModalBox\'+id_element).on(\'hide.bs.modal\', function () {
                  $(\'body\').removeClass(\'modal-open\');
                  if ($(\'#ModalBox\'+id_element+\' .videoplayer\').length > 0) {
                    $(\'#ModalBox\'+id_element+\' .videoplayer\').each(function() {
                      if ($(this).find(\'video\').length > 0) {
                        $(this).find(\'video\').get(0).player.remove();
                      }
                    });
                  }
                  setTimeout(function() {
                    var clone = $(\'#ModalBox\'+id_element).clone();
                    $(\'#ModalBox\'+id_element).remove();
                    $(\'body\').append(clone);
                    ModalEvent(id_element);
                  }, 300);
                });
              }

              ModalEvent(' . $element['ID'] . ');
            </script>';

        return ['output' => $output, 'js' => $js, 'iframeResizer' => $iframeResizer, 'social_share' => $social_share, 'formulaire_paiement' => $formulaire_paiement];
    }

    public function admin_display_element($element, $data_tunnel = [], $parent_span = 'span12')
    {
        $output = '
            <div class="box-header-nav black">
                <h2><i class="fa fa-inbox"></i> ' . __('Boîte') . '</h2>
            </div>';

        if (isset($data_tunnel['idformation'])) {
            $output .= '
            <div class="boxcontainer" data-id_page="' . $element['idpage'] . '" ' . ($element['y'] ? 'style="height:' . ($element['y'] + 40) . 'px"' : '') . '>
                <div class="content" id="box-content' . $element['ID'] . '">';
        } elseif (isset($data_tunnel['id_popup'])) {
            $output .= '
            <div class="boxcontainer" data-id_popup="' . $element['id_popup'] . '" data-id_line="' . $element['id_line'] . '" ' . ($element['y'] ? 'style="height:' . ($element['y'] + 40) . 'px"' : '') . '>
                <div class="content" id="box-content' . $element['ID'] . '">';
        } elseif (isset($data_tunnel['id_mail'])) {
            $output .= '
            <div class="boxcontainer" data-id_mail="' . $element['id_mail'] . '" data-id_line="' . $element['id_line'] . '" ' . ($element['y'] ? 'style="height:' . ($element['y'] + 40) . 'px"' : '') . '>
                <div class="content" id="box-content' . $element['ID'] . '">';
        } elseif (isset($data_tunnel['id_formulaire'])) {
            $output .= '
            <div class="boxcontainer" data-id_formulaire="' . $element['id_formulaire'] . '" data-id_line="' . $element['id_line'] . '" ' . ($element['y'] ? 'style="height:' . ($element['y'] + 40) . 'px"' : '') . '>
                <div class="content" id="box-content' . $element['ID'] . '">';
        } else {
            $output .= '
            <div class="boxcontainer" data-id_page="' . $element['id_page'] . '" data-id_line="' . $element['id_line'] . '" ' . ($element['y'] ? 'style="height:' . ($element['y'] + 40) . 'px"' : '') . '>
                <div class="content" id="box-content' . $element['ID'] . '">';
        }

        $p_style_colors = '';
        if ($element['eval']) {
            //on affiche le bouton + les éléments cachés
            $icone = '';
            $align = 'left';
            $color = '';
            $color_text = '';
            $size = '';
            $border = $border_color = $border_radius = null;
            if ($element['contenu']) {
                $contenu = json_decode($element['contenu'], true);
                if (isset($contenu['icone']) and $contenu['icone']) {
                    $icone = $contenu['icone'];
                }
                if (isset($contenu['align']) and $contenu['align']) {
                    $align = $contenu['align'];
                }
                if (isset($contenu['color']) and $contenu['color']) {
                    $color = $contenu['color'];
                }
                if (isset($contenu['color_text']) and $contenu['color_text']) {
                    $color_text = $contenu['color_text'];
                }
                if (isset($contenu['size']) and $contenu['size']) {
                    $size = $contenu['size'];
                }
                if (isset($contenu['border'])) {
                    $border = $contenu['border'];
                }
                if (isset($contenu['border_color']) and $contenu['border_color']) {
                    $border_color = $contenu['border_color'];
                }
                if (isset($contenu['border_radius'])) {
                    $border_radius = $contenu['border_radius'];
                }

                //styles du popup
                if (isset($contenu['p_bg_color']) and $contenu['p_bg_color']) {
                    $p_style_colors .= 'background: ' . $contenu['p_bg_color'] . ';';
                } else {
                    $p_style_colors .= 'background: #FFFFFF;';
                }
                if (isset($contenu['p_bg_opacity']) and $contenu['p_bg_opacity']) {
                    $p_style_colors .= 'opacity: ' . $contenu['p_bg_opacity'] . ';';
                }
                if (isset($contenu['p_border_color']) and $contenu['p_border_color']) {
                    $p_style_colors .= 'border: 1px solid ' . $contenu['p_border_color'] . ';';
                }
                if (isset($contenu['p_border_radius']) and $contenu['p_border_radius']) {
                    $p_style_colors .= 'border-radius: ' . $contenu['p_border_radius'] . 'px; -moz-border-radius: ' . $contenu['p_border_radius'] . 'px; -webkit-border-radius: ' . $contenu['p_border_radius'] . 'px;';
                }
            }

            //style du popup
            if (!$p_style_colors) {
                $p_style_colors .= 'background: #FFFFFF;';
            }

            //style du bouton
            $style = '';
            if ($color) {
                $style .= 'background:' . $color . ';';
            }
            if ($color_text) {
                $style .= 'color:' . $color_text . ';';
            }
            if (null !== $border) {
                $style .= 'border:' . $border . 'px solid;';
            }
            if (null !== $border_color) {
                $style .= 'border-color:' . $border_color . ';';
            }
            if (null !== $border_radius) {
                $style .= 'border-radius: ' . $border_radius . 'px; -webkit-border-radius: ' . $border_radius . 'px; -moz-border-radius: ' . $border_radius . 'px;';
            }

            $output .= '
                <div class="box box-element col-md-12" id="' . $element['ID'] . '">
                    <div class="box-content">';

            if ($element['url_webm']) {
                $style = $width = $height = '';
                if ($element['largeur']) {
                    $width = ' width="' . $element['largeur'] . '"';
                    $style .= 'width:' . $element['largeur'] . 'px;';
                }
                if ($element['hauteur']) {
                    $height = ' height="' . $element['hauteur'] . '"';
                    $style .= 'height:' . $element['hauteur'] . 'px;';
                }

                $output .= '
				    <div class="button_link" style="text-align:' . $align . '">
				        <a><img src="' . $element['url_webm'] . '" ' . $width . $height . ' ' . ($style ? 'style="' . $style . '"' : '') . '></a>
				    </div>';
            } else {
                $output .= '
				    <div class="button_link" style="text-align:' . $align . '">
				        <a class="btn btn-primary ' . $size . '" style="' . $style . '">' . ($icone ? '<i class="fa ' . $icone . '"></i> ' : '') . $element['contenutexte'] . '</a>
				    </div>';
            }

            $output .= '
                        <a class="btn btn-small btn-default" onclick="ShowBoxElements(' . $element['ID'] . ');" style="position:absolute; top:-25px; right:0px;">' . __('Afficher les éléments') . '</a>
                    </div>
                </div>
                <div style="clear:both"></div>';
        }

        //affichage des éléments
        $output .= '<div id="box_elements' . $element['ID'] . '" style="' . $p_style_colors . ' display:' . ($element['eval'] ? 'none' : 'block') . '">';

        if (isset($data_tunnel['idformation'])) {
            $box_elements = eden()->Formation_PagesElements()->getPageElementsByBox($element['idpage'], $element['ID']);
            if ($box_elements) {
                foreach ($box_elements as $box_element) {
                    $output .= eden()->Formation_PagesElements()->admin_display_element($box_element, $data_tunnel, $element['span']);
                }
            }
            $output .= '
                    <div id="BoxAddElement' . $element['ID'] . '" style="clear:both"></div>
                    <br><a class="btn btn-primary btn-small btn-add-element" id="BtnAddElement" onclick="$(\'#insertafterelement\').val(0); $(\'#insertinbox\').val(' . $element['ID'] . '); $(\'#AddElement\').modal(\'show\');"><i class="fa fa-plus"></i> ' . __('Ajouter un élément') . '</a>';
        } else {
            if (isset($data_tunnel['id_tunnel'])) {
                $box_elements = eden()->TunnelsPagesElements()->getPageElementsByBox($element['id_page'], $element['ID']);
                if ($box_elements) {
                    foreach ($box_elements as $box_element) {
                        $output .= eden()->TunnelsPagesElements()->admin_display_element($box_element, $data_tunnel, $element['span']);
                    }
                }
            } elseif (isset($data_tunnel['id_popup'])) {
                $box_elements = eden()->Popups_Elements()->getPopupElementsByBox($element['id_popup'], $element['ID']);
                if ($box_elements) {
                    foreach ($box_elements as $box_element) {
                        $output .= eden()->Popups_Elements()->admin_display_element($box_element, $data_tunnel, $element['span']);
                    }
                }
            } elseif (isset($data_tunnel['id_mail'])) {
                $box_elements = eden()->Lbar_Mails_Elements()->getMailElementsByBox($element['id_mail'], $element['ID']);
                if ($box_elements) {
                    foreach ($box_elements as $box_element) {
                        $output .= eden()->Lbar_Mails_Elements()->admin_display_element($box_element, $data_tunnel, $element['span']);
                    }
                }
            } elseif (isset($data_tunnel['id_formulaire'])) {
                $box_elements = eden()->Lbar_Formulaires_Elements()->getFormulaireElementsByBox($element['id_formulaire'], $element['ID']);
                if ($box_elements) {
                    foreach ($box_elements as $box_element) {
                        $output .= eden()->Lbar_Formulaires_Elements()->admin_display_element($box_element, $data_tunnel, $element['span']);
                    }
                }
            } else {
                $box_elements = eden()->Pages_Elements()->getPageElementsByBox($element['id_page'], $element['ID']);
                if ($box_elements) {
                    foreach ($box_elements as $box_element) {
                        $output .= eden()->Pages_Elements()->admin_display_element($box_element, $data_tunnel, $element['span']);
                    }
                }
            }

            if (isset($_SESSION['editor_level']) and 'expert' == $_SESSION['editor_level']) {
                $output .= '
                    <div id="BoxAddElement' . $element['ID'] . '" style="clear:both"></div>
                    <br><a class="btn btn-primary btn-small btn-add-element" id="BtnAddElement" onclick="$(\'#insertinline\').val(' . $element['id_line'] . '); $(\'#insertafterelement\').val(0); $(\'#insertinbox\').val(' . $element['ID'] . '); $(\'#AddElement\').modal(\'show\');"><i class="fa fa-plus"></i> ' . __('Ajouter un élément') . '</a>';
            }
        }

        $output .= '
                    </div>
                    <div style="clear:both"></div>
                </div>
            </div>';

        return $output;
    }

    /********************************************************/
    /********************** FORMULAIRE **********************/
    /********************************************************/

    public function getForm($id_page, $id_element, $page, $element)
    {
        $output = '';

        $p_bg_color = $p_bg_opacity = $p_border_color = $p_border_radius = $p_width = $p_height = '';

        if ($element['contenu']) {
            $contenu = json_decode($element['contenu'], true);
            if (isset($contenu['p_bg_color']) and $contenu['p_bg_color']) {
                $p_bg_color = $contenu['p_bg_color'];
            }
            if (isset($contenu['p_bg_opacity']) and $contenu['p_bg_opacity']) {
                $p_bg_opacity = $contenu['p_bg_opacity'];
            }
            if (isset($contenu['p_border_color']) and $contenu['p_border_color']) {
                $p_border_color = $contenu['p_border_color'];
            }
            if (isset($contenu['p_border_radius']) and $contenu['p_border_radius']) {
                $p_border_radius = $contenu['p_border_radius'];
            }
            if (isset($contenu['p_width']) and $contenu['p_width']) {
                $p_width = $contenu['p_width'];
            }
            if (isset($contenu['p_height']) and $contenu['p_height']) {
                $p_height = $contenu['p_height'];
            }
        }

        $buttonForm = eden()->TunnelsPagesElements()->getButtonForm($element);

        $output .= '
        <div class="tab-pane active" id="tab_box">

            <div class="form-group">
				<label class="control-label" for="box_height">' . __('Hauteur de la boîte') . '</label>
				<div class="controls">
				    <input class="form-control input-small" type="number" min="0" max="9999" name="box_height" maxlength="4" value="' . $element['y'] . '">
               </div>
               <p><small><em>' . __('Utilisez ce paramètre pour forcer la hauteur de la boîte. Laissez vide ou à 0 pour que la hauteur soit calculée automatiquement.') . '</em></small></p>
            </div>

        </div>

        <div class="tab-pane" id="tab_popup">

            <div class="form-group">
                <label class="control-label">' . __('Lightbox (Popup)') . '</label>
                <div class="controls">
                    <label for="eval">
                        <input type="checkbox" name="eval" id="eval" ' . ($element['eval'] ? 'checked' : '') . ' onchange="$(\'#button_config\').toggle();" /> ' . __('Cochez cette case pour configurer cette boîte comme une lightbox et configurez l\'apparence du bouton ci-dessous.') . '
                    </label>
                </div>
            </div>


            <div id="button_config" style="display:' . ($element['eval'] ? 'block' : 'none') . '">

				<hr>
				<h4>' . __('Configuration de la lightbox (popup)') . '</h4>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                    		<label class="control-label" for="p_colorSelector' . $element['ID'] . '">' . __('Couleur de fond') . '</label>
                    		<div class="controls">
                    		    <input class="form-control input-small" type="text" name="p_bg_color" maxlength="7" data-opacity="' . $p_bg_opacity . '" id="p_colorSelector' . $element['ID'] . '" value="' . $p_bg_color . '">
                    		    <input type="hidden" name="p_bg_opacity" id="p_colorSelector' . $element['ID'] . 'opacity" value="' . $p_bg_opacity . '">
                            </div>
                        </div>

                        <div class="form-group">
                    		<label class="control-label" for="p_colorSelector' . $element['ID'] . '2">' . __('Couleur de la bordure') . '</label>
                    		<div class="controls">
                    		    <input class="form-control input-small" type="text" name="p_border_color" maxlength="7" id="p_colorSelector' . $element['ID'] . '2" value="' . $p_border_color . '">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label" for="p_border_radius' . $element['ID'] . '2">' . __('Arrondi de la bordure') . '</label>
                    		<div class="controls">
                    		    <div class="input-group">
                        		    <input class="form-control input-small" type="number" name="p_border_radius" min="0" max="999" value="' . $p_border_radius . '">
                        		    <span class="input-group-addon">' . __('pixels') . ' (px)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                			<label class="control-label" for="box_height">' . __('Dimensions du popup') . '</label>
            			    <span class="help-block">' . __('Utilisez ce paramètre pour forcer les dimensions du popup. Laissez vide pour utiliser les dimensions par défaut.') . '</span>
                			<div class="controls">
                			    <input class="form-control input-small pull-left" type="number" min="0" max="9999" name="p_width" maxlength="4" value="' . $p_width . '">
                			    <span class="pull-left" style="padding-top:5px">&nbsp;x&nbsp;</span>
                			    <input class="form-control input-small pull-left" type="number" min="0" max="9999" name="p_height" maxlength="4" value="' . $p_height . '">
                			    <div style="clear:both"></div>
                           </div>
                        </div>
                    </div>
                </div>

				' . $buttonForm . '

			</div>

		</div>

        <script type="text/javascript">
			$("#p_colorSelector' . $element['ID'] . '").minicolors({
				opacity: true,
				theme: "bootstrap",
				change: function(hex, opacity) {
					if( !hex ) return;
					if( opacity ) hex += ", " + opacity;
					if( opacity )
					    $("#p_colorSelector' . $element['ID'] . 'opacity").val(opacity);
				},
			});

			$("#p_colorSelector' . $element['ID'] . '2").minicolors({
				opacity: false,
				theme: "bootstrap",
			});
		</script>';

        return $output;
    }

    /********************************************************/
    /************************ UPDATE ************************/
    /********************************************************/

    public function validatepost_element($cleaned_post)
    {
        $validPost = true;
        $error = '';

        if (isset($cleaned_post['eval']) and $cleaned_post['eval']) {
            $url_webm = filter_var($cleaned_post['url_webm'], FILTER_SANITIZE_SPECIAL_CHARS);
            $texte = filter_var($cleaned_post['contenu'], FILTER_UNSAFE_RAW);

            if (!$texte and !$url_webm) {
                $validPost = false;
                $error .= '<li>' . __('Veuillez entrer le texte du bouton ou sélectionner une image') . '</li>';
            }
        }

        return ['validPost' => $validPost, 'error' => $error];
    }

    public function update_element($cleaned_post)
    {
        $array_update = [];

        $array_contenu = [];

        $box_height = filter_var($cleaned_post['box_height'], FILTER_VALIDATE_INT);
        if ($box_height and $box_height < 0) {
            $box_height = 0;
        }
        $array_update['y'] = $box_height;

        //configuration du bouton
        if (isset($cleaned_post['eval']) and $cleaned_post['eval']) {
            $array_update['eval'] = 1;

            $array_update['url_webm'] = filter_var($cleaned_post['url_webm'], FILTER_SANITIZE_SPECIAL_CHARS);
            $array_update['contenutexte'] = filter_var($cleaned_post['contenu'], FILTER_UNSAFE_RAW);
            $array_update['contenutexte'] = nl2br($array_update['contenutexte']);

            if ($array_update['url_webm']) {
                $largeur = filter_var($cleaned_post['largeur'], FILTER_VALIDATE_INT);
                $hauteur = filter_var($cleaned_post['hauteur'], FILTER_VALIDATE_INT);

                if (!$largeur and false !== strpos($array_update['url_webm'], SITE_URL)) {
                    $full_url = WEB_PATH . '/' . str_replace(SITE_URL, '', $array_update['url_webm']);
                    $full_url = urldecode($full_url);

                    $getID3 = new getID3();
                    $imageInfos = $getID3->analyze($full_url);
                    if ($imageInfos and isset($imageInfos['video']['resolution_x']) and isset($imageInfos['video']['resolution_y'])) {
                        $largeur = $imageInfos['video']['resolution_x'];
                        $hauteur = $imageInfos['video']['resolution_y'];
                    }
                }

                $array_update['largeur'] = $largeur;
                $array_update['hauteur'] = $hauteur;
            }

            $icone = '';
            $align = 'left';
            $color = '';
            $color_text = '';
            $size = '';

            if (isset($cleaned_post['icone'])) {
                $array_contenu['icone'] = filter_var($cleaned_post['icone'], FILTER_SANITIZE_SPECIAL_CHARS);
            }

            if (isset($cleaned_post['align'])) {
                $array_contenu['align'] = filter_var($cleaned_post['align'], FILTER_SANITIZE_SPECIAL_CHARS);
            }

            if (isset($cleaned_post['color']) and $cleaned_post['color']) {
                $array_contenu['color'] = filter_var($cleaned_post['color'], FILTER_SANITIZE_SPECIAL_CHARS);
            }

            if (isset($cleaned_post['color_text']) and $cleaned_post['color_text']) {
                $array_contenu['color_text'] = filter_var($cleaned_post['color_text'], FILTER_SANITIZE_SPECIAL_CHARS);
            }

            if (isset($cleaned_post['size']) and $cleaned_post['size']) {
                $array_contenu['size'] = filter_var($cleaned_post['size'], FILTER_SANITIZE_SPECIAL_CHARS);
            }

            if (isset($cleaned_post['border'])) {
                $array_contenu['border'] = filter_var($cleaned_post['border'], FILTER_SANITIZE_SPECIAL_CHARS);
            }
            if (isset($cleaned_post['border_color']) and $cleaned_post['border_color']) {
                $array_contenu['border_color'] = filter_var($cleaned_post['border_color'], FILTER_SANITIZE_SPECIAL_CHARS);
            }
            if (isset($cleaned_post['border_radius'])) {
                $array_contenu['border_radius'] = filter_var($cleaned_post['border_radius'], FILTER_SANITIZE_SPECIAL_CHARS);
            }

            if (isset($cleaned_post['hover_color']) and $cleaned_post['hover_color']) {
                $array_contenu['hover_color'] = filter_var($cleaned_post['hover_color'], FILTER_SANITIZE_SPECIAL_CHARS);
            }
            if (isset($cleaned_post['hover_color_text']) and $cleaned_post['hover_color_text']) {
                $array_contenu['hover_color_text'] = filter_var($cleaned_post['hover_color_text'], FILTER_SANITIZE_SPECIAL_CHARS);
            }
            if (isset($cleaned_post['hover_border'])) {
                $array_contenu['hover_border'] = filter_var($cleaned_post['hover_border'], FILTER_VALIDATE_INT);
            }
            if (isset($cleaned_post['hover_border_color']) and $cleaned_post['hover_border_color']) {
                $array_contenu['hover_border_color'] = filter_var($cleaned_post['hover_border_color'], FILTER_SANITIZE_SPECIAL_CHARS);
            }

            //popup
            $array_contenu['p_bg_color'] = filter_var($cleaned_post['p_bg_color'], FILTER_SANITIZE_SPECIAL_CHARS);
            $array_contenu['p_bg_opacity'] = filter_var($cleaned_post['p_bg_opacity'], FILTER_SANITIZE_SPECIAL_CHARS);
            if (!$array_contenu['p_bg_opacity']) {
                $array_contenu['p_bg_opacity'] = 1;
            }

            $array_contenu['p_border_color'] = filter_var($cleaned_post['p_border_color'], FILTER_SANITIZE_SPECIAL_CHARS);
            $array_contenu['p_border_radius'] = filter_var($cleaned_post['p_border_radius'], FILTER_VALIDATE_INT);

            $array_contenu['p_width'] = filter_var($cleaned_post['p_width'], FILTER_VALIDATE_INT);
            $array_contenu['p_height'] = filter_var($cleaned_post['p_height'], FILTER_VALIDATE_INT);
            if ($array_contenu['p_width'] and $array_contenu['p_width'] < 0) {
                $array_contenu['p_width'] = 0;
            }
            if ($array_contenu['p_height'] and $array_contenu['p_height'] < 0) {
                $array_contenu['p_height'] = 0;
            }
        } else {
            $array_update['eval'] = 0;
        }

        $array_update['contenu'] = $array_contenu;

        return $array_update;
    }

    /********************************************************/
    /********************** FUNCTIONS ***********************/
    /********************************************************/
}
